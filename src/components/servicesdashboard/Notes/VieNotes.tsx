import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  BookAIcon,
  Edit3,
  Save,
  Calendar,
  Clock,
  User,
  Tag,
  FileText,
  Paperclip,
  Star,
  Heart,
  Share2,
  Download,
  Trash2,
  <PERSON>py,
  Eye,
  Pin,
  Lock,
  Unlock,
  Archive,
  MoreHorizontal,
  Sparkles,
  MessageSquare,
  History,
  Flag
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface NoteData {
  id: number;
  title: string;
  value: string;
  icon: typeof BookAIcon;
  change: string;
  changeLabel: string;
  description: string;
  attachment: File | null;
  // Enhanced properties for rich viewing
  createdAt?: string;
  updatedAt?: string;
  author?: string;
  tags?: string[];
  isPinned?: boolean;
  isPrivate?: boolean;
  wordCount?: number;
  readTime?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
}

interface ViewNoteModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  noteData: NoteData | null;
  onSubmit: (noteData: Omit<NoteData, "id" | "icon" | "change" | "changeLabel">) => void;
}

export default function ViewNoteModal({
  isOpen,
  onOpenChange,
  noteData,
  onSubmit,
}: ViewNoteModalProps) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    value: "Draft" as "Draft" | "Finalized",
    description: "",
    attachment: null as File | null,
  });

  // Sync formData with noteData when modal opens or noteData changes
  useEffect(() => {
    if (noteData) {
      setFormData({
        title: noteData.title,
        value: noteData.value as "Draft" | "Finalized",
        description: noteData.description,
        attachment: noteData.attachment,
      });
    }
  }, [noteData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };



  const handleSave = () => {
    onSubmit({
      title: formData.title,
      value: formData.value,
      description: formData.description,
      attachment: formData.attachment,
    });
    setIsEditMode(false);
  };

  const handleClose = () => {
    onOpenChange(false);
    setIsEditMode(false);
  };

  // Calculate reading time
  const calculateReadTime = (text: string) => {
    const wordsPerMinute = 200;
    const words = text.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  // Get priority color
  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high': return 'from-green-500 to-emerald-500';
      case 'medium': return 'from-yellow-500 to-orange-500';
      case 'low': return 'from-green-500 to-emerald-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  // Get category icon
  const getCategoryIcon = (category?: string) => {
    switch (category) {
      case 'Meeting': return Calendar;
      case 'Important': return Star;
      case 'General': return FileText;
      default: return BookAIcon;
    }
  };

  // Provide fallback data if noteData is missing
  const safeNoteData = noteData || {
    id: 1,
    title: "Sample Note",
    value: "Draft",
    icon: BookAIcon,
    change: "2h",
    changeLabel: "2 hours ago",
    description: "This is a sample note to demonstrate the beautiful ViewNotes modal.",
    attachment: null,
    author: "Demo User",
    category: "General"
  };

  if (!isOpen) return null;

  const CategoryIcon = getCategoryIcon(safeNoteData.category);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-3xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden"
          >
            {/* Header with Gradient */}
            <div className="relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-500"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-pink-500/20"></div>

              {/* Animated background elements */}
              <div className="absolute top-4 left-8 w-12 h-12 bg-white/10 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute top-8 right-16 w-8 h-8 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
              <div className="absolute bottom-4 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-xl animate-pulse delay-500"></div>

              <div className="relative p-8">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    {/* Note Icon */}
                    <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm shadow-lg">
                      <CategoryIcon className="h-8 w-8 text-white" />
                    </div>

                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      {isEditMode ? (
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={handleInputChange}
                          className="text-2xl font-bold bg-white/20 border-white/30 text-white placeholder-white/70 backdrop-blur-sm"
                          placeholder="Note title..."
                        />
                      ) : (
                        <h1 className="text-3xl font-bold text-white mb-2 leading-tight">
                          {safeNoteData.title}
                        </h1>
                      )}

                      {/* Meta Information */}
                      <div className="flex items-center gap-4 text-white/80 text-sm">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          <span>{safeNoteData.author || 'Unknown Author'}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{calculateReadTime(safeNoteData.description)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{safeNoteData.changeLabel}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  
                </div>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex">
              {/* Main Content */}
              <div className="flex-1 p-8 overflow-y-auto max-h-[calc(95vh-200px)]">
                {/* Tags */}
                {safeNoteData.tags && safeNoteData.tags.length > 0 && (
                  <div className="mb-6">
                    <div className="flex flex-wrap gap-2">
                      {safeNoteData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 rounded-full text-sm font-medium border border-blue-200"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Priority Indicator */}
                {safeNoteData.priority && (
                  <div className="mb-6">
                    <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-xl bg-gradient-to-r ${getPriorityColor(safeNoteData.priority)} text-white shadow-lg`}>
                      <Flag className="h-4 w-4" />
                      <span className="text-sm font-semibold capitalize">{safeNoteData.priority} Priority</span>
                    </div>
                  </div>
                )}

                {/* Content */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FileText className="h-5 w-5 text-indigo-600" />
                    Content
                  </h3>
                  {isEditMode ? (
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="w-full min-h-[200px] border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-xl resize-none"
                      placeholder="Write your note content here..."
                    />
                  ) : (
                    <div className="prose prose-lg max-w-none">
                      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                        <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                          {safeNoteData.description || 'No content available.'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Attachment */}
                {safeNoteData.attachment && (
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Paperclip className="h-5 w-5 text-indigo-600" />
                      Attachment
                    </h3>
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-500 rounded-lg">
                          <Paperclip className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{safeNoteData.attachment.name}</p>
                          <p className="text-sm text-gray-600">Click to download</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Word Count & Stats */}
                <div className="mb-8">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-700">Words</span>
                      </div>
                      <p className="text-2xl font-bold text-purple-900">
                        {safeNoteData.description.split(' ').length}
                      </p>
                    </div>
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-700">Read Time</span>
                      </div>
                      <p className="text-2xl font-bold text-green-900">
                        {calculateReadTime(safeNoteData.description)}
                      </p>
                    </div>
                    <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border border-orange-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Calendar className="h-4 w-4 text-orange-600" />
                        <span className="text-sm font-medium text-orange-700">Updated</span>
                      </div>
                      <p className="text-sm font-bold text-orange-900">
                        {safeNoteData.changeLabel}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="w-80 bg-gray-50 border-l border-gray-200 p-6">
                

                

                <hr className="my-6" />

                

                {/* Note Info */}
                
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}