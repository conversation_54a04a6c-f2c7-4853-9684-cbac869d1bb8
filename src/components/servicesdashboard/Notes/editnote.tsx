import React, { useState, useEffect } from "react";
import {
  FileText,
  Star,
  Calendar,
  Hash,
  Clock,
  User,
  Building2,
  <PERSON>,
  <PERSON>rk<PERSON>,
  CheckCircle2,
  Edit3
} from "lucide-react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";

interface NoteData {
  title: string;
  value: string; // Maps to note_type (e.g., "General", "Important")
  description: string;
  entity_type: string; // "customer", "prospect", or "leadfile"
  entity_id: string;
}

interface CreateNoteModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSubmit: (noteData: NoteData) => void;
  noteData?: NoteData; // Optional for editing
}

export default function CreateNoteModal({
  isOpen,
  onOpenChange,
  onSubmit,
  noteData,
}: CreateNoteModalProps) {
  const isEditMode = !!noteData;
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<NoteData>({
    title: "",
    value: "General", // Default note_type
    description: "",
    entity_type: "customer", // Default entity_type
    entity_id: "",
  });

  useEffect(() => {
    if (isEditMode && noteData) {
      setFormData({
        title: noteData.title,
        value: noteData.value,
        description: noteData.description,
        entity_type: noteData.entity_type,
        entity_id: noteData.entity_id,
      });
    } else {
      setFormData({
        title: "",
        value: "General",
        description: "",
        entity_type: "customer",
        entity_id: "",
      });
    }
  }, [isEditMode, noteData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleNoteTypeChange = (value: string) => {
    setFormData((prev) => ({ ...prev, value }));
  };

  const handleEntityTypeChange = (entity_type: string) => {
    setFormData((prev) => ({ ...prev, entity_type }));
  };

  const handleComplete = () => {
    onSubmit(formData);
    setCurrentStep(0);
    setFormData({
      title: "",
      value: "General",
      description: "",
      entity_type: "customer",
      entity_id: "",
    });
    onOpenChange(false);
  };

  // Get icon for note type
  const getNoteTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'meeting': return Calendar;
      case 'important': return Star;
      case 'general': return FileText;
      default: return FileText;
    }
  };

  // Get icon for entity type
  const getEntityTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'customer': return User;
      case 'prospect': return Users;
      case 'leadfile': return Building2;
      default: return User;
    }
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={(open) => {
        onOpenChange(open);
        if (!open) {
          setCurrentStep(0);
          setFormData({
            title: "",
            value: "General",
            description: "",
            entity_type: "customer",
            entity_id: "",
          });
        }
      }}
      title={isEditMode ? "Edit Note" : "Create New Note"}
      description={isEditMode ? "Update your note with the latest information" : "Capture your thoughts and ideas in a beautiful note"}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleComplete}
      size="lg"
      icon={
        <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl shadow-lg">
          <Edit3 className="h-6 w-6 text-white" />
        </div>
      }
      steps={[
        {
          title: "Note Details",
          content: (
            <div className="space-y-6 py-4">
              {/* Modern Title Input */}
              <div className="space-y-3">
                <Label htmlFor="title" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <div className="p-1 bg-indigo-100 rounded-md">
                    <Edit3 className="h-3 w-3 text-indigo-600" />
                  </div>
                  Note Title
                </Label>
                <Input
                  id="title"
                  placeholder="Give your note a descriptive title..."
                  value={formData.title}
                  onChange={handleInputChange}
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-xl px-4 py-3 text-base"
                />
              </div>

              {/* Modern Content Input */}
              <div className="space-y-3">
                <Label htmlFor="description" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <div className="p-1 bg-purple-100 rounded-md">
                    <FileText className="h-3 w-3 text-purple-600" />
                  </div>
                  Note Content
                </Label>
                <Textarea
                  id="description"
                  placeholder="Write your note content here..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="border-gray-300 focus:border-purple-500 focus:ring-purple-500 rounded-xl px-4 py-3 text-base min-h-[120px] resize-none"
                  rows={4}
                />
              </div>
              {/* Modern Note Type Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <div className="p-1 bg-green-100 rounded-md">
                    <Hash className="h-3 w-3 text-green-600" />
                  </div>
                  Note Type
                </Label>
                <RadioGroup
                  value={formData.value}
                  onValueChange={handleNoteTypeChange}
                  className="grid grid-cols-3 gap-3"
                >
                  {[
                    { value: "General", icon: FileText, color: "from-blue-500 to-indigo-500", bg: "bg-blue-50", border: "border-blue-200" },
                    { value: "Important", icon: Star, color: "from-yellow-500 to-orange-500", bg: "bg-yellow-50", border: "border-yellow-200" },
                    { value: "Meeting", icon: Calendar, color: "from-green-500 to-emerald-500", bg: "bg-green-50", border: "border-green-200" }
                  ].map((type) => {
                    const Icon = type.icon;
                    const isSelected = formData.value === type.value;
                    return (
                      <div key={type.value} className="relative">
                        <RadioGroupItem value={type.value} id={type.value.toLowerCase()} className="sr-only" />
                        <Label
                          htmlFor={type.value.toLowerCase()}
                          className={`flex flex-col items-center gap-3 p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:scale-105 ${
                            isSelected
                              ? `${type.border} ${type.bg} shadow-lg`
                              : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                          }`}
                        >
                          <div className={`p-3 rounded-lg bg-gradient-to-r ${type.color} shadow-md`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <span className={`text-sm font-medium ${isSelected ? 'text-gray-900' : 'text-gray-600'}`}>
                            {type.value}
                          </span>
                          {isSelected && (
                            <div className="absolute top-2 right-2">
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            </div>
                          )}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>
              {/* Modern Entity Type Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <div className="p-1 bg-pink-100 rounded-md">
                    <Users className="h-3 w-3 text-pink-600" />
                  </div>
                  Entity Type
                </Label>
                <RadioGroup
                  value={formData.entity_type}
                  onValueChange={handleEntityTypeChange}
                  className="grid grid-cols-3 gap-3"
                >
                  {[
                    { value: "customer", icon: User, color: "from-purple-500 to-pink-500", bg: "bg-purple-50", border: "border-purple-200", label: "Customer" },
                    { value: "prospect", icon: Users, color: "from-teal-500 to-cyan-500", bg: "bg-teal-50", border: "border-teal-200", label: "Prospect" },
                    { value: "leadfile", icon: Building2, color: "from-orange-500 to-red-500", bg: "bg-orange-50", border: "border-orange-200", label: "Lead File" }
                  ].map((type) => {
                    const Icon = type.icon;
                    const isSelected = formData.entity_type === type.value;
                    return (
                      <div key={type.value} className="relative">
                        <RadioGroupItem value={type.value} id={type.value} className="sr-only" />
                        <Label
                          htmlFor={type.value}
                          className={`flex flex-col items-center gap-3 p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:scale-105 ${
                            isSelected
                              ? `${type.border} ${type.bg} shadow-lg`
                              : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                          }`}
                        >
                          <div className={`p-3 rounded-lg bg-gradient-to-r ${type.color} shadow-md`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <span className={`text-sm font-medium ${isSelected ? 'text-gray-900' : 'text-gray-600'}`}>
                            {type.label}
                          </span>
                          {isSelected && (
                            <div className="absolute top-2 right-2">
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            </div>
                          )}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>
              {/* Modern Entity ID Input */}
              <div className="space-y-3">
                <Label htmlFor="entity_id" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <div className="p-1 bg-gray-100 rounded-md">
                    <Hash className="h-3 w-3 text-gray-600" />
                  </div>
                  Entity ID
                </Label>
                <Input
                  id="entity_id"
                  placeholder="Enter the entity ID (e.g., cust-123, lead-456)"
                  value={formData.entity_id}
                  onChange={handleInputChange}
                  className="border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-xl px-4 py-3 text-base"
                />
                <p className="text-xs text-gray-500 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  This helps organize your notes by linking them to specific entities
                </p>
              </div>
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="py-6">
              {/* Success Icon */}
              <div className="relative mx-auto w-20 h-20 mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-lg opacity-30"></div>
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 rounded-full w-full h-full flex items-center justify-center shadow-lg">
                  <Sparkles className="w-10 h-10 text-white" />
                </div>
              </div>

              {/* Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
                  {isEditMode ? "Update Note" : "Create Note"}
                </h3>
                <p className="text-gray-600">
                  {isEditMode ? "Review your changes before updating" : "Review your note details before creating"}
                </p>
              </div>

              {/* Modern Summary Cards */}
              <div className="space-y-4">
                {/* Title Card */}
                <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-200">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg shadow-md">
                      <Edit3 className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-xs font-semibold text-indigo-700 uppercase tracking-wider mb-1">Title</p>
                      <p className="text-gray-900 font-medium">{formData.title || "No title provided"}</p>
                    </div>
                  </div>
                </div>

                {/* Content Card */}
                <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg shadow-md">
                      <FileText className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-xs font-semibold text-purple-700 uppercase tracking-wider mb-1">Content</p>
                      <p className="text-gray-900 font-medium line-clamp-2">{formData.description || "No content provided"}</p>
                    </div>
                  </div>
                </div>

                {/* Type & Entity Info */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg shadow-md">
                        {React.createElement(getNoteTypeIcon(formData.value), { className: "h-4 w-4 text-white" })}
                      </div>
                      <div>
                        <p className="text-xs font-semibold text-green-700 uppercase tracking-wider">Type</p>
                        <p className="text-gray-900 font-medium">{formData.value}</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg shadow-md">
                        {React.createElement(getEntityTypeIcon(formData.entity_type), { className: "h-4 w-4 text-white" })}
                      </div>
                      <div>
                        <p className="text-xs font-semibold text-orange-700 uppercase tracking-wider">Entity</p>
                        <p className="text-gray-900 font-medium">{formData.entity_type}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Entity ID Card */}
                {formData.entity_id && (
                  <div className="p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-gray-500 to-slate-500 rounded-lg shadow-md">
                        <Hash className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="text-xs font-semibold text-gray-700 uppercase tracking-wider">Entity ID</p>
                        <p className="text-gray-900 font-medium font-mono">{formData.entity_id}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ),
        },
      ]}
    />
  );
}