import { useState, useMemo } from "react";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  FileText,
  Users,
  UserPlus,
  DollarSign,
  Search,
  Filter,
  Plus,
  Calendar,
  Clock,
  Star,
  Pin,
  Lock,
  Eye,
  Edit3,
  Trash2,
  Download,
  RefreshCw,
  BarChart3,
  TrendingUp,
  Activity,
  CheckSquare,
  Lightbulb,
  ArrowRight,
  Phone,
  Mail,
} from "lucide-react";
import { 
  useGetNotesQuery,
  useGetCustomerNotesQuery,
  useGetProspectNotesQuery,
  useGetSalesNotesQuery,
  useGetNotesMetricsQuery,
  useDeleteNoteMutation
} from "@/redux/slices/services";
import { NotesFilters, NOTE_CATEGORIES, NOTE_TYPES } from "@/types/notes";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import ViewNoteModal from "../../servicesdashboard/Notes/VieNotes";
import CreateNoteModal from "../../servicesdashboard/Notes/editnote";

// Icon mapping for note types
const getIconComponent = (iconName: string) => {
  const icons: Record<string, any> = {
    FileText, Calendar, Star, CheckSquare, Lightbulb, Clock, ArrowRight, Phone, Mail,
    Users, UserPlus, DollarSign
  };
  return icons[iconName] || FileText;
};

interface ClassifiedNotesViewProps {
  defaultEntityType?: "customer" | "prospect" | "sales" | "general";
}

export default function ClassifiedNotesView({ defaultEntityType = "general" }: ClassifiedNotesViewProps) {
  const [activeTab, setActiveTab] = useState(defaultEntityType);
  const [filters, setFilters] = useState<NotesFilters>({
    entity_type: defaultEntityType === "general" ? "" : defaultEntityType,
    search: "",
    note_type: "",
    priority: "",
    status: "",
  });
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<any>(null);

  // Fetch data based on active tab
  const { 
    data: allNotesData, 
    isLoading: allNotesLoading, 
    refetch: refetchAllNotes 
  } = useGetNotesQuery(filters, { skip: activeTab !== "all" });

  const { 
    data: customerNotesData, 
    isLoading: customerNotesLoading,
    refetch: refetchCustomerNotes
  } = useGetCustomerNotesQuery({ filters }, { skip: activeTab !== "customer" });

  const { 
    data: prospectNotesData, 
    isLoading: prospectNotesLoading,
    refetch: refetchProspectNotes
  } = useGetProspectNotesQuery({ filters }, { skip: activeTab !== "prospect" });

  const { 
    data: salesNotesData, 
    isLoading: salesNotesLoading,
    refetch: refetchSalesNotes
  } = useGetSalesNotesQuery({ filters }, { skip: activeTab !== "sales" });

  const { data: metricsData } = useGetNotesMetricsQuery();
  const [deleteNote] = useDeleteNoteMutation();

  // Get current data based on active tab
  const currentData = useMemo(() => {
    switch (activeTab) {
      case "customer": return customerNotesData;
      case "prospect": return prospectNotesData;
      case "sales": return salesNotesData;
      default: return allNotesData;
    }
  }, [activeTab, allNotesData, customerNotesData, prospectNotesData, salesNotesData]);

  const isLoading = useMemo(() => {
    switch (activeTab) {
      case "customer": return customerNotesLoading;
      case "prospect": return prospectNotesLoading;
      case "sales": return salesNotesLoading;
      default: return allNotesLoading;
    }
  }, [activeTab, allNotesLoading, customerNotesLoading, prospectNotesLoading, salesNotesLoading]);

  // Calculate metrics for display
  const metrics = useMemo(() => {
    const notes = currentData?.data?.results || [];
    return [
      {
        title: "Total Notes",
        value: metricsData?.totalNotes || notes.length,
        icon: FileText,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
        change: "+12%",
        changeLabel: "this month"
      },
      {
        title: "Customer Notes",
        value: metricsData?.customerNotes || 0,
        icon: Users,
        color: "text-green-600",
        bgColor: "bg-green-50",
        change: "+8%",
        changeLabel: "this week"
      },
      {
        title: "Prospect Notes",
        value: metricsData?.prospectNotes || 0,
        icon: UserPlus,
        color: "text-purple-600",
        bgColor: "bg-purple-50",
        change: "+15%",
        changeLabel: "this week"
      },
      {
        title: "Sales Notes",
        value: metricsData?.salesNotes || 0,
        icon: DollarSign,
        color: "text-emerald-600",
        bgColor: "bg-emerald-50",
        change: "+5%",
        changeLabel: "this month"
      },
    ];
  }, [currentData, metricsData]);

  const handleFilterChange = (key: keyof NotesFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setFilters(prev => ({
      ...prev,
      entity_type: value === "all" ? "" : value as any
    }));
  };

  const handleViewNote = (note: any) => {
    setSelectedNote(note);
    setIsViewModalOpen(true);
  };

  const handleDeleteNote = async (noteId: string) => {
    if (window.confirm("Are you sure you want to delete this note?")) {
      try {
        await deleteNote(noteId).unwrap();
        // Refetch current data
        switch (activeTab) {
          case "customer": refetchCustomerNotes(); break;
          case "prospect": refetchProspectNotes(); break;
          case "sales": refetchSalesNotes(); break;
          default: refetchAllNotes(); break;
        }
      } catch (error) {
        console.error("Failed to delete note:", error);
      }
    }
  };

  const handleRefresh = () => {
    switch (activeTab) {
      case "customer": refetchCustomerNotes(); break;
      case "prospect": refetchProspectNotes(); break;
      case "sales": refetchSalesNotes(); break;
      default: refetchAllNotes(); break;
    }
  };

  return (
    <Screen>
      <div className="space-y-8">
        {/* Enhanced Header */}
        <div className="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 dark:from-indigo-800 dark:via-purple-800 dark:to-pink-800 rounded-2xl shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative px-8 py-12">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                    <FileText className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold text-white">Classified Notes</h1>
                    <p className="text-indigo-100 text-lg">Organize notes by customers, prospects, and sales</p>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <PrimaryButton 
                  onClick={() => setIsCreateModalOpen(true)}
                  className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  New Note
                </PrimaryButton>
                <Button 
                  onClick={handleRefresh}
                  variant="outline"
                  className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30"
                >
                  <RefreshCw className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <Card key={metric.title} className={`${metric.bgColor} border-0 shadow-lg hover:shadow-xl transition-all duration-300`}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <metric.icon className={`h-8 w-8 ${metric.color}`} />
                  <TrendingUp className="h-4 w-4 text-green-500" />
                </div>
                <CardTitle className={`text-sm font-medium ${metric.color}`}>
                  {metric.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {metric.value}
                </div>
                <p className="text-sm text-gray-600">
                  <span className="text-green-600 font-medium">{metric.change}</span> {metric.changeLabel}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters Section */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>Filters & Search</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search notes..."
                    value={filters.search || ""}
                    onChange={(e) => handleFilterChange("search", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Note Type</label>
                <Select value={filters.note_type || ""} onValueChange={(value) => handleFilterChange("note_type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All types</SelectItem>
                    {Object.entries(NOTE_TYPES).map(([type, config]) => (
                      <SelectItem key={type} value={type}>
                        <div className="flex items-center space-x-2">
                          <span>{type}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <Select value={filters.priority || ""} onValueChange={(value) => handleFilterChange("priority", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All priorities</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={filters.status || ""} onValueChange={(value) => handleFilterChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="finalized">Finalized</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Actions</label>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={handleRefresh}>
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Classified Notes Tabs */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-4">
            <TabsTrigger value="all" className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>All Notes</span>
            </TabsTrigger>
            <TabsTrigger value="customer" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>Customers</span>
            </TabsTrigger>
            <TabsTrigger value="prospect" className="flex items-center space-x-2">
              <UserPlus className="w-4 h-4" />
              <span>Prospects</span>
            </TabsTrigger>
            <TabsTrigger value="sales" className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4" />
              <span>Sales</span>
            </TabsTrigger>
          </TabsList>

          {/* Tab Content */}
          {["all", "customer", "prospect", "sales"].map((tabValue) => (
            <TabsContent key={tabValue} value={tabValue} className="space-y-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-16">
                  <SpinnerTemp type="spinner-double" size="lg" />
                </div>
              ) : (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {currentData?.data?.results?.map((note: any) => {
                    const NoteTypeIcon = getIconComponent(NOTE_TYPES[note.note_type as keyof typeof NOTE_TYPES]?.icon || "FileText");

                    return (
                      <Card key={note.note_id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-lg bg-gradient-to-r ${
                                note.priority === 'high' ? 'from-red-500 to-pink-500' :
                                note.priority === 'medium' ? 'from-yellow-500 to-orange-500' :
                                'from-blue-500 to-indigo-500'
                              }`}>
                                <NoteTypeIcon className="w-5 h-5 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="font-semibold text-gray-900 truncate">{note.title}</h3>
                                <div className="flex items-center space-x-2 mt-1">
                                  <Badge variant="secondary" className="text-xs">
                                    {note.note_type}
                                  </Badge>
                                  {note.entity_type && (
                                    <Badge variant="outline" className="text-xs">
                                      {note.entity_type}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-1">
                              {note.is_pinned && <Pin className="w-4 h-4 text-yellow-500" />}
                              {note.is_private && <Lock className="w-4 h-4 text-red-500" />}
                            </div>
                          </div>
                        </CardHeader>

                        <CardContent className="pt-0">
                          <p className="text-sm text-gray-600 line-clamp-3 mb-4">
                            {note.content || "No content available"}
                          </p>

                          {note.entity_name && (
                            <div className="text-xs text-gray-500 mb-3">
                              Related to: <span className="font-medium">{note.entity_name}</span>
                            </div>
                          )}

                          <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{new Date(note.updated_at).toLocaleDateString()}</span>
                            </div>
                            {note.priority && (
                              <Badge
                                variant={note.priority === 'high' ? 'destructive' : note.priority === 'medium' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {note.priority}
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button size="sm" variant="outline" onClick={() => handleViewNote(note)}>
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit3 className="w-4 h-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteNote(note.note_id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}

              {!isLoading && (!currentData?.data?.results || currentData.data.results.length === 0) && (
                <div className="text-center py-16">
                  <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <FileText className="w-12 h-12 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No notes found</h3>
                  <p className="text-gray-600 mb-6">
                    {activeTab === "all"
                      ? "No notes match your current filters."
                      : `No ${activeTab} notes found. Create your first ${activeTab} note to get started.`
                    }
                  </p>
                  <PrimaryButton onClick={() => setIsCreateModalOpen(true)}>
                    <Plus className="w-5 h-5 mr-2" />
                    Create Note
                  </PrimaryButton>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Modals */}
        <ViewNoteModal
          isOpen={isViewModalOpen}
          onOpenChange={setIsViewModalOpen}
          noteData={selectedNote}
          onSubmit={() => {}}
        />

        <CreateNoteModal
          isOpen={isCreateModalOpen}
          onOpenChange={setIsCreateModalOpen}
          onSubmit={() => {}}
        />
      </div>
    </Screen>
  );
}
