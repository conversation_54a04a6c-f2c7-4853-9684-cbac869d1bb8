import { useEffect } from 'react';
import { Flag, Alert<PERSON>riangle, CheckCircle, Clock, Zap } from 'lucide-react';
import { motion } from 'framer-motion';

// FlagData interface matching the provided API structure
interface FlagData {
  flag_id: string;
  flag_reason: 'Follow-up Required' | 'Payment Issue' | 'Customer Complaint' | 'Documentation Missing' | 'Quality Issue' | 'Process Violation' | 'Other' | null;
  description: string;
  is_resolved: boolean;
  resolution_date: string | null;
  resolution_notes: string | null;
  follow_up_required: boolean;
  follow_up_date: string | null;
  set_reminder: boolean;
  reminder_time: string | null;
  client_type: 'Prospect' | 'Customer' | 'Sale';
  created_at: string;
  created_by: string | null;
  customer: string | null;
  prospect: number | null;
  sale: string | null;
}

export default function FlagsAnalytics({ flags }: { flags: FlagData[] }) {
  // Log flags for debugging
  useEffect(() => {
    console.log('📋 FlagsAnalytics Flags Data:', flags);
  }, [flags]);

  // Calculate metrics
  const total = flags.length;
  const active = flags.filter((f) => !f.is_resolved).length;
  const resolved = flags.filter((f) => f.is_resolved).length;
  const followUpRequired = flags.filter((f) => f.follow_up_required).length;

  // Breakdown by flag_reason
  const followUp = flags.filter((f) => f.flag_reason === 'Follow-up Required').length;
  const paymentIssue = flags.filter((f) => f.flag_reason === 'Payment Issue').length;
  const customerComplaint = flags.filter((f) => f.flag_reason === 'Customer Complaint').length;
  const documentationMissing = flags.filter((f) => f.flag_reason === 'Documentation Missing').length;
  const qualityIssue = flags.filter((f) => f.flag_reason === 'Quality Issue').length;
  const processViolation = flags.filter((f) => f.flag_reason === 'Process Violation').length;
  const other = flags.filter((f) => f.flag_reason === 'Other' || f.flag_reason === null).length;

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: 'easeOut',
      },
    }),
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Flags */}
        <motion.div
          custom={0}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="relative overflow-hidden bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-xl p-6 text-white"
        >
          <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Flag className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="text-3xl font-bold mb-1">{total}</div>
            <div className="text-green-100 text-sm font-medium">Total Flags</div>
          </div>
        </motion.div>

        {/* Active Flags */}
        <motion.div
          custom={1}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="relative overflow-hidden bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl shadow-xl p-6 text-white"
        >
          <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="text-3xl font-bold mb-1">{active}</div>
            <div className="text-orange-100 text-sm font-medium">Active Flags</div>
          </div>
        </motion.div>

        {/* Resolved Flags */}
        <motion.div
          custom={2}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-xl p-6 text-white"
        >
          <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="text-3xl font-bold mb-1">{resolved}</div>
            <div className="text-blue-100 text-sm font-medium">Resolved</div>
          </div>
        </motion.div>

        {/* Follow-up Required Flags */}
        <motion.div
          custom={3}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="relative overflow-hidden bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl shadow-xl p-6 text-white"
        >
          <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Clock className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="text-3xl font-bold mb-1">{followUpRequired}</div>
            <div className="text-yellow-100 text-sm font-medium">Follow-up Required</div>
          </div>
        </motion.div>
      </div>

      {/* Reason Breakdown */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border-2 border-blue-100 dark:border-gray-700 p-6 mb-8">
        <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Zap className="h-5 w-5 text-white" />
          </div>
          Reason Breakdown
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{followUp}</div>
            <div className="text-sm text-blue-500 dark:text-blue-400 font-medium">Follow-up</div>
          </div>
          <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">{paymentIssue}</div>
            <div className="text-sm text-red-500 dark:text-red-400 font-medium">Payment Issue</div>
          </div>
          <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-xl border border-orange-200 dark:border-orange-800">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{customerComplaint}</div>
            <div className="text-sm text-orange-500 dark:text-orange-400 font-medium">Complaint</div>
          </div>
          <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{documentationMissing}</div>
            <div className="text-sm text-yellow-500 dark:text-yellow-400 font-medium">Docs Missing</div>
          </div>
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{qualityIssue}</div>
            <div className="text-sm text-purple-500 dark:text-purple-400 font-medium">Quality Issue</div>
          </div>
          <div className="text-center p-4 bg-pink-50 dark:bg-pink-900/20 rounded-xl border border-pink-200 dark:border-pink-800">
            <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">{processViolation}</div>
            <div className="text-sm text-pink-500 dark:text-pink-400 font-medium">Process Violation</div>
          </div>
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-900/20 rounded-xl border border-gray-200 dark:border-gray-800">
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400">{other}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">Other/None</div>
          </div>
        </div>
      </div>
    </div>
  );
}