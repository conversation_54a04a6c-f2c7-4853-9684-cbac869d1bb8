import React from 'react';
import EngagementItem from './EngagementItem';
import { MessageCircle, Inbox } from 'lucide-react';


export interface Engagement {
  id: string;
  unread: boolean;
  status: string;
  title: string;
  customer: string;
  marketingTeam: string;
  createdDate: string;
  starred: boolean;
  participants: { id: string; avatar: string; name: string; role: string }[];
  comments: { id: string; text: string }[];
}

interface EngagementListProps {
  engagements: Engagement[];
  viewFilter: string;
  activeEngagement: string | null;
  handleEngagementClick: (id: string) => void;
  handleStarToggle: (e: React.MouseEvent<HTMLButtonElement>, id: string) => void;
  newComment: string;
  setNewComment: React.Dispatch<React.SetStateAction<string>>;
  attachments: File[];
  setAttachments: React.Dispatch<React.SetStateAction<File[]>>;
  handleAddComment: (engagementId: string, comment: string) => void;
  handleAttachFile: (file: File) => void;
}

function EngagementList({
  engagements,
  viewFilter,
  activeEngagement,
  handleEngagementClick,
  handleStarToggle,
  newComment,
  setNewComment,
  attachments,
  setAttachments,
  handleAddComment,
  handleAttachFile,
}: EngagementListProps) {
  const filteredEngagements = engagements.filter((engagement) => {
    if (viewFilter === 'all') return true;
    if (viewFilter === 'starred') return engagement.starred;
    if (viewFilter === 'unread') return engagement.unread;
    return true;
  });

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div className="bg-white overflow-y-auto">
        {filteredEngagements.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <Inbox className="w-16 h-16 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-1">No engagements found</h3>
            <p className="text-gray-500">Try adjusting your filters or create a new engagement</p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filteredEngagements.map((engagement) => (
              <EngagementItem
                key={engagement.id}
                engagement={engagement}
                activeEngagement={activeEngagement}
                handleEngagementClick={handleEngagementClick}
                handleStarToggle={handleStarToggle}
                newComment={newComment}
                setNewComment={setNewComment}
                attachments={attachments}
                setAttachments={setAttachments}
                handleAddComment={handleAddComment}
                handleAttachFile={handleAttachFile}
              />
            ))}
          </ul>
        )}
      </div>
      {!activeEngagement && (
        <div className="flex-1 flex flex-col items-center justify-center bg-gray-50 text-center p-8">
          <div className="w-24 h-24 rounded-full bg-blue-100 flex items-center justify-center mb-4">
            <MessageCircle className="w-12 h-12 text-blue-500" />
          </div>
          <h2 className="text-xl font-medium text-gray-900 mb-2">Select a marketing engagement</h2>
          <p className="text-gray-600 max-w-md">
            Select an engagement from the list to view the conversation between customers and marketers
          </p>
        </div>
      )}
    </div>
  );
}

export default EngagementList;