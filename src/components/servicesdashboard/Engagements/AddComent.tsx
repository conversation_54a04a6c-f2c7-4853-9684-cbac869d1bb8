import React from 'react';
import { Paperclip, Send } from 'lucide-react';

interface AddCommentProps {
  engagementId: string;
  newComment: string;
  setNewComment: React.Dispatch<React.SetStateAction<string>>;
  attachments: File[];
  setAttachments: React.Dispatch<React.SetStateAction<File[]>>;
  handleAddComment: (engagementId: string, comment: string) => void;
  handleAttachFile: (file: File) => void;
}

function AddComment({
  engagementId,
  newComment,
  setNewComment,
  attachments,
  setAttachments,
  handleAddComment,
  handleAttachFile,
}: AddCommentProps) {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    handleAttachFile(file);
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  return (
    <div className="mt-4">
      <div className="border border-gray-200 rounded-md p-2">
        <textarea
          className="w-full resize-none border-0 focus:ring-0 focus:outline-none"
          rows={3}
          placeholder="Add a comment..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
        />
        
        {attachments.length > 0 && (
          <div className="mt-2 space-y-2">
            {attachments.map((file, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <div className="flex items-center">
                  <Paperclip className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm truncate max-w-xs">{file.name}</span>
                </div>
                <button 
                  onClick={() => handleRemoveAttachment(index)}
                  className="text-red-500 text-sm hover:text-red-700"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        )}
        
        <div className="flex items-center justify-between pt-2 border-t mt-2">
          <div>
            <label className="cursor-pointer flex items-center text-gray-500 hover:text-gray-700">
              <Paperclip className="w-5 h-5" />
              <input 
                type="file" 
                className="hidden" 
                onChange={handleFileChange}
              />
            </label>
          </div>
          <button
            onClick={() => handleAddComment(engagementId, newComment)}
            disabled={!newComment.trim()}
            className={`inline-flex items-center px-3 py-1 rounded-md text-sm 
              ${newComment.trim() ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-300 text-gray-500'}`}
          >
            <Send className="w-4 h-4 mr-1" />
            Send
          </button>
        </div>
      </div>
    </div>
  );
}

export default AddComment;