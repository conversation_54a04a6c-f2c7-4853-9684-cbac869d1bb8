import React from 'react';
import { Star, <PERSON>Off, ChevronUp, ChevronDown, AlertCircle, Clock, CheckCircle } from 'lucide-react';
import Comment from './Comment';
import AddComment from './AddComent';
import { PrimaryButton } from '@/components/custom/buttons/buttons';

interface Engagement {
  id: string;
  unread: boolean;
  status: string;
  title: string;
  customer: string;
  marketingTeam: string;
  createdDate: string;
  starred: boolean;
  participants: { id: string; avatar: string; name: string; role: string }[];
  comments: { id: string; text: string }[];
}

interface EngagementItemProps {
  engagement: Engagement;
  activeEngagement: string | null;
  handleEngagementClick: (id: string) => void;
  handleStarToggle: (e: React.MouseEvent<HTMLButtonElement>, id: string) => void;
  newComment: string;
  setNewComment: React.Dispatch<React.SetStateAction<string>>;
  attachments: File[];
  setAttachments: React.Dispatch<React.SetStateAction<File[]>>;
  handleAddComment: (engagementId: string, comment: string) => void;
  handleAttachFile: (file: File) => void;
}

function EngagementItem({
  engagement,
  activeEngagement,
  handleEngagementClick,
  handleStarToggle,
  newComment,
  setNewComment,
  attachments,
  setAttachments,
  handleAddComment,
  handleAttachFile,
}: EngagementItemProps) {
  function getStatusStyle(status: string) {
    switch (status) {
      case 'Critical':
        return {
          bg: 'bg-red-100',
          text: 'text-red-800',
          icon: <AlertCircle className="w-3 h-3" />
        };
      case 'Feedback Required':
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          icon: <Clock className="w-3 h-3" />
        };
      default:
        return {
          bg: 'bg-green-100',
          text: 'text-green-800',
          icon: <CheckCircle className="w-3 h-3" />
        };
    }
  }

  function getTimeSince(createdDate: string) {
    const date = new Date(createdDate);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return diffInHours === 1 ? '1 hour ago' : `${diffInHours} hours ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return diffInDays === 1 ? '1 day ago' : `${diffInDays} days ago`;
    }
  }

  return (
    <li
      className={`${engagement.unread ? 'bg-blue-50' : 'bg-white'} hover:bg-gray-50 cursor-pointer border-l-4 ${
        engagement.status === 'Critical'
          ? 'border-red-500'
          : engagement.status === 'Feedback Required'
          ? 'border-yellow-500'
          : 'border-transparent'
      }`}
    >
      <div className="px-4 py-4" onClick={() => handleEngagementClick(engagement.id)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <PrimaryButton
              onClick={(e) => {
                return handleStarToggle(e, engagement.id);
              }}
              className="text-gray-400 hover:text-yellow-500"
            >
              {engagement.starred ? (
                <Star className="w-5 h-5 text-yellow-500" />
              ) : (
                <StarOff className="w-5 h-5" />
              )}
            </PrimaryButton>
            <div className="flex flex-col">
              <div className="flex items-center">
                <h3
                  className={`text-base ${
                    engagement.unread ? 'font-semibold' : 'font-medium'
                  } text-gray-900`}
                >
                  {engagement.title}
                </h3>
                <span
                  className={`ml-2 px-2 py-0.5 rounded-full text-xs font-medium flex items-center ${getStatusStyle(
                    engagement.status
                  ).bg} ${getStatusStyle(engagement.status).text}`}
                >
                  {getStatusStyle(engagement.status).icon}
                  <span className="ml-1">{engagement.status}</span>
                </span>
              </div>
              <div className="text-sm text-gray-500 mt-1">
                <span className="font-medium">{engagement.customer}</span> •{' '}
                {engagement.marketingTeam}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">{getTimeSince(engagement.createdDate)}</div>
            <div className="flex -space-x-2">
              {engagement.participants.slice(0, 3).map((participant) => (
                <img
                  key={participant.id}
                  src={participant.avatar}
                  alt={participant.name}
                  className="w-6 h-6 rounded-full border border-white"
                  title={`${participant.name} (${participant.role})`}
                />
              ))}
              {engagement.participants.length > 3 && (
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs border border-white">
                  +{engagement.participants.length - 3}
                </div>
              )}
            </div>
            <div>
              {activeEngagement === engagement.id ? (
                <ChevronUp className="w-5 h-5 text-gray-400" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-400" />
              )}
            </div>
          </div>
        </div>
        {activeEngagement === engagement.id && (
          <div className="mt-4 pl-8">
            <div className="mt-4 space-y-4">
              {engagement.comments.map((comment) => (
                <Comment key={comment.id} comment={comment} />
              ))}
            </div>
            <AddComment
              engagementId={engagement.id}
              newComment={newComment}
              setNewComment={setNewComment}
              attachments={attachments}
              setAttachments={setAttachments}
              handleAddComment={handleAddComment}
              handleAttachFile={handleAttachFile}
            />
          </div>
        )}
      </div>
    </li>
  );
}

export default EngagementItem;