import { useState, useEffect } from "react";
import { AlertCircle } from "lucide-react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface NotificationData {
  title: string;
  message: string;
  recipient: string;
  entity_type: string; 
  entity_id: string;
  priority: string; // "Low", "Normal", "High", "Urgent"
  notification_type: string; // "Info", "Alert", "Reminder", etc.
}

interface CreateNotificationModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onCreate: (notificationData: NotificationData) => void;
  notificationData?: NotificationData; // Optional for editing
}

export default function CreateNotificationModal({
  isOpen,
  onOpenChange,
  onCreate,
  notificationData,
}: CreateNotificationModalProps) {
  const isEditMode = !!notificationData;
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<NotificationData>({
    title: "",
    message: "",
    recipient: "",
    entity_type: "customer",
    entity_id: "",
    priority: "Normal",
    notification_type: "Info",
  });

  useEffect(() => {
    if (isEditMode && notificationData) {
      setFormData(notificationData);
    } else {
      setFormData({
        title: "",
        message: "",
        recipient: "",
        entity_type: "customer",
        entity_id: "",
        priority: "Normal",
        notification_type: "Info",
      });
    }
  }, [isEditMode, notificationData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleRadioChange = (field: keyof NotificationData) => (value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleComplete = () => {
    onCreate(formData);
    setCurrentStep(0);
    setFormData({
      title: "",
      message: "",
      recipient: "",
      entity_type: "customer",
      entity_id: "",
      priority: "Normal",
      notification_type: "Info",
    });
    onOpenChange(false);
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={(open) => {
        onOpenChange(open);
        if (!open) {
          setCurrentStep(0);
          setFormData({
            title: "",
            message: "",
            recipient: "",
            entity_type: "customer",
            entity_id: "",
            priority: "Normal",
            notification_type: "Info",
          });
        }
      }}
      title={isEditMode ? "Edit Notification" : "Create New Notification"}
      description={isEditMode ? "Update the notification details" : "Complete all steps to create a new notification"}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleComplete}
      steps={[
        {
          title: "Notification Info",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="Enter the notification title"
                  value={formData.title}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Input
                  id="message"
                  placeholder="Enter the notification message"
                  value={formData.message}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="recipient">Recipient</Label>
                <Input
                  id="recipient"
                  placeholder="Enter recipient ID or username"
                  value={formData.recipient}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label>Entity Type</Label>
                <RadioGroup
                  value={formData.entity_type}
                  onValueChange={handleRadioChange("entity_type")}
                  className="flex space-x-4"
                >
                  {["customer", "prospect", "leadfile"].map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <RadioGroupItem value={type} id={type} />
                      <Label htmlFor={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label htmlFor="entity_id">Entity ID</Label>
                <Input
                  id="entity_id"
                  placeholder="Enter entity ID (e.g., cust-123)"
                  value={formData.entity_id}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Additional Settings",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label>Priority</Label>
                <RadioGroup
                  value={formData.priority}
                  onValueChange={handleRadioChange("priority")}
                  className="flex space-x-4"
                >
                  {["Low", "Normal", "High", "Urgent"].map((priority) => (
                    <div key={priority} className="flex items-center space-x-2">
                      <RadioGroupItem value={priority} id={priority.toLowerCase()} />
                      <Label htmlFor={priority.toLowerCase()}>{priority}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label>Notification Type</Label>
                <RadioGroup
                  value={formData.notification_type}
                  onValueChange={handleRadioChange("notification_type")}
                  className="flex space-x-4"
                >
                  {["Info", "Alert", "Reminder", "Task", "Event", "Warning"].map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <RadioGroupItem value={type} id={type.toLowerCase()} />
                      <Label htmlFor={type.toLowerCase()}>{type}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6">
              <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-center">Almost Done!</h3>
              <p className="text-muted-foreground mt-2 text-center">
                Please review your notification details before submitting.
              </p>
              <div className="mt-4 space-y-2 text-sm">
                <p><strong>Title:</strong> {formData.title || "N/A"}</p>
                <p><strong>Message:</strong> {formData.message || "N/A"}</p>
                <p><strong>Recipient:</strong> {formData.recipient || "N/A"}</p>
                <p><strong>Entity Type:</strong> {formData.entity_type || "N/A"}</p>
                <p><strong>Entity ID:</strong> {formData.entity_id || "N/A"}</p>
                <p><strong>Priority:</strong> {formData.priority || "N/A"}</p>
                <p><strong>Notification Type:</strong> {formData.notification_type || "N/A"}</p>
              </div>
            </div>
          ),
        },
      ]}
    />
  );
}