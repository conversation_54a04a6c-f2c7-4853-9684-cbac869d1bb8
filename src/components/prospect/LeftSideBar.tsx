import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import {
  ChevronDown,
  ChevronUp,
  Pencil,
  MapPin,
  Calendar,
  Briefcase,
  AtSign,
  Phone,
  Mail,
  MessageSquare,
  FileText,
  User,
  Calendar as CalendarIcon,
  Edit,
  ArrowLeftRight,
  UserCheck2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ProspectTypes } from "@/types/prospects";
import CustomerAvatar from "../customer-section/CustomerAvatar";
import { PrimaryButton } from "../custom/buttons/buttons";
import EditProspects from "@/pages/Prospects/edit";
import ReallocateProspects from "@/pages/Prospects/reallocate";

interface LeftSideBarProps {
  prospect: ProspectTypes;
  onEdit: () => void;
}

const LeftSideBar = ({ prospect, onEdit }: LeftSideBarProps) => {
  const [copied, setCopied] = useState<string | null>(null);
  const [expandedMobile, setExpandedMobile] = useState(false);
  const [actionTitle, setActionTitle] = useState<string>("");
  const [isReallocateModalOpen, setIsReallocateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleViewProspect = () => {
    if (prospect) {
      setIsEditModalOpen(true);
    }
  };

  const handleReallocateProspect = (actionName: string) => {
    if (prospect) {
      setActionTitle(actionName);
      setIsReallocateModalOpen(true);
    }
  };

  const handleCopy = (text: string, type: string) => {
    if (!text) return;
    navigator.clipboard.writeText(text);
    setCopied(type);
    setTimeout(() => setCopied(null), 2000);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch (e) {
      return dateString;
    }
  };

  return (
    <ScrollArea className="">
      <div className="p-4 space-y-4">
        {/* Mobile Header */}
        <div className="md:hidden">
          {/* Avatar and Basic Info */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <CustomerAvatar name={prospect?.name} size="lg" />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={onEdit}
                  className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-white shadow-md border-2"
                >
                  <Pencil className="h-3 w-3" />
                  <span className="sr-only">Edit</span>
                </Button>
              </div>
              <div>
                <h2 className="text-base font-semibold">
                  {prospect?.name || "N/A"}
                </h2>
                <p className="text-xs text-muted-foreground">
                  Phone No: {prospect?.phone || "N/A"}
                </p>
                <p className="text-xs text-muted-foreground truncate max-w-[180px]">
                  {prospect?.email || "N/A"}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Action Buttons - Now Direct Links */}
          <div className="flex justify-start gap-3 mb-3">
            {prospect.email && (
              <a href={`mailto:${prospect.email}`} className="block">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 text-primary dark:text-primary-light relative group"
                  title="Email"
                >
                  <Mail className="h-4 w-4" />
                  {/* <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Email
                  </div> */}
                </Button>
              </a>
            )}
            {!prospect.email && (
              <Button
                disabled
                variant="outline"
                size="icon"
                className="h-10 w-10 relative group"
                title="Email"
              >
                <Mail className="h-4 w-4" />
                {/* <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Email
                </div> */}
              </Button>
            )}

            <a
              href="https://web.whatsapp.com"
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 text-primary dark:text-primary-light relative group"
                title="WhatsApp"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 32 32"
                  fill="currentColor"
                >
                  <path d="M16.004 3.003c-7.18 0-13 5.82-13 13 0 2.284.598 4.524 1.73 6.496L3 29l6.68-1.74a12.902 12.902 0 0 0 6.324 1.603c7.18 0 13-5.82 13-13s-5.82-12.86-13-12.86zm0 23.573c-2.027 0-4.015-.547-5.756-1.582l-.41-.242-3.968 1.034 1.058-3.866-.266-.412a10.451 10.451 0 0 1-1.61-5.642c0-5.785 4.712-10.497 10.497-10.497s10.497 4.712 10.497 10.497-4.712 10.497-10.497 10.497zm5.76-7.91c-.316-.158-1.868-.922-2.158-1.027-.29-.106-.502-.158-.715.158-.211.316-.82 1.026-1.005 1.242-.184.211-.368.237-.684.079-.316-.158-1.336-.492-2.547-1.566-.94-.84-1.574-1.875-1.757-2.191-.184-.316-.02-.487.138-.645.143-.144.316-.368.474-.553.158-.184.211-.316.316-.527.106-.211.053-.395-.026-.553-.08-.158-.715-1.729-.98-2.364-.257-.618-.519-.533-.715-.543-.184-.01-.395-.01-.605-.01-.211 0-.553.08-.843.395s-1.106 1.08-1.106 2.633 1.133 3.054 1.29 3.267c.158.211 2.228 3.4 5.398 4.768.755.326 1.342.521 1.801.667.757.237 1.447.204 1.99.124.606-.09 1.868-.763 2.132-1.498.263-.737.263-1.368.184-1.498-.079-.132-.29-.21-.605-.369z" />
                </svg>
                {/* <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  WhatsApp
                </div> */}
              </Button>
            </a>

            {prospect?.phone && (
              <a href={`tel:${prospect?.phone}`} className="block">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 text-primary dark:text-primary-light relative group"
                  title="Call"
                >
                  <Phone className="h-4 w-4" />
                  {/* <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Call
                  </div> */}
                </Button>
              </a>
            )}
            {!prospect?.phone && (
              <Button
                disabled
                variant="outline"
                size="icon"
                className="h-10 w-10 relative group"
                title="Call"
              >
                <Phone className="h-4 w-4" />
                {/* <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Call
                </div> */}
              </Button>
            )}

            <a
              href="https://m.me"
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 text-primary dark:text-primary-light relative group"
                title="Message"
              >
                <MessageSquare className="h-4 w-4" />
                {/* <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Message
                </div> */}
              </Button>
            </a>
          </div>

          {/* Contact Information */}
          <div className="border-t pt-3">
            <h3 className="font-bold text-sm mb-2">Contact Information</h3>
            <div className="space-y-2 text-sm">
              {prospect?.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{prospect?.phone}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() => handleCopy(prospect?.phone || "", "phone")}
                  >
                    {copied === "phone" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}
              {prospect?.alternate_phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{prospect?.alternate_phone}</span>
                  <Badge
                    variant="outline"
                    className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                  >
                    Alt
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() =>
                      handleCopy(prospect?.alternate_phone || "", "altPhone")
                    }
                  >
                    {copied === "altPhone" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}
              {prospect?.email && (
                <div className="flex items-center">
                  <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span className="truncate">{prospect?.email}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() => handleCopy(prospect?.email || "", "email")}
                  >
                    {copied === "email" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}

              {(prospect?.city || prospect?.country) && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span className="truncate">
                    {prospect?.city} {`, ${prospect?.country}`}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* actions  */}
          <div className="border-y py-3 my-4">
            <h3 className="font-bold mb-2 text-sm">Prospect Actions</h3>
            <div className="flex space-x-2 justify-start">
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => handleViewProspect()}
                className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
              >
                <span title="Edit" className="flex items-center gap-2">
                  <Edit /> Edit
                </span>
              </PrimaryButton>

              {!prospect.marketer ? (
                <PrimaryButton
                  variant="primary"
                  size="sm"
                  onClick={() => handleReallocateProspect("Allocate")}
                  className="text-white bg-green-500 border-green-300 hover:bg-green-50 flex items-center space-x-1"
                >
                  <span className="flex items-center gap-2">
                    <UserCheck2 /> Allocate
                  </span>
                </PrimaryButton>
              ) : (
                <PrimaryButton
                  variant="primary"
                  size="sm"
                  onClick={() => handleReallocateProspect("Reallocate")}
                  className="bg-white !text-green-500 border border-green-300 hover:bg-green-300 hover:!text-white flex items-center space-x-1"
                >
                  <span title="Reallocate" className="flex items-center gap-2">
                    <ArrowLeftRight /> Reallocate
                  </span>
                </PrimaryButton>
              )}
            </div>
          </div>

          {/* Expandable Section Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpandedMobile(!expandedMobile)}
            className="text-xs w-full flex items-center justify-center mt-2"
          >
            {expandedMobile ? (
              <>
                Less Details <ChevronUp className="ml-1 h-3 w-3" />
              </>
            ) : (
              <>
                More Details <ChevronDown className="ml-1 h-3 w-3" />
              </>
            )}
          </Button>

          {/* Expanded Additional Details */}
          {expandedMobile && (
            <div className="mt-3 space-y-4 text-sm border-t pt-3">
              <div className="space-y-2">
                <h3 className="font-bold text-sm">Additional Details</h3>
                <div className="grid grid-cols-2 gap-y-2">
                  <div>
                    <p className="text-xs text-muted-foreground">Country</p>
                    <p>{prospect?.country || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Created on</p>
                    <p>{formatDate(prospect?.date)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Lead Type</p>
                    <p>{prospect?.lead_type || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Marketer</p>
                    <p>{prospect?.marketer || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">
                      Marketer Department
                    </p>
                    <p>{prospect?.marketer_department || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">
                      Shared / Allocated By
                    </p>
                    <p>{prospect?.department_member_name || "self"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">
                      Allocator Department
                    </p>
                    <p>{prospect?.department_name || "self"}</p>
                  </div>
                </div>
              </div>

              {prospect?.department && (
                <div className="space-y-2 border-t pt-3">
                  <h3 className="font-bold text-sm">Company Information</h3>
                  <div className="space-y-2">
                    {prospect?.department && (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{prospect?.department_member_name}</span>
                      </div>
                    )}
                    {prospect?.department_member_name && (
                      <div className="flex items-center">
                        <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{prospect?.department_name}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Desktop Header */}
        <div className="hidden md:block">
          {/* Avatar and Basic Info - Horizontal Layout */}
          <div className="flex items-start mb-6">
            <div className="relative mr-4">
              <CustomerAvatar name={prospect?.name} size="lg" />
              <Button
                variant="outline"
                size="icon"
                onClick={onEdit}
                className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-white shadow-md border-2"
              >
                <Pencil className="h-3 w-3" />
                <span className="sr-only">Edit</span>
              </Button>
            </div>
            <div>
              <h2 className="text-lg font-semibold">
                {prospect?.name || "N/A"}
              </h2>
              <p className="text-sm text-muted-foreground">
                Phone No: {prospect?.phone || "N/A"}
              </p>
              <p className="text-sm text-muted-foreground">
                {prospect?.email || "N/A"}
              </p>
            </div>
          </div>

          {/* Quick Action Buttons - Now Direct Links */}
          <div className="grid grid-cols-4 gap-2 mb-6 border-t border-b py-3">
            {prospect?.email && (
              <a href={`mailto:${prospect?.email}`} className="block">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center justify-center w-full text-primary dark:text-primary-light"
                >
                  <Mail className="h-4 w-4 mr-2" />
                  {/* <span>Email</span> */}
                </Button>
              </a>
            )}
            {!prospect?.email && (
              <Button
                disabled
                variant="outline"
                size="sm"
                className="flex items-center justify-center w-full"
                title="Email"
              >
                <Mail className="h-4 w-4 mr-2" />
                {/* <span>Email</span> */}
              </Button>
            )}

            <a
              href="https://web.whatsapp.com"
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <Button
                variant="outline"
                size="sm"
                title="WhatsApp"
                className="flex items-center justify-center w-full text-primary dark:text-primary-light"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 32 32"
                  fill="currentColor"
                >
                  <path d="M16.004 3.003c-7.18 0-13 5.82-13 13 0 2.284.598 4.524 1.73 6.496L3 29l6.68-1.74a12.902 12.902 0 0 0 6.324 1.603c7.18 0 13-5.82 13-13s-5.82-12.86-13-12.86zm0 23.573c-2.027 0-4.015-.547-5.756-1.582l-.41-.242-3.968 1.034 1.058-3.866-.266-.412a10.451 10.451 0 0 1-1.61-5.642c0-5.785 4.712-10.497 10.497-10.497s10.497 4.712 10.497 10.497-4.712 10.497-10.497 10.497zm5.76-7.91c-.316-.158-1.868-.922-2.158-1.027-.29-.106-.502-.158-.715.158-.211.316-.82 1.026-1.005 1.242-.184.211-.368.237-.684.079-.316-.158-1.336-.492-2.547-1.566-.94-.84-1.574-1.875-1.757-2.191-.184-.316-.02-.487.138-.645.143-.144.316-.368.474-.553.158-.184.211-.316.316-.527.106-.211.053-.395-.026-.553-.08-.158-.715-1.729-.98-2.364-.257-.618-.519-.533-.715-.543-.184-.01-.395-.01-.605-.01-.211 0-.553.08-.843.395s-1.106 1.08-1.106 2.633 1.133 3.054 1.29 3.267c.158.211 2.228 3.4 5.398 4.768.755.326 1.342.521 1.801.667.757.237 1.447.204 1.99.124.606-.09 1.868-.763 2.132-1.498.263-.737.263-1.368.184-1.498-.079-.132-.29-.21-.605-.369z" />
                </svg>

                {/* <span>WhatsApp</span> */}
              </Button>
            </a>

            {prospect?.phone && (
              <a href={`tel:${prospect?.phone}`} className="block">
                <Button
                  variant="outline"
                  size="sm"
                  title="Call"
                  className="flex items-center justify-center w-full text-primary dark:text-primary-light"
                >
                  <Phone className="h-4 w-4 mr-2" />
                  {/* <span>Call</span> */}
                </Button>
              </a>
            )}
            {!prospect?.phone && (
              <Button
                disabled
                variant="outline"
                size="sm"
                title="Call"
                className="flex items-center justify-center w-full"
              >
                <Phone className="h-4 w-4 mr-2" />
                {/* <span>Call</span> */}
              </Button>
            )}

            <a
              href="https://m.me"
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <Button
                variant="outline"
                size="sm"
                title="Message"
                className="flex items-center justify-center w-full text-primary dark:text-primary-light"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                {/* <span>Message</span> */}
              </Button>
            </a>
          </div>

          <div className="space-y-6">
            {/* Contact Information */}
            <div className="space-y-3">
              <h3 className="font-bold text-sm">Contact Information</h3>
              <div className="grid grid-cols-1 gap-2">
                {prospect?.phone && (
                  <div className="flex items-center group">
                    <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{prospect?.phone}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(prospect?.phone || "", "desktopPhone")
                      }
                    >
                      {copied === "desktopPhone" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}
                {prospect?.alternate_phone && (
                  <div className="flex items-center group">
                    <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{prospect?.alternate_phone}</span>
                    <Badge
                      variant="outline"
                      className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                    >
                      Alt
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(
                          prospect?.alternate_phone || "",
                          "desktopAltPhone"
                        )
                      }
                    >
                      {copied === "desktopAltPhone" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}
                {prospect?.email && (
                  <div className="flex items-center group">
                    <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{prospect?.email}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(prospect?.email || "", "desktopEmail")
                      }
                    >
                      {copied === "desktopEmail" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}

                {(prospect?.city || prospect?.country) && (
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span className="truncate">
                      {prospect?.city} {`, ${prospect?.country}`}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* actions  */}
            <div className="border-y py-3 my-4">
              <h3 className="font-bold mb-2 text-sm">Prospect Action</h3>
              <div className="flex space-x-2 justify-start">
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewProspect()}
                  className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
                >
                  <span title="Edit" className="flex items-center gap-2">
                    <Edit /> Edit
                  </span>
                </PrimaryButton>

                {!prospect.marketer ? (
                  <PrimaryButton
                    variant="primary"
                    size="sm"
                    onClick={() => handleReallocateProspect("Allocate")}
                    className="text-white bg-green-500 border-green-300 hover:bg-green-50 flex items-center space-x-1"
                  >
                    <span className="flex items-center gap-2">
                      <UserCheck2 /> Allocate
                    </span>
                  </PrimaryButton>
                ) : (
                  <PrimaryButton
                    variant="primary"
                    size="sm"
                    onClick={() => handleReallocateProspect("Reallocate")}
                    className="bg-white !text-green-500 border border-green-300 hover:bg-green-300 hover:!text-white flex items-center space-x-1"
                  >
                    <span
                      title="Reallocate"
                      className="flex items-center gap-2"
                    >
                      <ArrowLeftRight /> Reallocate
                    </span>
                  </PrimaryButton>
                )}
              </div>
            </div>

            {/* Additional Details */}
            <div className="space-y-3">
              <h3 className="font-bold text-sm">Additional Details</h3>
              <div className="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
                <div className="col-span-2">
                  <p className="text-xs text-muted-foreground">Marketer</p>
                  <p>{prospect?.marketer || "N/A"}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-xs text-muted-foreground">
                    Marketer Department
                  </p>
                  <p>{prospect?.marketer_department || "N/A"}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-xs text-muted-foreground">
                    Shared / Allocated By
                  </p>
                  <p>{prospect?.department_member_name || "self"}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-xs text-muted-foreground">
                    Allocator Department
                  </p>
                  <p>{prospect?.department_name || "self"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">
                    Country of Residence
                  </p>
                  <p>{prospect?.country || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">
                    Date of Creation
                  </p>
                  <p>{formatDate(prospect?.date)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Lead Type</p>
                  <p>{prospect?.lead_type || "N/A"}</p>
                </div>
              </div>
            </div>

            {/* Company Information */}
            {prospect?.department && (
              <div className="space-y-3">
                <h3 className="font-bold text-sm">Company Information</h3>
                <div className="space-y-2">
                  {prospect?.marketer_department && (
                    <div className="flex items-center">
                      <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />{" "}
                      {""}
                      <span>{prospect?.marketer_department}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {isEditModalOpen && (
        <EditProspects
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
          }}
          prospect={prospect}
        />
      )}

      {isReallocateModalOpen && (
        <ReallocateProspects
          isOpen={isReallocateModalOpen}
          onClose={() => {
            setIsReallocateModalOpen(false);
          }}
          prospect={prospect}
          title={actionTitle}
        />
      )}
    </ScrollArea>
  );
};

export default LeftSideBar;
