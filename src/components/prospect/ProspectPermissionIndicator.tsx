import React from 'react';
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useProspectPermissions } from '@/hooks/useProspectPermissions';
import { Shield, AlertTriangle, Bug } from 'lucide-react';
import { PROSPECT_PERMISSION_CODES } from '@/utils/prospectPermissions';

const ProspectPermissionIndicator: React.FC = () => {
  const {
    hasAnyProspectAccess,
    permissionLevel,
    apiParams,
    userPermissions,
    userDetails,
    // Keep these for backward compatibility with the indicator
    canViewHQProspects,
    canViewKarenProspects,
    canViewAllOfficesProspects,
    canViewOwnProspects,
    canViewAllMarketersProspects,
    canViewDiasporaTeamProspects,
    canViewDigitalTeamProspects,
    canViewTelemarketingTeamProspects,
    canViewOtherTeamProspects,
    canViewAllTeamsProspects,
    canViewDiasporaRegionProspects,
    canViewAllDiasporaRegionsProspects
  } = useProspectPermissions();

  // Debug section to show raw permissions
  const DebugPermissions = () => {
    // Extract normalized permissions for display
    const normalizedPermissions = userPermissions.map(p => 
      typeof p === 'string' ? parseInt(p, 10) : p
    );
    
    return (
      <div className="mt-4 p-3 border border-gray-300 rounded bg-gray-50">
        <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
          <Bug className="h-4 w-4 mr-1" /> Debug Information
        </h4>
        <div className="text-xs">
          <p><strong>Raw User Permissions Array:</strong> {JSON.stringify(userPermissions)}</p>
          <p><strong>Normalized Permissions:</strong> {JSON.stringify(normalizedPermissions)}</p>
          <p><strong>Has Permission 3001 (VIEW_PROSPECT_HQ):</strong> {normalizedPermissions.includes(3001) ? "Yes" : "No"}</p>
          <p><strong>Has Permission 3002 (VIEW_PROSPECT_KAREN):</strong> {normalizedPermissions.includes(3002) ? "Yes" : "No"}</p>
          <p><strong>hasAnyProspectAccess:</strong> {hasAnyProspectAccess ? "Yes" : "No"}</p>
          <p><strong>canViewHQProspects:</strong> {canViewHQProspects ? "Yes" : "No"}</p>
          <p><strong>canViewKarenProspects:</strong> {canViewKarenProspects ? "Yes" : "No"}</p>
          <p><strong>API Params:</strong> {JSON.stringify(apiParams)}</p>
          <p><strong>Permission Level:</strong> {permissionLevel}</p>
          <p><strong>User Details:</strong> {JSON.stringify(userDetails)}</p>
        </div>
      </div>
    );
  };

  return (
    <Card className={hasAnyProspectAccess ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
      <CardHeader className="pb-3">
        <CardTitle className={`flex items-center gap-2 ${hasAnyProspectAccess ? "text-green-700" : "text-red-700"}`}>
          {hasAnyProspectAccess ? (
            <>
              <Shield className="h-4 w-4" />
              Prospect Access Level: {permissionLevel}
            </>
          ) : (
            <>
              <AlertTriangle className="h-4 w-4" />
              No Prospect Access
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!hasAnyProspectAccess ? (
          <p className="text-sm text-red-600">
            You don't have permission to view prospect data. Contact your administrator to request access.
          </p>
        ) : (
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Your Prospect Permissions:</h4>
              <div className="flex flex-wrap gap-2">
                {/* Office Permissions */}
                {canViewAllOfficesProspects && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">All Offices</Badge>
                )}
                {canViewHQProspects && !canViewAllOfficesProspects && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">HQ Office</Badge>
                )}
                {canViewKarenProspects && !canViewAllOfficesProspects && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">Karen Office</Badge>
                )}

                {/* Marketer Permissions */}
                {canViewAllMarketersProspects && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">All Marketers</Badge>
                )}
                {canViewOwnProspects && !canViewAllMarketersProspects && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">Own Prospects Only</Badge>
                )}

                {/* Team Permissions */}
                {canViewAllTeamsProspects && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">All Teams</Badge>
                )}
                {canViewDiasporaTeamProspects && !canViewAllTeamsProspects && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Diaspora Team</Badge>
                )}
                {canViewDigitalTeamProspects && !canViewAllTeamsProspects && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Digital Team</Badge>
                )}
                {canViewTelemarketingTeamProspects && !canViewAllTeamsProspects && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Telemarketing Team</Badge>
                )}
                {canViewOtherTeamProspects && !canViewAllTeamsProspects && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Other Team</Badge>
                )}

                {/* Diaspora Region Permissions */}
                {canViewAllDiasporaRegionsProspects && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">All Diaspora Regions</Badge>
                )}
                {canViewDiasporaRegionProspects && !canViewAllDiasporaRegionsProspects && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">Your Diaspora Region</Badge>
                )}
              </div>
            </div>

            {userDetails && (
              <div className="text-xs text-gray-500 pt-2 border-t">
                <p>Employee: {userDetails.employee_no} | Office: {userDetails.office} | Team: {userDetails.team}</p>
              </div>
            )}
          </div>
        )}
        
        {/* Uncomment for debugging */}
        {/* <DebugPermissions /> */}
      </CardContent>
    </Card>
  );
};

export default ProspectPermissionIndicator;