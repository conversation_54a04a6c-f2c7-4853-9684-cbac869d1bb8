import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Image } from "lucide-react";

const EditProfileForm = () => {
  return (
    <div className="bg-white  dark:bg-gray-800 dark:text-white p-6 rounded-lg shadow-sm">
      <div className="mb-6">
        <Label>Profile Image</Label>
        <div className="mt-2 flex items-center gap-4">
          <Avatar className="h-24 w-24">
            <AvatarImage src="https://randomuser.me/api/portraits/men/1.jpg" />
            <AvatarFallback>WJ</AvatarFallback>
          </Avatar>
          <Button variant="outline" size="sm">
            <Image className="mr-2 h-4 w-4" />
            Change Image
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name</Label>
          <Input id="fullName" defaultValue="Will Jonto" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" defaultValue="<EMAIL>" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input id="phone" defaultValue="(1) 2536 2561 2365" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="department">Department</Label>
          <Input id="department" defaultValue="Design" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="designation">Designation</Label>
          <Input id="designation" defaultValue="UI/UX Designer" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="language">Language</Label>
          <Input id="language" defaultValue="English" />
        </div>

        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            defaultValue="Lorem Ipsum is simply dummy text of the printing and typesetting industry."
          />
        </div>
      </div>

      <div className="mt-6 flex justify-end gap-4">
        <Button variant="outline">Cancel</Button>
        <Button>Save</Button>
      </div>
    </div>
  );
};

export default EditProfileForm;