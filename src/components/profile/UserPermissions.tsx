
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useState } from "react";

const UserPermissions = () => {
  // Mock data for permissions
  const [permissions, setPermissions] = useState([
    { id: 1, name: "View Dashboard", enabled: true },
    { id: 2, name: "Create Projects", enabled: true },
    { id: 3, name: "Edit Projects", enabled: true },
    { id: 4, name: "Delete Projects", enabled: false },
    { id: 5, name: "Manage Users", enabled: false },
    { id: 6, name: "View Reports", enabled: true },
    { id: 7, name: "Export Data", enabled: true },
    { id: 8, name: "System Settings", enabled: false }
  ]);

  const [viewMode, setViewMode] = useState(true);

  const togglePermission = (id:any) => {
    setPermissions(permissions.map(permission => 
      permission.id === id ? { ...permission, enabled: !permission.enabled } : permission
    ));
  };

  return (
    <div className="bg-white  dark:bg-gray-800 dark:text-white  p-6 rounded-lg shadow-sm ">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">User Permissions</h2>
        <Button 
          variant={viewMode ? "outline" : "default"} 
          onClick={() => setViewMode(!viewMode)}
        >
          {viewMode ? "Edit Permissions" : "View Only"}
        </Button>
      </div>

      {viewMode ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Permission</TableHead>
              <TableHead className="w-24 text-right">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {permissions.map(permission => (
              <TableRow key={permission.id}>
                <TableCell>{permission.name}</TableCell>
                <TableCell className="text-right">
                  {permission.enabled ? 
                    <span className="text-green-600 font-medium">Enabled</span> : 
                    <span className="text-red-600 font-medium">Disabled</span>}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {permissions.map(permission => (
                <div key={permission.id} className="flex items-center justify-between">
                  <Label htmlFor={`permission-${permission.id}`}>{permission.name}</Label>
                  <Switch 
                    id={`permission-${permission.id}`} 
                    checked={permission.enabled}
                    onCheckedChange={() => togglePermission(permission.id)}
                  />
                </div>
              ))}
              <div className="pt-4 flex justify-end gap-4">
                <Button variant="outline" onClick={() => setViewMode(true)}>Cancel</Button>
                <Button onClick={() => setViewMode(true)}>Save Changes</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserPermissions;