import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

const NotificationSettings = () => {
  return (
    <div className="bg-white  dark:bg-gray-800 dark:text-white  p-6 rounded-lg shadow-sm">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Label htmlFor="company-news">Company News</Label>
          <Switch id="company-news" />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="push-notifications">Push Notifications</Label>
          <Switch id="push-notifications" defaultChecked />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="weekly-newsletter">Weekly News Letters</Label>
          <Switch id="weekly-newsletter" defaultChecked />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="meetups">Meetups Near you</Label>
          <Switch id="meetups" />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="orders">Orders Notifications</Label>
          <Switch id="orders" defaultChecked />
        </div>

        <div className="pt-4">
          <Button>Save Changes</Button>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;