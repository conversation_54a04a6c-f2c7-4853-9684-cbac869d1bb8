import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";

const UserGroups = () => {
  // Mock data for user groups
  const [userGroups, setUserGroups] = useState([
    { id: 1, name: "Administrators", description: "Full access to all features" },
    { id: 2, name: "Editors", description: "Can edit content and projects" },
    { id: 3, name: "Viewers", description: "Read-only access to projects and reports" }
  ]);

  // Current user's groups
  const [currentGroups, setCurrentGroups] = useState(["Editors"]);
  const [selectedGroup, setSelectedGroup] = useState("");

  const addToGroup = () => {
    if (selectedGroup && !currentGroups.includes(selectedGroup)) {
      setCurrentGroups([...currentGroups, selectedGroup]);
      setSelectedGroup("");
    }
  };

  const removeFromGroup = (groupName:any) => {
    setCurrentGroups(currentGroups.filter(group => group !== groupName));
  };

  return (
    <div className="bg-white  dark:bg-gray-800 dark:text-white p-6 rounded-lg shadow-sm">
      <div className="space-y-6">
        {/* Current Groups */}
        <Card>
          <CardHeader>
            <CardTitle>Current Groups</CardTitle>
            <CardDescription>Groups that Will Jonto belongs to</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentGroups.length > 0 ? (
                currentGroups.map((group, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b">
                    <div>
                      <p className="font-medium">{group}</p>
                      <p className="text-sm text-gray-500">
                        {userGroups.find(g => g.name === group)?.description || ""}
                      </p>
                    </div>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => removeFromGroup(group)}
                    >
                      Remove
                    </Button>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">User is not part of any groups</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Add to Group */}
        <Card>
          <CardHeader>
            <CardTitle>Add to Group</CardTitle>
            <CardDescription>Add Will Jonto to another user group</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="select-group">Select Group</Label>
                <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                  <SelectTrigger id="select-group">
                    <SelectValue placeholder="Select a group" />
                  </SelectTrigger>
                  <SelectContent>
                    {userGroups.map(group => (
                      <SelectItem 
                        key={group.id} 
                        value={group.name}
                        disabled={currentGroups.includes(group.name)}
                      >
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={addToGroup} disabled={!selectedGroup || currentGroups.includes(selectedGroup)}>
                Add to Group
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserGroups;