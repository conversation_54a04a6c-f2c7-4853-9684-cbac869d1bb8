import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

const ChangePasswordForm = () => {
  return (
    <div className="bg-white  dark:bg-gray-800 dark:text-white p-6 rounded-lg shadow-sm max-w-md">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="newPassword">New Password</Label>
          <Input id="newPassword" type="password" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input id="confirmPassword" type="password" />
        </div>

        <div className="pt-4">
          <Button className="w-full">Save Changes</Button>
        </div>
      </div>
    </div>
  );
};

export default ChangePasswordForm;