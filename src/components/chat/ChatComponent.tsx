import React, { useState, useRef, useEffect, useCallback } from 'react';
import { MessageCircle, Send, ChevronDown, } from 'lucide-react';
import { Outlet } from 'react-router-dom';
import ChatButton from './ChatButton';
import { usePostChatMutation } from '@/redux/slices/aiChat';
import useAIChatStore from '@/zustand/aiChatStore';

interface Message {
    id: string;
    text: string;
    sender: 'user' | 'contact';
    timestamp: Date;
}

interface ChatComponentProps {
    contactName?: string;
    onlineStatus?: boolean;
}

const ChatComponent: React.FC<ChatComponentProps> = ({
    contactName = "Amani",
    onlineStatus = true
}) => {

    const [postChat, { isLoading }] = usePostChatMutation();

    const {messages,setMessages} = useAIChatStore()

    const [isOpen, setIsOpen] = useState(false);
    const [messagess, setMessagess] = useState<Message[]>([
        {
            id: '1',
            text: 'Hello! How can I help you today?',
            sender: 'contact',
            timestamp: new Date(Date.now() - 60000)
        },
        // {
        //     id: '2',
        //     text: "Hi there! 👋 I'm Amani, your CRM assistant. I'm here to make customer management easier for you. Whether you're looking for customer info, sales data, title updates, or a full customer overview — I've got you covered. What would you like to do today?\n\n---\n*🤖 Amani CRM Assistant v4.0 | Session: default-... | Enhanced Customer Workflow*",
        //     sender: 'contact',
        //     timestamp: new Date(Date.now() - 60000)
        // },
        // {
        //     id: '3',
        //     text: "No results were found for your search. Here are some suggestions:\n\n1. **Check Spelling**: Ensure the name or search term is spelled correctly.\n2. **Search Alternatives**: Would you like me to search for prospects or leads instead?\n3. **Example Search**: Try searching with a customer ID or a partial name (e.g., \"John\" instead of \"John Doe\").\n\nLet me know how you'd like to proceed!\n\n---\n*🤖 Amani CRM Assistant v4.0 | Session: default-... | Enhanced Customer Workflow*",
        //     sender: 'contact',
        //     timestamp: new Date(Date.now() - 60000)
        // }
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleSendMessage = useCallback(async () => {
        if (inputMessage.trim()) {
            const userMessage: Message = {
                id: Date.now().toString(),
                text: inputMessage,
                sender: 'user',
                timestamp: new Date()
            };

            // setMessages(prevMessages => [...prevMessages, userMessage]);
            setMessages(userMessage)
            setInputMessage('');

            try {
                const res = await postChat({ chatInput: inputMessage }).unwrap();
                const botMessage: Message = {
                    id: (Date.now() + 1).toString(),
                    text: res?.output,
                    sender: 'contact',
                    timestamp: new Date()
                };

                // setMessages(prevMessages => [...prevMessages, botMessage]);
                setMessages(botMessage)
            } catch (error) {
                const botMessage: Message = {
                    id: (Date.now() + 1).toString(),
                    text: 'An error occured, please try again later.',
                    sender: 'contact',
                    timestamp: new Date()
                };

                // setMessages(prevMessages => [...prevMessages, botMessage]);
                setMessages(botMessage)
            }
        }
    }, [inputMessage, postChat]);

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    return (
        <>
            <Outlet />
            {/* Floating Chat Button */}
            <div
                onClick={() => setIsOpen(true)}
                className={`
                    fixed bottom-6 right-6 z-50 cursor-pointer rounded-full
                    hover:scale-110 hover:shadow-lg hover:shadow-cyan-500/60
                    ${isOpen ? 'opacity-0 pointer-events-none' : 'opacity-100'}
                `}
            >
                <button
                    className={`
                        w-14 h-14 
                        rounded-full shadow-lg
                        flex items-center justify-center 
                        bg-gradient-to-br from-cyan-400 to-purple-800 transition-all duration-300  animate-slow-spin hover:animate-none
                    `}
                >
                </button>
                <MessageCircle className="w-6 h-6  text-white absolute top-4 left-4" />
            </div>

            {/* Chat Modal */}
            {isOpen && (
                <div className="fixed bottom-6 right-6 z-50 w-[22rem] h-[28rem] bg-white dark:bg-gray-800 rounded-lg shadow-2xl overflow-hidden">
                    {/* Header */}
                    <div className=" px-4 py-2.5 flex items-center justify-between">
                        <div className="flex items-center gap-1">
                            <ChatButton />
                            <div className='-space-y-0.5'>
                                <h3 className="font-semibold text-xs">{contactName}</h3>
                                <div className='flex gap-1 items-center'>
                                    {/* <span className="relative flex size-3">
                                        <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-emerald-200 opacity-75"></span>
                                        <span className="relative inline-flex size-3 rounded-full bg-emerald-400"></span>
                                    </span> */}
                                    <p className="text-[10px] opacity-90">{onlineStatus ? 'Online' : 'Last seen recently'}</p>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center">
                            <button
                                onClick={() => setIsOpen(false)}
                                className="p-1 hover:scale-125 rounded-full transition-colors"
                            >
                                <ChevronDown className="w-6 h-6" />
                            </button>
                        </div>
                    </div>

                    {/* Messages Area */}
                    <div
                        className="flex-1 overflow-y-auto p-4 space-y-3 h-80 bg-gray-100 dark:bg-gray-700">
                        {messages.map((message) => (
                            <div
                                key={message.id}
                                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                            >
                                <div
                                    className={`max-w-xs px-3 py-4 rounded-lg text-xs
                                        ${message.sender === 'user'
                                            ? 'bg-primary text-white rounded-br-none'
                                            : 'bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 rounded-bl-none shadow-md'
                                        }
                                    `}
                                >
                                    {/* <p>{message.text}</p> */}
                                    {EnhancedChatMessage(message.text)}
                                    {/* <p className={`text-xs mt-1 ${message.sender === 'user' ? 'text-emerald-100' : 'text-gray-500 dark:text-gray-400'}`}>
                                        {formatTime(message.timestamp)}
                                    </p> */}
                                </div>
                            </div>
                        ))}
                        {isLoading && (
                            <div className="flex space-x-1 items-end">
                                <div className="w-1.5 h-1.5 bg-gray-500 rounded-full animate-[aggressive-bounce_0.8s_infinite_0ms]"></div>
                                <div className="w-1.5 h-1.5 bg-gray-500 rounded-full animate-[aggressive-bounce_0.8s_infinite_0.2s]"></div>
                                <div className="w-1.5 h-1.5 bg-gray-500 rounded-full animate-[aggressive-bounce_0.8s_infinite_0.4s]"></div>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </div>

                    {/* Input Area */}
                    <div className="px-4 py-2.5 bg-white dark:bg-gray-800 border-t dark:border-gray-600">
                        <div className="relative flex items-center">
                            <input
                                type="text"
                                value={inputMessage}
                                onChange={(e) => setInputMessage(e.target.value)}
                                onKeyPress={handleKeyPress}
                                placeholder="Type a message..."
                                className="
                w-full px-3 pt-2.5 pb-2 pr-12 border border-gray-300 dark:border-gray-600 
                rounded-full focus:outline-none focus:ring-2 focus:ring-primary
                dark:bg-gray-700 dark:text-gray-100 text-sm
            "
                            />
                            <button
                                onClick={handleSendMessage}
                                disabled={!inputMessage.trim()}
                                className={`
                absolute right-2 p-2 rounded-full transition-colors
                ${inputMessage.trim()
                                        ? 'bg-primary text-white hover:bg-primary/90 transition-all duration-300 transform hover:scale-110'
                                        : 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                                    }
            `}
                            >
                                <Send className="w-4 h-4 -translate-x-[1px] translate-y-[1px]" />
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ChatComponent;

export const EnhancedChatMessage = (message: string) => {
    const cleanedText = cleanChatResponse(message)

    const renderFormattedText = (text: any) => {
        // Split into lines for processing
        const lines = text.split('\n')
        const elements = []

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i]
            const trimmedLine = line.trim()

            if (trimmedLine === '') {
                elements.push(<div key={i} className="h-2" />)
                continue
            }

            // Handle bold text
            if (trimmedLine.includes('**')) {
                const parts = trimmedLine.split('**')
                elements.push(
                    <p key={i} className="">
                        {parts.map((part: any, j: any) => (
                            j % 2 === 0 ? part : <strong key={j} className="font-semibold">{part}</strong>
                        ))}
                    </p>
                )
            }
            // Handle numbered lists
            else if (/^\d+\./.test(trimmedLine)) {
                elements.push(
                    <p key={i} className="ml-4 text-sm">
                        {trimmedLine}
                    </p>
                )
            }
            // Handle bullet points
            else if (trimmedLine.startsWith('- ')) {
                elements.push(
                    <p key={i} className="ml-4 text-sm">
                        • {trimmedLine.substring(2)}
                    </p>
                )
            }
            // Regular paragraphs
            else {
                elements.push(
                    <p key={i} className="">
                        {trimmedLine}
                    </p>
                )
            }
        }

        return elements
    }

    return (

        <span>{renderFormattedText(cleanedText)}</span>

    )
}

// Function to clean up the chat response
const cleanChatResponse = (text: any) => {
    if (!text) return ''

    // Split by lines and filter out the signature line
    const lines = text.split('\n')
    const cleanedLines = []

    for (const line of lines) {
        const trimmedLine = line.trim()

        // Skip empty lines that come before the signature
        if (trimmedLine === '---') {
            break // Stop processing when we hit the separator
        }

        // Skip lines that start with the bot signature pattern
        if (trimmedLine.startsWith('*🤖') || trimmedLine.includes('🤖 Amani')) {
            break // Stop processing when we hit the signature
        }

        cleanedLines.push(line)
    }

    // Join back and trim any trailing whitespace
    return cleanedLines.join('\n').trim()
}