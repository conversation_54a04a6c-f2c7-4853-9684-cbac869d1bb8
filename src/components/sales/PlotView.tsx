import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs'
import SaleInformation from './GeneralSalesTab'
import PenaltiesSalesTab from './PenaltiesSalesTab'
import OverpaymentsSalesTab from './OverpaymentsSalesTab'
import { ScrollArea } from '../ui/scroll-area'

const PlotView = () => {
    return (
        <ScrollArea className="h-full w-full">
            <Tabs defaultValue="one" className="w-full overflow-x-auto px-2 pt-2">
                <TabsList className="grid grid-cols-3 w-auto mb-4 bg-primary text-white">
                    <TabsTrigger value="one">General</TabsTrigger>
                    <TabsTrigger value="two">Penalties</TabsTrigger>
                    <TabsTrigger value="three">Title Status</TabsTrigger>
                </TabsList>

                <TabsContent value="one" className='overflow-x-auto box-content'>
                    <SaleInformation />
                </TabsContent>
 
                <TabsContent value="two" >
                   <PenaltiesSalesTab/>
                </TabsContent>

                <TabsContent value="three">
                    <OverpaymentsSalesTab/>
                </TabsContent>
            </Tabs>
        </ScrollArea>
    )
}

export default PlotView