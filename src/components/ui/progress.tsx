"use client";

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";

import { cn } from "@/lib/utils";
import { getBgDynamicColor } from "@/helpers";

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {
    orientation?: "horizontal" | "vertical";
    color?: string;
  }
>(({ color, orientation = "horizontal", className, value, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      `relative ${
        orientation == "vertical" ? "w-8 h-60" : "h-4 w-full"
      } overflow-hidden rounded-full bg-black/20 `,
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={`h-full w-full flex-1 ${getBgDynamicColor(
        color
      )} transition-all ${
        orientation === "vertical"
          ? "border-t-4  border-t-white rounded-sm"
          : "border-r-4  border-r-white rounded-sm"
      }`}
      style={{
        transform: `${
          orientation === "vertical"
            ? `translateY(${100 - (value || 0)}%)`
            : `translateX(-${100 - (value || 0)}%)`
        }`,
      }}
    />
  </ProgressPrimitive.Root>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
