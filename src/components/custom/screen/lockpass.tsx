
import { Input } from "@/components/ui/input";
import { OutlinedButton, PrimaryButton, IconButton } from "../buttons/buttons";
import { Card, CardHeader, CardDescription } from "@/components/ui/card";
import {  LucideEye, LucideEyeClosed } from "lucide-react";
import { useState } from "react";
const LockScreen: React.FC = () => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  function onClick() {
    setIsPasswordVisible((prev) => !prev);
  }

  //

  return (
    <Card className="w-96 p-6 shadow-md rounded-lg bg-white">
        <CardHeader className="mb-4">
        <div className="w-full flex items-center justify-center p-9 ">
          <img
            src="https://www.optiven.co.ke/wp-content/uploads/2023/09/optiven-group-logo.png"
            alt="Email Template"
            
          />
        </div>
            
            <CardDescription className="text-center text-gray-500">
                Hello <EMAIL> ,kindly re-enter your password to continue with the session.
            </CardDescription>
        </CardHeader>
      <h2 className="text-xl font-semibold text-center mb-4">Enter Password</h2>
        <div className="relative mb-4">
            <Input
                type={isPasswordVisible ? "text" : "password"}
                placeholder="Enter your password"
                className="pr-10"
            />
              <IconButton
            variant="secondary"
            className="absolute right-3 top-1/2 transform -translate-y-1/2"
            aria-label="Toggle password visibility"
            onClick={onClick}
          >
              <LucideEyeClosed className="w-4 h-4 text-gray-500" />
              </IconButton>
        </div>
        <PrimaryButton variant="primary" >
            Proceed
        </PrimaryButton>
    </Card>
            

            

    
  );
};

export default LockScreen;

