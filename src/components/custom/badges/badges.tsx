import React from "react";

interface BadgeProps {
  children: React.ReactNode;
  variant?: "default" | "primary" | "secondary" | "destructive" | "outline";
  size?: "sm" | "md" | "lg";
  rounded?: "none" | "sm" | "md" | "full";
  dot?: boolean;
  icon?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

const Badge = ({
  children,
  variant = "default",
  size = "md",
  rounded = "md",
  dot = false,
  icon,
  className = "",
  onClick,
}: BadgeProps) => {
  const baseStyles =
    "inline-flex items-center justify-center font-medium transition-colors";

  const sizeStyles = {
    sm: "text-xs px-2 py-0.5",
    md: "text-sm px-2.5 py-1",
    lg: "text-base px-3 py-1.5",
  };

  const roundedStyles = {
    none: "rounded-none",
    sm: "rounded",
    md: "rounded-md",
    full: "rounded-full",
  };

  const variantStyles = {
    default: "bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]",
    primary: "bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]",
    secondary:
      "bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))]",
    destructive:
      "bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))]",
    outline:
      "border border-[hsl(var(--border))] text-[hsl(var(--foreground))] bg-transparent",
  };

  const dotColors = {
    default: "bg-[hsl(var(--muted-foreground))]",
    primary: "bg-[hsl(var(--primary-foreground))]",
    secondary: "bg-[hsl(var(--secondary-foreground))]",
    destructive: "bg-[hsl(var(--destructive-foreground))]",
    outline: "bg-[hsl(var(--foreground))]",
  };

  const badgeStyles = `
    ${baseStyles}
    ${sizeStyles[size]}
    ${roundedStyles[rounded]}
    ${variantStyles[variant]}
    ${onClick ? "cursor-pointer hover:opacity-80" : ""}
    ${className}
  `;

  return (
    <span className={badgeStyles} onClick={onClick}>
      {dot && (
        <span
          className={`inline-block w-2 h-2 rounded-full mr-1.5 ${dotColors[variant]}`}
        />
      )}
      {icon && <span className="mr-1">{icon}</span>}
      {children}
    </span>
  );
};

const DefaultBadge = (props: BadgeProps) => <Badge {...props} />;

const PrimaryBadge = (props: BadgeProps) => (
  <Badge {...props} variant="primary" />
);

const SecondaryBadge = (props: BadgeProps) => (
  <Badge {...props} variant="secondary" />
);

const DestructiveBadge = (props: BadgeProps) => (
  <Badge {...props} variant="destructive" />
);

const OutlineBadge = (props: BadgeProps) => (
  <Badge {...props} variant="outline" />
);

const PillBadge = (props: BadgeProps) => <Badge {...props} rounded="full" />;

const DotBadge = (props: BadgeProps) => <Badge {...props} dot={true} />;

interface CounterBadgeProps {
  count: number;
  maxCount?: number;
  className?: string;
  variant?: BadgeProps["variant"];
}

const CounterBadge = ({
  count,
  maxCount = 99,
  className = "",
  variant = "destructive",
}: CounterBadgeProps) => {
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  return (
    <Badge
      variant={variant}
      rounded="full"
      size="sm"
      className={`min-w-5 h-5 px-1.5 ${className}`}
    >
      {displayCount}
    </Badge>
  );
};

interface IconBadgeProps extends Omit<BadgeProps, "icon"> {
  iconElement: React.ReactNode;
}

const IconBadge = ({ iconElement, ...props }: IconBadgeProps) => (
  <Badge {...props} icon={iconElement} />
);

// Notification Badge (for absolute positioning on elements)
interface NotificationBadgeProps {
  count?: number;
  dot?: boolean;
  variant?: BadgeProps["variant"];
  className?: string;
}

const NotificationBadge = ({
  count,
  dot = false,
  variant = "destructive",
  className = "",
}: NotificationBadgeProps) => {
  return (
    <span className={`absolute -top-1 -right-1 ${className}`}>
      {dot ? (
        <span
          className={`block w-2.5 h-2.5 rounded-full ${
            variant === "outline"
              ? "bg-[hsl(var(--foreground))]"
              : "bg-[hsl(var(--destructive))]"
          }`}
        ></span>
      ) : (
        <CounterBadge count={count || 0} variant={variant} />
      )}
    </span>
  );
};

export {
  Badge,
  DefaultBadge,
  PrimaryBadge,
  SecondaryBadge,
  DestructiveBadge,
  OutlineBadge,
  PillBadge,
  DotBadge,
  CounterBadge,
  IconBadge,
  NotificationBadge,
};

export type {
  BadgeProps,
  CounterBadgeProps,
  IconBadgeProps,
  NotificationBadgeProps,
};
