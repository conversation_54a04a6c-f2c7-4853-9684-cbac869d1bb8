import { Chevron<PERSON>eft, ChevronRight, Chev<PERSON><PERSON>eft, ChevronsRight, ChevronDown } from 'lucide-react';

/**
 * Enhanced Pagination component with items per page selector
 * @param {Object} props Component props
 * @param {number} props.currentPage Current active page (state from parent)
 * @param {Function} props.setCurrentPage setState function from parent to update current page
 * @param {number} props.itemsPerPage Number of items to show per page (state from parent)
 * @param {Function} props.setItemsPerPage setState function from parent to update items per page
 * @param {number} props.totalItems Total count of items from API
 * @param {number} props.siblingCount Number of sibling pages to show on each side of current page (default: 1)
 * @param {Array} props.itemsPerPageOptions Options for items per page selector (default: [10, 20, 50, 100])
 */

 
type PaginatorProps = {
  currentPage?: number;
  setCurrentPage?: (page: number) => void;
  itemsPerPage?: number;
  setItemsPerPage?: (items: number) => void;
  totalItems?: number;
  siblingCount?: number;
  itemsPerPageOptions?: number[];
  className?: string;
};

// const Paginator: React.FC<PaginatorProps> = ({ 

const Paginator: React.FC<PaginatorProps> = ({ 
  currentPage=1, 
  setCurrentPage=() => {}, 
  itemsPerPage=20,
  setItemsPerPage=() => {},
  totalItems=20,
  siblingCount = 1,
  itemsPerPageOptions = [10, 20, 50, 100],
  className = ""
}) => {
  // Calculate total pages based on total items and items per page
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Calculate showing information
  const getShowingInfo = () => {
    if (totalItems === 0) return "Showing 0 of 0";
    
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);
    
    return `Showing ${startItem}-${endItem} of ${totalItems}`;
  };

  // Handle page change
  const handlePageChange = (pageNumber:any) => {
    // Ensure the page number is within valid range
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage:any) => {
    setItemsPerPage(newItemsPerPage);
    // Reset to page 1 when changing items per page to avoid showing empty pages
    setCurrentPage(1);
  };

  // Generate the page numbers to display
  const getPageNumbers = () => {
    // Always show first and last page
    const firstPage = 1;
    const lastPage = totalPages;
    
    // Calculate the range of pages to show around the current page
    const leftSiblingIndex = Math.max(currentPage - siblingCount, firstPage);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, lastPage);
    
    // Initialize array to hold page numbers and ellipses
    const pageNumbers = [];
    
    // Use a Set to prevent duplicate page numbers
    const uniquePages = new Set();
    
    // Add first page
    uniquePages.add(firstPage);
    pageNumbers.push(firstPage);
    
    // Add ellipsis if there's a gap after first page
    if (leftSiblingIndex > firstPage + 1) {
      pageNumbers.push('...');
    } else if (leftSiblingIndex === firstPage + 1) {
      // If the gap is just 1, show the page instead of ellipsis
      uniquePages.add(firstPage + 1);
      pageNumbers.push(firstPage + 1);
    }
    
    // Add pages around current page (excluding first and last)
    for (let i = leftSiblingIndex; i <= rightSiblingIndex; i++) {
      if (i !== firstPage && i !== lastPage && !uniquePages.has(i)) {
        uniquePages.add(i);
        pageNumbers.push(i);
      }
    }
    
    // Add ellipsis if there's a gap before last page
    if (rightSiblingIndex < lastPage - 1) {
      pageNumbers.push('...');
    } else if (rightSiblingIndex === lastPage - 1 && !uniquePages.has(lastPage - 1)) {
      // If the gap is just 1, show the page instead of ellipsis
      uniquePages.add(lastPage - 1);
      pageNumbers.push(lastPage - 1);
    }
    
    // Add last page if it's not the same as first page
    if (lastPage !== firstPage && !uniquePages.has(lastPage)) {
      pageNumbers.push(lastPage);
    }
    
    return pageNumbers;
  };

  // Only show pagination if there are items
  if (totalItems <= 0) return null;

  const pageNumbers = getPageNumbers();

  return (
    <div className={`flex items-center justify-center sm:justify-between w-full py-4 ${className}`}>
      {/* Items per page selector */}
      <div className="hidden sm:flex items-center space-x-2 ">
        <span className="text-sm">Show</span>
        <div className="relative">
          <select
        value={itemsPerPage}
        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
        className="appearance-none border border-gray-300 rounded-md pl-3 pr-8 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
        {itemsPerPageOptions.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
          </select>
          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 pointer-events-none" />
        </div>
        <span className="text-sm">items</span>
      </div>

      {/* Pagination navigation */}
      <nav className="flex items-center space-x-1" aria-label="Pagination">
        {/* First page button */}
        <button
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
          className="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
          aria-label="Go to first page"
        >
          <ChevronsLeft className="w-4 h-4" />
        </button>
        
        {/* Previous page button */}
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
          aria-label="Go to previous page"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
        
        {/* Page numbers */}
        {pageNumbers.map((pageNumber, index) => (
          pageNumber === '...' ? (
            <span key={`ellipsis-${index}`} className="w-8 h-8 flex items-center justify-center text-gray-500">
              {pageNumber}
            </span>
          ) : (
            <button
              key={`page-${pageNumber}`}
              onClick={() => handlePageChange(pageNumber)}
              className={`w-8 h-8 flex items-center justify-center rounded-full text-sm font-medium transition-colors
                ${currentPage === pageNumber 
                  ? 'bg-blue-500 text-white' 
                  : 'hover:bg-gray-100'}`}
              aria-label={`Page ${pageNumber}`}
              aria-current={currentPage === pageNumber ? 'page' : undefined}
            >
              {pageNumber}
            </button>
          )
        ))}
        
        {/* Next page button */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
          aria-label="Go to next page"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
        
        {/* Last page button */}
        <button
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
          className="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
          aria-label="Go to last page"
        >
          <ChevronsRight className="w-4 h-4" />
        </button>
      </nav>

      {/* Showing information */}
      <div className="text-sm hidden sm:flex">
        {getShowingInfo()}
      </div>
    </div>
  );
};

export default Paginator;