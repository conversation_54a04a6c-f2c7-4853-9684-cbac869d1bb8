import React from 'react';

// Types for column and data
export type ColumnDefinitionST<T> = {
    key: keyof T | string;
    header: string;
    renderCell?: (row: T) => React.ReactNode;
    headerClassName?: string;
    cellClassName?: string;
};

type TableProps<T> = {
    data: T[];
    columns: ColumnDefinitionST<T>[];
    containerClassName?: string;
    tableClassName?: string;
    tHeaderClassName?: string;
    tBodyClassName?: string;
    tRowClassName?: string;
    headerCellsClassName?: string;
    bodyCellsClassName?: string;
    striped?: boolean;
    hoverable?: boolean;
};



const SimpleTable = <T extends Record<string, any>>({
    data,
    columns,
    containerClassName = '',
    tableClassName = '',
    tHeaderClassName = '',
    tBodyClassName = '',
    tRowClassName = '',
    headerCellsClassName = '',
    bodyCellsClassName = '',
    striped = true,
    hoverable = true,
}: TableProps<T>) => {
    return (
        <div className={`table-mobile-scroll border border-secondary  ${containerClassName}`}>
            <table className={`border min-w-full divide-y divide-secondary   ${tableClassName}`}>
                <thead className={` ${tHeaderClassName}`}>
                    <tr>
                        {columns.map((column, index) => (
                            <th
                                key={index}
                                scope="col"
                                className={`bg-card/40 text-left text-xs  uppercase  ${headerCellsClassName} ${column.headerClassName || ''}`}
                            >
                                {column.header}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody className={` divide-y divide-secondary ${tBodyClassName}`}>
                    {data.map((row, rowIndex) => (
                        <tr
                            key={rowIndex}
                            className={`
                ${striped && rowIndex % 2 === 1 ? 'bg-card/40' : ''}
                ${hoverable ? 'hover:bg-card/40' : ''}
                ${tRowClassName}
              `}
                        >
                            {columns.map((column, colIndex) => {
                                const key = column.key as string;
                                return (
                                    <td
                                        key={colIndex}
                                        className={` whitespace-nowrap text-sm ${bodyCellsClassName} ${column.cellClassName || ''}`}
                                    >
                                        {column.renderCell ? column.renderCell(row) : row[key]}
                                    </td>
                                );
                            })}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default SimpleTable;