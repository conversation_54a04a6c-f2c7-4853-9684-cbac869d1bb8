import React from "react";
import { useState } from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  type?: "button" | "submit" | "reset";
  variant?: "default" | "primary" | "secondary" | "destructive" | "outline" | "ghost" | "link" | "accent";

  size?: "sm" | "md" | "lg";
  rounded?: "none" | "sm" | "md" | "full";
}

const Button = ({
  children,
  onClick,
  disabled = false,
  className = "",
  type = "button",
  variant = "default",
  size = "md",
  rounded = "md",
  ...props
}: ButtonProps) => {
  const baseStyles = "inline-flex items-center justify-center font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2";

  const variantStyles = {
    default: 'bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]',
    primary: 'bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]',
    secondary: 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))]',
    destructive: 'bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))]',
    outline: 'border border-[hsl(var(--border))] text-[hsl(var(--foreground))] bg-transparent',
    ghost: 'bg-transparent text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]',
    link: 'text-[hsl(var(--primary))] underline hover:text-[hsl(var(--primary-foreground))]',
    accent: 'bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))]'
  };

  const sizeStyles = {
    sm: "px-2 py-1 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  };

  const roundedStyles = {
    none: "rounded-none",
    sm: "rounded",
    md: "rounded-md",
    full: "rounded-full",
  };

  const buttonStyles = `
    ${baseStyles}
    ${variantStyles[variant]}
    ${sizeStyles[size]}
    ${roundedStyles[rounded]}
    ${disabled ? "opacity-50 cursor-not-allowed" : ""}
    ${className}
  `;

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={buttonStyles}
      {...props}
    >
      {children}
    </button>
  );
};


const TextButton = (props: ButtonProps) => {
  return (
    <button
      type={props.type || 'button'}
      onClick={props.onClick}
      disabled={props.disabled}
      className={`font-medium text-foreground hover:text-opacity-80 transition-all duration-200 bg-transparent border-none px-4 py-2 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-opacity-50 ${
        props.disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${props.className || ''}`}
    >
      {props.children}
    </button>
  );
};

const PrimaryButton = ({
  variant = "default",
  size = "md",
  className = "",
  ...props
}: ButtonProps) => {
  // Define variant classes
  const variantClasses = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/90",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
    outline: "border border-border text-foreground",
    accent: "bg-accent text-accent-foreground hover:bg-accent/90",
    ghost: "bg-transparent text-foreground border border-transparent hover:border-border",
    link: "text-primary underline hover:text-primary/90",
    
  }[variant];

  // Define custom size classes
  const sizeClasses = {
    sm: "px-2 py-1 text-sm",
    md: "px-4 py-2 text-base",
    default: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  }[size as "sm" | "md" | "default" | "lg"];

  return (
    <button
      {...props}
      className={`${variantClasses} ${sizeClasses} rounded-md transition-all duration-200 ${className}`}
    >
      {props.children}
    </button>
  );
};

const LargeButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={` text-default px-8 py-4 text-lg rounded-lg hover:bg-default ${
        props.className || ''
      }`}
    />
  );
};

const LargeTextButton = (props: ButtonProps) => {
  return (
    <button
      type={props.type || 'button'}
      onClick={props.onClick}
      disabled={props.disabled}
      className={`font-medium text-lg text-foreground hover:text-opacity-80 transition-all duration-200 bg-transparent border-none px-6 py-3 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-opacity-50 ${
        props.disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${props.className || ''}`}
    >
      {props.children}
    </button>
  );
};

const TextLinkButton = ({
  children,
  className = "",
  disabled = false,
  variant = "default",
  size = "md",
  ...props
}: ButtonProps) => {
  // Define variant-specific text styles
  const variantStyles = {
    default: "text-[hsl(var(--primary))] hover:text-[hsl(var(--primary),0.85)]",
    primary: "text-[hsl(var(--primary))] hover:text-[hsl(var(--primary),0.85)]",
    secondary: "text-[hsl(var(--secondary))] hover:text-[hsl(var(--secondary),0.85)]",
    destructive: "text-[hsl(var(--destructive))] hover:text-[hsl(var(--destructive),0.85)]",
    outline: "text-[hsl(var(--foreground))] hover:text-[hsl(var(--foreground),0.85)]",
    ghost: "text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--muted-foreground),0.85)]",
    link: "text-[hsl(var(--primary))] underline hover:text-[hsl(var(--primary),0.85)]",
    accent: "bg-accent text-accent-foreground hover:bg-accent/90",
  }[variant];

  const baseStyles = "font-medium underline transition-all duration-200 focus:outline-none";

  const disabledStyles = disabled ? "opacity-50 cursor-not-allowed" : "";

  return (
    <button
      type={props.type || "button"}
      disabled={disabled}
      className={`${baseStyles} ${variantStyles} ${disabledStyles} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};
const OutlinedButton = ({
  variant = "default",
  size = "md",
  className = "",
  children,
  ...props
}: ButtonProps) => {
  // Define variant-specific border and text styles
  const variantStyles = {
    default: "border-border text-foreground hover:bg-muted/50",
    primary: "border-primary text-primary hover:bg-primary/10",
    secondary: "border-secondary text-secondary hover:bg-secondary/10",
    destructive: "border-destructive text-destructive hover:bg-destructive/10",
    accent: "border-accent text-accent hover:bg-accent/10",
    ghost: "border-transparent text-foreground hover:bg-muted/50",
    link: "border-primary text-primary underline hover:text-primary/90",
  };

  // Define size classes
  const sizeClasses = {
    sm: "px-2 py-1 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  };

  // Get the variant style or default to the default style if variant is invalid
  const variantStyle = variantStyles[variant as keyof typeof variantStyles] || variantStyles.default;
  const sizeClass = sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.md;

  return (
    <button
      type={props.type || "button"}
      onClick={props.onClick}
      disabled={props.disabled}
      className={`inline-flex items-center justify-center font-medium transition-all bg-transparent border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${variantStyle} ${sizeClass} ${props.disabled ? "opacity-50 cursor-not-allowed" : ""} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

interface ActionButtonProps extends ButtonProps {
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const ActionButton = ({ 
  icon, 
  iconPosition = 'left', 
  children, 
  ...rest 
}: ActionButtonProps) => {
  return (
    <Button
      {...rest}
      className={` text-secondary-foreground px-5 py-2 rounded-lg hover:bg-primary/90 flex items-center justify-center gap-2 ${
        rest.className || ''
      }`}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
};

const AnimatedButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={`bg-accent text-accent-foreground px-6 py-3 rounded-full hover:bg-accent/90 transform hover:scale-105 ${
        props.className || ''
      }`}
    />
  );
};

const DestructiveButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={`bg-destructive text-destructive-foreground px-6 py-3 rounded-full hover:bg-destructive/90 transform hover:scale-105 ${
        props.className || ''
      }`}
    />
  );
};
const AccentButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={`bg-accent text-destructive-foreground px-6 py-3 rounded-full hover: transform hover:scale-105 ${
        props.className || ''
      }`}
    />
  );
};
const DefaultButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={`bg-default text-destructive-foreground px-6 py-3 rounded-full hover: transform hover:scale-105 ${
        props.className || ''
      }`}
    />
  );
};

const SuccessButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={`bg-success text-success-foreground px-6 py-3 rounded-full hover:bg-success/90 transform hover:scale-105 ${
        props.className || ''
      }`}
    />
  );
};

const GhostButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={`text-foreground px-6 py-3 rounded-md hover:bg-muted border border-transparent hover:border-border ${
        props.className || ''
      }`}
    />
  );
};

const LinkButton = (props: ButtonProps) => {
  return (
    <Button
      {...props}
      className={` underline ${
        props.className || ''
      }`}
    />
  );
};
interface SocialIconButtonProps extends ButtonProps {
  icon: React.ReactNode;
  label?: string;
}

const SocialIconButton = ({ 
  icon, 
  label, 
  className = "", 
  ...props 
}: SocialIconButtonProps) => {
  return (
    <Button
      {...props}
      className={`bg-accent text-accent-foreground p-3 rounded-full hover:bg-accent/90 flex items-center justify-center gap-2 ${
        className || ''
      }`}
    >
      {icon}
      {label && <span className="text-sm font-medium">{label}</span>}
    </Button>
  );
};

const IconButton = ({
    children,
    className = "",
    ...props
  }: ButtonProps) => {
    return (
      <Button
        {...props}
        className={`bg-secondary text-secondary-foreground p-3 rounded-full hover:bg-secondary/90 flex items-center justify-center ${
          className || ""
        }`}
      >
        {children}
      </Button>
    );
  };

export {
  PrimaryButton,
  LargeButton,
  OutlinedButton,
  ActionButton,
  DestructiveButton,
  AnimatedButton,
  SuccessButton,
  GhostButton,
  LinkButton,
  IconButton,
  TextButton,
  LargeTextButton,
  TextLinkButton,
  SocialIconButton,
  AccentButton,
  DefaultButton 
};
export type { ButtonProps, ActionButtonProps };

