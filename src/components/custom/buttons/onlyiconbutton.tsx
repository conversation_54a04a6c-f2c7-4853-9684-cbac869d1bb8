import React from "react";
import { ButtonProps } from "./buttons";
import { LucideIcon } from "lucide-react";

interface OnlyIconButtonProps extends Omit<ButtonProps, "size"> {
  icon: LucideIcon; // The Lucide React Icon to render
  buttonSize?: "sm" | "md" | "lg"; // Size of the button
  variant?: "default" | "primary" | "secondary" | "destructive" | "outline" | "ghost" | "link"; // Button variant
}

const OnlyIconButton: React.FC<OnlyIconButtonProps> = ({
  icon: Icon,
  buttonSize = "md",
  variant = "default",
  className = "",
  ...props
}) => {
  const sizeClasses = {
    sm: "p-2",
    md: "p-3",
    lg: "p-4",
  }[buttonSize];

  const variantClasses = {
    default: "bg-[hsl(var(--background))] text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]",
    primary: "bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] hover:bg-[hsl(var(--primary),0.9)]",
    secondary: "bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--secondary),0.9)]",
    destructive: "bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))] hover:bg-[hsl(var(--destructive),0.9)]",
    outline: "border border-[hsl(var(--border))] text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]",
    ghost: "bg-transparent text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]",
    link: "text-[hsl(var(--primary))] underline hover:text-[hsl(var(--primary),0.9)]",
  }[variant];

  return (
    <button
      {...props}
      className={`flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 transition-all duration-200 ${sizeClasses} ${variantClasses} ${className}`}
    >
      <Icon className="w-5 h-5" />
    </button>
  );
};

export default OnlyIconButton;