/* HTML: <div class="loader"></div> */
.loader-opposing-spinner {
    width: 50px;
    aspect-ratio: 1;
    display: grid;
    animation: l14 4s infinite;
  }
  .loader-opposing-spinner::before,
  .loader-opposing-spinner::after {    
    content: "";
    grid-area: 1/1;
    border: 8px solid;
    border-radius: 50%;
    border-color: red red #0000 #0000;
    mix-blend-mode: darken;
    animation: l14 1s infinite linear;
  }
  .loader-opposing-spinner::after {
    border-color: #0000 #0000 blue blue;
    animation-direction: reverse;
  }
  @keyframes l14{ 
    100%{transform: rotate(1turn)}
  }




  /* HTML: <div class="loader"></div> */
.loader-spinner-multi-opposing {
    width: 50px;
    aspect-ratio: 1;
    display: grid;
    border: 4px solid #0000;
    border-radius: 50%;
    border-color: #ccc #0000;
    animation: l16 1s infinite linear;
  }
  .loader-spinner-multi-opposing::before,
  .loader-spinner-multi-opposing::after {    
    content: "";
    grid-area: 1/1;
    margin: 2px;
    border: inherit;
    border-radius: 50%;
  }
  .loader-spinner-multi-opposing::before {
    border-color: #f03355 #0000;
    animation: inherit; 
    animation-duration: .5s;
    animation-direction: reverse;
  }
  .loader-spinner-multi-opposing::after {
    margin: 8px;
  }
  @keyframes l16 { 
    100%{transform: rotate(1turn)}
  }



  /* HTML: <div class="loader"></div> */
.loader-spinner-wheel {
    width: 50px;
    --b: 8px;
    aspect-ratio: 1;
    border-radius: 50%;
    background: #514b82;
    -webkit-mask:
      repeating-conic-gradient(#0000 0deg,#000 1deg 70deg,#0000 71deg 90deg),
      radial-gradient(farthest-side,#0000 calc(100% - var(--b) - 1px),#000 calc(100% - var(--b)));
    -webkit-mask-composite: destination-in;
            mask-composite: intersect;
    animation: l5 1s infinite;
  }
  @keyframes l5 {to{transform: rotate(.5turn)}}




  /* HTML: <div class="loader"></div> */
.loader-spinner-double {
    width: 50px;
    aspect-ratio: 1;
    display: grid;
    border: 4px solid #0000;
    border-radius: 50%;
    border-right-color: #25b09b;
    animation: l15 1s infinite linear;
  }
  .loader-spinner-double::before,
  .loader-spinner-double::after {    
    content: "";
    grid-area: 1/1;
    margin: 2px;
    border: inherit;
    border-radius: 50%;
    animation: l15 2s infinite;
  }
  .loader-spinner-double::after {
    margin: 8px;
    animation-duration: 3s;
  }
  @keyframes l15{ 
    100%{transform: rotate(1turn)}
  }




  /* HTML: <div class="loader"></div> */
.loader-tail-spinner {
    width: 50px;
    aspect-ratio: 1;
    border-radius: 50%;
    background: 
      radial-gradient(farthest-side,#ffa516 94%,#0000) top/8px 8px no-repeat,
      conic-gradient(#0000 30%,#ffa516);
    -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 8px),#000 0);
    animation: l13 1s infinite linear;
  }
  @keyframes l13{ 
    100%{transform: rotate(1turn)}
  }



  /* HTML: <div class="loader"></div> */
.loader-shape-cutting {
    width: 70px;
    height: 26px;
    background: #d0af03;
    border-radius: 50px;
    --c:no-repeat radial-gradient(farthest-side,#000 92%,#0000);
    --s:18px 18px;
    -webkit-mask:
      var(--c) left  4px top 50%,
      var(--c) center,
      var(--c) right 4px top 50%,
      linear-gradient(#000 0 0);
    -webkit-mask-composite:xor;
            mask-composite:exclude;
    animation: l1 1.5s infinite;
  }
  @keyframes l1 {
    0%    {-webkit-mask-size:0    0  ,0    0  ,0    0  ,auto}
    16.67%{-webkit-mask-size:var(--s),0    0  ,0    0  ,auto}
    33.33%{-webkit-mask-size:var(--s),var(--s),0    0  ,auto}
    50%   {-webkit-mask-size:var(--s),var(--s),var(--s),auto}
    66.67%{-webkit-mask-size:0    0  ,var(--s),var(--s),auto}
    83.33%{-webkit-mask-size:0    0  ,0    0  ,var(--s),auto}
    100%  {-webkit-mask-size:0    0  ,0    0  ,0    0  ,auto}
  }





  /* HTML: <div class="loader"></div> */
.loader-shape-box {
    width: 40px;
    height: 40px;
    --c:no-repeat linear-gradient(orange 0 0);
    background: var(--c),var(--c),var(--c),var(--c);
    background-size: 21px 21px;
    animation: l5b 1.5s infinite cubic-bezier(0.3,1,0,1);
}
@keyframes l5b {
   0%   {background-position: 0    0,100% 0   ,100% 100%,0 100%}
   33%  {background-position: 0    0,100% 0   ,100% 100%,0 100%;width:60px;height: 60px}
   66%  {background-position: 100% 0,100% 100%,0    100%,0 0   ;width:60px;height: 60px}
   100% {background-position: 100% 0,100% 100%,0    100%,0 0   }
}



/* HTML: <div class="loader"></div> */
.loader-clones-box {
    width: 60px;
    aspect-ratio: 1;
    background: 
      linear-gradient(45deg,#60B99A 50%,#0000 0),
      linear-gradient(45deg,#0000 50%,#60B99A 0),
      linear-gradient(-45deg,#f77825 50%,#0000 0),
      linear-gradient(-45deg,#0000 50%,#f77825 0),
      linear-gradient(#554236 0 0);
    background-size: 50% 50%;
    background-repeat: no-repeat;
    animation: l18 1.5s infinite;
  }
  @keyframes l18{
    0%   {background-position:50% 50%,50% 50%,50%  50% ,50% 50%,50% 50%}
    25%  {background-position:0  100%,100%  0,50%  50% ,50% 50%,50% 50%}
    50%  {background-position:0  100%,100%  0,100% 100%,0   0  ,50% 50%}
    75%  {background-position:50% 50%,50% 50%,100% 100%,0   0  ,50% 50%}
    100% {background-position:50% 50%,50% 50%,50%  50% ,50% 50%,50% 50%}
  }



  /* HTML: <div class="loader"></div> */
.loader-classic {
    font-weight: bold;
    font-family: sans-serif;
    font-size: 30px;
    animation: l1 1s linear infinite alternate;
  }
  .loader-classic:before {
    content:"Loading..."
  }
  @keyframes l1 {to{opacity: 0}}