import "./spinner.css";
export interface SpinnerType {
    type?: "opposing-spinner" | "opposing-double-spinner" | "spinner-wheel" | "spinner-double" | "tail-spinner" | "shape-cutting" | "shape-box" | "clones-box" | "classic";
    size?: "sm" | "md" | "lg" | "xl"; 

}
const sizeMap: Record<NonNullable<SpinnerType["size"]>, number> = {
    sm: 30,
    md: 50,
    lg: 70,
    xl: 80,
}

const typeMapper: Record<NonNullable<SpinnerType["type"]>, string> = {
    "opposing-spinner": "loader-opposing-spinner",
    "opposing-double-spinner": "loader-spinner-multi-opposing",
    "spinner-wheel": "loader-spinner-wheel",
    "spinner-double": "loader-spinner-double",
    "tail-spinner": "loader-tail-spinner",
    "shape-cutting": "loader-shape-cutting",
    "shape-box": "loader-shape-box",
    "clones-box": "loader-clones-box",
    "classic": "loader-classic",
}


const SpinnerTemp: React.FC<SpinnerType> = ({ type = "opposing-spinner", size = "md" }) => {
    const px:number = sizeMap[size] ;
    const defaultType = typeMapper[type] ;

    
    return (
        <div className={`${defaultType} `} style={{ width: `${px}px`, height: `${px}px`  } } ></div>
    )
}
export default SpinnerTemp;