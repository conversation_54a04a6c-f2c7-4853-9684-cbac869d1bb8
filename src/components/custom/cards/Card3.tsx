"use client"

import * as React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export interface Card3Props {
  title: string
  description?: string
  imageUrl?: string
  badges?: Array<{ label: string; variant?: "default" | "secondary" | "destructive" | "outline" }>
  content?: string
  addToCartLabel?: string
  onAddToCart?: () => void
  buyNowLabel?: string
  onBuyNow?: () => void
}
export function Card3({
  title,
  description,
  imageUrl,
  badges = [],
  content,
  addToCartLabel = "Add to cart",
  onAddToCart,
  buyNowLabel = "Buy now",
  onBuyNow,
}: Card3Props) {
  return (
    <Card>
      <CardHeader className="flex-col items-start space-y-2">
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-2">
        {imageUrl && (
          <img
            src={imageUrl}
            alt={title}
            className="w-full rounded-lg border"
          />
        )}
        <div className="flex flex-wrap gap-2">
          {badges.map((badge, index) => (
            <Badge key={index} variant={badge.variant ?? "secondary"}>
              {badge.label}
            </Badge>
          ))}
        </div>
        {content && <p className="text-sm">{content}</p>}
      </CardContent>
      <CardFooter className="flex space-x-2">
        <Button variant="outline" onClick={onAddToCart}>
          {addToCartLabel}
        </Button>
        <Button onClick={onBuyNow}>{buyNowLabel}</Button>
      </CardFooter>
    </Card>
  )
}
