"use client"
import * as React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export interface Card1Props {
  avatarUrl?: string
  name: string
  role: string
  description?: string
  badges?: Array<{ label: string; variant?: "default" | "secondary" | "destructive" | "outline" }>
  buttonLabel?: string
  onButtonClick?: () => void
}


export function Card1({
  avatarUrl,
  name,
  role,
  description,
  badges = [],
  buttonLabel = "Follow",
  onButtonClick,
}: Card1Props) {
  return (
    <Card>
      <CardHeader className="flex items-center space-x-4">
        <img
          src={avatarUrl ?? "https://via.placeholder.com/50"}
          alt={name}
          className="h-10 w-10 rounded-full"
        />
        <div className="flex flex-col space-y-1">
          <CardTitle>{name}</CardTitle>
          <CardDescription>{role}</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {description && <p>{description}</p>}
        <div className="flex flex-wrap gap-2">
          {badges.map((badge, index) => (
            <Badge key={index} variant={badge.variant ?? "secondary"}>
              {badge.label}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={onButtonClick}>{buttonLabel}</Button>
      </CardFooter>
    </Card>
  )
}
