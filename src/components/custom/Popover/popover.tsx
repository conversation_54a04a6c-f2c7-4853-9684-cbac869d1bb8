import React, { useState, useRef, useEffect } from "react";

// Define variant types
type PopoverVariant = "default" | "primary" | "secondary" | "danger" | "success" | "accent"|'destructive' | "outline" | "ghost" | "link";

// Base Popover Props
interface BasePopoverProps {
  children: React.ReactNode; // The content inside the popover
  align?: "left" | "right" | "center"; // Horizontal alignment
  position?: "top" | "bottom" | "left" | "right"; // Direction of the popover
  className?: string; // Additional classes for the popover
  offset?: number; // Distance from trigger in pixels
  width?: string; // Width of the popover (e.g., "w-48", "w-64")
  variant?: PopoverVariant; // Visual styling variant
}

// Click Popover Props
interface ClickPopoverProps extends BasePopoverProps {
  trigger: React.ReactNode; // The element that triggers the popover
  triggerClassName?: string; // Additional classes for the trigger
}

// Hover Popover Props
interface HoverPopoverProps extends BasePopoverProps {
  trigger: React.ReactNode; // The element that triggers the popover
  triggerClassName?: string; // Additional classes for the trigger
  delay?: number; // Delay before showing/hiding in milliseconds
}

// Menu Popover Props
interface MenuPopoverProps extends BasePopoverProps {
  trigger: React.ReactNode; // The element that triggers the popover
  items: { label: string; onClick: () => void; icon?: React.ReactNode }[];
  triggerClassName?: string; // Additional classes for the trigger
}

// Helper function to get variant classes
const getVariantClasses = (variant: PopoverVariant = "default") => {
  const variants = {
    default: 'bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]',
    primary: 'bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]',
    secondary: 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))]',
    destructive: 'bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))]',
    danger: 'bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))]', // Alias for destructive
    outline: 'border border-[hsl(var(--border))] text-[hsl(var(--foreground))] bg-transparent',
    ghost: 'bg-transparent text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]',
    link: 'text-[hsl(var(--primary))] underline hover:text-[hsl(var(--primary-foreground))]',
    accent: 'bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))]',
    success: 'bg-[hsl(var(--success))] text-[hsl(var(--success-foreground))]'
  };

  return variants[variant];
};

export const Popover: React.FC<ClickPopoverProps> = ({
  trigger,
  children,
  align = "center",
  position = "bottom",
  className = "",
  triggerClassName = "",
  offset = 8,
  width = "w-48",
  variant = "default",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);

  const togglePopover = () => setIsOpen(!isOpen);
  const closePopover = () => setIsOpen(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        closePopover();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const alignmentClasses = {
    left: "left-0",
    right: "right-0",
    center: "left-1/2 transform -translate-x-1/2",
  };

  const positionClasses = {
    top: `bottom-full mb-${offset}`,
    bottom: `top-full mt-${offset}`,
    left: `right-full mr-${offset}`,
    right: `left-full ml-${offset}`,
  };

  const variantClasses = getVariantClasses(variant);

  return (
    <div className="relative inline-block" ref={popoverRef}>
      {/* Trigger Element */}
      <div onClick={togglePopover} className={`cursor-pointer ${triggerClassName}`}>
        {trigger}
      </div>

      {/* Popover Content */}
      {isOpen && (
        <div
          className={`absolute z-10 p-4 ${width} ${variantClasses} border shadow-lg rounded-[var(--radius)] ${alignmentClasses[align]} ${positionClasses[position]} ${className}`}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export const HoverPopover: React.FC<HoverPopoverProps> = ({
  trigger,
  children,
  align = "center",
  position = "bottom",
  className = "",
  triggerClassName = "",
  delay = 200,
  offset = 8,
  width = "w-48",
  variant = "default",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  const showPopover = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => setIsOpen(true), delay);
  };

  const hidePopover = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => setIsOpen(false), delay);
  };

  const handleMouseEnter = () => showPopover();
  const handleMouseLeave = () => hidePopover();

  const alignmentClasses = {
    left: "left-0",
    right: "right-0",
    center: "left-1/2 transform -translate-x-1/2",
  };

  const positionClasses = {
    top: `bottom-full mb-${offset}`,
    bottom: `top-full mt-${offset}`,
    left: `right-full mr-${offset}`,
    right: `left-full ml-${offset}`,
  };

  const variantClasses = getVariantClasses(variant);

  return (
    <div 
      className="relative inline-block" 
      ref={popoverRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Trigger Element */}
      <div className={triggerClassName}>
        {trigger}
      </div>

      {/* Popover Content */}
      {isOpen && (
        <div
          className={`absolute z-10 p-4 ${width} ${variantClasses} border shadow-lg rounded-[var(--radius)] ${alignmentClasses[align]} ${positionClasses[position]} ${className}`}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export const MenuPopover: React.FC<MenuPopoverProps> = ({
  trigger,
  items,
  align = "right",
  position = "bottom",
  className = "",
  triggerClassName = "",
  offset = 8,
  width = "w-48",
  variant = "default",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);

  const togglePopover = () => setIsOpen(!isOpen);

  const closePopover = () => setIsOpen(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        closePopover();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const alignmentClasses = {
    left: "left-0",
    right: "right-0",
    center: "left-1/2 transform -translate-x-1/2",
  };

  const positionClasses = {
    top: `bottom-full mb-${offset}`,
    bottom: `top-full mt-${offset}`,
    left: `right-full mr-${offset}`,
    right: `left-full ml-${offset}`,
  };

  const variantClasses = getVariantClasses(variant);
  
  // Calculate hover variant style for menu items
  const getHoverClass = () => {
    switch(variant) {
      case "primary": return "hover:bg-[hsl(var(--primary-hover))]";
      case "secondary": return "hover:bg-[hsl(var(--secondary-hover))]";
      case "danger": return "hover:bg-[hsl(var(--destructive-hover))]";
      case "success": return "hover:bg-[hsl(var(--success-hover))]";
      case "accent": return "hover:bg-[hsl(var(--accent-hover))]";
      default: return "hover:bg-[hsl(var(--muted))]";
    }
  };

  const hoverClass = getHoverClass();

  return (
    <div className="relative inline-block" ref={popoverRef}>
      {/* Trigger Element */}
      <div onClick={togglePopover} className={`cursor-pointer ${triggerClassName}`}>
        {trigger}
      </div>

      {/* Popover Content */}
      {isOpen && (
        <div
          className={`absolute z-10 py-1 ${width} ${variantClasses} border shadow-lg rounded-[var(--radius)] ${alignmentClasses[align]} ${positionClasses[position]} ${className}`}
        >
          <div className="divide-y divide-[hsl(var(--border))]">
            {items.map((item, index) => (
              <div
                key={index}
                className={`px-4 py-2 flex items-center gap-2 ${hoverClass} cursor-pointer`}
                onClick={() => {
                  item.onClick();
                  closePopover();
                }}
              >
                {item.icon && <span>{item.icon}</span>}
                <span>{item.label}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export const TooltipPopover: React.FC<HoverPopoverProps> = ({
  trigger,
  children,
  align = "center",
  position = "top",
  className = "",
  triggerClassName = "",
  delay = 100,
  offset = 4,
  width = "w-auto",
  variant = "default",
}) => {
  return (
    <HoverPopover
      trigger={trigger}
      align={align}
      position={position}
      className={`px-2 py-1 text-xs font-medium ${className}`}
      triggerClassName={triggerClassName}
      delay={delay}
      offset={offset}
      width={width}
      variant={variant}
    >
      {children}
    </HoverPopover>
  );
};

export default Popover;