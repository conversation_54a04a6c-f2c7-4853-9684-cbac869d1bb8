import {
  getCustomCheckboxInputColorClassNames,
  getInputColorClassNames,
} from "@/helpers";
import { CheckCircle } from "lucide-react";

type Props = {
  label: string;
  name: string;
  checked?: boolean;
  color?: string;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  id?: string;
};

const Checkbox = ({
  label,
  checked,
  disabled,
  id,
  name,
  color,
  onChange,
}: Props) => {
  return (
    <label className="gap-2 flex items-center">
      <input
        type="checkbox"
        checked={checked}
        id={id}
        name={name}
        disabled={disabled}
        className={`w-4 h-4 bg-gray-100 border-gray-300 rounded focus:ring-2 ${getInputColorClassNames(
          color
        )}`}
        onChange={onChange ? (e) => onChange(e.target.checked) : undefined}
      />
      <label htmlFor={id}>{label}</label>
    </label>
  );
};

export const CheckboxRound = ({
  label,
  checked,
  disabled,
  id,
  name,
  color,
  onChange,
}: Props) => {
  return (
    <label className="gap-2 flex items-center relative">
      <input
        type="checkbox"
        checked={checked}
        id={id}
        name={name}
        disabled={disabled}
        className={`appearance-none relative w-4 h-4 bg-gray-100 border-gray-300 rounded-full focus:ring-2 ${getInputColorClassNames(
          color
        )}  ${getCustomCheckboxInputColorClassNames(color)}`}
        onChange={onChange ? (e) => onChange(e.target.checked) : undefined}
      />
      {checked && (
        <CheckCircle className="absolute text-white left-[1px] " size={14} />
      )}
      <label htmlFor={id}>{label}</label>
    </label>
  );
};

export default Checkbox;
