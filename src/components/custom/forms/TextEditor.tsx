import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

type Props = {
  value: string;
  setValue: (value: string) => void;
};

const TextEditor = ({ value, setValue }: Props) => {
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ align: [] }],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["link", "image", "video"],
      ["clean"],
      ["code-block"],
      [{ color: [] }, { background: [] }],
    ],
  };

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "align",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "video",
    "code-block",
    "color",
    "background",
  ];
  return (
    <ReactQuill
      theme="snow"
      value={value}
      onChange={(value) => setValue(value)}
      modules={modules}
      formats={formats}
      placeholder="Write something amazing..."
    />
  );
};

export default TextEditor;
