"use client"

import { useForm } from "react-hook-form"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"

interface Form2Props {
  onSubmit: (data: any) => void
  defaultValues?: {
    largeinput?: string
    defaultinput?: string
    smallinput?: string
  }
}

export function Form2({ onSubmit, defaultValues }: Form2Props) {
  const form = useForm({
    defaultValues: defaultValues || {
      largeinput: "",
      defaultinput: "",
      smallinput: "",
    },
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="largeinput"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input 
                  className="h-12 text-lg" 
                  placeholder="form-control-lg"
                  {...field} 
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="defaultinput"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input 
                  placeholder="Default input"
                  {...field} 
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="smallinput"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input 
                  className="h-8 text-sm"
                  placeholder="form-control-sm"
                  {...field} 
                />
              </FormControl>
              <FormMessage>
                Your password must be 8-20 characters long, contain letters and numbers.
              </FormMessage>
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}
