import React, { useState, useMemo } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { countries, defaultCountry, type Country } from '@/data/countries';
import { Globe, Search } from 'lucide-react';

interface CountrySelectProps {
  value?: string;
  onChange?: (countryCode: string) => void;
  onCountryChange?: (country: Country) => void;
  label?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  showFlag?: boolean;
  showPhoneCode?: boolean;
}

const CountrySelect: React.FC<CountrySelectProps> = ({
  value = '',
  onChange,
  onCountryChange,
  label = 'Country',
  placeholder = 'Select a country',
  required = false,
  className = '',
  showFlag = true,
  showPhoneCode = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleCountryChange = (countryCode: string) => {
    onChange?.(countryCode);
    const country = countries.find(c => c.code === countryCode);
    if (country) {
      onCountryChange?.(country);
    }
  };

  const selectedCountry = countries.find(c => c.code === value);

  // Filter countries based on search term
  const filteredCountries = useMemo(() => {
    if (!searchTerm) return countries;

    const searchLower = searchTerm.toLowerCase();
    return countries.filter(country =>
      country.name.toLowerCase().includes(searchLower) ||
      country.code.toLowerCase().includes(searchLower) ||
      (showPhoneCode && country.phoneCode.includes(searchTerm))
    );
  }, [searchTerm, showPhoneCode]);

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor="country-select" className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      <div className="relative">
        <Globe className="absolute left-3 top-3 w-4 h-4 text-gray-400 z-10" />
        <Select value={value} onValueChange={handleCountryChange}>
          <SelectTrigger className="pl-10" id="country-select">
            <SelectValue placeholder={placeholder}>
              {selectedCountry && (
                <div className="flex items-center gap-2">
                  {showFlag && <span className="text-lg">{selectedCountry.flag}</span>}
                  <span className="text-sm">{selectedCountry.name}</span>
                  {showPhoneCode && (
                    <span className="text-xs text-gray-500">({selectedCountry.phoneCode})</span>
                  )}
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-h-[300px]">
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search countries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 h-8"
                />
              </div>
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              {filteredCountries.length === 0 ? (
                <div className="p-2 text-sm text-gray-500 text-center">
                  No countries found
                </div>
              ) : (
                filteredCountries.map((country) => (
                  <SelectItem key={country.code} value={country.code}>
                    <div className="flex items-center gap-2">
                      {showFlag && <span className="text-lg">{country.flag}</span>}
                      <span className="text-sm">{country.name}</span>
                      {showPhoneCode && (
                        <span className="text-xs text-gray-500">({country.phoneCode})</span>
                      )}
                    </div>
                  </SelectItem>
                ))
              )}
            </div>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default CountrySelect;
