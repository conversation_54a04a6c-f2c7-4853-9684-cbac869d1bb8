// src/components/custom/forms/MultiStepForm.tsx

import React, { useEffect, Children, ReactElement } from 'react';
import { FormProvider, useFormContext } from './FormContext';
import { Button } from '@/components/ui/button';
import StepIndicator from './StepIndicator';
import { ArrowLeft, ArrowRight, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

export type MultiStepFormVariant = 'horizontal' | 'vertical' | 'tabs' | 'numbered';

interface MultiStepFormProps {
  children: ReactElement | ReactElement[];
  title?: string;
  description?: string;
  onComplete?: (data: Record<string, any>) => void;
  initialData?: Record<string, any>;
  initialStep?: number;
  submitLabel?: string;
  className?: string;
  variant?: MultiStepFormVariant;
  showStepTitles?: boolean;
  allowSkipSteps?: boolean;
}

const MultiStepForm: React.FC<MultiStepFormProps> = ({
  children,
  title,
  description,
  onComplete,
  initialData = {},
  initialStep = 1,
  submitLabel = 'Complete',
  className = '',
  variant = 'horizontal',
  showStepTitles = true,
  allowSkipSteps = true,
}) => {
  return (
    <FormProvider
      initialData={initialData}
      initialStep={initialStep}
      allowSkipSteps={allowSkipSteps}
    >
      <MultiStepFormContent
        title={title}
        description={description}
        onComplete={onComplete}
        submitLabel={submitLabel}
        className={className}
        variant={variant}
        showStepTitles={showStepTitles}
      >
        {children}
      </MultiStepFormContent>
    </FormProvider>
  );
};

interface MultiStepFormContentProps {
  children: ReactElement | ReactElement[];
  title?: string;
  description?: string;
  onComplete?: (data: Record<string, any>) => void;
  submitLabel?: string;
  className?: string;
  variant?: MultiStepFormVariant;
  showStepTitles?: boolean;
}

const MultiStepFormContent: React.FC<MultiStepFormContentProps> = ({
  children,
  title,
  description,
  onComplete,
  submitLabel = 'Complete',
  className = '',
  variant = 'horizontal',
  showStepTitles = true,
}) => {
  const {
    currentStep,
    nextStep,
    prevStep,
    isFirstStep,
    isLastStep,
    formData,
    setTotalSteps,
    goToStep,
    validateStep,
  } = useFormContext();

  const childrenArray = Children.toArray(children) as ReactElement[];
  const stepTitles = childrenArray.map(child => child.props.title || '');

  useEffect(() => {
    setTotalSteps(childrenArray.length);
  }, [childrenArray.length, setTotalSteps]);

  // Scroll to top when step changes
  useEffect(() => {
    // Use setTimeout to ensure DOM has updated before scrolling
    const timer = setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      // Also try scrolling the main container if it exists
      const mainContainer = document.querySelector('main');
      if (mainContainer) {
        mainContainer.scrollTo({ top: 0, behavior: 'smooth' });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [currentStep]);

  const currentChild = childrenArray[currentStep - 1];

  const handleComplete = () => {
    if (onComplete) {
      onComplete(formData);
    }
  };

  const handleNextStep = () => {
    const isValid = validateStep(currentStep);
    if (isValid) {
      nextStep();
    } else {
      // You could add a toast notification here for better UX
      console.warn('Please complete all required fields before proceeding');
    }
  };

  const renderFormNavigation = () => (
    <div className="flex justify-between mt-8 pt-4 border-t">
      {!isFirstStep && (
        <Button
          type="button"
          variant="outline"
          onClick={prevStep}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Previous
        </Button>
      )}

      {isLastStep ? (
        <Button
          type="button"
          onClick={handleComplete}
          disabled={!formData.confirmed}
          className={cn(
            "flex items-center gap-2 ml-auto",
            !formData.confirmed && "opacity-50 cursor-not-allowed"
          )}
        >
          {submitLabel}
          <Check className="h-4 w-4" />
        </Button>
      ) : (
        <Button
          type="button"
          onClick={handleNextStep}
          className="flex items-center gap-2 ml-auto"
        >
          Next
          <ArrowRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  );

  const renderVerticalSteps = () => (
    <div className="flex flex-row gap-8">
      <div className="w-1/4 pr-4">
        <div className="flex flex-col space-y-4">
          {childrenArray.map((child, index) => {
            const stepNumber = index + 1;
            const isActive = stepNumber === currentStep;
            const isCompleted = stepNumber < currentStep;

            return (
              <button
                key={stepNumber}
                onClick={() => goToStep(stepNumber)}
                className={cn(
                  "flex items-center p-3 rounded-md text-left transition-all",
                  isActive
                    ? "bg-primary/10 text-primary font-medium"
                    : isCompleted
                      ? "text-primary/80 hover:bg-primary/5"
                      : "text-muted-foreground hover:bg-muted"
                )}
              >
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full mr-3 shrink-0",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : isCompleted
                        ? "bg-primary/80 text-primary-foreground"
                        : "bg-muted text-muted-foreground"
                  )}
                >
                  {isCompleted ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <span>{stepNumber}</span>
                  )}
                </div>
                <span className="text-sm">{child.props.title || `Step ${stepNumber}`}</span>
              </button>
            );
          })}
        </div>
      </div>
      <div className="w-3/4 p-6 bg-card rounded-lg border shadow-sm">
        <div className="animate-fade-in">
          {title && showStepTitles && (
            <div className="mb-6">
              <h3 className="text-lg font-medium">{title}</h3>
              {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
            </div>
          )}
          {currentChild}
          {renderFormNavigation()}
        </div>
      </div>
    </div>
  );

  const renderTabsLayout = () => (
    <div className="space-y-8">
      <div className="flex border-b">
        {childrenArray.map((child, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;

          return (
            <button
              key={stepNumber}
              onClick={() => goToStep(stepNumber)}
              className={cn(
                "px-4 py-2 -mb-px text-sm font-medium",
                isActive
                  ? "border-b-2 border-primary text-primary"
                  : isCompleted
                    ? "text-primary/80 hover:text-primary"
                    : "text-muted-foreground hover:text-foreground"
              )}
            >
              {isCompleted && <Check className="inline h-3 w-3 mr-1" />}
              {child.props.title || `Step ${stepNumber}`}
            </button>
          );
        })}
      </div>
      <div className="p-6 bg-card rounded-lg border shadow-sm">
        <div className="animate-fade-in">
          {currentChild}
          {renderFormNavigation()}
        </div>
      </div>
    </div>
  );

  const renderNumberedLayout = () => (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        {childrenArray.map((child, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;

          return (
            <div key={stepNumber} className="flex flex-col items-center">
              <button
                onClick={() => goToStep(stepNumber)}
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center transition-all",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : isCompleted
                      ? "bg-primary/80 text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                )}
              >
                {isCompleted ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span>{stepNumber}</span>
                )}
              </button>
              {showStepTitles && child.props.title && (
                <span
                  className={cn(
                    "mt-2 text-xs font-medium text-center",
                    isActive ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  {child.props.title}
                </span>
              )}
            </div>
          );
        })}
      </div>
      <div className="p-6 bg-card rounded-lg border shadow-sm">
        <div className="animate-fade-in">
          {currentChild}
          {renderFormNavigation()}
        </div>
      </div>
    </div>
  );

  const renderHorizontalLayout = () => (
    <div className="space-y-8">
      <div className="mb-8">
        <StepIndicator
          steps={childrenArray.length}
          currentStep={currentStep}
          labels={showStepTitles ? stepTitles : undefined}
          onStepClick={goToStep}
        />
      </div>
      <div className="p-6 bg-card rounded-lg border shadow-sm">
        <div className="animate-fade-in">
          {title && (
            <div className="mb-6">
              <h3 className="text-lg font-medium">{title}</h3>
              {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
            </div>
          )}
          {currentChild}
          {renderFormNavigation()}
        </div>
      </div>
    </div>
  );

  const renderSelectedLayout = () => {
    switch (variant) {
      case 'vertical':
        return renderVerticalSteps();
      case 'tabs':
        return renderTabsLayout();
      case 'numbered':
        return renderNumberedLayout();
      case 'horizontal':
      default:
        return renderHorizontalLayout();
    }
  };

  return (
    <div className={cn("w-full", className)}>
      {renderSelectedLayout()}
    </div>
  );
};

export default MultiStepForm;
