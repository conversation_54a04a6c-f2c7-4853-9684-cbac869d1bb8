import  { forwardRef } from "react";
import { X } from "lucide-react";
import { 
  She<PERSON>,
  <PERSON>etContent,
  Sheet<PERSON><PERSON>le,
  SheetDescription
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { BaseModalProps } from "./BaseModal";

export interface DrawerModalProps extends BaseModalProps {
    side?: "left" | "right" | "top" | "bottom";
    size?: "sm" | "md" | "lg" | "xl" | "full";
  }


const DrawerModal = forwardRef<HTMLDivElement, DrawerModalProps>(
  (
    {
      isOpen,
      onOpenChange,
      side = "right",
      size = "md",
      title,
      description,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const getSizeClasses = (size: string, side: string) => {
      if (side === "top" || side === "bottom") {
        return ""; // Top/bottom drawers use default height
      }
      
      switch (size) {
        case "sm":
          return "w-full sm:w-80 sm:max-w-sm";
        case "md":
          return "w-full sm:w-96 sm:max-w-md";
        case "lg":
          return "w-full sm:w-1/2 sm:max-w-lg md:max-w-xl";
        case "xl":
          return "w-full sm:w-2/3 sm:max-w-2xl md:max-w-4xl";
        case "full":
          return "w-full";
        default:
          return "w-full sm:w-96 sm:max-w-md";
      }
    };

    return (
      <Sheet open={isOpen} onOpenChange={onOpenChange} {...props}>
        <SheetContent
          ref={ref}
          side={side}
          className={cn(
            "p-0 gap-0 bg-background focus:outline-none",
            getSizeClasses(size, side),
            className
          )}
        >
          <div className="p-4 sm:p-5 border-b sticky top-0 bg-background z-10 flex justify-between items-start gap-4">
            <div className="flex-1 min-w-0">
              {title && <SheetTitle className="text-base sm:text-lg truncate">{title}</SheetTitle>}
              {description && <SheetDescription className="text-sm mt-1 line-clamp-2 sm:line-clamp-none">{description}</SheetDescription>}
            </div>
            <button
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shrink-0"
              onClick={() => onOpenChange?.(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          <div className="p-4 sm:p-5 overflow-auto flex-1">
            {children}
          </div>
        </SheetContent>
      </Sheet>
    );
  }
);

DrawerModal.displayName = "DrawerModal";
export default DrawerModal;