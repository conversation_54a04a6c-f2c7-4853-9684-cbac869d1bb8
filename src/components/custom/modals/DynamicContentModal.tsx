import  { forwardRef, ReactNode } from "react";
import { AlertCircle, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import BaseModal from "./BaseModal";
import { BaseModalProps } from "./BaseModal";

export interface DynamicContentModalProps extends BaseModalProps {
    contentId: string;
    isLoading?: boolean;
    fallback?: ReactNode;
    onRetry?: () => void;
  }
  
const DynamicContentModal = forwardRef<HTMLDivElement, DynamicContentModalProps>(
  (
    {
      contentId,
      isLoading = false,
      fallback,
      onRetry,
      children,
      ...props
    },
    ref
  ) => {
    const renderContent = () => {
      if (isLoading) {
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
            <p className="text-sm text-muted-foreground">Loading content...</p>
          </div>
        );
      }

      if (!children && fallback) {
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
            <p className="text-sm text-muted-foreground mb-4">{fallback}</p>
            {onRetry && (
              <Button type="button" variant="outline" size="sm" onClick={onRetry}>
                Try Again
              </Button>
            )}
          </div>
        );
      }

      return children;
    };

    return (
      <BaseModal ref={ref} {...props}>
        {renderContent()}
      </BaseModal>
    );
  }
);

DynamicContentModal.displayName = "DynamicContentModal";
export default DynamicContentModal;