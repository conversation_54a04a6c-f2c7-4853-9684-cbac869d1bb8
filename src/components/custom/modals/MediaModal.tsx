import  { forwardRef } from "react";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import BaseModal from "./BaseModal";
import { BaseModalProps } from "./BaseModal";

export interface MediaModalProps extends Omit<BaseModalProps, "children"> {
    src: string;
    alt?: string;
    type: "image" | "video" | "pdf";
    downloadable?: boolean;
  }

const MediaModal = forwardRef<HTMLDivElement, MediaModalProps>(
  (
    {
      src,
      alt = "Media preview",
      type,
      downloadable = true,
      ...props
    },
    ref
  ) => {
    const handleDownload = () => {
      const a = document.createElement("a");
      a.href = src;
      a.download = src.split("/").pop() || "download";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    };

    const footer = downloadable ? (
      <Button type="button" onClick={handleDownload} variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        Download
      </Button>
    ) : null;

    const renderMedia = () => {
      switch (type) {
        case "image":
          return (
            <div className="flex justify-center">
              <img
                src={src}
                alt={alt}
                className="max-h-[70vh] max-w-full object-contain rounded"
              />
            </div>
          );
        case "video":
          return (
            <div className="flex justify-center">
              <video
                src={src}
                controls
                className="max-h-[70vh] max-w-full rounded"
              />
            </div>
          );
        case "pdf":
          return (
            <div className="flex justify-center h-[70vh]">
              <iframe
                src={src}
                title={alt}
                className="w-full h-full border-0 rounded"
              />
            </div>
          );
        default:
          return <div>Unsupported media type</div>;
      }
    };

    return (
      <BaseModal
        ref={ref}
        footer={footer}
        size="xl"
        {...props}
      >
        <div className="py-4">
          {renderMedia()}
        </div>
      </BaseModal>
    );
  }
);

MediaModal.displayName = "MediaModal";
export default MediaModal;