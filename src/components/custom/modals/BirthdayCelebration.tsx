import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface BirthdayCelebrationProps {
  isOpen: boolean;
  onClose: () => void;
  userName?: string;
}

const BirthdayCelebration: React.FC<BirthdayCelebrationProps> = ({ 
  isOpen, 
  onClose, 
  userName = "Valued Team Member" 
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [showCloseButton, setShowCloseButton] = useState(false);
  const confettiRef = useRef<HTMLCanvasElement>(null);

  const birthdayMessages = [
    { text: "Happy Birthday!", language: "English" },
    { text: "Siku ya Kuzal<PERSON>wa Njema!", language: "Swahili" },
    { text: "Joyeux Anniversaire!", language: "French" },
    { text: "Buon Compleanno!", language: "Italian" },
    { text: "¡Feliz <PERSON>s!", language: "Spanish" },
    { text: "Alles Gute zum Geburtstag!", language: "German" },
    { text: "お誕生日おめでとう!", language: "Japanese" }
  ];

  // Rotate messages every second
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % birthdayMessages.length);
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen, birthdayMessages.length]);

  // Show close button after 5 seconds
  useEffect(() => {
    if (!isOpen) return;

    const timer = setTimeout(() => {
      setShowCloseButton(true);
    }, 5000);

    return () => clearTimeout(timer);
  }, [isOpen]);

  // Confetti animation
  useEffect(() => {
    if (!isOpen || !confettiRef.current) return;

    const canvas = confettiRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const confettiPieces: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      color: string;
      size: number;
      rotation: number;
      rotationSpeed: number;
    }> = [];

    // Initial confetti burst
    for (let i = 0; i < 50; i++) {
      confettiPieces.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height * 0.5, // Start from top half
        vx: (Math.random() - 0.5) * 8,
        vy: Math.random() * 5 + 2,
        color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#FF9F43', '#6C5CE7', '#A29BFE', '#FD79A8', '#FDCB6E'][Math.floor(Math.random() * 12)],
        size: Math.random() * 12 + 6,
        rotation: Math.random() * Math.PI * 2,
        rotationSpeed: (Math.random() - 0.5) * 0.3
      });
    }

    let animationId: number;
    let lastConfettiTime = 0;
    const confettiInterval = 100; // Add new confetti every 100ms (faster)

    const animate = (currentTime: number) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Add new confetti pieces continuously
      if (currentTime - lastConfettiTime > confettiInterval) {
        for (let i = 0; i < 8; i++) { // Increased from 3 to 8 pieces per interval
          confettiPieces.push({
            x: Math.random() * canvas.width,
            y: -10,
            vx: (Math.random() - 0.5) * 6, // Increased horizontal spread
            vy: Math.random() * 4 + 3, // Increased vertical speed
            color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#FF9F43', '#6C5CE7', '#A29BFE', '#FD79A8', '#FDCB6E'][Math.floor(Math.random() * 12)], // More colors
            size: Math.random() * 12 + 6, // Increased size range
            rotation: 0,
            rotationSpeed: (Math.random() - 0.5) * 0.3 // Increased rotation speed
          });
        }
        lastConfettiTime = currentTime;
      }

      // Update and draw existing confetti pieces
      for (let i = confettiPieces.length - 1; i >= 0; i--) {
        const piece = confettiPieces[i];
        piece.x += piece.vx;
        piece.y += piece.vy;
        piece.vy += 0.1; // gravity
        piece.rotation += piece.rotationSpeed;

        ctx.save();
        ctx.translate(piece.x, piece.y);
        ctx.rotate(piece.rotation);
        ctx.fillStyle = piece.color;
        
        // Draw different shapes for variety
        const shapeType = Math.floor(Math.random() * 4);
        switch (shapeType) {
          case 0: // Square
            ctx.fillRect(-piece.size / 2, -piece.size / 2, piece.size, piece.size);
            break;
          case 1: // Circle
            ctx.beginPath();
            ctx.arc(0, 0, piece.size / 2, 0, Math.PI * 2);
            ctx.fill();
            break;
          case 2: // Triangle
            ctx.beginPath();
            ctx.moveTo(0, -piece.size / 2);
            ctx.lineTo(-piece.size / 2, piece.size / 2);
            ctx.lineTo(piece.size / 2, piece.size / 2);
            ctx.closePath();
            ctx.fill();
            break;
          case 3: // Diamond
            ctx.beginPath();
            ctx.moveTo(0, -piece.size / 2);
            ctx.lineTo(piece.size / 2, 0);
            ctx.lineTo(0, piece.size / 2);
            ctx.lineTo(-piece.size / 2, 0);
            ctx.closePath();
            ctx.fill();
            break;
        }
        
        ctx.restore();

        // Remove pieces that are off screen
        if (piece.y > canvas.height + 10) {
          confettiPieces.splice(i, 1);
        }
      }

      if (isOpen) {
        animationId = requestAnimationFrame(animate);
      }
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
        onClick={showCloseButton ? onClose : undefined}
      >
        {/* Confetti Canvas */}
        <canvas
          ref={confettiRef}
          className="absolute inset-0 pointer-events-none"
        />

        {/* Main Birthday Card */}
        <motion.div
          initial={{ scale: 0.5, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.5, opacity: 0, y: 50 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.6 
          }}
          className="relative bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl shadow-2xl max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg w-full mx-2 sm:mx-4 border border-white/20"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close Button */}
          {showCloseButton && (
            <motion.button
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              onClick={onClose}
              className="absolute top-4 right-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
            >
              <X className="w-5 h-5 text-gray-600" />
            </motion.button>
          )}

          {/* Header Icons */}
          <div className="flex justify-center mb-4 sm:mb-6">
            <motion.div
              animate={{ 
                rotate: [0, -10, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3
              }}
              className="relative"
            >
              <Cake className="w-12 h-12 sm:w-16 sm:h-16 text-pink-500" />
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{ 
                  duration: 1.5,
                  repeat: Infinity,
                  repeatDelay: 1
                }}
                className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2"
              >
                <Sparkles className="w-4 h-4 sm:w-6 sm:h-6 text-yellow-400" />
              </motion.div>
            </motion.div>
          </div>

          {/* Birthday Message */}
          <div className="text-center mb-4 sm:mb-6">
            <motion.h1
              key={currentMessageIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-2"
            >
              {birthdayMessages[currentMessageIndex].text}
            </motion.h1>
            
            <motion.p
              key={`lang-${currentMessageIndex}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4"
            >
              {birthdayMessages[currentMessageIndex].language}
            </motion.p>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-sm sm:text-base lg:text-lg text-gray-700"
            >
              {userName}
            </motion.p>
          </div>

          {/* Corporate Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white/50 rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-4 sm:mb-6 text-center"
          >
            <Heart className="w-6 h-6 sm:w-8 sm:h-8 text-red-400 mx-auto mb-2 sm:mb-3" />
            <p className="text-xs sm:text-sm lg:text-base text-gray-700 leading-relaxed">
              Today we celebrate you and the incredible value you bring to our Optiven family. 
              Your dedication, passion, and commitment inspire us all. 
              May this new year of life be filled with joy, success, and countless opportunities to shine.
            </p>
          </motion.div>

          {/* Decorative Elements */}
          <div className="flex justify-center space-x-3 sm:space-x-4 mb-4 sm:mb-6">
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                delay: 0
              }}
            >
              <Gift className="w-6 h-6 sm:w-8 sm:h-8 text-purple-400" />
            </motion.div>
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, -5, 5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                delay: 0.5
              }}
            >
              <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-400" />
            </motion.div>
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                delay: 1
              }}
            >
              <Heart className="w-6 h-6 sm:w-8 sm:h-8 text-red-400" />
            </motion.div>
          </div>

          {/* Countdown to Close */}
          {!showCloseButton && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center"
            >
              <div className="inline-flex items-center space-x-2 text-xs sm:text-sm text-gray-500">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span>You can close this in a moment...</span>
              </div>
            </motion.div>
          )}

          {/* Close Button */}
          {showCloseButton && (
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onClose}
              className="w-full bg-gradient-to-r from-pink-500 to-purple-500 text-white font-semibold py-2 sm:py-3 px-4 sm:px-6 rounded-lg sm:rounded-xl hover:from-pink-600 hover:to-purple-600 transition-all duration-300 shadow-lg text-sm sm:text-base"
            >
              Thank You! 🎉
            </motion.button>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BirthdayCelebration;
