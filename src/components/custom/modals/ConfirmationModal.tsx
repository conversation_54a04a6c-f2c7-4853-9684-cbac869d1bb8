import { forwardRef, ReactNode } from "react";
import { AlertTriangle, AlertCircle, CheckCircle, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import BaseModal from "./BaseModal";
import { BaseModalProps } from "./BaseModal";
import { cn } from "@/lib/utils";

const variantIcons = {
  default: null,
  warning: <AlertTriangle className="h-5 w-5 text-amber-500" />,
  danger: <AlertCircle className="h-5 w-5 text-red-500" />,
  success: <CheckCircle className="h-5 w-5 text-green-500" />,
  info: <Info className="h-5 w-5 text-blue-500" />,
};

const variantClasses = {
  default: "",
  warning: "border-l-4 border-amber-500 bg-amber-50 dark:bg-amber-950/10 px-4 py-3",
  danger: "border-l-4 border-red-500 bg-red-50 dark:bg-red-950/10 px-4 py-3",
  success: "border-l-4 border-green-500 bg-green-50 dark:bg-green-950/10 px-4 py-3",
  info: "border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-950/10 px-4 py-3",
};

export interface ConfirmModalProps extends Omit<BaseModalProps, "children"> {
    onConfirm: () => void;
    onCancel?: () => void;
    confirmText?: string;
    cancelText?: string;
    confirmVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
    cancelVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
    variant?: ModalVariant;
    message?: ReactNode;
    isLoading?: boolean;
    hideCancel?: boolean;
  }
  export type ModalVariant = 
  | "default"
  | "warning"
  | "danger" 
  | "success" 
  | "info";

const ConfirmModal = forwardRef<HTMLDivElement, ConfirmModalProps>(
  (
    {
      onConfirm,
      onCancel,
      confirmText = "Confirm",
      cancelText = "Cancel",
      confirmVariant = "default",
      cancelVariant = "outline",
      variant = "default",
      message,
      isLoading = false,
      hideCancel = false,
      icon = variantIcons[variant],
      ...props
    },
    ref
  ) => {
    const handleConfirm = () => {
      onConfirm();
      if (props.onOpenChange) {
        props.onOpenChange(false);
      }
    };

    const handleCancel = () => {
      onCancel?.();
      if (props.onOpenChange) {
        props.onOpenChange(false);
      }
    };

    const footer = (
      <>
        {!hideCancel && (
          <Button
            type="button"
            variant={cancelVariant}
            onClick={handleCancel}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
        )}
        <Button
          type="button"
          variant={confirmVariant}
          onClick={handleConfirm}
          disabled={isLoading}
        >
          {isLoading ? "Processing..." : confirmText}
        </Button>
      </>
    );

    return (
      <BaseModal
        ref={ref}
        icon={icon}
        footer={footer}
        {...props}
      >
        <div className={cn("my-2", variantClasses[variant])}>
          {message}
        </div>
      </BaseModal>
    );
  }
);

ConfirmModal.displayName = "ConfirmModal";
export default ConfirmModal