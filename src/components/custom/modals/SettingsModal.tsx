
import { forwardRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import BaseModal from "./BaseModal";
import { BaseModalProps } from "./BaseModal";


export type SettingType = {
    id: string;
    label: string;
    type: "toggle" | "checkbox" | "select" | "radio" | "text";
    value: any;
    options?: { label: string; value: string }[];
  };

  export interface SettingsModalProps extends BaseModalProps {
    sections: {
      title: string;
      settings: SettingType[];
    }[];
    onSave?: (settings: Record<string, any>) => void | Promise<void>;
    isLoading?: boolean;
    submitText?: string;
    cancelText?: string;
  }
const SettingsModal = forwardRef<HTMLDivElement, SettingsModalProps>(
  (
    {
      sections,
      onSave,
      isLoading,
      submitText = "Save Settings",
      cancelText = "Cancel",
      ...props
    },
    ref
  ) => {
    const [settingsValues, setSettingsValues] = useState(() => {
      const initialValues: Record<string, any> = {};
      sections.forEach((section) => {
        section.settings.forEach((setting) => {
          initialValues[setting.id] = setting.value;
        });
      });
      return initialValues;
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSettingChange = (id: string, value: any) => {
      setSettingsValues((prev) => ({
        ...prev,
        [id]: value,
      }));
    };

    const handleSubmit = async () => {
      setIsSubmitting(true);
      try {
        await onSave?.(settingsValues);
        props.onOpenChange?.(false);
      } finally {
        setIsSubmitting(false);
      }
    };

    const renderSetting = (setting: SettingType) => {
      const { id, label, type, options } = setting;
      const value = settingsValues[id];

      switch (type) {
        case "toggle":
          return (
            <div className="flex items-center justify-between">
              <Label htmlFor={id}>{label}</Label>
              <Switch
                id={id}
                checked={value}
                onCheckedChange={(checked) => handleSettingChange(id, checked)}
              />
            </div>
          );
        case "checkbox":
          return (
            <div className="flex items-center space-x-2">
              <Checkbox
                id={id}
                checked={value}
                onCheckedChange={(checked) => handleSettingChange(id, checked)}
              />
              <Label htmlFor={id}>{label}</Label>
            </div>
          );
        case "select":
          return (
            <div className="space-y-2">
              <Label htmlFor={id}>{label}</Label>
              <Select
                value={value}
                onValueChange={(value) => handleSettingChange(id, value)}
              >
                <SelectTrigger id={id}>
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {options?.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          );
        case "radio":
          return (
            <div className="space-y-2">
              <Label>{label}</Label>
              <RadioGroup
                value={value}
                onValueChange={(value) => handleSettingChange(id, value)}
              >
                {options?.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={`${id}-${option.value}`} />
                    <Label htmlFor={`${id}-${option.value}`}>{option.label}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          );
        case "text":
          return (
            <div className="space-y-2">
              <Label htmlFor={id}>{label}</Label>
              <Input
                id={id}
                value={value}
                onChange={(e) => handleSettingChange(id, e.target.value)}
              />
            </div>
          );
        default:
          return null;
      }
    };

    const footer = (
      <>
        <Button variant="outline" onClick={() => props.onOpenChange?.(false)}>
          {cancelText}
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting || isLoading}>
          {isSubmitting || isLoading ? "Saving..." : submitText}
        </Button>
      </>
    );

    return (
      <BaseModal ref={ref} footer={footer} {...props}>
        <div className="space-y-6 py-2">
          {sections.map((section, index) => (
            <div key={index} className={index > 0 ? "pt-4 border-t" : ""}>
              <h3 className="text-lg font-medium mb-4">{section.title}</h3>
              <div className="space-y-4">
                {section.settings.map((setting) => (
                  <div key={setting.id}>{renderSetting(setting)}</div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </BaseModal>
    );
  }
);

SettingsModal.displayName = "SettingsModal";
export default SettingsModal;