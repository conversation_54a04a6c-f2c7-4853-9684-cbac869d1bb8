import React, { forwardRef, useState, useEffect, ReactNode } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Check } from "lucide-react";
import BaseModal from "./BaseModal";
import { cn } from "@/lib/utils";
import { BaseModalProps } from "./BaseModal";

export interface MultiStepModalProps extends BaseModalProps {
    steps: {
      title: string;
      content: ReactNode;
    }[];
    currentStep?: number;
    onStepChange?: (step: number) => void;
    onComplete?: () => void;
  }

const MultiStepModal = forwardRef<HTMLDivElement, MultiStepModalProps>(
  (
    {
      steps,
      currentStep: externalCurrentStep,
      onStepChange,
      onComplete,
      ...props
    },
    ref
  ) => {
    const [currentStep, setCurrentStep] = useState(externalCurrentStep || 0);
    
    useEffect(() => {
      if (externalCurrentStep !== undefined) {
        setCurrentStep(externalCurrentStep);
      }
    }, [externalCurrentStep]);

    const handleStepChange = (step: number) => {
      setCurrentStep(step);
      onStepChange?.(step);
    };

    const handleBack = () => {
      if (currentStep > 0) {
        handleStepChange(currentStep - 1);
      }
    };

    const handleNext = () => {
      if (currentStep < steps.length - 1) {
        handleStepChange(currentStep + 1);
      } else {
        onComplete?.();
        props.onOpenChange?.(false);
      }
    };

    const footer = (
      <>
        <div className="flex-1 flex justify-start">
          {currentStep > 0 && (
            <Button 
              type="button" 
              variant="outline" 
              size="sm" 
              onClick={handleBack}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
        </div>
        <Button 
          type="button" 
          onClick={handleNext}
        >
          {currentStep < steps.length - 1 ? (
            <>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </>
          ) : (
            <>
              Complete
              <Check className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </>
    );

    return (
      <BaseModal
        ref={ref}
        title={steps[currentStep]?.title || props.title}
        footer={footer}
        footerClassName="flex justify-between w-full"
        {...props}
      >
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => (
              <React.Fragment key={index}>
                <div className="flex flex-col items-center">
                  <div 
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium border transition-colors",
                      index === currentStep 
                        ? "border-primary bg-primary text-primary-foreground" 
                        : index < currentStep
                        ? "border-primary/30 bg-primary/10 text-primary"
                        : "border-gray-300 text-gray-500 dark:border-gray-600"
                    )}
                    aria-current={index === currentStep ? "step" : undefined}
                  >
                    {index + 1}
                  </div>
                  <div className="text-xs mt-1 text-center max-w-[60px] truncate">
                    {step.title}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div 
                    className={cn(
                      "flex-1 h-px mx-1",
                      index < currentStep 
                        ? "bg-primary" 
                        : "bg-gray-300 dark:bg-gray-600"
                    )}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
        <div>{steps[currentStep]?.content}</div>
      </BaseModal>
    );
  }
);

MultiStepModal.displayName = "MultiStepModal";
export default MultiStepModal;