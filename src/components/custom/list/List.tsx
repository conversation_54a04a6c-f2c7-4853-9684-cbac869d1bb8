"use client"
import {
    ListTemp,
} from "./ListTemp";
import { PrimaryButton } from "../buttons/buttons";
import { Screen } from "@/app-components/layout/screen";
import { useEffect, useState } from "react";
import Tab2 from '@/components/custom/tabs/Tab2';
import { Lock, User } from 'lucide-react';
import { toast } from '@/components/custom/Toast/MyToast';

type User = {
    id: number;
    name: string;
    email: string;
};

type Transaction = {
    transactionID: number;
    date: string;
    status: boolean;
    amount: string;
}

const transactions: Transaction[] = [
    { transactionID: 739784759, date: '27 Sep 2025', status: true, amount: 'Ksh 20,000.00' },
    { transactionID: 839274859, date: '15 Aug 2024', status: false, amount: 'Ksh 15,500.00' },
    { transactionID: 928374659, date: '03 Jan 2023', status: true, amount: 'Ksh 50,000.00' },
    { transactionID: 102938475, date: '12 Dec 2022', status: false, amount: 'Ksh 10,000.00' },
    { transactionID: 564738291, date: '19 Mar 2023', status: true, amount: 'Ksh 5,000.00' },
    { transactionID: 847362910, date: '07 Jul 2024', status: false, amount: 'Ksh 30,000.00' }
];

const transactionColumns: { header: string; accessor: keyof Transaction }[] = [
    { header: "Transaction ID", accessor: "transactionID" },
    { header: "Date", accessor: "date" },
    { header: "Status", accessor: "status" },
    { header: "Amount", accessor: "amount" },
];

const users: User[] = [
    { id: 1, name: "Alice", email: "<EMAIL>" },
    { id: 2, name: "Bob", email: "<EMAIL>" },
];

const userColumns = [
    { header: "ID", accessor: "id" },
    { header: "Name", accessor: "name" },
    { header: "Email", accessor: "email" },
] as const as { header: string; accessor: keyof User }[];

const ListComponent: React.FC = () => {
    const [query, setQuery] = useState(""); // Search query
    const [results, setResults] = useState<string[]>([]); // Search results


    const mockData = [
        "Nelson Mandela",
        "George Kimemia",
        "Kevin Mageto",
        "Julius Muthui",
        "Ericson Antonnio",
        "Joshua Hakim",

    ]; // Mock data to simulate a search
    const myTab = [
        {
            value: "todo",
            title: "To Do List",
            icon: <User />,
            content:
            <div className="">
                                <ListTemp  data={users} columns={userColumns} />

            </div>

            //   <div className="border-[0.5px] border-collapse rounded-lg">
            //     <table className="border border-collapse bg-white overflow-hidden rounded-lg w-full h-full " >
            //       <tr className="bg-opacity-10  primary text-sm  bg-opacity-20 overflow-hidden bg-orange-200  ">
            //         <th className=" text-left pl-2 py-2 overflow-hidden ">Task Name</th>
            //         <th className="text-left pl-4">Assigned To</th>
            //         <th className="text-left pl-4">Due Date</th>
            //         <th className="text-left pl-4">Status</th>
            //         <th className="text-left pl-4 pr-2">Action</th>
            //       </tr>
            //       <tr className="primary text-sm border overflow-x-hidden">
            //         <td className="pl-2 text-left py-2">Hotel Management Syst...</td>
            //         <td className="text-left pl-4">Mickael Tominae</td>
            //         <td className="text-left pl-4">27 Mar 2024</td>
            //         <td className="text-left pl-4"><p className="px-3 rounded-xl pb-0.5  bg-green-200 text-green-600">active</p></td>
            //         <td className="text-center pl-4"><EllipsisVertical /></td>
            //       </tr>
            //       <tr className="primary text-sm border overflow-x-hidden">
            //         <td className="pl-2 text-left py-2 overflow-x-hidden">Hotel Management Syst...</td>
            //         <td className="text-left pl-4">John Kimotho</td>
            //         <td className="text-left pl-4">27 Mar 2024</td>
            //         <td className="text-left pl-4"><p className="px-3 rounded-xl pb-0.5  bg-green-200 text-green-600">active</p></td>
            //         <td className="text-center pl-4 "><EllipsisVertical /></td>
            //       </tr>
            //     </table>
            //     </div>

        },
        {
            value: "leads",
            title: "Recent Leads",
            icon: <Lock />,
            content: <div>Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                Praesent elementum <br /> finibus arcu vitae scelerisque. Etiam rutrum
                blandit condimentum. <br /> Maecenas condimentum massa vitae quam interdum,
                et lacinia urna tempor</div>
        },

    ];

    useEffect(() => {
        if (query.trim() === "") {
            setResults([]);
            return;
        }

        const filteredResults = mockData.filter((item) =>
            item.toLowerCase().includes(query.toLowerCase())
        );
        setResults(filteredResults);
    }, [query]);

    return (
        <Screen>

            <h1 className="primary font-semibold text-xl  mb-10 mx-auto ">List Templates</h1>
            <div className="w-full md:w-[70%] lg:w-[65] flex-col flex items-center  p-2 h-fit gap-10  rounded-lg mx-auto my-10">
                <h1 className="primary font-medium text-md">
                    List Templates knock yourself out.
                </h1>
                <div>
                    <Tab2 tabs={myTab} />

                </div>
                <div className="w-full">
                    <ListTemp
                        title
                        data={transactions}
                        columns={transactionColumns}
                        heading="Last Transactions"
                        button
                        buttonText="View More"
                        onclick={() => {
                            toast.success('View more Rows', {
                                description: 'Toast success',
                            });
                        }}
                    />
                </div>

                <div className="bg-secondary p-4 rounded-md overflow-x-auto">
                <pre className="text-sm text-orange-400 bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code className="">
                                    {`
    type Transaction = {
    transactionID: number;
    date: string;
    status: boolean;
    amount: string;
}

const transactions: Transaction[] = [
    { transactionID: 739784759, date: '27 Sep 2025', status: true, amount: 'Ksh 20,000.00' },
    { transactionID: 839274859, date: '15 Aug 2024', status: false, amount: 'Ksh 15,500.00' },
    { transactionID: 928374659, date: '03 Jan 2023', status: true, amount: 'Ksh 50,000.00' },
    { transactionID: 102938475, date: '12 Dec 2022', status: false, amount: 'Ksh 10,000.00' },
    { transactionID: 564738291, date: '19 Mar 2023', status: true, amount: 'Ksh 5,000.00' },
    { transactionID: 847362910, date: '07 Jul 2024', status: false, amount: 'Ksh 30,000.00' }
];

const transactionColumns: { header: string; accessor: keyof Transaction }[] = [
    { header: "Transaction ID", accessor: "transactionID" },
    { header: "Date", accessor: "date" },
    { header: "Status", accessor: "status" },
    { header: "Amount", accessor: "amount" },
];
                                    `}
                                </code>
                            </pre>
                </div>





            </div>

        </Screen>
    );
};

export default ListComponent;
