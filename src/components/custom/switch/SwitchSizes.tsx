import * as React from "react"

export const SwitchSizes = ({ checked, onChange, size = "md" }: { checked: boolean; onChange: () => void; size?: "sm" | "md" | "lg" }) => {
  const sizeMap = {
    sm: "w-8 h-4 left-0.5 top-0.5 w-3.5 h-3.5",
    md: "w-11 h-6 left-0.5 top-0.5 w-5 h-5",
    lg: "w-14 h-8 left-1 top-1 w-6 h-6"
  }

  const container = sizeMap[size].split(" ").slice(0, 2).join(" ")
  const thumb = sizeMap[size].split(" ").slice(2).join(" ")

  return (
    <label className="relative inline-flex items-center cursor-pointer">
      <input type="checkbox" className="sr-only peer" checked={checked} onChange={onChange} />
      <div className={`${container} bg-gray-200 rounded-full peer-checked:bg-purple-600 transition-all`}></div>
      <div className={`absolute ${thumb} bg-white rounded-full shadow-md transform peer-checked:translate-x-full transition-transform`}></div>
    </label>
  )
}
