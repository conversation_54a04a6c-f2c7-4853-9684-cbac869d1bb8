import * as React from "react"

export const VariationSwitch = ({ label, checked, onChange }: { label: string; checked: boolean; onChange: () => void }) => (
  <label className="relative inline-flex items-center cursor-pointer">
    <input type="checkbox" className="sr-only peer" checked={checked} onChange={onChange} />
    <div className="w-14 h-7 bg-red-500 rounded-full peer-checked:bg-green-600 transition-all flex items-center justify-center text-white text-xs font-semibold uppercase">
      <span className="peer-checked:hidden">{label}</span>
      <span className="hidden peer-checked:inline">On</span>
    </div>
    <div className="absolute left-0.5 top-0.5 w-6 h-6 bg-white rounded-full shadow-md transform peer-checked:translate-x-7 transition-transform"></div>
  </label>
)

