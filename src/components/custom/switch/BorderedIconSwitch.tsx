import * as React from "react"
import { Check } from "lucide-react"

export const BorderedIconSwitch = ({ checked, onChange }: { checked: boolean; onChange: () => void }) => (
  <label className="relative inline-flex items-center cursor-pointer">
    <input type="checkbox" className="sr-only peer" checked={checked} onChange={onChange} />
    <div className="w-11 h-6 border-2 border-blue-500 rounded-full flex items-center justify-center peer-checked:bg-blue-500 transition-all">
      {checked && <Check className="text-white w-4 h-4" />}
    </div>
    <div className="absolute left-0.5 top-0.5 w-5 h-5 bg-white rounded-full shadow-md transform peer-checked:translate-x-5 transition-transform"></div>
  </label>
)