import React, { ReactNode, useState } from 'react';
import { Tooltip } from 'react-tooltip'
import { useId } from 'react';
import { cn } from '@/lib/utils';
export interface AuroraTooltipProps {
    
    position?: "top" | "top-start" | "top-end" | "right" | "right-start" | "right-end" | "bottom" | "bottom-start" | "bottom-end" | "left" | "left-start" | "left-end";
    children: ReactNode;
    content: ReactNode;
    color?: string;
    uniqueId?: number;
    className?: string


}



const TooltipTemp: React.FC<AuroraTooltipProps> = ({  position = "top", children, color, content , className}) => {
    const [colorState, setColor] = useState<AuroraTooltipProps['color']>(color);
    const uniqueID = useId();
    const safeUniqueID = `tooltip-${uniqueID.replace(/[^a-zA-Z0-9-_]/g, '')}`;
    


    return (
        <div>
            <a id={safeUniqueID}>
                <div>
                    {children}
                </div>
            </a>
            <Tooltip
                className={cn(className)}
                style={{ backgroundColor: colorState }}
                anchorSelect={`#${safeUniqueID}`}
                place={position}
            >
                {content}
            </Tooltip>
        </div>
    );


    

}

export default TooltipTemp;