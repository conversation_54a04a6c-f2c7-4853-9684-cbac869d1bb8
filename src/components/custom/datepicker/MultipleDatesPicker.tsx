import * as React from "react"
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"

interface MultipleDatesPickerProps {
  label?: string
  values?: Date[]
  onChange?: (dates: Date[]) => void
  fromYear?: number
  toYear?: number
}

export function MultipleDatesPicker({
  label = "Pick multiple dates",
  values,
  onChange,
  fromYear = 2020,
  toYear = 2030,
}: MultipleDatesPickerProps) {
  const [selectedDates, setSelectedDates] = React.useState<Date[]>(values || [])

  React.useEffect(() => {
    if (onChange) {
      onChange(selectedDates)
    }
  }, [selectedDates, onChange])

  // Create a comma-separated list to display
  const labelText = selectedDates.length
    ? selectedDates.map((d) => d.toLocaleDateString()).join(", ")
    : "Select one or more dates"

  return (
    <div className="flex flex-col gap-1 w-full max-w-xs">
      {label && <label className="text-sm font-medium">{label}</label>}

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="flex items-center justify-start text-left font-normal">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {labelText}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="multiple"
            selected={selectedDates}
            onSelect={(dates) => {
              // DayPicker in "multiple" mode will return an array of dates
              if (Array.isArray(dates)) {
                setSelectedDates(dates)
              }
            }}
            captionLayout="dropdown"
            fromYear={fromYear}
            toYear={toYear}
            numberOfMonths={1}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
