import React, { useEffect, useState } from 'react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useLocation, useNavigate } from 'react-router-dom';

interface TabItem {
  value: string;
  title: string | React.ReactNode;
  icon?: React.ReactNode;
  content: React.ReactNode;
}

interface CustomTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  className?: string;
  TabsListClassName?: string;
  TabsTriggerClassName?: string;
  TabsContentClassName?: string;
}

const NavTab2 = ({ 
  tabs, 
  defaultValue = tabs[0]?.value, 
  className, 
  TabsListClassName, 
  TabsTriggerClassName, 
  TabsContentClassName 
}: CustomTabsProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(defaultValue);

  // Extract hash from URL and set active tab
  useEffect(() => {
    const hash = location.hash.replace('#', '');
    if (hash && tabs.some(tab => tab.value === hash)) {
      setActiveTab(hash);
    }
  }, [location.hash, tabs]);

  // Update URL hash when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`#${value}`, { replace: true });
  };

  return (
    <Tabs
      value={activeTab}
      onValueChange={handleTabChange}
      className={`w-full ${className || ''}`}
    >
      <TabsList className={`!bg-background !flex !flex-nowrap justify-start h-auto p-0 w-full border-b border-b-blue-500 !rounded-b-none ${TabsListClassName || ''}`}>
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className={`!flex-none !rounded-sm !rounded-b-none cursor-pointer border border-transparent px-2 py-2 sm:px-8 sm:py-4 data-[state=active]:border-t-blue-500 data-[state=active]:border-x-blue-500 data-[state=active]:shadow-none !text-foreground data-[state=active]:translate-y-0.5 min-w-0 ${TabsTriggerClassName || ''}`}
          >
            {tab.icon && <span className="mr-1 flex-shrink-0 [&_svg:not([class*='size-'])]:size-4 hidden sm:block">{tab.icon}</span>}
            <span className="truncate">{typeof tab.title === 'string' ? tab.title : tab.title}</span>
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent
          key={tab.value}
          value={tab.value}
          className={`transform transition-all duration-800 origin-bottom animate-in fade-in slide-in-from-bottom-8 ${TabsContentClassName || ''}`}
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default NavTab2;