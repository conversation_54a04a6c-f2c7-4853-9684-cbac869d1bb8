import React, { useEffect, useState } from 'react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useLocation, useNavigate } from 'react-router-dom';

interface TabItem {
  value: string;
  title: string | React.ReactNode;
  icon?: React.ReactNode;
  content: React.ReactNode;
}

interface CustomTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  className?: string;
  TabsListClassName?: string;
  TabsTriggerClassName?: string;
  TabsContentClassName?: string;
}

const NavTab1 = ({
  tabs,
  defaultValue = tabs[0]?.value,
  className,
  TabsListClassName,
  TabsTriggerClassName,
  TabsContentClassName,
}: CustomTabsProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(defaultValue);

  // Extract hash from URL and set active tab
  useEffect(() => {
    const hash = location.hash.replace('#', '');
    if (hash && tabs.some(tab => tab.value === hash)) {
      setActiveTab(hash);
    }
  }, [location.hash, tabs]);

  // Update URL hash when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`#${value}`, { replace: true });
  };

  return (
    <Tabs
      value={activeTab}
      onValueChange={handleTabChange}
      className={`w-full ${className || ''}`}
    >
      <TabsList className={`!bg-background !flex !flex-nowrap justify-start h-auto p-0 overflow-x-auto ${TabsListClassName || ''}`}>
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className={`!flex-none border-none !rounded-sm cursor-pointer px-2 py-2 md:px-8 md:py-4 data-[state=active]:!bg-accent !text-foreground ${TabsTriggerClassName || ''}`}
          >
            {tab.icon && <span className="mr-1 [&_svg:not([class*='size-'])]:size-4 hidden sm:block">{tab.icon}</span>}
            <span className="truncate">{typeof tab.title === 'string' ? tab.title : tab.title}</span>
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent
          key={tab.value}
          value={tab.value}
          className={`transform transition-all duration-800 origin-bottom animate-in fade-in slide-in-from-bottom-8 ${TabsContentClassName || ''}`}
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default NavTab1;