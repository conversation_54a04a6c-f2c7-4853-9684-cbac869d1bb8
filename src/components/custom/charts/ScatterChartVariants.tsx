import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, 
  Legend, ResponsiveContainer, Cell, Line, ReferenceArea, Label
} from 'recharts';
import React from 'react';

// Sample data
const data01 = [
  { x: 10, y: 30 },
  { x: 30, y: 50 },
  { x: 45, y: 28 },
  { x: 50, y: 40 },
  { x: 70, y: 48 },
  { x: 80, y: 60 },
  { x: 90, y: 62 },
  { x: 100, y: 70 },
];

const data02 = [
  { x: 20, y: 60 },
  { x: 40, y: 25 },
  { x: 55, y: 45 },
  { x: 65, y: 52 },
  { x: 75, y: 35 },
  { x: 85, y: 48 },
  { x: 95, y: 78 },
];

// Data for 3D scatter chart
const data3d = [
  { x: 10, y: 30, z: 200 },
  { x: 30, y: 50, z: 100 },
  { x: 45, y: 28, z: 300 },
  { x: 50, y: 40, z: 250 },
  { x: 70, y: 48, z: 400 },
  { x: 80, y: 60, z: 150 },
  { x: 90, y: 62, z: 350 },
  { x: 100, y: 70, z: 280 },
];

// Data for bubble chart
const bubbleData = [
  { x: 10, y: 30, z: 400 },
  { x: 30, y: 50, z: 200 },
  { x: 45, y: 28, z: 800 },
  { x: 50, y: 40, z: 300 },
  { x: 70, y: 48, z: 600 },
  { x: 80, y: 60, z: 900 },
  { x: 90, y: 62, z: 500 },
  { x: 100, y: 70, z: 700 },
];

// Data for scatter chart with cells
const colors = [
  'hsl(var(--primary))',
  'hsl(var(--accent))',
  'hsl(var(--success))',
  'hsl(var(--warning))',
  'hsl(var(--destructive))',
  'hsl(var(--secondary))',
  'hsl(var(--muted))'
];
const names = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
const cellData = data01.map((entry, index) => ({
  ...entry,
  name: names[index % names.length]
}));

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  strokeDash: "3 3",
  axisLineStyle: { stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 },
  axisTick: { fill: 'hsl(var(--foreground))', fontSize: 12 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Scatter Chart
export const SimpleScatterChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Y" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Scatter 
            name="Series A" 
            data={data01} 
            fill="hsl(var(--primary))" 
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

// Three Dimension Scatter Chart
export const ThreeDimScatterChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Y" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <ZAxis 
            type="number" 
            dataKey="z" 
            name="Z" 
            range={[60, 400]} 
            domain={[0, 500]}
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Legend />
          <Scatter 
            name="3D Values" 
            data={data3d} 
            fill="hsl(var(--primary))" 
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

// Joint Line Scatter Chart
export const JointLineScatterChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Y" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Legend />
          <Scatter 
            name="Series A" 
            data={data01} 
            fill="hsl(var(--primary))" 
            line={{ stroke: 'hsl(var(--primary))', strokeWidth: 1 }}
            shape="circle"
          />
          <Scatter 
            name="Series B" 
            data={data02} 
            fill="hsl(var(--destructive))" 
            line={{ stroke: 'hsl(var(--destructive))', strokeWidth: 1 }}
            shape="circle"
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bubble Chart
export const BubbleChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Y" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <ZAxis 
            type="number" 
            dataKey="z" 
            range={[60, 400]} 
            name="Size" 
            unit="px" 
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Legend />
          <Scatter 
            name="Bubble Size" 
            data={bubbleData} 
            fill="hsl(var(--primary) / 0.5)" 
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

// Scatter Chart With Labels
export const ScatterChartWithLabels = () => {
  const CustomizedLabel = (props) => {
    const { x, y, index } = props;
    
    return (
      <text 
        key={`scatter-label-${index}`}
        x={x} 
        y={y - 15} 
        textAnchor="middle" 
        dominantBaseline="middle"
        fill="hsl(var(--foreground))"
        fontSize={12}
      >
        {data01[index].x}, {data01[index].y}
      </text>
    );
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Y" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Legend />
          <Scatter 
            name="Values" 
            data={data01} 
            fill="hsl(var(--primary))" 
            label={<CustomizedLabel />}
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

// Multiple Y-Axes Scatter Chart
export const MultipleYAxesScatterChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            yAxisId="left"
            type="number" 
            dataKey="y" 
            name="Y1" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <YAxis 
            yAxisId="right"
            orientation="right"
            type="number" 
            dataKey="y" 
            name="Y2" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Legend />
          <Scatter 
            yAxisId="left"
            name="Set A" 
            data={data01} 
            fill="hsl(var(--primary))" 
          />
          <Scatter 
            yAxisId="right"
            name="Set B" 
            data={data02} 
            fill="hsl(var(--destructive))" 
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

// Scatter Chart With Cells
export const ScatterChartWithCells = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="X" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 110]}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Y" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <Tooltip 
            cursor={{ strokeDasharray: '3 3' }} 
            contentStyle={chartStyles.tooltipStyle}
          />
          <Legend />
          <Scatter
            name="Categories" 
            data={cellData}
          >
            {cellData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={colors[index % colors.length]} 
                name={entry.name}
              />
            ))}
          </Scatter>
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
}; 