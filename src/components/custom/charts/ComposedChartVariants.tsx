import {
  Composed<PERSON><PERSON>, Line, Bar, Area, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, 
  <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ReferenceLine
} from 'recharts';
import React from 'react';

// Sample data
const data = [
  { name: 'Jan', value: 400, pv: 800, uv: 1000, amt: 300 },
  { name: 'Feb', value: 300, pv: 900, uv: 1300, amt: 400 },
  { name: 'Mar', value: 600, pv: 300, uv: 800, amt: 700 },
  { name: 'Apr', value: 800, pv: 600, uv: 700, amt: 1000 },
  { name: 'May', value: 500, pv: 700, uv: 900, amt: 800 },
  { name: 'Jun', value: 350, pv: 500, uv: 600, amt: 500 },
];

// Data for scatter plot with line of best fit
const scatterData = [
  { x: 10, y: 30 },
  { x: 30, y: 50 },
  { x: 45, y: 28 },
  { x: 50, y: 40 },
  { x: 70, y: 48 },
  { x: 80, y: 60 },
  { x: 90, y: 62 },
  { x: 100, y: 70 },
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  strokeDash: "3 3",
  axisLineStyle: { stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 },
  axisTick: { fill: 'hsl(var(--foreground))', fontSize: 12 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Line Bar Area Composed Chart
export const LineBarAreaComposedChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area 
            type="monotone" 
            dataKey="amt" 
            fill="hsl(var(--primary) / 0.3)" 
            stroke="hsl(var(--primary))" 
            strokeWidth={1}
          />
          <Bar 
            dataKey="pv" 
            fill="hsl(var(--accent))" 
            radius={[4, 4, 0, 0]}
          />
          <Line 
            type="monotone" 
            dataKey="uv" 
            stroke="hsl(var(--destructive))" 
            strokeWidth={2}
            dot={{ fill: 'hsl(var(--destructive))', r: 4, strokeWidth: 1, stroke: 'white' }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

// Same Data Composed Chart
export const SameDataComposedChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area 
            type="monotone" 
            dataKey="value" 
            fill="hsl(var(--primary) / 0.3)" 
            stroke="hsl(var(--primary))" 
            strokeWidth={1}
            name="Value (Area)"
          />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--accent))" 
            radius={[4, 4, 0, 0]}
            name="Value (Bar)"
            barSize={20}
          />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="hsl(var(--destructive))" 
            strokeWidth={2}
            dot={{ fill: 'hsl(var(--destructive))', r: 4, strokeWidth: 1, stroke: 'white' }}
            name="Value (Line)"
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

// Vertical Composed Chart
export const VerticalComposedChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          layout="vertical"
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            dataKey="name"
            type="category"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area 
            dataKey="amt" 
            fill="hsl(var(--primary) / 0.3)" 
            stroke="hsl(var(--primary))" 
            strokeWidth={1}
          />
          <Bar 
            dataKey="pv" 
            fill="hsl(var(--accent))" 
            barSize={20}
            radius={[0, 4, 4, 0]}
          />
          <Line 
            dataKey="uv" 
            stroke="hsl(var(--destructive))" 
            strokeWidth={2}
            dot={{ fill: 'hsl(var(--destructive))', r: 4, strokeWidth: 1, stroke: 'white' }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

// Composed Chart With Axis Labels
export const ComposedChartWithAxisLabels = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            label={{ value: 'Month', position: 'insideBottomRight', offset: 0 }}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            label={{ value: 'Value', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))"
            radius={[4, 4, 0, 0]}
            barSize={20}
          />
          <Line 
            type="monotone" 
            dataKey="pv" 
            stroke="hsl(var(--destructive))" 
            strokeWidth={2}
            dot={{ fill: 'hsl(var(--destructive))', r: 4, strokeWidth: 1, stroke: 'white' }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

// Scatter and Line of Best Fit
export const ScatterAndLineOfBestFit = () => {
  // Simple linear regression calculation
  const calculateLineOfBestFit = (data) => {
    const n = data.length;
    let sumX = 0;
    let sumY = 0;
    let sumXY = 0;
    let sumXX = 0;
    
    for (let i = 0; i < n; i++) {
      sumX += data[i].x;
      sumY += data[i].y;
      sumXY += data[i].x * data[i].y;
      sumXX += data[i].x * data[i].x;
    }
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    return { slope, intercept };
  };

  const { slope, intercept } = calculateLineOfBestFit(scatterData);
  
  // Generate trend line data points
  const trendData = [
    { x: 0, y: intercept },
    { x: 100, y: slope * 100 + intercept }
  ];

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number"
            dataKey="x"
            name="X"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <YAxis 
            type="number"
            dataKey="y"
            name="Y"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 100]}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Scatter 
            name="Data Points" 
            data={scatterData} 
            fill="hsl(var(--primary))" 
          />
          <Line 
            name="Line of Best Fit" 
            data={trendData} 
            dataKey="y" 
            stroke="hsl(var(--destructive))" 
            strokeWidth={2}
            dot={false}
            activeDot={false}
            legendType="line"
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

// Banded Chart
export const BandedChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          
          {/* Lower band area */}
          <Area 
            type="monotone" 
            dataKey="pv" 
            fill="hsl(var(--primary) / 0.1)" 
            stroke="none" 
            activeDot={false}
            name="Min Bound"
          />
          
          {/* Upper band area */}
          <Area 
            type="monotone" 
            dataKey="uv" 
            fill="hsl(var(--primary) / 0.2)" 
            stroke="none" 
            activeDot={false}
            name="Max Bound"
          />
          
          {/* Actual line */}
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="hsl(var(--primary))" 
            strokeWidth={2}
            dot={{ fill: 'hsl(var(--primary))', r: 4, strokeWidth: 1, stroke: 'white' }}
            name="Actual Value"
          />
          
          {/* Target reference line */}
          <ReferenceLine y={500} stroke="hsl(var(--destructive))" strokeDasharray="3 3" label="Target" />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
}; 