import {
  Funnel<PERSON><PERSON>, Funnel, LabelList, Tooltip, ResponsiveContainer, Cell
} from 'recharts';
import React from 'react';

// Sample data
const data = [
  { name: 'Leads', value: 5000 },
  { name: 'Qualified', value: 3200 },
  { name: 'Proposals', value: 2100 },
  { name: 'Negotiations', value: 1100 },
  { name: 'Closed', value: 700 },
];

// Custom colors
const COLORS = [
  'hsl(var(--primary))',
  'hsl(var(--accent))',
  'hsl(var(--success))',
  'hsl(var(--warning))',
  'hsl(var(--destructive))',
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 20, right: 30, left: 30, bottom: 0 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Funnel Chart
export const SimpleFunnelChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <FunnelChart width={730} height={250}>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Funnel
            dataKey="value"
            data={data}
            isAnimationActive
            nameKey="name"
          >
            <LabelList 
              position="right" 
              fill="hsl(var(--foreground))" 
              stroke="none" 
              dataKey="name" 
            />
            <LabelList 
              position="right" 
              fill="hsl(var(--foreground))" 
              stroke="none" 
              dataKey="value" 
              formatter={(value) => `${value}`}
              offset={60}
            />
          </Funnel>
        </FunnelChart>
      </ResponsiveContainer>
    </div>
  );
};

// Funnel Chart With Custom Colors
export const FunnelChartWithCustomColors = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <FunnelChart width={730} height={250}>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Funnel
            dataKey="value"
            data={data}
            isAnimationActive
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
            <LabelList 
              position="center" 
              fill="hsl(var(--background))" 
              stroke="none" 
              dataKey="name" 
              fontSize={14}
              fontWeight="bold"
            />
            <LabelList 
              position="center" 
              fill="hsl(var(--background))" 
              stroke="none" 
              dataKey="value" 
              formatter={(value) => `${value}`}
              offset={30}
              fontSize={12}
            />
          </Funnel>
        </FunnelChart>
      </ResponsiveContainer>
    </div>
  );
};

// Funnel Chart With Gradient
export const FunnelChartWithGradient = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <FunnelChart width={730} height={250}>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <defs>
            <linearGradient id="funnelGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="hsl(var(--primary))" stopOpacity={0.95} />
              <stop offset="100%" stopColor="hsl(var(--destructive))" stopOpacity={0.85} />
            </linearGradient>
          </defs>
          <Funnel
            dataKey="value"
            data={data}
            isAnimationActive
            fill="url(#funnelGradient)"
          >
            <LabelList 
              position="right" 
              fill="hsl(var(--foreground))" 
              stroke="none" 
              dataKey="name" 
              formatter={(value) => `${value}`}
            />
            <LabelList 
              position="right" 
              fill="hsl(var(--foreground))" 
              stroke="none" 
              dataKey="value" 
              formatter={(value) => `${value}`}
              offset={60}
            />
          </Funnel>
        </FunnelChart>
      </ResponsiveContainer>
    </div>
  );
};

// Conversion Rate Funnel Chart
export const ConversionRateFunnelChart = () => {
  // Calculate conversion rates
  const getConversionData = () => {
    const result = [...data];
    for (let i = 0; i < result.length - 1; i++) {
      const convRate = ((result[i+1].value / result[i].value) * 100).toFixed(1);
      result[i].conversionRate = `${convRate}%`;
    }
    return result;
  };

  const conversionData = getConversionData();

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <FunnelChart width={730} height={250}>
          <Tooltip 
            contentStyle={chartStyles.tooltipStyle} 
            formatter={(value, name, props) => {
              if (name === 'value') {
                return [value, 'Count'];
              }
              return [props.payload.conversionRate || 'N/A', 'Conversion Rate'];
            }}
          />
          <Funnel
            dataKey="value"
            data={conversionData}
            isAnimationActive
          >
            {conversionData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
            <LabelList 
              position="right" 
              fill="hsl(var(--foreground))" 
              stroke="none" 
              dataKey="name" 
            />
            <LabelList 
              position="right" 
              fill="hsl(var(--foreground))" 
              stroke="none" 
              dataKey="conversionRate" 
              offset={60}
            />
          </Funnel>
        </FunnelChart>
      </ResponsiveContainer>
    </div>
  );
};

// Vertical Funnel Chart
export const VerticalFunnelChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <FunnelChart width={730} height={250} layout="vertical">
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Funnel
            dataKey="value"
            data={data}
            isAnimationActive
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
            <LabelList 
              position="center" 
              fill="hsl(var(--background))" 
              stroke="none" 
              dataKey="name" 
            />
          </Funnel>
        </FunnelChart>
      </ResponsiveContainer>
    </div>
  );
}; 