import {
  Ra<PERSON><PERSON>ar<PERSON>hart, <PERSON>dialBar, Legend, ResponsiveContainer, Tooltip,
  PolarAngleAxis, PolarGrid, PolarRadiusAxis
} from 'recharts';
import React from 'react';

// Sample data
const data = [
  { name: '18-24', uv: 31.47, fill: 'hsl(var(--primary))' },
  { name: '25-29', uv: 26.69, fill: 'hsl(var(--accent))' },
  { name: '30-34', uv: 15.69, fill: 'hsl(var(--destructive))' },
  { name: '35-39', uv: 8.22, fill: 'hsl(var(--success))' },
  { name: '40-49', uv: 8.63, fill: 'hsl(var(--warning))' },
  { name: '50+', uv: 2.63, fill: 'hsl(var(--info))' },
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 350 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Radial Bar Chart
export const SimpleRadialBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RadialBarChart 
          cx="50%" 
          cy="50%" 
          innerRadius="20%" 
          outerRadius="80%" 
          barSize={10} 
          data={data}
          startAngle={180}
          endAngle={0}
        >
          <RadialBar
            minAngle={15}
            label={{ position: 'insideStart', fill: 'hsl(var(--foreground))', fontSize: 12 }}
            background
            clockWise
            dataKey="uv"
          />
          <Legend 
            iconSize={10} 
            layout="vertical" 
            verticalAlign="middle" 
            align="right"
            wrapperStyle={{ fontSize: 12 }}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
        </RadialBarChart>
      </ResponsiveContainer>
    </div>
  );
}; 