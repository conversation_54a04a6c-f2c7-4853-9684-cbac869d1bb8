import {
  LineChart as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

type Props = {
  data: {
    name: string;
    [key: string]: any;
  }[];
  dataKey: string;
  color?: string;
  showGrid?: boolean;
  showTooltip?: boolean;
  showLegend?: boolean;
};

const LineChart = ({
  data,
  dataKey,
  color = "primary",
  showGrid = true,
  showTooltip = true,
  showLegend = true
}: Props) => {
  return (
    <div className="w-full h-64">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-20" />}
          <XAxis 
            dataKey="name" 
            axisLine={{ stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 }}
            tick={{ fill: 'hsl(var(--foreground))', fontSize: 12 }}
          />
          <YAxis 
            axisLine={{ stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 }}
            tick={{ fill: 'hsl(var(--foreground))', fontSize: 12 }}
          />
          {showTooltip && (
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'hsl(var(--card))', 
                border: '1px solid hsl(var(--border))',
                borderRadius: '0.5rem',
                color: 'hsl(var(--card-foreground))'
              }} 
            />
          )}
          {showLegend && <Legend />}
          <Line
            type="monotone"
            dataKey={dataKey}
            stroke={`hsl(var(--${color}))`}
            strokeWidth={2}
            activeDot={{ r: 8, fill: `hsl(var(--${color}))`, strokeWidth: 2, stroke: 'white' }}
            dot={{ r: 4, fill: `hsl(var(--${color}))`, strokeWidth: 2, stroke: 'white' }}
            animationDuration={1500}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default LineChart; 