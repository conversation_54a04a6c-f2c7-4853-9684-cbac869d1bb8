import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Cell, Too<PERSON><PERSON>, Legend, ResponsiveContainer,
  Sector, Label, RadialBar
} from 'recharts';
import { <PERSON><PERSON><PERSON> } from 'lucide-react';
import React from 'react';

// Sample data
const data = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
  { name: 'Group E', value: 100 },
];

const twoLevelData = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
];

const innerData = [
  { name: 'Inner A', value: 700 },
  { name: 'Inner B', value: 500 },
];

const COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#EF4444', // Red
  '#8B5CF6', // Violet
  '#06B6D4', // <PERSON><PERSON>
  '#84CC16', // Lime
  '#F97316', // Orange
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 350 },
  margin: { top: 20, right: 20, left: 20, bottom: 20 },
  tooltipStyle: { 
    backgroundColor: 'rgba(255, 255, 255, 0.95)', 
    border: '1px solid #e5e7eb',
    borderRadius: '0.75rem',
    color: '#374151',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    padding: '12px 16px',
    fontSize: '14px',
    fontWeight: '500'
  },
  legendStyle: {
    fontSize: '12px',
    fontWeight: '500',
    color: '#6b7280'
  }
};

// Simple Pie Chart
export const SimplePieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={true}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            label={(entry) => entry.name}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Two Level Pie Chart
export const TwoLevelPieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={twoLevelData}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            label={(entry) => entry.name}
          >
            {twoLevelData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Pie
            data={innerData}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={60}
            fill="hsl(var(--accent))"
            dataKey="value"
            nameKey="name"
          >
            {innerData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Straight Angle Pie Chart
export const StraightAnglePieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            startAngle={180}
            endAngle={0}
            innerRadius={60}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            label={(entry) => entry.name}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Two Simple Pie Chart
export const TwoSimplePieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={twoLevelData}
            cx="30%"
            cy="50%"
            outerRadius={60}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
          >
            {twoLevelData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Pie
            data={innerData}
            cx="70%"
            cy="50%"
            outerRadius={60}
            fill="hsl(var(--accent))"
            dataKey="value"
            nameKey="name"
          >
            {innerData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Custom Active Shape Pie Chart
interface CustomActiveShapePieChartProps {
  data?: Array<{ name: string; value: number }>;
}

export const CustomActiveShapePieChart = ({ data: propData }: CustomActiveShapePieChartProps) => {
  // Use provided data or fallback to sample data
  const chartData = propData && propData.length > 0 ? propData : data;
  const [activeIndex, setActiveIndex] = React.useState(-1);

  const onPieEnter = (_, index) => {
    setActiveIndex(index);
  };

  const onPieLeave = () => {
    setActiveIndex(-1);
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const total = chartData.reduce((sum, item) => sum + item.value, 0);
      const percentage = ((data.value / total) * 100).toFixed(1);
      
      return (
        <div style={chartStyles.tooltipStyle}>
          <div className="flex items-center gap-2 mb-1">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: data.payload.fill }}
            />
            <span className="font-semibold text-gray-900">{data.name}</span>
          </div>
          <div className="text-sm text-gray-600">
            <div>Visits: <span className="font-medium">{data.value}</span></div>
            <div>Percentage: <span className="font-medium">{percentage}%</span></div>
          </div>
        </div>
      );
    }
    return null;
  };

  // Custom legend component
  const CustomLegend = ({ payload }) => {
    if (!payload || payload.length === 0) return null;
    
    return (
      <div className="flex flex-wrap justify-center gap-3 mt-4">
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span style={chartStyles.legendStyle}>
              {entry.value}
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Show message when no data is available
  if (!chartData || chartData.length === 0) {
    return (
      <div style={chartStyles.containerStyle}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <PieChart className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm font-medium mb-1">No site visit data available</p>
            <p className="text-xs text-gray-400">Site visits with valid project names will appear here</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart margin={chartStyles.margin}>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={70}
            outerRadius={110}
            paddingAngle={2}
            dataKey="value"
            nameKey="name"
            onMouseEnter={onPieEnter}
            onMouseLeave={onPieLeave}
            stroke="#ffffff"
            strokeWidth={2}
          >
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={COLORS[index % COLORS.length]}
                stroke="#ffffff"
                strokeWidth={2}
                style={{
                  filter: activeIndex === index ? 'brightness(1.1)' : 'none',
                  transition: 'all 0.2s ease-in-out'
                }}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart With Customized Label
export const PieChartWithCustomizedLabel = () => {
  const renderCustomizedLabel = ({
    cx, cy, midAngle, innerRadius, outerRadius, percent, index,
  }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
    const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);
  
    return (
      <text 
        key={`label-${index}`}
        x={x} 
        y={y} 
        fill="hsl(var(--background))" 
        textAnchor="middle" 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart With Padding Angle
export const PieChartWithPaddingAngle = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="hsl(var(--primary))"
            paddingAngle={5}
            dataKey="value"
            nameKey="name"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart With Needle
export const PieChartWithNeedle = () => {
  // Target value
  const targetValue = 65; // Percentage
  const total = 100;
  
  // Calculate angle for needle
  const needleAngle = 180 - (targetValue / total) * 180;

  // Modified data for the gauge
  const gaugeData = [
    { name: 'Low', value: 33, color: 'hsl(var(--destructive))' },
    { name: 'Moderate', value: 33, color: 'hsl(var(--warning))' },
    { name: 'High', value: 34, color: 'hsl(var(--success))' },
  ];

  const needleRadius = 80;
  const cx = '50%';
  const cy = '50%';

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={gaugeData}
            cx={cx}
            cy={cy}
            startAngle={180}
            endAngle={0}
            innerRadius={40}
            outerRadius={80}
            dataKey="value"
            nameKey="name"
          >
            {gaugeData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
            <Label
              value={`${targetValue}%`}
              position="center"
              fill="hsl(var(--foreground))"
              style={{ fontSize: '24px', fontWeight: 'bold' }}
            />
          </Pie>

          {/* Needle */}
          <svg>
            <defs>
              <marker
                id="arrow"
                viewBox="0 0 10 10"
                refX="5"
                refY="5"
                markerWidth="6"
                markerHeight="6"
                orient="auto-start-reverse"
              >
                <path key="arrow-path" d="M 0 0 L 10 5 L 0 10 z" fill="hsl(var(--foreground))" />
              </marker>
            </defs>
            <line
              key="needle-line"
              x1="50%"
              y1="50%"
              x2={`${50 + 45 * Math.cos((needleAngle * Math.PI) / 180)}%`}
              y2={`${50 - 45 * Math.sin((needleAngle * Math.PI) / 180)}%`}
              stroke="hsl(var(--foreground))"
              strokeWidth={2}
              markerEnd="url(#arrow)"
            />
            <circle key="needle-center" cx="50%" cy="50%" r="5" fill="hsl(var(--foreground))" />
          </svg>

          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
}; 