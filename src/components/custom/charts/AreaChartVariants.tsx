import { 
  AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  Brush, CartesianAxis, ReferenceLine
} from 'recharts';

// Sample data
const data = [
  { name: '<PERSON>', value: 400 },
  { name: 'Feb', value: 300 },
  { name: '<PERSON>', value: 600 },
  { name: 'Apr', value: 800 },
  { name: 'May', value: 500 },
  { name: 'Jun', value: 350 },
];

const multiData = [
  { name: 'Jan', uv: 4000, pv: 2400, amt: 2400 },
  { name: 'Feb', uv: 3000, pv: 1398, amt: 2210 },
  { name: '<PERSON>', uv: 2000, pv: 9800, amt: 2290 },
  { name: 'Apr', uv: 2780, pv: 3908, amt: 2000 },
  { name: 'May', uv: 1890, pv: 4800, amt: 2181 },
  { name: 'Jun', uv: 2390, pv: 3800, amt: 2500 },
];

const dataWithNulls = [
  { name: '<PERSON>', value: 400 },
  { name: 'Feb', value: null },
  { name: '<PERSON>', value: 600 },
  { name: 'Apr', value: null },
  { name: 'May', value: 500 },
  { name: 'Jun', value: 350 },
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  strokeDash: "3 3",
  axisLineStyle: { stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 },
  axisTick: { fill: 'hsl(var(--foreground))', fontSize: 12 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Area Chart
export const SimpleAreaChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.3)"
            strokeWidth={2}
            activeDot={{ r: 8, fill: "hsl(var(--primary))", strokeWidth: 2, stroke: 'white' }}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Stacked Area Chart
export const StackedAreaChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area
            type="monotone"
            dataKey="uv"
            stackId="1"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.4)"
            strokeWidth={1}
            animationDuration={1500}
          />
          <Area
            type="monotone"
            dataKey="pv"
            stackId="1"
            stroke="hsl(var(--accent))"
            fill="hsl(var(--accent) / 0.4)"
            strokeWidth={1}
            animationDuration={1500}
          />
          <Area
            type="monotone"
            dataKey="amt"
            stackId="1"
            stroke="hsl(var(--destructive))"
            fill="hsl(var(--destructive) / 0.4)"
            strokeWidth={1}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Tiny Area Chart
export const TinyAreaChart = () => {
  return (
    <div style={{ ...chartStyles.containerStyle, height: 150 }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
        >
          <Area
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.3)"
            strokeWidth={2}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Percent Area Chart
export const PercentAreaChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={multiData}
          margin={chartStyles.margin}
          stackOffset="expand" // Set to expand for percentage mode
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
          />
          <Tooltip 
            contentStyle={chartStyles.tooltipStyle} 
            formatter={(value) => `${(value * 100).toFixed(2)}%`}
          />
          <Legend />
          <Area
            type="monotone"
            dataKey="uv"
            stackId="1"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.4)"
            strokeWidth={1}
            animationDuration={1500}
          />
          <Area
            type="monotone"
            dataKey="pv"
            stackId="1"
            stroke="hsl(var(--accent))"
            fill="hsl(var(--accent) / 0.4)"
            strokeWidth={1}
            animationDuration={1500}
          />
          <Area
            type="monotone"
            dataKey="amt"
            stackId="1"
            stroke="hsl(var(--destructive))"
            fill="hsl(var(--destructive) / 0.4)"
            strokeWidth={1}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Cardinal Area Chart
export const CardinalAreaChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area
            type="cardinal" // Using cardinal interpolation
            dataKey="value"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.3)"
            strokeWidth={2}
            activeDot={{ r: 8, fill: "hsl(var(--primary))", strokeWidth: 2, stroke: 'white' }}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Area Chart Connect Nulls
export const AreaChartConnectNulls = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={dataWithNulls}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Area
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.3)"
            strokeWidth={2}
            connectNulls={true}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Synchronized Area Chart
export const SynchronizedAreaChart = () => {
  return (
    <div>
      <div style={chartStyles.containerStyle}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={chartStyles.margin}
            syncId="anyId"
          >
            <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
            <XAxis 
              dataKey="name" 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <YAxis 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <Tooltip contentStyle={chartStyles.tooltipStyle} />
            <Area
              type="monotone"
              dataKey="value"
              stroke="hsl(var(--primary))"
              fill="hsl(var(--primary) / 0.3)"
              strokeWidth={2}
              animationDuration={1500}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
      <div style={{ ...chartStyles.containerStyle, marginTop: 10 }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={multiData}
            margin={chartStyles.margin}
            syncId="anyId"
          >
            <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
            <XAxis 
              dataKey="name" 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <YAxis 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <Tooltip contentStyle={chartStyles.tooltipStyle} />
            <Area
              type="monotone"
              dataKey="pv"
              stroke="hsl(var(--destructive))"
              fill="hsl(var(--destructive) / 0.3)"
              strokeWidth={2}
              animationDuration={1500}
            />
            <Brush 
              dataKey="name" 
              height={30} 
              stroke="hsl(var(--muted-foreground))"
              fill="hsl(var(--secondary))"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Area Chart Fill By Value
export const AreaChartFillByValue = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <ReferenceLine y={450} strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <defs>
            <linearGradient id="splitColor" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="hsl(var(--destructive) / 0.4)" stopOpacity={1} />
              <stop offset="50%" stopColor="hsl(var(--primary) / 0.4)" stopOpacity={1} />
            </linearGradient>
          </defs>
          <Area
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            fill="url(#splitColor)"
            strokeWidth={2}
            activeDot={{ r: 8, fill: "hsl(var(--primary))", strokeWidth: 2, stroke: 'white' }}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}; 