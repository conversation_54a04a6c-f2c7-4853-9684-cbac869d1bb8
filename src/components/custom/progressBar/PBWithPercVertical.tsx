import { Progress } from "@/components/ui/progress";

type Props = {
  value?: number;
  color?: string;
  orientation?: "horizontal" | "vertical";
};

const PBWithPercVertical = ({ value, color, orientation }: Props) => {
  return (
    <div className="py-3 relative">
      <Progress value={value} color={color} orientation={orientation} />
      <div className="absolute top-0 -left-2 flex items-center justify-center w-full h-full text-xs font-bold text-black">
        {value}%
      </div>
    </div>
  );
};

export default PBWithPercVertical;
