import React, { useState, useRef, useEffect } from 'react';
import { DropdownButtonProps } from './dropdown';


interface IconDropdownProps extends Omit<DropdownButtonProps, 'label' | 'icon'> {
  icon: React.ReactNode;
  ariaLabel: string;
}

const IconDropdown = ({ 
  icon, 
  ariaLabel, 
  items, 
  variant = 'default',
  disabled = false, 
  align = 'left',
  className = '',
  dropdownClassName = ''
}: IconDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    if (!disabled) setIsOpen(!isOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const variantStyles = {
    default: 'bg-[hsl(var(--background))] text-[hsl(var(--foreground))] border border-[hsl(var(--border))] hover:bg-[hsl(var(--muted))]',
    primary: 'bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] hover:bg-[hsl(var(--primary))] hover:opacity-90',
    secondary: 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--secondary))] hover:opacity-90',
    destructive: 'bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))] hover:bg-[hsl(var(--destructive))] hover:opacity-90',
    outline: 'border border-[hsl(var(--border))] text-[hsl(var(--foreground))] bg-transparent hover:bg-[hsl(var(--muted))]',
    ghost: 'bg-transparent text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]'
  };

  const buttonStyles = `
    inline-flex items-center justify-center p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2
    ${variantStyles[variant]}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${className}
  `;

  const dropdownStyles = `
    absolute z-10 min-w-[12rem] py-1 mt-1 bg-[hsl(var(--background))] rounded-md shadow-lg border border-[hsl(var(--border))]
    ${align === 'right' ? 'right-0' : 'left-0'}
    ${dropdownClassName}
  `;

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        aria-label={ariaLabel}
        className={buttonStyles}
        onClick={toggleDropdown}
        disabled={disabled}
        type="button"
      >
        {icon}
      </button>
      
      {isOpen && (
        <div className={dropdownStyles}>
          <ul className="py-1">
            {items.map((item, index) => (
              <li key={index}>
                <button
                  className={`w-full text-left block px-4 py-2 text-sm transition-all duration-200 ${
                    item.disabled
                      ? 'text-[hsl(var(--muted-foreground))] bg-[hsl(var(--muted))] cursor-not-allowed'
                      : 'text-[hsl(var(--foreground))] hover:bg-[hsl(var(--primary))] hover:text-[hsl(var(--primary-foreground))]'
                  }`}
                  onClick={() => {
                    if (!item.disabled) {
                      item.onClick();
                      setIsOpen(false);
                    }
                  }}
                  disabled={item.disabled}
                >
                  <div className="flex items-center">
                    {item.icon && <span className="mr-2">{item.icon}</span>}
                    {item.label}
                  </div>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default IconDropdown;