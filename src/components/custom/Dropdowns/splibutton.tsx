import React, { useState, useRef, useEffect } from "react";
import { DropdownButtonProps } from "./dropdown";

interface SplitButtonDropdownProps extends DropdownButtonProps {
  onButtonClick: () => void;
}

const SplitButtonDropdown = ({
  label,
  items,
  variant = "default",
  size = "md",
  className = "",
  onButtonClick,
  disabled = false,
  align = "left",
}: SplitButtonDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    if (!disabled) setIsOpen(!isOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const variantStyles = {
    default: "bg-[hsl(var(--background))] text-[hsl(var(--foreground))] border border-[hsl(var(--border))] hover:bg-[hsl(var(--muted))]",
    primary: "bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] hover:bg-[hsl(var(--primary),0.9)]",
    secondary: "bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--secondary),0.9)]",
    destructive: "bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))] hover:bg-[hsl(var(--destructive),0.9)]",
    outline: "border border-[hsl(var(--border))] text-[hsl(var(--foreground))] bg-transparent hover:bg-[hsl(var(--muted))]",
    ghost: "bg-transparent text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]",
  };

  const sizeMap = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  const paddingMap = {
    sm: "px-2.5 py-1.5",
    md: "px-4 py-2",
    lg: "px-5 py-2.5",
  };

  return (
    <div className={`relative inline-flex shadow-sm rounded-md ${className}`} ref={dropdownRef}>
      {/* Main Button */}
      <button
        type="button"
        className={`
          relative inline-flex items-center justify-center rounded-l-md ${sizeMap[size]} font-medium ${paddingMap[size]}
          focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2
          ${variantStyles[variant]}
          ${disabled ? "opacity-50 cursor-not-allowed" : ""}
        `}
        onClick={onButtonClick}
        disabled={disabled}
      >
        {label}
      </button>

      {/* Dropdown Toggle Button */}
      <button
        type="button"
        className={`
          relative inline-flex items-center justify-center rounded-r-md ${sizeMap[size]} font-medium px-2 ${size === "sm" ? "py-1.5" : "py-2"}
          focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2
          ${variantStyles[variant]}
          ${disabled ? "opacity-50 cursor-not-allowed" : ""}
          border-l border-[hsl(var(--border))]
        `}
        onClick={toggleDropdown}
        disabled={disabled}
      >
        <svg
          className="h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={`absolute z-10 ${
            align === "right" ? "right-0" : "left-0"
          } mt-2 w-48 shadow-lg bg-[hsl(var(--background))] border border-[hsl(var(--border))] rounded-md`}
        >
          <ul className="py-1">
            {items.map((item, index) => (
              <button
                key={index}
                className={`
                  block w-full text-left px-4 py-2 text-sm transition-all duration-200
                  ${
                    item.disabled
                      ? "text-[hsl(var(--muted-foreground))] cursor-not-allowed"
                      : "text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]"
                  }
                `}
                onClick={() => {
                  if (!item.disabled) {
                    item.onClick();
                    setIsOpen(false);
                  }
                }}
                disabled={item.disabled}
              >
                <div className="flex items-center">
                  {item.icon && <span className="mr-2">{item.icon}</span>}
                  {item.label}
                </div>
              </button>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default SplitButtonDropdown;