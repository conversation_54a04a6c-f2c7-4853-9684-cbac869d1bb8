import React from 'react';
import DropdownButton, { DropdownButtonProps } from './dropdown';

export const DefaultDropdown = (props: DropdownButtonProps) => (
  <DropdownButton {...props} variant="default" />
);

export const PrimaryDropdown = (props: DropdownButtonProps) => (
  <DropdownButton {...props} variant="primary" />
);

export const SecondaryDropdown = (props: DropdownButtonProps) => (
  <DropdownButton {...props} variant="secondary" />
);

export const DestructiveDropdown = (props: DropdownButtonProps) => (
  <DropdownButton {...props} variant="destructive" />
);

export const OutlineDropdown = (props: DropdownButtonProps) => (
  <DropdownButton {...props} variant="outline" />
);

export const GhostDropdown = (props: DropdownButtonProps) => (
  <DropdownButton {...props} variant="ghost" />
);