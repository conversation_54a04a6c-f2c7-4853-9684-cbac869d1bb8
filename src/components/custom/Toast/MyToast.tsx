import { Toaster as SonnerToaster, toast as sonnerToast } from 'sonner';
import { 
  CheckCircle2, 
  XCircle, 
  Info, 
  AlertTriangle,
  Loader2 
} from 'lucide-react';
import type { ToasterProps } from 'sonner';

type ToastType = 'success' | 'error' | 'info' | 'warning' | 'loading';
type ToastPosition = ToasterProps['position'];
type ToastDuration = number;

interface ToastOptions {
  position?: ToastPosition;
  duration?: ToastDuration;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  closeButton?: boolean;
}

const iconMap = {
  success: <CheckCircle2 className="h-4 w-4" />,
  error: <XCircle className="h-4 w-4" />,
  info: <Info className="h-4 w-4" />,
  warning: <AlertTriangle className="h-4 w-4" />,
  loading: <Loader2 className="h-4 w-4 animate-spin" />,
};

export const toast = (
  type: ToastType,
  title: string,
  options?: ToastOptions
) => {
  const { position = 'top-center', duration = 4000, ...rest } = options || {};

  return sonnerToast[type](title, {
    position,
    duration,
    icon: iconMap[type],
    ...rest,
  });
};

export const ToastProvider = ({
  position = 'top-center',
  theme = 'dark',
  ...props
}: ToasterProps) => (
  <SonnerToaster
    position={position}
    theme={theme}
    richColors
    closeButton
    visibleToasts={3}
    toastOptions={{
      classNames: {
        toast: 'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
        description: 'group-[.toast]:text-muted-foreground',
        actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
        closeButton: 'group-[.toast]:text-foreground',
      },
    }}
    {...props}
  />
);

// Convenience methods
toast.success = (title: string, options?: ToastOptions) => 
  toast('success', title, options);
toast.error = (title: string, options?: ToastOptions) => 
  toast('error', title, options);
toast.info = (title: string, options?: ToastOptions) => 
  toast('info', title, options);
toast.warning = (title: string, options?: ToastOptions) => 
  toast('warning', title, options);
toast.loading = (title: string, options?: ToastOptions) => 
  toast('loading', title, options);
toast.dismiss = sonnerToast.dismiss;
toast.promise = sonnerToast.promise;