import { useState } from "react";
import { X } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { useEditMpesaTransactionsMutation } from "@/redux/slices/projects";
import { toast } from "sonner";
import SpinnerTemp from "../custom/spinners/SpinnerTemp";

// Define the MpesaTransaction interface
interface MpesaTransaction {
  trans_id: string;
  amount: number;
  date: string;
  account_ref: string;
  name1: string;
  number: string;
  status: "OPEN" | "CLOSED";
  confirmation: boolean;
  notif: string;
  utilityBalance: string;
}

interface MpesaTransactionsTableProps {
  data: any;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  itemsPerPage: number;
  setItemsPerPage: (page: number) => void;
  totalItems: number;
  searchValue: string;
  setSearchValue: (page: string) => void;
}

const MpesaTransactionsTable = ({
  data,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  setItemsPerPage,
  totalItems,
  searchValue,
  setSearchValue,
}: MpesaTransactionsTableProps) => {
  const [editTransaction, { data: updatedTransaction, isLoading: editting }] =
    useEditMpesaTransactionsMutation();

  const handleCloseTransaction = async (txnId: string) => {
    console.log(`Transaction ${txnId} marked as closed`);
    try {
      const res = await editTransaction({
        trans_id: txnId,
        status: "CLOSED",
      }).unwrap();
      if (res.trans_id) {
        toast.success(`Transaction ${txnId} closed successfully.`);
        // Optionally, you can refetch the data or update the state to reflect the changes
      }
    } catch (error) {
      console.error("Error closing transaction:", error);
    }
  };

  const columns: ColumnDef<MpesaTransaction>[] = [
    {
      accessorKey: "trans_id",
      header: "Mpesa Txn ID",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "name1",
      header: "Name",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "number",
      header: "Number",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: (info) => {
        const amount = info.getValue() as number;
        return (
          <span className="font-medium">
            {amount.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "account_ref",
      header: "Account Ref",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "date",
      header: "Date",
      cell: (info) => {
        const date = info.getValue() as Date;
        return (
          <span className="font-medium whitespace-nowrap">
            {format(date, "yyyy-MM-dd HH:mm:ss")}
          </span>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => {
        const status = info.getValue() as "OPEN" | "CLOSED";
        return (
          <Badge
            variant="outline"
            className={
              status === "OPEN"
                ? "bg-gradient-to-r from-green-100 to-green-500 text-white font-medium px-4 py-0.5"
                : "bg-gradient-to-r from-red-100 to-red-500 text-white font-medium px-4 py-0.5"
            }
          >
            {status}
          </Badge>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const transaction = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            {transaction.status === "OPEN" &&
              (editting ? (
                <SpinnerTemp type="spinner-double" size="sm" />
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleCloseTransaction(transaction.trans_id)}
                  className="bg-white text-red-500 border-red-300 hover:bg-red-50 hover:text-red-500 flex items-center space-x-1"
                >
                  <X className="h-3.5 w-3.5" />
                  <span>Close</span>
                </Button>
              ))}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  function SearchComponent() {
    return (
      <input
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        placeholder="Search transactions..."
      />
    );
  }

  return (
    <div className="space-y-4">
      <DataTable<MpesaTransaction>
        data={data || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        searchInput={
          <input
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search transactions..."
          />
        }
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-secondary/20"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t !p-1"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={totalItems}
      />
    </div>
  );
};

export default MpesaTransactionsTable;
