
import { useFormContext } from '@/components/custom/forms/FormContext';
import { format } from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Calendar, Clock, Users } from 'lucide-react';

const ConfirmDetails = () => {
  const { formData, updateFormData } = useFormContext();

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <MapPin className="w-5 h-5 text-blue-600" />
            <span>Site Visit Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Project Information */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold mb-3 flex items-center text-blue-900">
                <MapPin className="w-4 h-4 mr-2" />
                Project Details
              </h3>
              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-700 font-medium">Project:</span>
                  <span className="font-semibold text-blue-900">
                    {formData.projectName || 'Not selected'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700 font-medium">Project ID:</span>
                  <span className="font-mono text-sm text-blue-800">
                    {formData.projectId || 'N/A'}
                  </span>
                </div>
              </div>
            </div>

            {/* Schedule Information */}
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-semibold mb-3 flex items-center text-green-900">
                <Calendar className="w-4 h-4 mr-2" />
                Schedule Details
              </h3>
              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-700 font-medium">Pickup Location:</span>
                  <span className="font-semibold text-green-900">
                    {formData.pickupLocation || 'Not specified'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700 font-medium">Date:</span>
                  <span className="font-semibold text-green-900">
                    {formData.pickupDate ? format(formData.pickupDate, 'EEEE, MMMM dd, yyyy') : 'Not selected'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700 font-medium">Time:</span>
                  <span className="font-semibold text-green-900">
                    {formData.pickupTime || 'Not selected'}
                  </span>
                </div>
                {formData.selfDrive && (
                  <div className="mt-2 p-2 bg-green-100 rounded border-l-4 border-green-500">
                    <span className="text-green-800 font-medium">
                      ✓ Client(s) opted for self-drive
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Client Information */}
            {formData.clients && formData.clients.length > 0 && (
              <div className="bg-purple-50 rounded-lg p-4">
                <h3 className="font-semibold mb-3 flex items-center text-purple-900">
                  <Users className="w-4 h-4 mr-2" />
                  Client Information
                </h3>
                <div className="space-y-4">
                  {formData.clients.map((client: any, index: number) => (
                    <div key={index} className="bg-white rounded-lg p-3 border border-purple-200">
                      <h4 className="font-semibold text-purple-900 mb-2">
                        {client.client_type === 'representative' ? `Representative ${index + 1}` : `Client ${index + 1}`}
                      </h4>
                      <div className="grid grid-cols-1 gap-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-purple-700 font-medium">Type:</span>
                          <span className="font-semibold text-purple-900 capitalize">
                            {client.client_type || 'Client'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-purple-700 font-medium">Name:</span>
                          <span className="font-semibold text-purple-900">
                            {`${client.firstName} ${client.lastName}`}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-purple-700 font-medium">Phone:</span>
                          <span className="font-semibold text-purple-900">
                            {client.phone}
                          </span>
                        </div>
                        {client.email && (
                          <div className="flex justify-between">
                            <span className="text-purple-700 font-medium">Email:</span>
                            <span className="font-semibold text-purple-900">
                              {client.email}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="confirm"
              checked={formData.confirmed}
              onCheckedChange={(checked) => updateFormData({ confirmed: checked })}
              className="mt-1"
            />
            <div className="space-y-2">
              <Label htmlFor="confirm" className="text-base font-medium cursor-pointer">
                I confirm the details above are correct
              </Label>
              <p className="text-sm text-gray-600">
                Please review all the information carefully before submitting your site visit request.
                Once submitted, you will receive a confirmation and our team will contact you to finalize the arrangements.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfirmDetails;