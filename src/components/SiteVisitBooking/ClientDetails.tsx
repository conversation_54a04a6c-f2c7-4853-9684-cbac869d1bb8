import { useState } from 'react';
import { useFormContext } from '@/components/custom/forms/FormContext';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, User, Users } from 'lucide-react';

const ClientDetails = () => {
  const { formData, updateFormData } = useFormContext();
  const [clients, setClients] = useState(formData.clients || [{ firstName: '', lastName: '', email: '', phone: '', client_type: 'client' }]);

  const handleClientChange = (index: number, field: string, value: string) => {
    const updatedClients = [...clients];
    updatedClients[index] = { ...updatedClients[index], [field]: value };
    setClients(updatedClients);
    updateFormData({ clients: updatedClients });
  };

  const addClient = () => {
    setClients([...clients, { firstName: '', lastName: '', email: '', phone: '', client_type: 'client' }]);
  };

  return (
    <div className="space-y-6">
      {clients.map((client:any, index:any) => (
        <div key={index} className="space-y-4 p-4 border rounded-lg">
          <h3 className="font-medium">
            {client.client_type === 'representative' ? `Representative ${index + 1}` : `Client ${index + 1}`}
          </h3>
          
          {/* Client Type Dropdown */}
          <div className="space-y-2">
            <Label htmlFor={`clientType-${index}`} className="flex items-center space-x-2">
              <User className="w-4 h-4 text-blue-600" />
              <span>Who is going to the site? *</span>
            </Label>
            <Select
              value={client.client_type || 'client'}
              onValueChange={(value) => handleClientChange(index, 'client_type', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select who is going to the site" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="client">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-green-600" />
                    <span>Client</span>
                  </div>
                </SelectItem>
                <SelectItem value="representative">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span>Representative</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor={`firstName-${index}`}>
                {client.client_type === 'representative' ? 'Representative First Name*' : 'Client First Name*'}
              </Label>
              <Input
                id={`firstName-${index}`}
                value={client.firstName}
                onChange={(e) => handleClientChange(index, 'firstName', e.target.value)}
                placeholder={client.client_type === 'representative' ? 'Representative first name' : 'Client first name'}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`lastName-${index}`}>
                {client.client_type === 'representative' ? 'Representative Last Name*' : 'Client Last Name*'}
              </Label>
              <Input
                id={`lastName-${index}`}
                value={client.lastName}
                onChange={(e) => handleClientChange(index, 'lastName', e.target.value)}
                placeholder={client.client_type === 'representative' ? 'Representative last name' : 'Client last name'}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`email-${index}`}>
                {client.client_type === 'representative' ? 'Representative Email (Optional)' : 'Client Email (Optional)'}
              </Label>
              <Input
                id={`email-${index}`}
                type="email"
                value={client.email}
                onChange={(e) => handleClientChange(index, 'email', e.target.value)}
                placeholder={client.client_type === 'representative' ? '<EMAIL>' : '<EMAIL>'}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`phone-${index}`}>
                {client.client_type === 'representative' ? 'Representative Phone Number*' : 'Client Phone Number*'}
              </Label>
              <Input
                id={`phone-${index}`}
                type="tel"
                value={client.phone}
                onChange={(e) => handleClientChange(index, 'phone', e.target.value)}
                placeholder="+254700000000"
                required
              />
            </div>
          </div>
        </div>
      ))}
      
      <Button
        type="button"
        variant="outline"
        onClick={addClient}
        className="w-full"
      >
        <Plus className="mr-2 h-4 w-4" /> Add Another Person
      </Button>
    </div>
  );
};

export default ClientDetails;