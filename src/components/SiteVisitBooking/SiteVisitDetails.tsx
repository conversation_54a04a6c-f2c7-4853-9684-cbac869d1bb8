// src/components/SiteVisitBooking/SiteVisitDetails.tsx

import React, { useMemo, useEffect } from "react";
import { useFormContext } from "@/components/custom/forms/FormContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { SingleDatePicker } from "../custom/datepicker/DatePicker";
import { useGetProjectsQuery, Project } from "@/redux/slices/projects";
import { useAuthHook } from "@/utils/useAuthHook";
import {
  Loader2,
  MapPin,
  AlertCircle,
  Car,
  Key,
  User,
  Truck,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const SiteVisitDetails = () => {
  const { formData, updateFormData } = useFormContext();
  const { user_details } = useAuthHook();

  // Fetch projects from API
  const {
    data: projectsResponse,
    isLoading: projectsLoading,
    isError: projectsError,
    error: projectsErrorDetails,
  } = useGetProjectsQuery({
    page: 1,
    page_size: 100, // Get all projects for selection
    ordering: "name", // Order by name for better UX
    visibiliy: "SHOW", // Only show visible projects
  });

  // Transform projects data for the select component
  const projects = useMemo(() => {
    // Check if projectsResponse is directly an array or has data.results structure
    if (Array.isArray(projectsResponse)) {
      return projectsResponse;
    }
    return projectsResponse?.data?.results || [];
  }, [projectsResponse]);

  // Set marketer from logged-in user
  useEffect(() => {
    if (user_details?.employee_no && !formData.marketerName) {
      updateFormData({ marketerName: user_details.employee_no });
    }
  }, [user_details, formData.marketerName, updateFormData]);

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {/* Project Selection */}
        <div className="space-y-2">
          <Label htmlFor="projectId" className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-blue-600" />
            <span>Select Project *</span>
          </Label>

          {projectsError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load projects. Please refresh the page and try again.
              </AlertDescription>
            </Alert>
          )}

          <Select
            value={formData.projectId}
            onValueChange={(value) => {
              // Find the selected project to store both ID and name
              const selectedProject = projects.find(
                (p: any) => p.projectId === value
              );
              updateFormData({
                projectId: value,
                projectName: selectedProject?.name || "",
              });
            }}
            disabled={projectsLoading || projectsError}
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={
                  projectsLoading ? "Loading projects..." : "Select a project"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {projectsLoading && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  <span className="text-sm text-gray-500">
                    Loading projects...
                  </span>
                </div>
              )}
              {!projectsLoading && projects.length === 0 && (
                <div className="flex items-center justify-center py-4">
                  <AlertCircle className="w-4 h-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-500">
                    No projects available
                  </span>
                </div>
              )}
              {!projectsLoading &&
                projects.map((project: Project) => (
                  <SelectItem key={project.projectId} value={project.projectId}>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-green-600" />
                      <div>
                        <div className="font-medium">{project.name}</div>
                        {project.description && (
                          <div className="text-xs text-gray-500">
                            {project.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>

          {formData.projectId && (
            <div className="text-xs text-gray-500 mt-1">
              Selected: {formData.projectName}
            </div>
          )}
        </div>

        {/* Transport Type */}
        <div className="space-y-2">
          <Label
            htmlFor="transportType"
            className="flex items-center space-x-2"
          >
            <Car className="w-4 h-4 text-blue-600" />
            <span>Transport Type *</span>
          </Label>
          <Select
            value={formData.transportType || "company_vehicle"}
            onValueChange={(value) => {
              const updates: any = {
                transportType: value,
                selfDrive: value === "self_drive",
              };

              // Auto-set pickup location based on transport type
              if (value === "self_drive") {
                updates.pickupLocation = "Self Drive";
              } else if (value === "own_means") {
                updates.pickupLocation = "Own Means";
              } else if (
                formData.pickupLocation === "Self Drive" ||
                formData.pickupLocation === "Own Means"
              ) {
                // Clear pickup location if changing from self_drive/own_means to other types
                updates.pickupLocation = "";
              }

              updateFormData(updates);
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select transport type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="company_vehicle">
                <div className="flex items-center space-x-2">
                  <Car className="w-4 h-4 text-blue-600" />
                  <span>Company Vehicle</span>
                </div>
              </SelectItem>
              <SelectItem value="self_drive">
                <div className="flex items-center space-x-2">
                  <Key className="w-4 h-4 text-green-600" />
                  <span>Self Drive (Staff Vehicle)</span>
                </div>
              </SelectItem>
              <SelectItem value="own_means">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-purple-600" />
                  <span>Own Means (Client Vehicle)</span>
                </div>
              </SelectItem>
              {/* <SelectItem value="outsourced">
                        <div className="flex items-center space-x-2">
                          <Truck className="w-4 h-4 text-orange-600" />
                          <span>Outsourced</span>
                          </div>
                          </SelectItem> */}
            </SelectContent>
          </Select>
        </div>

        {/* Pickup Location */}
        <div className="space-y-2">
          <Label htmlFor="pickupLocation">
            Pickup Location{" "}
            {formData.transportType !== "self_drive" &&
            formData.transportType !== "own_means"
              ? "*"
              : ""}
          </Label>
          <Input
            id="pickupLocation"
            placeholder={
              formData.transportType === "self_drive"
                ? "Staff will drive company vehicle"
                : formData.transportType === "own_means"
                ? "Client will use their own vehicle"
                : "e.g. ABSA Towers, Westlands"
            }
            value={formData.pickupLocation || ""}
            onChange={(e) => updateFormData({ pickupLocation: e.target.value })}
            disabled={
              formData.transportType === "self_drive" ||
              formData.transportType === "own_means"
            }
          />
          {(formData.transportType === "self_drive" ||
            formData.transportType === "own_means") && (
            <p className="text-xs text-gray-500">
              Pickup location not required for{" "}
              {formData.transportType === "self_drive"
                ? "self-drive (staff vehicle)"
                : "own means (client vehicle)"}{" "}
              transport
            </p>
          )}
        </div>

        {/* Date & Time */}
        <div className="grid grid-cols-2 gap-4">
          {/* Pickup Date */}
          <div className="space-y-2">
            <Label htmlFor="pickupDate">Pickup Date</Label>
            <SingleDatePicker
              label=""
              value={formData.pickupDate}
              onChange={(date: Date | undefined) => {
                console.log("Single date selected:", date);
                updateFormData({ pickupDate: date });
              }}
              disablePastDates={true}
            />
          </div>

          {/* Pickup Time */}
          <div className="space-y-2">
            <Label htmlFor="pickupTime">Pickup Time</Label>
            <Input
              id="pickupTime"
              type="time"
              value={formData.pickupTime || ""}
              onChange={(e) => updateFormData({ pickupTime: e.target.value })}
              className="w-full"
            />
          </div>
        </div>

        {/* Remarks */}
        <div className="space-y-2">
          <Label htmlFor="remarks">Additional Remarks (Optional)</Label>
          <textarea
            id="remarks"
            placeholder="Add any additional notes or remarks about this site visit..."
            value={formData.remarks || ""}
            onChange={(e) => updateFormData({ remarks: e.target.value })}
            className="w-full min-h-[80px] px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white resize-none"
            rows={3}
          />
          <p className="text-xs text-gray-500">
            These remarks will be visible to all team members and can be updated later
          </p>
        </div>
      </div>
    </div>
  );
};

export default SiteVisitDetails;
