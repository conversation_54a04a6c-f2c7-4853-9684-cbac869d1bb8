import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCcw } from 'lucide-react';
import { useRefreshPermissions } from '@/hooks/useRefreshPermissions';
import { useToast } from '../../hooks/use-toast';

interface RefreshPermissionsButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showText?: boolean;
}

/**
 * Button component that refreshes user permissions when clicked
 * Can be placed in any view where permissions need to be manually refreshed
 */
const RefreshPermissionsButton: React.FC<RefreshPermissionsButtonProps> = ({
  variant = 'outline',
  size = 'sm',
  className = '',
  showText = true
}) => {
  const { refreshPermissions } = useRefreshPermissions();
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      const success = await refreshPermissions();
      
      if (success) {
        toast({
          title: 'Permissions refreshed',
          description: 'Your permissions have been updated successfully.',
          variant: 'default',
        });
      } else {
        toast({
          title: 'Refresh failed',
          description: 'Could not refresh permissions. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An error occurred while refreshing permissions.',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={className}
    >
      <RefreshCcw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
      {showText && <span className="ml-2">Refresh Permissions</span>}
    </Button>
  );
};

export default RefreshPermissionsButton;