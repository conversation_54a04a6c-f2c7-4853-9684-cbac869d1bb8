import React from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDes<PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Trash2, 
  Edit, 
  Eye, 
  CheckCircle, 
  AlertTriangle,
  Flag,
  User,
  Calendar,
  FileText
} from "lucide-react";
import Multiselect from "@/components/custom/forms/Multiselect";
import { useGetUsersQuery } from "@/redux/slices/user";

interface FlagsModalsProps {
  activeModal: string | null;
  onCloseModal: () => void;
  onOpenModal: (modalName: string) => void;
  flagForm: any;
  onFlagFormChange: (field: string, value: any) => void;
  formErrors: Record<string, string>;
  successMessage: string;
  editingFlag: any;
  onOpenEditFlag: (flag: any) => void;
  onCreateFlag: () => void;
  onUpdateFlag: () => void;
  onDeleteFlag: (flagId: string, createdBy: string) => void;
  onResolveFlag: (flagId: string, resolutionNotes: string) => void;
  flagItems: any[];
  currentUser?: any;
  isCreating: boolean;
  isDeleting: boolean;
  renderFlagTypeBadge: (type: string) => JSX.Element;
  renderSeverityBadge: (severity: string) => JSX.Element;
  renderStatusBadge: (status: string) => JSX.Element;
  formatDate: (date: string) => string;
}

const FlagsModals: React.FC<FlagsModalsProps> = ({
  activeModal,
  onCloseModal,
  onOpenModal,
  flagForm,
  onFlagFormChange,
  formErrors,
  successMessage,
  editingFlag,
  onOpenEditFlag,
  onCreateFlag,
  onUpdateFlag,
  onDeleteFlag,
  onResolveFlag,
  flagItems,
  currentUser,
  isCreating,
  isDeleting,
  renderFlagTypeBadge,
  renderSeverityBadge,
  renderStatusBadge,
  formatDate,
}) => {
  const [resolutionNotes, setResolutionNotes] = React.useState("");
  const [selectedFlagForResolve, setSelectedFlagForResolve] = React.useState<any>(null);

  // User fetching state
  const { data: usersData, isLoading: usersLoading } = useGetUsersQuery({});
  const [userOptions, setUserOptions] = React.useState<{ value: string; label: string }[]>([]);
  const [selectedUser, setSelectedUser] = React.useState<{ label: string; value: string } | null>(null);

  // Process users data for the multiselect
  React.useEffect(() => {
    if (usersData?.data?.results && usersData.data.results.length > 0) {
      const options = usersData.data.results.map((user: any) => ({
        label: `${user?.fullnames} (${user?.employee_no})`,
        value: user?.employee_no,
      }));
      setUserOptions(options);
    }
  }, [usersData]);

  // Set selected user when editing a flag
  React.useEffect(() => {
    if (editingFlag && activeModal === "editFlag" && userOptions.length > 0) {
      const assignedUser = userOptions.find(option => option.value === flagForm.assigned_to);
      setSelectedUser(assignedUser || null);
    }
  }, [editingFlag, flagForm.assigned_to, userOptions, activeModal]);

  // Handle user selection
  const handleUserSelection = (user: { label: string; value: string } | null) => {
    setSelectedUser(user);
    onFlagFormChange('assigned_to', user?.value || '');
  };

  // Reset selected user when modal closes or changes
  React.useEffect(() => {
    if (!activeModal || activeModal === "flags") {
      setSelectedUser(null);
    }
  }, [activeModal]);

  const handleResolveFlag = () => {
    if (selectedFlagForResolve && resolutionNotes.trim()) {
      onResolveFlag(selectedFlagForResolve.flag_id, resolutionNotes);
      setResolutionNotes("");
      setSelectedFlagForResolve(null);
      onCloseModal();
    }
  };

  const openResolveModal = (flag: any) => {
    setSelectedFlagForResolve(flag);
    setResolutionNotes(flag.resolution_notes || "");
    onOpenModal("resolveFlag");
  };

  const flagTypeOptions = [
    "Customer Issue",
    "Data Quality", 
    "Security",
    "Compliance",
    "Performance",
    "Content"
  ];

  const severityOptions = [
    "Info",
    "Warning", 
    "Error",
    "Critical"
  ];

  const statusOptions = [
    "Active",
    "Resolved",
    "Dismissed"
  ];

  return (
    <>
      {/* Flags List Sheet */}
      <Sheet open={activeModal === "flags"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[800px] sm:max-w-[800px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Flag className="h-5 w-5" />
              Flags ({flagItems.length})
            </SheetTitle>
            <SheetDescription>
              Manage flags for this entity
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <Button 
                onClick={() => onOpenModal("createFlag")}
                className="flex items-center gap-2"
              >
                <Flag className="h-4 w-4" />
                Create Flag
              </Button>
            </div>

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4">
                {flagItems.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Flag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No flags found</p>
                  </div>
                ) : (
                  flagItems.map((flag) => (
                    <Card key={flag.flag_id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <CardTitle className="text-lg">{flag.title}</CardTitle>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <User className="h-3 w-3" />
                              <span>{flag.created_by_name || "Unknown"}</span>
                              <span>•</span>
                              <Calendar className="h-3 w-3" />
                              <span>{formatDate(flag.created_at)}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onOpenEditFlag(flag)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {flag.status === "Active" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openResolveModal(flag)}
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            )}
                            {currentUser?.id === flag.created_by && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDeleteFlag(flag.flag_id, flag.created_by)}
                                disabled={isDeleting}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 flex-wrap">
                            {renderFlagTypeBadge(flag.flag_type)}
                            {renderSeverityBadge(flag.severity)}
                            {renderStatusBadge(flag.status)}
                          </div>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                            {flag.description}
                          </p>
                          
                          {flag.assigned_to_name && (
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <User className="h-3 w-3" />
                              <span>Assigned to: {flag.assigned_to_name}</span>
                            </div>
                          )}
                          
                          {flag.resolution_notes && (
                            <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded text-sm">
                              <div className="flex items-center gap-1 text-green-700 dark:text-green-300 mb-1">
                                <CheckCircle className="h-3 w-3" />
                                <span className="font-medium">Resolution Notes:</span>
                              </div>
                              <p className="text-green-600 dark:text-green-400">{flag.resolution_notes}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>

      {/* Create Flag Sheet */}
      <Sheet open={activeModal === "createFlag"} onOpenChange={onCloseModal}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Create New Flag</SheetTitle>
            <SheetDescription>
              Create a new flag for this entity
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {successMessage && (
              <Alert className="bg-green-50 border-green-200 text-green-800">
                <AlertDescription>{successMessage}</AlertDescription>
              </Alert>
            )}

            {formErrors.submit && (
              <Alert className="bg-red-50 border-red-200 text-red-800">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{formErrors.submit}</AlertDescription>
              </Alert>
            )}

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4 pr-4">
                <div className="space-y-2">
                  <Label htmlFor="flag_reason">Flag Reason *</Label>
                  <Select
                    value={flagForm.flag_reason}
                    onValueChange={(value) => onFlagFormChange("flag_reason", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select flag reason" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Follow-up Required">Follow-up Required</SelectItem>
                      <SelectItem value="Payment Issue">Payment Issue</SelectItem>
                      <SelectItem value="Customer Complaint">Customer Complaint</SelectItem>
                      <SelectItem value="Documentation Missing">Documentation Missing</SelectItem>
                      <SelectItem value="Quality Issue">Quality Issue</SelectItem>
                      <SelectItem value="Process Violation">Process Violation</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.flag_reason && (
                    <p className="text-sm text-red-600">{formErrors.flag_reason}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={flagForm.description}
                    onChange={(e) => onFlagFormChange("description", e.target.value)}
                    placeholder="Describe the flag..."
                    rows={4}
                  />
                  {formErrors.description && (
                    <p className="text-sm text-red-600">{formErrors.description}</p>
                  )}
                </div>

                {/* Follow-up Section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="follow_up_required"
                      checked={flagForm.follow_up_required}
                      onChange={(e) => onFlagFormChange("follow_up_required", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="follow_up_required">Follow-up Required</Label>
                  </div>

                  {flagForm.follow_up_required && (
                    <div className="space-y-2">
                      <Label htmlFor="follow_up_date">Follow-up Date</Label>
                      <Input
                        type="date"
                        id="follow_up_date"
                        value={flagForm.follow_up_date}
                        onChange={(e) => onFlagFormChange("follow_up_date", e.target.value)}
                      />
                    </div>
                  )}
                </div>

                {/* Reminder Section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="set_reminder"
                      checked={flagForm.set_reminder}
                      onChange={(e) => onFlagFormChange("set_reminder", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="set_reminder">Set Reminder</Label>
                  </div>

                  {flagForm.set_reminder && (
                    <div className="space-y-2">
                      <Label htmlFor="reminder_time">Reminder Time</Label>
                      <Input
                        type="datetime-local"
                        id="reminder_time"
                        value={flagForm.reminder_time}
                        onChange={(e) => onFlagFormChange("reminder_time", e.target.value)}
                      />
                    </div>
                  )}
                </div>

                {/* Resolution Section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_resolved"
                      checked={flagForm.is_resolved}
                      onChange={(e) => onFlagFormChange("is_resolved", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="is_resolved">Mark as Resolved</Label>
                  </div>

                  {flagForm.is_resolved && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="resolution_date">Resolution Date</Label>
                        <Input
                          type="date"
                          id="resolution_date"
                          value={flagForm.resolution_date}
                          onChange={(e) => onFlagFormChange("resolution_date", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="resolution_notes">Resolution Notes</Label>
                        <Textarea
                          id="resolution_notes"
                          value={flagForm.resolution_notes}
                          onChange={(e) => onFlagFormChange("resolution_notes", e.target.value)}
                          placeholder="Enter resolution notes..."
                          rows={3}
                        />
                      </div>
                    </>
                  )}
                </div>

                <Separator />

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={onCloseModal}>
                    Cancel
                  </Button>
                  <Button onClick={onCreateFlag} disabled={isCreating}>
                    {isCreating ? "Creating..." : "Create Flag"}
                  </Button>
                </div>
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>

      {/* Edit/View Flag Sheet */}
      <Sheet open={activeModal === "editFlag"} onOpenChange={onCloseModal}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Edit Flag</SheetTitle>
            <SheetDescription>
              Update flag details
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {successMessage && (
              <Alert className="bg-green-50 border-green-200 text-green-800">
                <AlertDescription>{successMessage}</AlertDescription>
              </Alert>
            )}

            {formErrors.submit && (
              <Alert className="bg-red-50 border-red-200 text-red-800">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{formErrors.submit}</AlertDescription>
              </Alert>
            )}

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4 pr-4">
                <div className="space-y-2">
                  <Label htmlFor="flag_reason">Flag Reason *</Label>
                  <Select
                    value={flagForm.flag_reason}
                    onValueChange={(value) => onFlagFormChange("flag_reason", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select flag reason" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Follow-up Required">Follow-up Required</SelectItem>
                      <SelectItem value="Payment Issue">Payment Issue</SelectItem>
                      <SelectItem value="Customer Complaint">Customer Complaint</SelectItem>
                      <SelectItem value="Documentation Missing">Documentation Missing</SelectItem>
                      <SelectItem value="Quality Issue">Quality Issue</SelectItem>
                      <SelectItem value="Process Violation">Process Violation</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.flag_reason && (
                    <p className="text-sm text-red-600">{formErrors.flag_reason}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={flagForm.description}
                    onChange={(e) => onFlagFormChange("description", e.target.value)}
                    placeholder="Describe the flag..."
                    rows={4}
                  />
                  {formErrors.description && (
                    <p className="text-sm text-red-600">{formErrors.description}</p>
                  )}
                </div>

                {/* Follow-up Section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="follow_up_required_edit"
                      checked={flagForm.follow_up_required}
                      onChange={(e) => onFlagFormChange("follow_up_required", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="follow_up_required_edit">Follow-up Required</Label>
                  </div>

                  {flagForm.follow_up_required && (
                    <div className="space-y-2">
                      <Label htmlFor="follow_up_date_edit">Follow-up Date</Label>
                      <Input
                        type="date"
                        id="follow_up_date_edit"
                        value={flagForm.follow_up_date}
                        onChange={(e) => onFlagFormChange("follow_up_date", e.target.value)}
                      />
                    </div>
                  )}
                </div>

                {/* Reminder Section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="set_reminder_edit"
                      checked={flagForm.set_reminder}
                      onChange={(e) => onFlagFormChange("set_reminder", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="set_reminder_edit">Set Reminder</Label>
                  </div>

                  {flagForm.set_reminder && (
                    <div className="space-y-2">
                      <Label htmlFor="reminder_time_edit">Reminder Time</Label>
                      <Input
                        type="datetime-local"
                        id="reminder_time_edit"
                        value={flagForm.reminder_time}
                        onChange={(e) => onFlagFormChange("reminder_time", e.target.value)}
                      />
                    </div>
                  )}
                </div>

                {/* Resolution Section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_resolved_edit"
                      checked={flagForm.is_resolved}
                      onChange={(e) => onFlagFormChange("is_resolved", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="is_resolved_edit">Mark as Resolved</Label>
                  </div>

                  {flagForm.is_resolved && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="resolution_date_edit">Resolution Date</Label>
                        <Input
                          type="date"
                          id="resolution_date_edit"
                          value={flagForm.resolution_date}
                          onChange={(e) => onFlagFormChange("resolution_date", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="resolution_notes_edit">Resolution Notes</Label>
                        <Textarea
                          id="resolution_notes_edit"
                          value={flagForm.resolution_notes}
                          onChange={(e) => onFlagFormChange("resolution_notes", e.target.value)}
                          placeholder="Enter resolution notes..."
                          rows={3}
                        />
                      </div>
                    </>
                  )}
                </div>

                <Separator />

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={onCloseModal}>
                    Cancel
                  </Button>
                  <Button onClick={onUpdateFlag} disabled={isCreating}>
                    {isCreating ? "Updating..." : "Update Flag"}
                  </Button>
                </div>
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>

      {/* Resolve Flag Sheet */}
      <Sheet open={activeModal === "resolveFlag"} onOpenChange={onCloseModal}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Resolve Flag
            </SheetTitle>
            <SheetDescription>
              Add resolution notes and resolve this flag
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {selectedFlagForResolve && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{selectedFlagForResolve.title}</CardTitle>
                  <div className="flex items-center gap-2 flex-wrap">
                    {renderFlagTypeBadge(selectedFlagForResolve.flag_type)}
                    {renderSeverityBadge(selectedFlagForResolve.severity)}
                    {renderStatusBadge(selectedFlagForResolve.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {selectedFlagForResolve.description}
                  </p>
                </CardContent>
              </Card>
            )}

            <div className="space-y-2">
              <Label htmlFor="resolution_notes">Resolution Notes *</Label>
              <Textarea
                id="resolution_notes"
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                placeholder="Describe how this flag was resolved..."
                rows={4}
              />
              <p className="text-sm text-gray-500">
                Provide detailed notes about how this flag was resolved.
              </p>
            </div>

            <Separator />

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={onCloseModal}>
                Cancel
              </Button>
              <Button 
                onClick={handleResolveFlag} 
                disabled={!resolutionNotes.trim()}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Resolve Flag
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* View Flag Modal */}
      <Sheet open={activeModal === 'viewFlag'} onOpenChange={onCloseModal}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>Flag Details</SheetTitle>
            <SheetDescription>
              View flag information
            </SheetDescription>
          </SheetHeader>

          {editingFlag && (
            <div className="space-y-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Flag Reason:</label>
                  <div className="mt-1">
                    {renderFlagTypeBadge(editingFlag.flag_reason || editingFlag.flag_type)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Client Type:</label>
                  <div className="mt-1">
                    <Badge variant="outline" className="text-xs">
                      {editingFlag.client_type}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Description:</label>
                <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{editingFlag.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Status:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {editingFlag.is_resolved ? 'Resolved' : 'Active'}
                  </p>
                </div>
                {editingFlag.is_resolved && editingFlag.resolution_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Resolution Date:</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(editingFlag.resolution_date)}
                    </p>
                  </div>
                )}
              </div>

              {editingFlag.resolution_notes && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Resolution Notes:</label>
                  <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{editingFlag.resolution_notes}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Follow-up Required:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {editingFlag.follow_up_required ? 'Yes' : 'No'}
                  </p>
                </div>
                {editingFlag.follow_up_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Follow-up Date:</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(editingFlag.follow_up_date)}
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Reminder Set:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {editingFlag.set_reminder ? 'Yes' : 'No'}
                  </p>
                </div>
                {editingFlag.reminder_time && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Reminder Time:</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(editingFlag.reminder_time)}
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Created By:</label>
                  <p className="mt-1 text-sm text-gray-900">{editingFlag.created_by || 'Unknown'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Created At:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDate(editingFlag.created_at)}
                  </p>
                </div>
              </div>

              {/* Entity Information */}
              <div>
                <label className="text-sm font-medium text-gray-700">Related To:</label>
                <p className="mt-1 text-sm text-gray-900">
                  {editingFlag.customer && `Customer: ${editingFlag.customer}`}
                  {editingFlag.prospect && `Prospect: ${editingFlag.prospect}`}
                  {editingFlag.sale && `Sale: ${editingFlag.sale}`}
                </p>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Close
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default FlagsModals;