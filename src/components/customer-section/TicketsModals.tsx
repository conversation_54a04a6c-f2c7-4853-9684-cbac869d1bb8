import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from '@/components/ui/sheet';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Ticket,
  MessageSquare,
  Paperclip,
  Send,
  X,
  Calendar,
  User,
  AlertCircle,
  CheckCircle2,
  Clock,
  FileText,
  Image
} from 'lucide-react';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import {
  TicketFormData,
  TicketItem,
  TicketMessage,
  TicketAttachment,
  TicketCategory,
  TicketSource,
  TicketMessageFormData,
  TicketCategoryFormData,
  TicketSourceFormData,
  TicketActionLogFormData,
  TicketActionLog,
  BadgeRenderer,
  DateFormatter
} from './types';

interface TicketsModalsProps {
  // Modal states
  activeModal: string | null;
  onClose: () => void;

  // Form data and handlers
  ticketForm: TicketFormData;
  onTicketFormChange: (field: keyof TicketFormData, value: string | number) => void;
  onCreateTicket: () => void;
  onUpdateTicket: () => void;

  // File attachment
  onFileChange?: (file: File | null) => void;
  selectedFile?: File | null;

  // Message form
  messageForm: TicketMessageFormData;
  onMessageFormChange: (field: keyof TicketMessageFormData, value: string | number) => void;
  onSendMessage: () => void;
  onDeleteMessage?: (messageId: number) => void;
  onEditMessage?: (message: TicketMessage) => void;

  // Category management
  categoryForm?: TicketCategoryFormData;
  onCategoryFormChange?: (field: keyof TicketCategoryFormData, value: string) => void;
  onCreateCategory?: () => void;
  onUpdateCategory?: () => void;
  onDeleteCategory?: (categoryId: number) => void;
  editingCategory?: TicketCategory | null;

  // Source management
  sourceForm?: TicketSourceFormData;
  onSourceFormChange?: (field: keyof TicketSourceFormData, value: string) => void;
  onCreateSource?: () => void;
  onUpdateSource?: () => void;
  onDeleteSource?: (sourceId: number) => void;
  editingSource?: TicketSource | null;

  // Attachment management
  onUploadAttachment?: (file: File) => void;
  onDeleteAttachment?: (attachmentId: number) => void;

  // Action logs
  actionLogs?: TicketActionLog[];
  onCreateActionLog?: (action: string, comment?: string) => void;

  // Navigation
  onNavigateToTicket?: (ticketId: number) => void;

  // Data
  editingTicket: TicketItem | null;
  selectedTicket: TicketItem | null;
  ticketMessages: TicketMessage[];
  ticketAttachments: TicketAttachment[];
  categories: TicketCategory[];
  sources: TicketSource[];
  ticketItems?: TicketItem[];

  // Loading states
  isCreating: boolean;
  isUpdating: boolean;
  isSendingMessage: boolean;
  messagesLoading: boolean;
  attachmentsLoading: boolean;
  isDeleting?: boolean;
  isUploadingAttachment?: boolean;

  // Error handling
  formErrors: Record<string, string>;
  successMessage: string;

  // Utility functions
  renderPriorityBadge: BadgeRenderer;
  renderStatusBadge: BadgeRenderer;
  formatDate: DateFormatter;
  timeAgo: DateFormatter;

  // Current user
  currentUser?: {
    id: string;
    name: string;
  };
}

const TicketsModals: React.FC<TicketsModalsProps> = ({
  activeModal,
  onClose,
  ticketForm,
  onTicketFormChange,
  onCreateTicket,
  onUpdateTicket,
  onFileChange,
  selectedFile,
  messageForm,
  onMessageFormChange,
  onSendMessage,
  onDeleteMessage,
  onEditMessage,
  categoryForm,
  onCategoryFormChange,
  onCreateCategory,
  onUpdateCategory,
  onDeleteCategory,
  editingCategory,
  sourceForm,
  onSourceFormChange,
  onCreateSource,
  onUpdateSource,
  onDeleteSource,
  editingSource,
  onUploadAttachment,
  onDeleteAttachment,
  actionLogs,
  onCreateActionLog,
  onNavigateToTicket,
  editingTicket,
  selectedTicket,
  ticketMessages,
  ticketAttachments,
  categories,
  sources,
  ticketItems,
  isCreating,
  isUpdating,
  isSendingMessage,
  messagesLoading,
  attachmentsLoading,
  isDeleting,
  isUploadingAttachment,
  formErrors,
  successMessage,
  renderPriorityBadge,
  renderStatusBadge,
  formatDate,
  timeAgo,
  currentUser,
}) => {
  // File attachment state
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);

  // Handle file change with preview
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selected = e.target.files?.[0] || null;
    if (selected) {
      setIsPdf(selected.type === "application/pdf");

      if (selected.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(selected));
      } else {
        setPreviewUrl(null);
      }
    } else {
      setPreviewUrl(null);
      setIsPdf(false);
    }

    if (onFileChange) {
      onFileChange(selected);
    }
  };
  const renderCreateTicketDrawer = () => (
    <SheetContent className="w-full sm:w-[500px] md:w-[600px] max-w-[95vw] overflow-y-auto">
      <SheetHeader>
        <SheetTitle className="flex items-center gap-2">
          <Ticket className="h-5 w-5" />
          Create New Ticket
        </SheetTitle>
        <SheetDescription>
          Create a new support ticket for this customer
        </SheetDescription>
      </SheetHeader>

      {successMessage && (
        <Alert className="bg-green-50 border-green-200 text-green-800 mt-4">
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4 mt-6">
        <div className="grid grid-cols-1 gap-4 items-center text-sm">
          <div className="space-y-2">
            <label className="px-1 text-xs">
              Ticket Title <span className="text-destructive">*</span>
            </label>
            <input
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              type="text"
              name="title"
              value={ticketForm.title}
              onChange={(e) => onTicketFormChange('title', e.target.value)}
              placeholder="Enter Title"
              required
            />
            {formErrors.title && (
              <p className="text-sm text-red-500">{formErrors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="px-1 text-xs">
              Ticket Description <span className="text-destructive">*</span>
            </label>
            <textarea
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              name="description"
              value={ticketForm.description}
              onChange={(e) => onTicketFormChange('description', e.target.value)}
              placeholder="Enter Description"
              rows={4}
              required
            />
            {formErrors.description && (
              <p className="text-sm text-red-500">{formErrors.description}</p>
            )}
          </div>

          <div className="grid sm:grid-cols-2 grid-cols-1 gap-4">
            <div className="space-y-2">
              <label className="px-1 text-xs">
                Category <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="category"
                value={ticketForm.category?.toString() || ''}
                onChange={(e) => onTicketFormChange('category', parseInt(e.target.value))}
                required
              >
                <option value="" className="text-gray-400">
                  Select Ticket Category
                </option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="px-1 text-xs">
                Source <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="source"
                value={ticketForm.source?.toString() || ''}
                onChange={(e) => onTicketFormChange('source', parseInt(e.target.value))}
                required
              >
                <option value="" className="text-gray-400">
                  Select Ticket Source
                </option>
                {sources.map((source) => (
                  <option key={source.id} value={source.id}>
                    {source.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="px-1 text-xs">
                Priority <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="priority"
                value={ticketForm.priority}
                onChange={(e) => onTicketFormChange('priority', e.target.value)}
                required
              >
                <option value="" className="text-gray-400">
                  Select Priority
                </option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="px-1 text-xs">
                Status <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="status"
                value={ticketForm.status}
                onChange={(e) => onTicketFormChange('status', e.target.value)}
                required
              >
                <option value="" className="text-gray-400">
                  Select Status
                </option>
                <option value="open">open</option>
                <option value="in_progress">in_progress</option>
                <option value="resolved">resolved</option>
                <option value="closed">closed</option>
              </select>
            </div>
          </div>

          {/* Reminder controls */}
          <div className="space-y-2 border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <label className="px-1 text-xs font-medium">Add Reminder</label>
              <input
                type="checkbox"
                className="h-4 w-4"
                // We'll use a string flag on ticketForm to keep this controlled in parent (CustomerSidebar)
                checked={(ticketForm as any).add_reminder === 'true'}
                onChange={(e) => onTicketFormChange('add_reminder' as any, e.target.checked ? 'true' : 'false')}
              />
            </div>

            {((ticketForm as any).add_reminder === 'true') && (
              <div className="grid sm:grid-cols-2 grid-cols-1 gap-4 mt-2">
                <div className="space-y-2">
                  <label className="px-1 text-xs">Reminder Type</label>
                  <select
                    className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={(ticketForm as any).reminder_type || 'General'}
                    onChange={(e) => onTicketFormChange('reminder_type' as any, e.target.value)}
                  >
                    <option value="General">General</option>
                    <option value="Follow-up Call">Follow-up Call</option>
                    <option value="Payment Reminder">Payment Reminder</option>
                    <option value="Document Collection">Document Collection</option>
                    <option value="Site Visit">Site Visit</option>
                    <option value="Meeting">Meeting</option>
                    <option value="Email">Email</option>
                    <option value="SMS">SMS</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="px-1 text-xs">Reminder Date</label>
                  <input
                    type="date"
                    className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={(ticketForm as any).reminder_date || ''}
                    onChange={(e) => onTicketFormChange('reminder_date' as any, e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <label className="px-1 text-xs">Reminder Time</label>
                  <input
                    type="time"
                    className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={(ticketForm as any).reminder_time || ''}
                    onChange={(e) => onTicketFormChange('reminder_time' as any, e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <label className="px-1 text-xs">Reminder Priority</label>
                  <select
                    className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={(ticketForm as any).reminder_priority || 'Normal'}
                    onChange={(e) => onTicketFormChange('reminder_priority' as any, e.target.value)}
                  >
                    <option value="Normal">Normal</option>
                    <option value="Low">Low</option>
                    <option value="High">High</option>
                    <option value="Urgent">Urgent</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label className="px-1 text-xs">Attachment</label>
            <label
              htmlFor="file-upload"
              className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
            >
              <input
                id="file-upload"
                type="file"
                accept="image/*,application/pdf"
                onChange={handleFileChange}
                className="hidden"
              />

              {previewUrl && !isPdf ? (
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-64 object-contain rounded-xl shadow-md"
                />
              ) : selectedFile && isPdf ? (
                <div className="flex flex-col items-center">
                  <FileText className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-gray-700 font-medium">PDF Selected</p>
                </div>
              ) : (
                <>
                  <Image className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-gray-500">Click to upload image or PDF</p>
                </>
              )}
            </label>

            {selectedFile && (
              <p className="mt-2 text-center text-sm text-gray-600">
                Selected file: <span className="font-medium">{selectedFile.name}</span>
              </p>
            )}
          </div>
        </div>

        {formErrors.submit && (
          <Alert className="bg-red-50 border-red-200">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-800">
              {formErrors.submit}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onCreateTicket} disabled={isCreating}>
            {isCreating ? (
              <>
                <SpinnerTemp type="spinner-double" size="sm" />
                Creating...
              </>
            ) : (
              'Create Ticket'
            )}
          </Button>
        </div>
      </div>
    </SheetContent>
  );

  const renderTicketDetailsModal = () => (
    <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden w-[95vw] sm:w-full">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Ticket className="h-5 w-5" />
          Ticket Details - {selectedTicket?.ticket_id}
        </DialogTitle>
      </DialogHeader>

      {selectedTicket && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[70vh]">
          {/* Left Column - Ticket Info */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <h3 className="text-lg font-semibold">{selectedTicket.title}</h3>
              <div className="flex items-center gap-2">
                {renderStatusBadge(selectedTicket.status)}
                {renderPriorityBadge(selectedTicket.priority)}
              </div>
            </div>

            <p className="text-sm text-muted-foreground">{selectedTicket.description}</p>

            <Separator />

            {/* Messages Section */}
            <div className="flex-1 flex flex-col">
              <div className="flex items-center gap-2 mb-3">
                <MessageSquare className="h-4 w-4" />
                <span className="font-medium">Messages</span>
                <Badge variant="outline">{ticketMessages.length}</Badge>
              </div>

              <ScrollArea className="flex-1 border rounded-md p-3 mb-3">
                {messagesLoading ? (
                  <div className="flex justify-center py-4">
                    <SpinnerTemp type="spinner-double" size="sm" />
                  </div>
                ) : ticketMessages.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No messages yet
                  </p>
                ) : (
                  <div className="space-y-3">
                    {ticketMessages.map((message) => (
                      <div key={message.id} className="border-b pb-3 last:border-b-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-sm">{message.sender_name || message.sender}</span>
                          <span className="text-xs text-muted-foreground">{timeAgo(message.created_at)}</span>
                        </div>
                        <p className="text-sm">{message.message}</p>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>

              {/* Message Input */}
              <div className="flex gap-2">
                <Textarea
                  value={messageForm.message}
                  onChange={(e) => onMessageFormChange('message', e.target.value)}
                  placeholder="Type your message..."
                  rows={2}
                  className="flex-1"
                />
                <Button
                  onClick={onSendMessage}
                  disabled={isSendingMessage || !messageForm.message.trim()}
                  size="sm"
                >
                  {isSendingMessage ? (
                    <SpinnerTemp type="spinner-double" size="sm" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Right Column - Ticket Metadata */}
          <div className="space-y-4">
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <User className="h-4 w-4" />
                Details
              </h4>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Customer:</span>
                  <span>{selectedTicket.customer_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Assigned to:</span>
                  <span>{selectedTicket.user_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Category:</span>
                  <span>{selectedTicket.category_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Source:</span>
                  <span>{selectedTicket.source_name || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{formatDate(selectedTicket.created_at)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Updated:</span>
                  <span>{formatDate(selectedTicket.updated_at)}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Attachments Section */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Paperclip className="h-4 w-4" />
                Attachments
                <Badge variant="outline">{ticketAttachments.length}</Badge>
              </h4>

              {attachmentsLoading ? (
                <div className="flex justify-center py-4">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : ticketAttachments.length === 0 ? (
                <p className="text-sm text-muted-foreground">No attachments</p>
              ) : (
                <div className="space-y-2">
                  {ticketAttachments.map((attachment) => (
                    <div key={attachment.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <Paperclip className="h-3 w-3" />
                        <span className="text-sm truncate">
                          {attachment.file.split('/').pop()}
                        </span>
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <a href={attachment.file} target="_blank" rel="noopener noreferrer">
                          View
                        </a>
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>
    </DialogContent>
  );

  // Render tickets list modal
  const renderTicketsListModal = () => (
    <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden w-[95vw] sm:w-full">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Ticket className="h-5 w-5" />
          All Tickets
        </DialogTitle>
      </DialogHeader>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button onClick={() => activeModal === 'createTicket' ? onClose() : onClose()}>
              <Ticket className="h-4 w-4 mr-2" />
              Create New Ticket
            </Button>
          </div>
        </div>

        <ScrollArea className="h-[60vh]">
          {ticketItems && ticketItems.length > 0 ? (
            <div className="space-y-3">
              {ticketItems.map((ticket) => (
                <div key={ticket.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{ticket.title}</h4>
                      <Badge variant="outline">{ticket.ticket_id}</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      {renderStatusBadge(ticket.status)}
                      {renderPriorityBadge(ticket.priority)}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{ticket.description}</p>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 text-xs text-muted-foreground">
                    <span>Customer: {ticket.customer_name}</span>
                    <span>Created: {timeAgo(ticket.created_at)}</span>
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button size="sm" variant="outline" onClick={() => {
                      // Navigate to ticket details page
                      if (onNavigateToTicket) {
                        onNavigateToTicket(ticket.id);
                      }
                      onClose();
                    }}>
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Ticket className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No tickets found</p>
            </div>
          )}
        </ScrollArea>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>
    </DialogContent>
  );

  // Render edit ticket modal
  const renderEditTicketModal = () => (
    <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Ticket className="h-5 w-5" />
          Edit Ticket
        </DialogTitle>
      </DialogHeader>

      {successMessage && (
        <Alert className="bg-green-50 border-green-200 text-green-800">
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-title">Title *</Label>
            <Input
              id="edit-title"
              value={ticketForm.title}
              onChange={(e) => onTicketFormChange('title', e.target.value)}
              placeholder="Enter ticket title"
              className={formErrors.title ? 'border-red-500' : ''}
            />
            {formErrors.title && (
              <p className="text-sm text-red-500">{formErrors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-priority">Priority</Label>
            <Select value={ticketForm.priority} onValueChange={(value) => onTicketFormChange('priority', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-status">Status</Label>
            <Select value={ticketForm.status} onValueChange={(value) => onTicketFormChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
                <SelectItem value="escalated">Escalated</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-category">Category</Label>
            <Select
              value={ticketForm.category?.toString()}
              onValueChange={(value) => onTicketFormChange('category', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-user">Assigned User *</Label>
          <Input
            id="edit-user"
            value={ticketForm.user || ''}
            onChange={(e) => onTicketFormChange('user', e.target.value)}
            placeholder="Enter user ID or employee number"
            className={formErrors.user ? 'border-red-500' : ''}
          />
          {formErrors.user && (
            <p className="text-sm text-red-500">{formErrors.user}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-description">Description *</Label>
          <Textarea
            id="edit-description"
            value={ticketForm.description}
            onChange={(e) => onTicketFormChange('description', e.target.value)}
            placeholder="Describe the issue or request"
            rows={4}
            className={formErrors.description ? 'border-red-500' : ''}
          />
          {formErrors.description && (
            <p className="text-sm text-red-500">{formErrors.description}</p>
          )}
        </div>

        {formErrors.submit && (
          <Alert className="bg-red-50 border-red-200">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-800">
              {formErrors.submit}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onUpdateTicket} disabled={isUpdating}>
            {isUpdating ? (
              <>
                <SpinnerTemp type="spinner-double" size="sm" />
                Updating...
              </>
            ) : (
              'Update Ticket'
            )}
          </Button>
        </div>
      </div>
    </DialogContent>
  );

  return (
    <>
      <Sheet open={activeModal === 'createTicket'} onOpenChange={onClose}>
        {renderCreateTicketDrawer()}
      </Sheet>

      <Dialog open={activeModal === 'editTicket'} onOpenChange={onClose}>
        {renderEditTicketModal()}
      </Dialog>

      <Dialog open={activeModal === 'ticketDetails'} onOpenChange={onClose}>
        {renderTicketDetailsModal()}
      </Dialog>

      <Dialog open={activeModal === 'ticketsList'} onOpenChange={onClose}>
        {renderTicketsListModal()}
      </Dialog>
    </>
  );
};

export default TicketsModals;
