import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Bell, 
  BellRing, 
  Clock, 
  User, 
  Mail, 
  MessageSquare, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  AlertCircle,
  Zap,
  Calendar,
  Eye,
  EyeOff,
  Edit2,
  Trash2,
  Send,
  Shield
} from "lucide-react";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import Multiselect from "@/components/custom/forms/Multiselect";
import { useGetUsersQuery } from "@/redux/slices/user";

interface NotificationFormData {
  // New API fields
  client_type: string;
  notification_type: string;
  priority: string;
  title: string;
  message: string;
  is_read: boolean;
  read_at: string;
  action_url: string;
  recipient: string;
  sender: string;
  created_by: string;
  customer?: string;
  prospect?: number;
  sale?: string;

  // Legacy fields for backward compatibility
  entity_type: string;
  entity_id: string;
  customer_no: string;
  prospect_id: string;
  lead_file_no: string;
  client_status: string;
  expires_at: string;
  is_active: boolean;
}

interface NotificationsModalsProps {
  activeModal: string | null;
  onCloseModal: () => void;
  onOpenModal: (modalName: string) => void;
  notificationForm: NotificationFormData;
  onNotificationFormChange: (field: keyof NotificationFormData, value: string | boolean | number) => void;
  formErrors: Record<string, string>;
  successMessage: string;
  editingNotification: any;
  onOpenEditNotification: (notification: any) => void;
  onCreateNotification: () => void;
  onUpdateNotification: () => void;
  onDeleteNotification: (notificationId: string, createdBy: string) => void;
  onMarkAsRead: (notificationId: string) => void;
  notificationItems: any[];
  currentUser: any;
  isCreating: boolean;
  isDeleting: boolean;
  renderNotificationTypeBadge: (type: string) => JSX.Element;
  renderPriorityBadge: (priority: string) => JSX.Element;
  renderStatusBadge: (status: string) => JSX.Element;
  formatDate: (dateString: string) => string;
  timeAgo: (dateString: string) => string;
  unreadCount?: number;
  selectedNotification?: any;
}

const NotificationsModals: React.FC<NotificationsModalsProps> = ({
  activeModal,
  onCloseModal,
  onOpenModal,
  notificationForm,
  onNotificationFormChange,
  formErrors,
  successMessage,
  editingNotification,
  onOpenEditNotification,
  onCreateNotification,
  onUpdateNotification,
  onDeleteNotification,
  onMarkAsRead,
  notificationItems,
  currentUser,
  isCreating,
  isDeleting,
  renderNotificationTypeBadge,
  renderPriorityBadge,
  renderStatusBadge,
  formatDate,
  timeAgo,
  unreadCount = 0,
  selectedNotification,
}) => {
  // User fetching state
  const { data: usersData, isLoading: usersLoading } = useGetUsersQuery({});
  const [userOptions, setUserOptions] = useState<{ value: string; label: string }[]>([]);
  const [selectedRecipient, setSelectedRecipient] = useState<{ label: string; value: string } | null>(null);
  const [selectedSender, setSelectedSender] = useState<{ label: string; value: string } | null>(null);

  // Process users data for the multiselect
  useEffect(() => {
    console.log("Users data:", usersData);
    if (usersData?.data?.results && usersData.data.results.length > 0) {
      const options = usersData.data.results.map((user: any) => ({
        label: `${user?.fullnames} (${user?.employee_no})`,
        value: user?.employee_no,
      }));
      setUserOptions(options);
    }
  }, [usersData]);

  // Set selected users when editing a notification
  useEffect(() => {
    if (editingNotification && activeModal === "editNotification" && userOptions.length > 0) {
      const recipient = userOptions.find(option => option.value === notificationForm.recipient);
      const sender = userOptions.find(option => option.value === notificationForm.sender);
      setSelectedRecipient(recipient || null);
      setSelectedSender(sender || null);
    }
  }, [editingNotification, notificationForm.recipient, notificationForm.sender, userOptions, activeModal]);

  // Handle user selection
  const handleRecipientSelection = (user: { label: string; value: string } | null) => {
    setSelectedRecipient(user);
    onNotificationFormChange('recipient', user?.value || '');
  };

  const handleSenderSelection = (user: { label: string; value: string } | null) => {
    setSelectedSender(user);
    onNotificationFormChange('sender', user?.value || '');
  };

  // Reset selected users when modal closes or changes
  useEffect(() => {
    if (!activeModal || activeModal === "notifications") {
      setSelectedRecipient(null);
      setSelectedSender(null);
    }
  }, [activeModal]);

  const getNotificationTypeIcon = (type: string) => {
    const icons = {
      Info: Info,
      Warning: AlertTriangle,
      Error: AlertCircle,
      Success: CheckCircle,
      Reminder: Clock,
      Alert: Zap,
    };
    const IconComponent = icons[type as keyof typeof icons] || Bell;
    return <IconComponent className="h-4 w-4" />;
  };

  const getPriorityIcon = (priority: string) => {
    const icons = {
      Low: Info,
      Normal: Bell,
      High: AlertTriangle,
      Urgent: Zap,
    };
    const IconComponent = icons[priority as keyof typeof icons] || Bell;
    return <IconComponent className="h-4 w-4" />;
  };

  const formatDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return "N/A";
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canEditNotification = (notification: any) => {
    return currentUser?.id === notification.created_by;
  };

  const canDeleteNotification = (notification: any) => {
    return currentUser?.id === notification.created_by;
  };

  const isNotificationExpired = (expiresAt: string) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  return (
    <>
      {/* Notifications List Modal */}
      <Sheet open={activeModal === "notifications"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[600px] sm:max-w-[600px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              All Notifications
              {unreadCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unreadCount} unread
                </Badge>
              )}
            </SheetTitle>
            <SheetDescription>
              View and manage all notifications
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-between items-center my-4">
            <p className="text-sm text-muted-foreground">
              {notificationItems.length} notification{notificationItems.length !== 1 ? 's' : ''} found
            </p>
            <Button onClick={() => onOpenModal("createNotification")} size="sm">
              <Send className="h-4 w-4 mr-2" />
              Create Notification
            </Button>
          </div>

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4">
              {notificationItems.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center text-muted-foreground">
                      <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No notifications found</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => onOpenModal("createNotification")}
                      >
                        Create First Notification
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                notificationItems.map((notification) => (
                  <Card 
                    key={notification.notification_id} 
                    className={`relative ${!notification.is_read ? 'border-l-4 border-l-blue-500 bg-blue-50/50' : ''} ${isNotificationExpired(notification.expires_at) ? 'opacity-60' : ''}`}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getNotificationTypeIcon(notification.notification_type)}
                          <div className="flex-1">
                            <CardTitle className="text-sm flex items-center gap-2">
                              {notification.title}
                              {!notification.is_read && (
                                <Badge variant="secondary" className="text-xs">
                                  New
                                </Badge>
                              )}
                              {isNotificationExpired(notification.expires_at) && (
                                <Badge variant="outline" className="text-xs text-red-600">
                                  Expired
                                </Badge>
                              )}
                            </CardTitle>
                            <CardDescription className="text-xs">
                              {notification.notification_type} • {timeAgo(notification.created_at)}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {renderNotificationTypeBadge(notification.notification_type)}
                          {renderPriorityBadge(notification.priority)}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {notification.message}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          {notification.recipient_display && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              To: {notification.recipient_display}
                            </span>
                          )}
                          {notification.sender_display && (
                            <span className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              From: {notification.sender_display}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {!notification.is_read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onMarkAsRead(notification.notification_id)}
                              className="h-7 w-7 p-0"
                              title="Mark as read"
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                          )}
                          
                          {canEditNotification(notification) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onOpenEditNotification(notification)}
                              className="h-7 w-7 p-0"
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          )}
                          
                          {canDeleteNotification(notification) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onDeleteNotification(notification.notification_id, notification.created_by)}
                              className="h-7 w-7 p-0 text-red-600 hover:text-red-700"
                              disabled={isDeleting}
                            >
                              {isDeleting ? (
                                <SpinnerTemp />
                              ) : (
                                <Trash2 className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      <div className="mt-2 pt-2 border-t">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            Created: {formatDateTime(notification.created_at)}
                          </span>
                          {notification.expires_at && (
                            <span className={isNotificationExpired(notification.expires_at) ? 'text-red-600' : ''}>
                              Expires: {formatDateTime(notification.expires_at)}
                            </span>
                          )}
                        </div>
                        {notification.action_url && (
                          <div className="mt-1">
                            <a
                              href={notification.action_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs text-blue-600 hover:text-blue-800 underline"
                            >
                              View Action
                            </a>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Create Notification Modal */}
      <Sheet open={activeModal === "createNotification"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[500px] sm:max-w-[500px]">
          <SheetHeader>
            <SheetTitle>Create New Notification</SheetTitle>
            <SheetDescription>
              Send a new notification to users
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {formErrors.submit && (
            <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
              <AlertDescription>{formErrors.submit}</AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="notification_type">Notification Type *</Label>
                  <Select
                    value={notificationForm.notification_type}
                    onValueChange={(value) => onNotificationFormChange('notification_type', value)}
                  >
                    <SelectTrigger className={formErrors.notification_type ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Info">Info</SelectItem>
                      <SelectItem value="Warning">Warning</SelectItem>
                      <SelectItem value="Error">Error</SelectItem>
                      <SelectItem value="Success">Success</SelectItem>
                      <SelectItem value="Reminder">Reminder</SelectItem>
                      <SelectItem value="Alert">Alert</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.notification_type && (
                    <p className="text-sm text-red-600">{formErrors.notification_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priority *</Label>
                  <Select
                    value={notificationForm.priority}
                    onValueChange={(value) => onNotificationFormChange('priority', value)}
                  >
                    <SelectTrigger className={formErrors.priority ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Normal">Normal</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                      <SelectItem value="Urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.priority && (
                    <p className="text-sm text-red-600">{formErrors.priority}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={notificationForm.title}
                  onChange={(e) => onNotificationFormChange('title', e.target.value)}
                  placeholder="Enter notification title"
                  className={formErrors.title ? "border-red-500" : ""}
                />
                {formErrors.title && (
                  <p className="text-sm text-red-600">{formErrors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message *</Label>
                <Textarea
                  id="message"
                  value={notificationForm.message}
                  onChange={(e) => onNotificationFormChange('message', e.target.value)}
                  placeholder="Enter notification message"
                  rows={4}
                  className={formErrors.message ? "border-red-500" : ""}
                />
                {formErrors.message && (
                  <p className="text-sm text-red-600">{formErrors.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Recipient *</Label>
                <Multiselect
                  value={selectedRecipient}
                  data={userOptions}
                  setValue={handleRecipientSelection}
                  loading={usersLoading}
                  isClearable={true}
                  isDisabled={false}
                  isMultiple={false}
                  isSearchable={true}
                />
                {formErrors.recipient && (
                  <p className="text-sm text-red-600">{formErrors.recipient}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Sender (Optional)</Label>
                <Multiselect
                  value={selectedSender}
                  data={userOptions}
                  setValue={handleSenderSelection}
                  loading={usersLoading}
                  isClearable={true}
                  isDisabled={false}
                  isMultiple={false}
                  isSearchable={true}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="action_url">Action URL (Optional)</Label>
                <Input
                  id="action_url"
                  type="url"
                  value={notificationForm.action_url}
                  onChange={(e) => onNotificationFormChange('action_url', e.target.value)}
                  placeholder="https://example.com/action"
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_read"
                    checked={notificationForm.is_read}
                    onCheckedChange={(checked) => onNotificationFormChange('is_read', checked as boolean)}
                  />
                  <Label htmlFor="is_read">Mark as Read</Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Mark this notification as read when created
                </p>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_active"
                    checked={notificationForm.is_active}
                    onCheckedChange={(checked) => onNotificationFormChange('is_active', checked as boolean)}
                  />
                  <Label htmlFor="is_active">Active Notification</Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Inactive notifications are hidden from the main view
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="expires_at">Legacy Expiration Date (Optional)</Label>
                <Input
                  id="expires_at"
                  type="date"
                  value={notificationForm.expires_at}
                  onChange={(e) => onNotificationFormChange('expires_at', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  For backward compatibility only
                </p>
              </div>
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Cancel
            </Button>
            <Button onClick={onCreateNotification} disabled={isCreating}>
              {isCreating ? (
                <>
                  <SpinnerTemp />
                  Creating...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Notification
                </>
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Edit Notification Modal */}
      <Sheet open={activeModal === "editNotification"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[500px] sm:max-w-[500px]">
          <SheetHeader>
            <SheetTitle>Edit Notification</SheetTitle>
            <SheetDescription>
              Update notification details
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {formErrors.submit && (
            <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
              <AlertDescription>{formErrors.submit}</AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_notification_type">Notification Type *</Label>
                  <Select
                    value={notificationForm.notification_type}
                    onValueChange={(value) => onNotificationFormChange('notification_type', value)}
                  >
                    <SelectTrigger className={formErrors.notification_type ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Info">Info</SelectItem>
                      <SelectItem value="Warning">Warning</SelectItem>
                      <SelectItem value="Error">Error</SelectItem>
                      <SelectItem value="Success">Success</SelectItem>
                      <SelectItem value="Reminder">Reminder</SelectItem>
                      <SelectItem value="Alert">Alert</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.notification_type && (
                    <p className="text-sm text-red-600">{formErrors.notification_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_priority">Priority *</Label>
                  <Select
                    value={notificationForm.priority}
                    onValueChange={(value) => onNotificationFormChange('priority', value)}
                  >
                    <SelectTrigger className={formErrors.priority ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Normal">Normal</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                      <SelectItem value="Urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.priority && (
                    <p className="text-sm text-red-600">{formErrors.priority}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_title">Title *</Label>
                <Input
                  id="edit_title"
                  value={notificationForm.title}
                  onChange={(e) => onNotificationFormChange('title', e.target.value)}
                  placeholder="Enter notification title"
                  className={formErrors.title ? "border-red-500" : ""}
                />
                {formErrors.title && (
                  <p className="text-sm text-red-600">{formErrors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_message">Message *</Label>
                <Textarea
                  id="edit_message"
                  value={notificationForm.message}
                  onChange={(e) => onNotificationFormChange('message', e.target.value)}
                  placeholder="Enter notification message"
                  rows={4}
                  className={formErrors.message ? "border-red-500" : ""}
                />
                {formErrors.message && (
                  <p className="text-sm text-red-600">{formErrors.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Recipient *</Label>
                <Multiselect
                  value={selectedRecipient}
                  data={userOptions}
                  setValue={handleRecipientSelection}
                  loading={usersLoading}
                  isClearable={true}
                  isDisabled={false}
                  isMultiple={false}
                  isSearchable={true}
                />
                {formErrors.recipient && (
                  <p className="text-sm text-red-600">{formErrors.recipient}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Sender (Optional)</Label>
                <Multiselect
                  value={selectedSender}
                  data={userOptions}
                  setValue={handleSenderSelection}
                  loading={usersLoading}
                  isClearable={true}
                  isDisabled={false}
                  isMultiple={false}
                  isSearchable={true}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_action_url">Action URL (Optional)</Label>
                <Input
                  id="edit_action_url"
                  type="url"
                  value={notificationForm.action_url}
                  onChange={(e) => onNotificationFormChange('action_url', e.target.value)}
                  placeholder="https://example.com/action"
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_is_read"
                    checked={notificationForm.is_read}
                    onCheckedChange={(checked) => onNotificationFormChange('is_read', checked as boolean)}
                  />
                  <Label htmlFor="edit_is_read">Mark as Read</Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Mark this notification as read
                </p>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_is_active"
                    checked={notificationForm.is_active}
                    onCheckedChange={(checked) => onNotificationFormChange('is_active', checked as boolean)}
                  />
                  <Label htmlFor="edit_is_active">Active Notification</Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Inactive notifications are hidden from the main view
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_expires_at">Legacy Expiration Date (Optional)</Label>
                <Input
                  id="edit_expires_at"
                  type="date"
                  value={notificationForm.expires_at}
                  onChange={(e) => onNotificationFormChange('expires_at', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  For backward compatibility only
                </p>
              </div>
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Cancel
            </Button>
            <Button onClick={onUpdateNotification} disabled={isCreating}>
              {isCreating ? (
                <>
                  <SpinnerTemp />
                  Updating...
                </>
              ) : (
                <>
                  <Edit2 className="h-4 w-4 mr-2" />
                  Update Notification
                </>
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Notification Details Modal */}
      <Dialog open={activeModal === "notificationDetails"} onOpenChange={onCloseModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Details
            </DialogTitle>
          </DialogHeader>

          {selectedNotification && (
            <div className="space-y-6">
              {/* Header with title and badges */}
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">{selectedNotification.title}</h3>
                  <div className="flex items-center gap-2">
                    {renderNotificationTypeBadge(selectedNotification.notification_type)}
                    {renderPriorityBadge(selectedNotification.priority)}
                    {!selectedNotification.is_read && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Bell className="h-3 w-3" />
                        Unread
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                  <p>Created {timeAgo(selectedNotification.created_at)}</p>
                  {selectedNotification.sender && (
                    <p>by {selectedNotification.sender}</p>
                  )}
                </div>
              </div>

              <Separator />

              {/* Message */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Message</Label>
                <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">{selectedNotification.message}</p>
                </div>
              </div>

              {/* Recipient */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Recipient</Label>
                <p className="text-sm text-muted-foreground">{selectedNotification.recipient}</p>
              </div>

              {/* Action URL */}
              {selectedNotification.action_url && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Action URL</Label>
                  <a
                    href={selectedNotification.action_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 underline break-all"
                  >
                    {selectedNotification.action_url}
                  </a>
                </div>
              )}

              {/* Read Status */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Status</Label>
                <div className="flex items-center gap-2">
                  {selectedNotification.is_read ? (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3" />
                      Read
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Bell className="h-3 w-3" />
                      Unread
                    </Badge>
                  )}
                  {selectedNotification.read_at && (
                    <span className="text-xs text-muted-foreground">
                      Read {timeAgo(selectedNotification.read_at)}
                    </span>
                  )}
                </div>
              </div>

              {/* Legacy expiration date */}
              {selectedNotification.expires_at && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Legacy Expiration Date</Label>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(selectedNotification.expires_at)}
                    </p>
                  </div>
                </>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={onCloseModal}>
              Close
            </Button>
            <div className="flex gap-2">
              {selectedNotification && !selectedNotification.is_read && (
                <Button
                  variant="secondary"
                  onClick={() => {
                    onMarkAsRead(selectedNotification.notification_id);
                    onCloseModal();
                  }}
                >
                  Mark as Read
                </Button>
              )}
              {selectedNotification && (
                <Button
                  onClick={() => {
                    onOpenEditNotification(selectedNotification);
                    onCloseModal();
                  }}
                >
                  Edit Notification
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default NotificationsModals;