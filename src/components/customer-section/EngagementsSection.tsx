import React from 'react';
import { Handshake, PlusCircle, ChevronDown, Eye, Edit } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { BadgeRenderer, DateFormatter } from './types';

interface EngagementsSectionProps {
  expanded: boolean;
  onToggle: () => void;
  items: any[];
  loading: boolean;
  error: boolean;
  onOpenList: () => void;
  onOpenCreate: () => void;
  onOpenDetails?: (engagement: any) => void;
  onEdit?: (engagement: any) => void;
  renderEngagementTypeBadge: BadgeRenderer;
  renderEngagementStatusBadge: BadgeRenderer;
  formatDate: DateFormatter;
}

const EngagementsSection: React.FC<EngagementsSectionProps> = ({
  expanded,
  onToggle,
  items,
  loading,
  error,
  onOpenList,
  onOpenCreate,
  onOpenDetails,
  onEdit,
  renderEngagementTypeBadge,
  renderEngagementStatusBadge,
  formatDate,
}) => {


  return (
    <div className="space-y-2">
      <div
        className="flex items-center justify-between cursor-pointer group transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-md"
        onClick={onToggle}
      >
        <div className="flex items-center text-sm font-medium">
          <Handshake className="h-4 w-4 mr-2 text-indigo-500" />
          <span>Engagements</span>
          <Badge variant="outline" className="ml-2">
            {items.length}
          </Badge>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100">
          {expanded ? <ChevronDown className="h-4 w-4 rotate-180" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>

      {expanded && (
        <div className="pl-6 space-y-3">
          {loading ? (
            <div className="flex justify-center py-4">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : error ? (
            <div className="text-sm text-red-500 text-center py-4">
              Failed to load engagements
            </div>
          ) : items.length === 0 ? (
            <div className="text-sm text-muted-foreground text-center py-4">
              No engagements found
            </div>
          ) : (
            <>
              {items.slice(0, 2).map((item) => (
                <div
                  key={item.engagement_id}
                  className="border rounded-md p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 hover:shadow-sm"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">{item.subject}</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {item.client_type}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-gray-100"
                          onClick={() => onOpenDetails && onOpenDetails(item)}
                          title="View Details"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>

                        {onEdit && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-gray-100"
                            onClick={() => onEdit(item)}
                            title="Edit Engagement"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    {renderEngagementTypeBadge(item.engagement_type)}
                    <span className="text-xs text-muted-foreground">
                      {item.created_by || "Unknown"}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Created {formatDate(item.created_at)}
                  </div>
                  {item.follow_up_required && item.follow_up_date && (
                    <div className="text-xs text-blue-600 mt-1">
                      Follow-up: {formatDate(item.follow_up_date)}
                    </div>
                  )}
                  {item.set_reminder && item.reminder_time && (
                    <div className="text-xs text-orange-600 mt-1">
                      Reminder: {formatDate(item.reminder_time)}
                    </div>
                  )}
                </div>
              ))}
              {items.length > 2 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs justify-center items-center flex gap-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  onClick={onOpenList}
                >
                  <span>View all engagements ({items.length})</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              )}
            </>
          )}
          <Button
            variant="outline"
            size="sm"
            className="w-full text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 flex gap-1"
            onClick={onOpenCreate}
          >
            <PlusCircle className="h-3 w-3" />
            <span>Add engagement</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default EngagementsSection;