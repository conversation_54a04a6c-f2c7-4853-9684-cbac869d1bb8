import React from 'react';
import { StickyNote, PlusCircle, ChevronDown, Pin, Eye, Edit } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { BadgeRenderer, DateFormatter } from './types';

interface NotesSectionProps {
  expanded: boolean;
  onToggle: () => void;
  items: any[];
  loading: boolean;
  error: boolean;
  onOpenList: () => void;
  onOpenCreate: () => void;
  onOpenDetails?: (note: any) => void;
  onEdit?: (note: any) => void;
  renderNoteTypeBadge: BadgeRenderer;
  onTogglePin: (noteId: string, currentPinStatus: boolean) => void;
  timeAgo: DateFormatter;
}

const NotesSection: React.FC<NotesSectionProps> = ({
  expanded,
  onToggle,
  items,
  loading,
  error,
  onOpenList,
  onOpenCreate,
  onOpenDetails,
  onEdit,
  renderNoteTypeBadge,
  onTogglePin,
  timeAgo,
}) => {


  // Sort items to show pinned notes first
  const sortedItems = React.useMemo(() => {
    return [...items].sort((a, b) => {
      if (a.is_pinned && !b.is_pinned) return -1;
      if (!a.is_pinned && b.is_pinned) return 1;
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
  }, [items]);

  return (
    <div className="space-y-2">
      <div
        className="flex items-center justify-between cursor-pointer group transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-md"
        onClick={onToggle}
      >
        <div className="flex items-center text-sm font-medium">
          <StickyNote className="h-4 w-4 mr-2 text-amber-500" />
          <span>Notes</span>
          <Badge variant="outline" className="ml-2">
            {items.length}
          </Badge>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100">
          {expanded ? <ChevronDown className="h-4 w-4 rotate-180" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>

      {expanded && (
        <div className="pl-6 space-y-3">
          {loading ? (
            <div className="flex justify-center py-4">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : error ? (
            <div className="text-sm text-red-500 text-center py-4">
              Failed to load notes
            </div>
          ) : items.length === 0 ? (
            <div className="text-sm text-muted-foreground text-center py-4">
              No notes found
            </div>
          ) : (
            <>
              {sortedItems.slice(0, 2).map((item) => (
                <div key={item.note_id} className="border rounded-md p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <h4 className="font-medium text-sm truncate">{item.title}</h4>
                      {item.is_pinned && (
                        <Pin className="h-3 w-3 text-amber-500" />
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 hover:bg-gray-100"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTogglePin(item.note_id, item.is_pinned);
                        }}
                        title={item.is_pinned ? "Unpin note" : "Pin note"}
                      >
                        <Pin className={`h-3 w-3 ${item.is_pinned ? 'text-amber-500' : 'text-gray-400'}`} />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 hover:bg-gray-100"
                        onClick={() => onOpenDetails && onOpenDetails(item)}
                        title="View Details"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>

                      {onEdit && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-gray-100"
                          onClick={() => onEdit(item)}
                          title="Edit Note"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-2">
                    {renderNoteTypeBadge(item.note_type)}
                    <span className="text-xs text-muted-foreground">
                      {item.is_private && (
                        <span className="inline-flex items-center gap-1 text-red-600">
                          <span>Private</span>
                        </span>
                      )}
                    </span>
                  </div>

                  <div className="text-xs text-muted-foreground mb-2">
                    {item.content && item.content.length > 80
                      ? `${item.content.substring(0, 80)}...`
                      : item.content || 'No content'}
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>{timeAgo(item.created_at)}</span>
                    {item.tags && (
                      <span className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-xs">
                        {item.tags.split(',')[0].trim()}
                        {item.tags.split(',').length > 1 && ' +'}
                      </span>
                    )}
                  </div>
                </div>
              ))}
              {items.length > 2 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs justify-center items-center flex gap-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  onClick={onOpenList}
                >
                  <span>View all notes ({items.length})</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              )}
            </>
          )}
          <Button
            variant="outline"
            size="sm"
            className="w-full text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 flex gap-1"
            onClick={onOpenCreate}
          >
            <PlusCircle className="h-3 w-3" />
            <span>Add note</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotesSection;