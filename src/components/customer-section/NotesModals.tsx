import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle} from "@/components/ui/sheet";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import {
  StickyNote,
  Calendar,
  Clock,
  User,
  Tag,
  AlertCircle,
  Star,
  Edit2,
  Trash2,
  Pin,
  PinOff,
  Eye,
  EyeOff,
  Lock
} from "lucide-react";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";

interface NoteFormData {
  // New API fields
  client_type: string;
  note_type: string;
  title: string;
  content: string;
  is_private: boolean;
  is_pinned: boolean;
  tags: string;
  follow_up_required: boolean;
  follow_up_date: string;
  set_reminder: boolean;
  reminder_time: string;
  created_by: string;
  customer?: string;
  prospect?: number;
  sale?: string;

  // Legacy fields for backward compatibility
  reminder_date: string;
  is_active: boolean;
}

interface NotesModalsProps {
  activeModal: string | null;
  onCloseModal: () => void;
  onOpenModal: (modalName: string) => void;
  noteForm: NoteFormData;
  onNoteFormChange: (field: keyof NoteFormData, value: string | boolean) => void;
  formErrors: Record<string, string>;
  successMessage: string;
  editingNote: any;
  onOpenEditNote: (note: any) => void;
  onCreateNote: () => void;
  onUpdateNote: () => void;
  onDeleteNote: (noteId: string, createdBy: string) => void;
  onToggleNotePin: (noteId: string, currentPinStatus: boolean) => void;
  noteItems: any[];
  currentUser: any;
  isCreating: boolean;
  isDeleting: boolean;
  renderNoteTypeBadge: (type: string) => JSX.Element;
  formatDate: (dateString: string) => string;
  timeAgo: (dateString: string) => string;
  selectedNote?: any;
}

const NotesModals: React.FC<NotesModalsProps> = ({
  activeModal,
  onCloseModal,
  onOpenModal,
  noteForm,
  onNoteFormChange,
  formErrors,
  successMessage,
  editingNote,
  onOpenEditNote,
  onCreateNote,
  onUpdateNote,
  onDeleteNote,
  onToggleNotePin,
  noteItems,
  currentUser,
  isCreating,
  isDeleting,
  renderNoteTypeBadge,
  formatDate,
  timeAgo,
  selectedNote,
}) => {
  const getNoteTypeIcon = (type: string) => {
    const icons = {
      General: StickyNote,
      Important: AlertCircle,
      Reminder: Calendar,
      "Follow-up": Star,
      Internal: User,
      "Customer Facing": Eye,
    };
    const IconComponent = icons[type as keyof typeof icons] || StickyNote;
    return <IconComponent className="h-4 w-4" />;
  };

  const formatDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return "N/A";
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canEditNote = (note: any) => {
    return currentUser?.id === note.created_by;
  };

  const canDeleteNote = (note: any) => {
    return currentUser?.id === note.created_by;
  };

  const sortedNotes = [...noteItems].sort((a, b) => {
    // First sort by pinned status (pinned notes first)
    if (a.is_pinned && !b.is_pinned) return -1;
    if (!a.is_pinned && b.is_pinned) return 1;
    
    // Then sort by creation date (newest first)
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });

  return (
    <>
      {/* Notes List Modal */}
      <Sheet open={activeModal === "notes"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[600px] sm:max-w-[600px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <StickyNote className="h-5 w-5" />
              All Notes
            </SheetTitle>
            <SheetDescription>
              View and manage all customer notes
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-between items-center my-4">
            <p className="text-sm text-muted-foreground">
              {noteItems.length} note{noteItems.length !== 1 ? 's' : ''} found
            </p>
            <Button onClick={() => onOpenModal("createNote")} size="sm">
              Add New Note
            </Button>
          </div>

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4">
              {sortedNotes.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center text-muted-foreground">
                      <StickyNote className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No notes found</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => onOpenModal("createNote")}
                      >
                        Create First Note
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                sortedNotes.map((note) => (
                  <Card key={note.note_id} className={`relative ${note.is_pinned ? 'ring-2 ring-yellow-200 bg-yellow-50/50' : ''}`}>
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getNoteTypeIcon(note.note_type)}
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <CardTitle className="text-sm flex items-center gap-2">
                                {note.title}
                                {note.is_pinned && (
                                  <Pin className="h-3 w-3 text-yellow-600" />
                                )}
                                {note.is_private && (
                                  <EyeOff className="h-3 w-3 text-gray-600" />
                                )}
                              </CardTitle>
                            </div>
                            <CardDescription className="text-xs">
                              {note.note_type} • {timeAgo(note.created_at)}
                              {note.reminder_date && (
                                <span className="flex items-center gap-1 mt-1">
                                  <Calendar className="h-3 w-3" />
                                  Reminder: {formatDate(note.reminder_date)}
                                </span>
                              )}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {renderNoteTypeBadge(note.note_type)}
                          {note.is_private && (
                            <Badge variant="secondary" className="text-xs">
                              Private
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                        {note.content}
                      </p>
                      
                      {note.tags && (
                        <div className="flex items-center gap-1 mb-3">
                          <Tag className="h-3 w-3 text-muted-foreground" />
                          <div className="flex flex-wrap gap-1">
                            {note.tags.split(',').map((tag:any, index:any) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag.trim()}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          {note.created_by_display && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {note.created_by_display}
                            </span>
                          )}
                          {note.updated_at !== note.created_at && (
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Updated {timeAgo(note.updated_at)}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {canEditNote(note) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onToggleNotePin(note.note_id, note.is_pinned)}
                              className="h-7 w-7 p-0"
                              title={note.is_pinned ? "Unpin note" : "Pin note"}
                            >
                              {note.is_pinned ? (
                                <PinOff className="h-3 w-3" />
                              ) : (
                                <Pin className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                          
                          {canEditNote(note) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onOpenEditNote(note)}
                              className="h-7 w-7 p-0"
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          )}
                          
                          {canDeleteNote(note) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onDeleteNote(note.note_id, note.created_by)}
                              className="h-7 w-7 p-0 text-red-600 hover:text-red-700"
                              disabled={isDeleting}
                            >
                              {isDeleting ? (
                                <SpinnerTemp />
                              ) : (
                                <Trash2 className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Create Note Modal */}
      <Sheet open={activeModal === "createNote"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[500px] sm:max-w-[500px]">
          <SheetHeader>
            <SheetTitle>Create New Note</SheetTitle>
            <SheetDescription>
              Add a new note for this customer
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {formErrors.submit && (
            <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
              <AlertDescription>{formErrors.submit}</AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="note_type">Note Type *</Label>
                <Select
                  value={noteForm.note_type}
                  onValueChange={(value) => onNoteFormChange('note_type', value)}
                >
                  <SelectTrigger className={formErrors.note_type ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select note type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Important">Important</SelectItem>
                    <SelectItem value="Reminder">Reminder</SelectItem>
                    <SelectItem value="Follow-up">Follow-up</SelectItem>
                    <SelectItem value="Internal">Internal</SelectItem>
                    <SelectItem value="Customer Facing">Customer Facing</SelectItem>
                  </SelectContent>
                </Select>
                {formErrors.note_type && (
                  <p className="text-sm text-red-600">{formErrors.note_type}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={noteForm.title}
                  onChange={(e) => onNoteFormChange('title', e.target.value)}
                  placeholder="Enter note title"
                  className={formErrors.title ? "border-red-500" : ""}
                />
                {formErrors.title && (
                  <p className="text-sm text-red-600">{formErrors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Content *</Label>
                <Textarea
                  id="content"
                  value={noteForm.content}
                  onChange={(e) => onNoteFormChange('content', e.target.value)}
                  placeholder="Enter note content"
                  rows={4}
                  className={formErrors.content ? "border-red-500" : ""}
                />
                {formErrors.content && (
                  <p className="text-sm text-red-600">{formErrors.content}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={noteForm.tags}
                  onChange={(e) => onNoteFormChange('tags', e.target.value)}
                  placeholder="Enter tags separated by commas"
                />
                <p className="text-xs text-muted-foreground">
                  Use commas to separate multiple tags (e.g., urgent, follow-up, important)
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="follow_up_required"
                    checked={noteForm.follow_up_required}
                    onCheckedChange={(checked) => onNoteFormChange('follow_up_required', checked as boolean)}
                  />
                  <Label htmlFor="follow_up_required" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Follow-up Required
                  </Label>
                </div>

                {noteForm.follow_up_required && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="follow_up_date">Follow-up Date</Label>
                    <Input
                      id="follow_up_date"
                      type="date"
                      value={noteForm.follow_up_date}
                      onChange={(e) => onNoteFormChange('follow_up_date', e.target.value)}
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="set_reminder"
                    checked={noteForm.set_reminder}
                    onCheckedChange={(checked) => onNoteFormChange('set_reminder', checked as boolean)}
                  />
                  <Label htmlFor="set_reminder" className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Set Reminder
                  </Label>
                </div>

                {noteForm.set_reminder && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="reminder_time">Reminder Time</Label>
                    <Input
                      id="reminder_time"
                      type="datetime-local"
                      value={noteForm.reminder_time}
                      onChange={(e) => onNoteFormChange('reminder_time', e.target.value)}
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="reminder_date">Legacy Reminder Date</Label>
                <Input
                  id="reminder_date"
                  type="date"
                  value={noteForm.reminder_date}
                  onChange={(e) => onNoteFormChange('reminder_date', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  For backward compatibility only
                </p>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_private"
                    checked={noteForm.is_private}
                    onCheckedChange={(checked) => onNoteFormChange('is_private', checked as boolean)}
                  />
                  <Label htmlFor="is_private" className="flex items-center gap-2">
                    <EyeOff className="h-4 w-4" />
                    Private Note
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Private notes are only visible to you
                </p>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_pinned"
                    checked={noteForm.is_pinned}
                    onCheckedChange={(checked) => onNoteFormChange('is_pinned', checked as boolean)}
                  />
                  <Label htmlFor="is_pinned" className="flex items-center gap-2">
                    <Pin className="h-4 w-4" />
                    Pin Note
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Pinned notes appear at the top of the list
                </p>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_active"
                    checked={noteForm.is_active}
                    onCheckedChange={(checked) => onNoteFormChange('is_active', checked as boolean)}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Inactive notes are hidden from the main view
                </p>
              </div>
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Cancel
            </Button>
            <Button onClick={onCreateNote} disabled={isCreating}>
              {isCreating ? (
                <>
                  <SpinnerTemp />
                  Creating...
                </>
              ) : (
                'Create Note'
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Edit Note Modal */}
      <Sheet open={activeModal === "editNote"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[500px] sm:max-w-[500px]">
          <SheetHeader>
            <SheetTitle>Edit Note</SheetTitle>
            <SheetDescription>
              Update note details
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {formErrors.submit && (
            <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
              <AlertDescription>{formErrors.submit}</AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit_note_type">Note Type *</Label>
                <Select
                  value={noteForm.note_type}
                  onValueChange={(value) => onNoteFormChange('note_type', value)}
                >
                  <SelectTrigger className={formErrors.note_type ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select note type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Important">Important</SelectItem>
                    <SelectItem value="Reminder">Reminder</SelectItem>
                    <SelectItem value="Follow-up">Follow-up</SelectItem>
                    <SelectItem value="Internal">Internal</SelectItem>
                    <SelectItem value="Customer Facing">Customer Facing</SelectItem>
                  </SelectContent>
                </Select>
                {formErrors.note_type && (
                  <p className="text-sm text-red-600">{formErrors.note_type}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_title">Title *</Label>
                <Input
                  id="edit_title"
                  value={noteForm.title}
                  onChange={(e) => onNoteFormChange('title', e.target.value)}
                  placeholder="Enter note title"
                  className={formErrors.title ? "border-red-500" : ""}
                />
                {formErrors.title && (
                  <p className="text-sm text-red-600">{formErrors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_content">Content *</Label>
                <Textarea
                  id="edit_content"
                  value={noteForm.content}
                  onChange={(e) => onNoteFormChange('content', e.target.value)}
                  placeholder="Enter note content"
                  rows={4}
                  className={formErrors.content ? "border-red-500" : ""}
                />
                {formErrors.content && (
                  <p className="text-sm text-red-600">{formErrors.content}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_tags">Tags</Label>
                <Input
                  id="edit_tags"
                  value={noteForm.tags}
                  onChange={(e) => onNoteFormChange('tags', e.target.value)}
                  placeholder="Enter tags separated by commas"
                />
                <p className="text-xs text-muted-foreground">
                  Use commas to separate multiple tags (e.g., urgent, follow-up, important)
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_follow_up_required"
                    checked={noteForm.follow_up_required}
                    onCheckedChange={(checked) => onNoteFormChange('follow_up_required', checked as boolean)}
                  />
                  <Label htmlFor="edit_follow_up_required" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Follow-up Required
                  </Label>
                </div>

                {noteForm.follow_up_required && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="edit_follow_up_date">Follow-up Date</Label>
                    <Input
                      id="edit_follow_up_date"
                      type="date"
                      value={noteForm.follow_up_date}
                      onChange={(e) => onNoteFormChange('follow_up_date', e.target.value)}
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_set_reminder"
                    checked={noteForm.set_reminder}
                    onCheckedChange={(checked) => onNoteFormChange('set_reminder', checked as boolean)}
                  />
                  <Label htmlFor="edit_set_reminder" className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Set Reminder
                  </Label>
                </div>

                {noteForm.set_reminder && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="edit_reminder_time">Reminder Time</Label>
                    <Input
                      id="edit_reminder_time"
                      type="datetime-local"
                      value={noteForm.reminder_time}
                      onChange={(e) => onNoteFormChange('reminder_time', e.target.value)}
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_reminder_date">Legacy Reminder Date</Label>
                <Input
                  id="edit_reminder_date"
                  type="date"
                  value={noteForm.reminder_date}
                  onChange={(e) => onNoteFormChange('reminder_date', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  For backward compatibility only
                </p>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_is_private"
                    checked={noteForm.is_private}
                    onCheckedChange={(checked) => onNoteFormChange('is_private', checked as boolean)}
                  />
                  <Label htmlFor="edit_is_private" className="flex items-center gap-2">
                    <EyeOff className="h-4 w-4" />
                    Private Note
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Private notes are only visible to you
                </p>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_is_pinned"
                    checked={noteForm.is_pinned}
                    onCheckedChange={(checked) => onNoteFormChange('is_pinned', checked as boolean)}
                  />
                  <Label htmlFor="edit_is_pinned" className="flex items-center gap-2">
                    <Pin className="h-4 w-4" />
                    Pin Note
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Pinned notes appear at the top of the list
                </p>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_is_active"
                    checked={noteForm.is_active}
                    onCheckedChange={(checked) => onNoteFormChange('is_active', checked as boolean)}
                  />
                  <Label htmlFor="edit_is_active">Active</Label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Inactive notes are hidden from the main view
                </p>
              </div>

              {editingNote && (
                <div className="mt-4 pt-4 border-t">
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>Created: {formatDateTime(editingNote.created_at)}</p>
                    {editingNote.updated_at !== editingNote.created_at && (
                      <p>Last updated: {formatDateTime(editingNote.updated_at)}</p>
                    )}
                    {editingNote.created_by_display && (
                      <p>Created by: {editingNote.created_by_display}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Cancel
            </Button>
            <Button onClick={onUpdateNote} disabled={isCreating}>
              {isCreating ? (
                <>
                  <SpinnerTemp />
                  Updating...
                </>
              ) : (
                'Update Note'
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Note Details Modal */}
      <Dialog open={activeModal === "noteDetails"} onOpenChange={onCloseModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <StickyNote className="h-5 w-5" />
              Note Details
            </DialogTitle>
          </DialogHeader>

          {selectedNote && (
            <div className="space-y-6">
              {/* Header with title and badges */}
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">{selectedNote.title}</h3>
                  <div className="flex items-center gap-2">
                    {renderNoteTypeBadge(selectedNote.note_type)}
                    {selectedNote.is_pinned && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Pin className="h-3 w-3" />
                        Pinned
                      </Badge>
                    )}
                    {selectedNote.is_private && (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Lock className="h-3 w-3" />
                        Private
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                  <p>Created {timeAgo(selectedNote.created_at)}</p>
                  {selectedNote.created_by && (
                    <p>by {selectedNote.created_by}</p>
                  )}
                </div>
              </div>

              <Separator />

              {/* Content */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Content</Label>
                <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">{selectedNote.content}</p>
                </div>
              </div>

              {/* Tags */}
              {selectedNote.tags && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Tags</Label>
                  <div className="flex flex-wrap gap-1">
                    {selectedNote.tags.split(',').map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Follow-up and Reminder Info */}
              {(selectedNote.follow_up_required || selectedNote.set_reminder) && (
                <>
                  <Separator />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedNote.follow_up_required && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Follow-up Required
                        </Label>
                        {selectedNote.follow_up_date && (
                          <p className="text-sm text-muted-foreground">
                            {formatDate(selectedNote.follow_up_date)}
                          </p>
                        )}
                      </div>
                    )}

                    {selectedNote.set_reminder && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Reminder Set
                        </Label>
                        {selectedNote.reminder_time && (
                          <p className="text-sm text-muted-foreground">
                            {formatDate(selectedNote.reminder_time)}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Legacy reminder date */}
              {selectedNote.reminder_date && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Legacy Reminder Date</Label>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(selectedNote.reminder_date)}
                    </p>
                  </div>
                </>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={onCloseModal}>
              Close
            </Button>
            {selectedNote && (
              <Button
                onClick={() => {
                  onOpenEditNote(selectedNote);
                  onCloseModal();
                }}
              >
                Edit Note
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default NotesModals;