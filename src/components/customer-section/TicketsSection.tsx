import React from 'react';
import { Ticket, PlusCircle, ChevronDown, MessageSquare, Paperclip } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { BadgeRenderer, DateFormatter, TicketItem } from './types';

interface TicketsSectionProps {
  expanded: boolean;
  onToggle: () => void;
  items: TicketItem[];
  loading: boolean;
  error: boolean;
  onOpenList: () => void;
  onOpenCreate: () => void;
  onOpenTicket: (ticket: TicketItem) => void;
  renderPriorityBadge: BadgeRenderer;
  renderStatusBadge: BadgeRenderer;
  timeAgo: DateFormatter;
}

const TicketsSection: React.FC<TicketsSectionProps> = ({
  expanded,
  onToggle,
  items,
  loading,
  error,
  onOpenList,
  onOpenCreate,
  onOpenTicket,
  renderPriorityBadge,
  renderStatusBadge,
  timeAgo,
}) => {
  const visibleItems = items.filter((t) => t.status !== 'closed');
  const renderTicketItem = (ticket: TicketItem) => (
    <div
      key={ticket.id}
      className="border rounded-md p-3 hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-gray-800 dark:hover:border-gray-600 transition-all duration-200 group cursor-pointer hover:shadow-sm"
      onClick={() => onOpenTicket(ticket)}
      title="Click to view ticket details"
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2 flex-1">
          <h4 className="font-medium text-sm truncate">{ticket.title}</h4>
          <Badge variant="outline" className="text-xs">
            {ticket.ticket_id}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          {renderStatusBadge(ticket.status)}
        </div>
      </div>
      
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {ticket.category_name && (
            <Badge variant="secondary" className="text-xs">
              {ticket.category_name}
            </Badge>
          )}
          {renderPriorityBadge(ticket.priority)}
        </div>
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          {ticket.messages.length > 0 && (
            <div className="flex items-center gap-1">
              <MessageSquare className="h-3 w-3" />
              <span>{ticket.messages.length}</span>
            </div>
          )}
          {ticket.attachments.length > 0 && (
            <div className="flex items-center gap-1">
              <Paperclip className="h-3 w-3" />
              <span>{ticket.attachments.length}</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>Created {timeAgo(ticket.created_at)}</span>
        {ticket.customer_name && (
          <span className="truncate max-w-[120px]">{ticket.customer_name}</span>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-2">
      <div
        className="flex items-center justify-between cursor-pointer group transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-md"
        onClick={onToggle}
      >
        <div className="flex items-center text-sm font-medium">
          <Ticket className="h-4 w-4 mr-2 text-blue-500" />
          <span>Tickets</span>
          <Badge variant="outline" className="ml-2">
            {loading ? "..." : items.filter((t) => t.status !== 'closed').length}
          </Badge>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100">
          {expanded ? <ChevronDown className="h-4 w-4 rotate-180" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>
      
      {expanded && (
        <div className="pl-6 space-y-3">
          {loading ? (
            <div className="flex justify-center py-4">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : error ? (
            <div className="text-sm text-red-500 text-center py-4">
              Failed to load tickets
            </div>
          ) : items.filter((t) => t.status !== 'closed').length === 0 ? (
            <div className="text-sm text-muted-foreground text-center py-4">
              No tickets found
            </div>
          ) : (
            <>
              {visibleItems.slice(0, 3).map(renderTicketItem)}
              {visibleItems.length > 3 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full text-xs justify-center items-center flex gap-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  onClick={onOpenList}
                >
                  <span>View all tickets ({visibleItems.length})</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              )}
            </>
          )}
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 flex gap-1"
            onClick={onOpenCreate}
          >
            <PlusCircle className="h-3 w-3" />
            <span>Create ticket</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default TicketsSection;
