import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import CustomerSalesTab from "../tabs/CustomerSalesTab";
import CustomerInstallmentsTab from "../tabs/CustomerInstallmentsTab";
import CustomerSiteVisitsTab from "../tabs/CustomerSiteVisitsTab";
import CustomerBookingsTab from "../tabs/CustomerBookingsTab";

interface CustomerTabNavigationProps {
  customerId: string;
  customerPhone?: string;
  customerName?: string;
  customerEmail?: string;
}

const CustomerTabNavigation = ({ customerId, customerPhone, customerName, customerEmail }: CustomerTabNavigationProps) => {
  return (
    <Tabs defaultValue="overview" className="w-full">
      {/* Desktop Tabs - Single Row */}
      <div className="hidden md:block mb-4">
        <TabsList className="grid grid-cols-4 w-full bg-primary text-white h-12">
          <TabsTrigger value="overview" className="text-sm font-medium px-2">
            Sales
          </TabsTrigger>
          <TabsTrigger value="activities" className="text-sm font-medium px-2">
            Installments
          </TabsTrigger>
          <TabsTrigger value="site-visits" className="text-sm font-medium px-2">
            Site Visits
          </TabsTrigger>
          <TabsTrigger value="bookings" className="text-sm font-medium px-2">
            Bookings
          </TabsTrigger>
        </TabsList>
      </div>

      {/* Mobile Tabs - Scrollable */}
      <div className="md:hidden mb-4">
        <TabsList className="inline-flex h-12 bg-primary text-white rounded-lg p-1 overflow-x-auto scrollbar-hide min-w-full">
          <div className="flex space-x-1 min-w-max">
            <TabsTrigger value="overview" className="whitespace-nowrap text-sm font-medium px-4 py-2">
              Sales
            </TabsTrigger>
            <TabsTrigger value="activities" className="whitespace-nowrap text-sm font-medium px-4 py-2">
              Installments
            </TabsTrigger>
            <TabsTrigger value="site-visits" className="whitespace-nowrap text-sm font-medium px-4 py-2">
              Site Visits
            </TabsTrigger>
            <TabsTrigger value="bookings" className="whitespace-nowrap text-sm font-medium px-4 py-2">
              Bookings
            </TabsTrigger>
          </div>
        </TabsList>
      </div>
     
      <TabsContent value="overview">
        <CustomerSalesTab customerId={customerId} />
      </TabsContent>
     
      <TabsContent value="activities">
        <CustomerInstallmentsTab customerId={customerId} />
      </TabsContent>
      
      <TabsContent value="site-visits">
        <CustomerSiteVisitsTab
          customerId={customerId}
          customerPhone={customerPhone}
          customerName={customerName}
          customerEmail={customerEmail}
        />
      </TabsContent>
      
      <TabsContent value="bookings">
        <CustomerBookingsTab customerId={customerId} customerPhone={customerPhone} />
      </TabsContent>
    </Tabs>
  );
};

export default CustomerTabNavigation;