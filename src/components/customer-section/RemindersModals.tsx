import React from 'react';
import { X, Clock, AlertCircle, CheckCircle, Timer, Edit2, Trash2, Calendar, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { ReminderFormData, ReminderType, ReminderPriority, ReminderStatus, BadgeRenderer, DateFormatter } from './types';

interface RemindersModalsProps {
  activeModal: string | null;
  onCloseModal: () => void;
  onOpenModal: (modal: string) => void;
  reminderForm: ReminderFormData;
  onReminderFormChange: (field: keyof ReminderFormData, value: any) => void;
  formErrors: Record<string, string>;
  successMessage: string;
  editingReminder: any;
  onOpenEditReminder: (reminder: any) => void;
  onCreateReminder: () => void;
  onUpdateReminder: () => void;
  onDeleteReminder: (reminderId: string, createdBy: string) => void;
  onCompleteReminder: (reminderId: string) => void;
  onSnoozeReminder: (reminderId: string, snoozeData: any) => void;
  reminderItems: any[];
  currentUser: any;
  isCreating: boolean;
  isDeleting: boolean;
  renderReminderTypeBadge: BadgeRenderer;
  renderPriorityBadge: BadgeRenderer;
  renderStatusBadge: BadgeRenderer;
  formatDate: DateFormatter;
  timeAgo: DateFormatter;
  selectedReminder?: any;
}

const RemindersModals: React.FC<RemindersModalsProps> = ({
  activeModal,
  onCloseModal,
  onOpenModal,
  reminderForm,
  onReminderFormChange,
  formErrors,
  successMessage,
  editingReminder,
  onOpenEditReminder,
  onCreateReminder,
  onUpdateReminder,
  onDeleteReminder,
  onCompleteReminder,
  onSnoozeReminder,
  reminderItems,
  currentUser,
  isCreating,
  isDeleting,
  renderReminderTypeBadge,
  renderPriorityBadge,
  renderStatusBadge,
  formatDate,
  timeAgo,
  selectedReminder,
}) => {
  const [snoozeMinutes, setSnoozeMinutes] = React.useState(15);

  const canEditReminder = (reminder: any) => {
    // Check multiple possible field names for created_by
    const createdBy = reminder.created_by || reminder.created_by_id || reminder.createdBy;

    // TEMPORARY FIX: Since API doesn't return creator info, allow all users to edit
    // TODO: Fix API to return creator information for proper permission checks
    if (!createdBy) {
      return true; // Allow all users to edit when creator info is missing
    }

    return currentUser?.id === createdBy;
  };

  const canDeleteReminder = (reminder: any) => {
    // Check multiple possible field names for created_by
    const createdBy = reminder.created_by || reminder.created_by_id || reminder.createdBy;

    // TEMPORARY FIX: Since API doesn't return creator info, allow all users to delete
    // TODO: Fix API to return creator information for proper permission checks
    if (!createdBy) {
      return true; // Allow all users to delete when creator info is missing
    }

    return currentUser?.id === createdBy;
  };

  const reminderTypes: ReminderType[] = ["Follow-up Call", "Payment Reminder", "Document Collection", "Site Visit", "Meeting", "Email", "SMS", "General"];
  const priorities: ReminderPriority[] = ["Low", "Normal", "High", "Urgent"];

  // Sort reminders to show overdue first, then by remind_at
  const sortedReminders = React.useMemo(() => {
    return [...reminderItems].sort((a, b) => {
      if (a.is_overdue && !b.is_overdue) return -1;
      if (!a.is_overdue && b.is_overdue) return 1;
      if (a.is_due && !b.is_due) return -1;
      if (!a.is_due && b.is_due) return 1;
      return new Date(a.remind_at).getTime() - new Date(b.remind_at).getTime();
    });
  }, [reminderItems]);

  if (!activeModal) return null;

  // Log modal rendering
  console.log("🎭 RemindersModals rendering with activeModal:", activeModal);
  console.log("📊 Reminder items count:", reminderItems.length);
  console.log("📝 Current reminder form:", reminderForm);

  return (
    <>
      {/* Reminders List Modal */}
      {activeModal === "reminders" && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold">All Reminders ({reminderItems.length})</h2>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onOpenModal("createReminder")}
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Add Reminder
                </Button>
                <Button variant="ghost" size="sm" onClick={onCloseModal}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {sortedReminders.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No reminders found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {sortedReminders.map((reminder) => (
                    <div
                      key={reminder.reminder_id}
                      className={`p-4 border rounded-lg ${reminder.is_overdue
                        ? 'border-red-200 bg-red-50'
                        : reminder.is_due
                          ? 'border-yellow-200 bg-yellow-50'
                          : 'border-gray-200 bg-white'
                        }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            {renderReminderTypeBadge(reminder.reminder_type)}
                            {renderPriorityBadge(reminder.priority)}
                            {renderStatusBadge(reminder.status)}
                            {reminder.is_overdue && (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            {reminder.is_due && !reminder.is_overdue && (
                              <Clock className="h-4 w-4 text-yellow-500" />
                            )}
                          </div>
                          <h3 className="font-medium text-lg mb-1">{reminder.title}</h3>
                          {reminder.description && (
                            <p className="text-gray-600 text-sm mb-2">{reminder.description}</p>
                          )}
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>Due: {formatDate(reminder.remind_at)}</span>
                            <span>Created: {timeAgo(reminder.created_at)}</span>
                            {reminder.advance_notice_minutes > 0 && (
                              <span>Notice: {reminder.advance_notice_minutes}min before</span>
                            )}
                          </div>
                          {reminder.tags && (
                            <div className="flex items-center gap-1 mt-2">
                              <Tag className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-500">{reminder.tags}</span>
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-2">
                          {/* Status Action Buttons */}
                          <div className="flex items-center gap-1">
                            {reminder.status === 'Active' && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => onCompleteReminder(reminder.reminder_id)}
                                  className="h-8 px-3 text-xs bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                                  title="Complete Reminder"
                                >
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Complete
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => onSnoozeReminder(reminder.reminder_id, { minutes: snoozeMinutes })}
                                  className="h-8 px-3 text-xs bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                                  title="Snooze Reminder"
                                >
                                  <Timer className="h-3 w-3 mr-1" />
                                  Snooze
                                </Button>
                              </>
                            )}
                          </div>

                          {/* Management Buttons */}
                          <div className="flex items-center gap-1 border-l pl-2 ml-1">
                            {canEditReminder(reminder) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onOpenEditReminder(reminder)}
                                className="h-7 w-7 p-0 hover:bg-gray-100"
                                title="Edit Reminder"
                              >
                                <Edit2 className="h-3 w-3" />
                              </Button>
                            )}

                            {canDeleteReminder(reminder) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDeleteReminder(reminder.reminder_id, reminder.created_by || reminder.created_by_id || currentUser?.id || "")}
                                className="h-7 w-7 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                disabled={isDeleting}
                                title="Delete Reminder"
                              >
                                {isDeleting ? (
                                  <SpinnerTemp />
                                ) : (
                                  <Trash2 className="h-3 w-3" />
                                )}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Reminder Modal */}
      {activeModal === "createReminder" && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold">Create New Reminder</h2>
              <Button variant="ghost" size="sm" onClick={onCloseModal}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reminder_type">Reminder Type</Label>
                    <Select
                      value={reminderForm.reminder_type}
                      onValueChange={(value) => onReminderFormChange("reminder_type", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {reminderTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.reminder_type && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.reminder_type}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select
                      value={reminderForm.priority}
                      onValueChange={(value) => onReminderFormChange("priority", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        {priorities.map((priority) => (
                          <SelectItem key={priority} value={priority}>
                            {priority}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.priority && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.priority}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={reminderForm.title}
                    onChange={(e) => onReminderFormChange("title", e.target.value)}
                    placeholder="Enter reminder title"
                  />
                  {formErrors.title && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.title}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={reminderForm.description}
                    onChange={(e) => onReminderFormChange("description", e.target.value)}
                    placeholder="Enter reminder description"
                    rows={3}
                  />
                  {formErrors.description && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reminder_date">Reminder Date *</Label>
                    <Input
                      id="reminder_date"
                      type="date"
                      value={reminderForm.reminder_date}
                      onChange={(e) => onReminderFormChange("reminder_date", e.target.value)}
                    />
                    {formErrors.reminder_date && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.reminder_date}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="reminder_time">Reminder Time</Label>
                    <Input
                      id="reminder_time"
                      type="time"
                      value={reminderForm.reminder_time}
                      onChange={(e) => onReminderFormChange("reminder_time", e.target.value)}
                    />
                    {formErrors.reminder_time && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.reminder_time}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="reminder_notes">Reminder Notes</Label>
                  <Textarea
                    id="reminder_notes"
                    value={reminderForm.reminder_notes}
                    onChange={(e) => onReminderFormChange("reminder_notes", e.target.value)}
                    placeholder="Enter reminder notes"
                    rows={2}
                  />
                  {formErrors.reminder_notes && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.reminder_notes}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="action_url">Action URL</Label>
                  <Input
                    id="action_url"
                    type="url"
                    value={reminderForm.action_url}
                    onChange={(e) => onReminderFormChange("action_url", e.target.value)}
                    placeholder="https://example.com"
                  />
                  {formErrors.action_url && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.action_url}</p>
                  )}
                </div>



                {successMessage && (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                    {successMessage}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-end gap-2 p-6 border-t">
              <Button variant="outline" onClick={onCloseModal}>
                Cancel
              </Button>
              <Button onClick={onCreateReminder} disabled={isCreating}>
                {isCreating ? (
                  <>
                    <SpinnerTemp />
                    Creating...
                  </>
                ) : (
                  <>
                    <Clock className="h-4 w-4 mr-2" />
                    Create Reminder
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Reminder Modal */}
      {activeModal === "editReminder" && editingReminder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold">Edit Reminder</h2>
              <Button variant="ghost" size="sm" onClick={onCloseModal}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit_reminder_type">Reminder Type</Label>
                    <Select
                      value={reminderForm.reminder_type}
                      onValueChange={(value) => onReminderFormChange("reminder_type", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {reminderTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="edit_priority">Priority</Label>
                    <Select
                      value={reminderForm.priority}
                      onValueChange={(value) => onReminderFormChange("priority", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        {priorities.map((priority) => (
                          <SelectItem key={priority} value={priority}>
                            {priority}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="edit_title">Title</Label>
                  <Input
                    id="edit_title"
                    value={reminderForm.title}
                    onChange={(e) => onReminderFormChange("title", e.target.value)}
                    placeholder="Enter reminder title"
                  />
                </div>

                <div>
                  <Label htmlFor="edit_description">Description</Label>
                  <Textarea
                    id="edit_description"
                    value={reminderForm.description}
                    onChange={(e) => onReminderFormChange("description", e.target.value)}
                    placeholder="Enter reminder description"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit_reminder_date">Reminder Date *</Label>
                    <Input
                      id="edit_reminder_date"
                      type="date"
                      value={reminderForm.reminder_date}
                      onChange={(e) => onReminderFormChange("reminder_date", e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="edit_reminder_time">Reminder Time</Label>
                    <Input
                      id="edit_reminder_time"
                      type="time"
                      value={reminderForm.reminder_time}
                      onChange={(e) => onReminderFormChange("reminder_time", e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="edit_reminder_notes">Reminder Notes</Label>
                  <Textarea
                    id="edit_reminder_notes"
                    value={reminderForm.reminder_notes}
                    onChange={(e) => onReminderFormChange("reminder_notes", e.target.value)}
                    placeholder="Enter reminder notes"
                    rows={2}
                  />
                </div>

                <div>
                  <Label htmlFor="edit_action_url">Action URL</Label>
                  <Input
                    id="edit_action_url"
                    type="url"
                    value={reminderForm.action_url}
                    onChange={(e) => onReminderFormChange("action_url", e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>



                {successMessage && (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                    {successMessage}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-end gap-2 p-6 border-t">
              <Button variant="outline" onClick={onCloseModal}>
                Cancel
              </Button>
              <Button onClick={onUpdateReminder} disabled={isCreating}>
                {isCreating ? (
                  <>
                    <SpinnerTemp />
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit2 className="h-4 w-4 mr-2" />
                    Update Reminder
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Reminder Details Modal */}
      <Dialog open={activeModal === "reminderDetails"} onOpenChange={onCloseModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Reminder Details
            </DialogTitle>
          </DialogHeader>

          {selectedReminder && (
            <div className="space-y-6">
              {/* Header with title and badges */}
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">{selectedReminder.title}</h3>
                  <div className="flex items-center gap-2">
                    {renderReminderTypeBadge(selectedReminder.reminder_type)}
                    {renderPriorityBadge(selectedReminder.priority)}
                    {renderStatusBadge(selectedReminder.status)}
                    {selectedReminder.is_overdue && (
                      <Badge variant="destructive" className="flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        Overdue
                      </Badge>
                    )}
                    {selectedReminder.is_due && !selectedReminder.is_overdue && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Due Soon
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                  <p>Created {timeAgo(selectedReminder.created_at)}</p>
                  {selectedReminder.created_by && (
                    <p>by {selectedReminder.created_by}</p>
                  )}
                </div>
              </div>

              <Separator />

              {/* Description */}
              {selectedReminder.description && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Description</Label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                    <p className="text-sm whitespace-pre-wrap">{selectedReminder.description}</p>
                  </div>
                </div>
              )}

              {/* Reminder Notes */}
              {selectedReminder.reminder_notes && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Reminder Notes</Label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                    <p className="text-sm whitespace-pre-wrap">{selectedReminder.reminder_notes}</p>
                  </div>
                </div>
              )}

              {/* Reminder Date and Time */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedReminder.reminder_date && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Reminder Date</Label>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(selectedReminder.reminder_date)}
                    </p>
                  </div>
                )}

                {selectedReminder.reminder_time && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Reminder Time</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedReminder.reminder_time}
                    </p>
                  </div>
                )}
              </div>

              {/* Action URL */}
              {selectedReminder.action_url && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Action URL</Label>
                  <a
                    href={selectedReminder.action_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 underline break-all"
                  >
                    {selectedReminder.action_url}
                  </a>
                </div>
              )}

              {/* Client Information */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Client Information</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-muted-foreground">Client Type</p>
                    <p className="text-sm">{selectedReminder.client_type || "N/A"}</p>
                  </div>
                  {selectedReminder.customer && (
                    <div>
                      <p className="text-xs text-muted-foreground">Customer</p>
                      <p className="text-sm">{selectedReminder.customer}</p>
                    </div>
                  )}
                  {selectedReminder.prospect && (
                    <div>
                      <p className="text-xs text-muted-foreground">Prospect</p>
                      <p className="text-sm">{selectedReminder.prospect}</p>
                    </div>
                  )}
                  {selectedReminder.sale && (
                    <div>
                      <p className="text-xs text-muted-foreground">Sale</p>
                      <p className="text-sm">{selectedReminder.sale}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Legacy fields */}
              {(selectedReminder.remind_at || selectedReminder.tags) && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Legacy Information</Label>
                    {selectedReminder.remind_at && (
                      <div>
                        <p className="text-xs text-muted-foreground">Legacy Remind At</p>
                        <p className="text-sm">{formatDate(selectedReminder.remind_at)}</p>
                      </div>
                    )}
                    {selectedReminder.tags && (
                      <div>
                        <p className="text-xs text-muted-foreground">Tags</p>
                        <p className="text-sm">{selectedReminder.tags}</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={onCloseModal}>
              Close
            </Button>
            {selectedReminder && (
              <Button
                onClick={() => {
                  onOpenEditReminder(selectedReminder);
                  onCloseModal();
                }}
              >
                Edit Reminder
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default RemindersModals;
