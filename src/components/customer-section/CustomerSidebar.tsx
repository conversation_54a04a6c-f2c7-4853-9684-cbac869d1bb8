import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  useGetCustomerTicketsQuery,
  useGetCustomerTicketDetailsQuery,
  useCreateCustomerTicketMutation,
  useUpdateCustomerTicketMutation,
  useDeleteCustomerTicketMutation,
  useGetCustomerTicketCategoriesQuery,
  useGetCustomerTicketSourcesQuery,
  useGetCustomerTicketMessagesQuery,
  useCreateCustomerTicketMessageMutation,
  useUpdateCustomerTicketMessageMutation,
  useDeleteCustomerTicketMessageMutation,
  useGetCustomerTicketAttachmentsQuery,
  useCreateCustomerTicketAttachmentMutation,
  useDeleteCustomerTicketAttachmentMutation,
  useGetCustomerTicketActionLogsQuery,
  useCreateCustomerTicketActionLogMutation,
  useCreateCustomerTicketCategoryMutation,
  useUpdateCustomerTicketCategoryMutation,
  useDeleteCustomerTicketCategoryMutation,
  useCreateCustomerTicketSourceMutation,
  useUpdateCustomerTicketSourceMutation,
  useDeleteCustomerTicketSourceMutation,
} from "@/redux/slices/customerTicketsApiSlice";
import { useCreateTicketMutation } from "@/redux/slices/tickets";
import {
  useGetEngagementsQuery,
  useCreateEngagementMutation,
  usePartialUpdateEngagementMutation,
  useDeleteEngagementMutation,
} from "@/redux/slices/engagementsApiSlice";
import {
  useGetNotificationsQuery,
  useGetUnreadNotificationsQuery,
  useCreateNotificationMutation,
  usePartialUpdateNotificationMutation,
  useDeleteNotificationMutation,
  useMarkNotificationAsReadMutation,
} from "@/redux/slices/notificationsApiSlice";
import {
  useGetNotesQuery,
  useCreateNoteMutation,
  usePartialUpdateNoteMutation,
  useDeleteNoteMutation,
  useToggleNotePinMutation,
} from "@/redux/slices/notesApiSlice";
import {
  useGetFlagsQuery,
  useCreateFlagMutation,
  usePartialUpdateFlagMutation,
  useDeleteFlagMutation,
} from "@/redux/slices/flagsApiSlice";
import TicketsModals from "./TicketsModals";
import {
  CustomerSidebarProps,
  EngagementFormData,
  ExpandedSections,
  BadgeRenderer,
  TicketFormData,
  TicketItem,
  TicketMessage,
  TicketAttachment,
  TicketCategory,
  TicketSource,
  TicketMessageFormData,
  TicketCategoryFormData,
  TicketSourceFormData,
  TicketActionLogFormData,
  TicketActionLog,
  DateFormatter,
} from "./types";
import EngagementsModals from "./EngagementsModals";

// Section components
import RemindersSection from "./RemindersSection";
import TicketsSection from "./TicketsSection";
import NotesSection from "./NotesSection";
import FlagsSection from "./FlagsSection";
import EngagementsSection from "./EngagementsSection";
import NotificationsSection from "./NotificationsSection";

import NotificationsModals from "./NotificationsModals";
import FlagsModals from "./FlagsModal";
import NotesModals from "./NotesModals";
import RemindersModals from "./RemindersModals";
import {
  useAddReminderMutation,
  useDeleteReminderMutation,
  useGetRemindersQuery,
  useUpdateReminderMutation,
} from "@/redux/slices/reminderApiSlice";

const CustomerSidebar: React.FC<CustomerSidebarProps> = ({
  className,
  customerNo,
  leadfileNo,
  salesCardCustomerId,
  entityType = "customer",
  entityId = "",
  currentUser,
  initialExpandedSection,
}) => {
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] =
    React.useState<ExpandedSections>(() => {
      // Default state (collapsed)
      const initialState: ExpandedSections = {
        reminders: true,
        tickets: true,
        notes: true,
        flags: true,
        engagements: true,
        notifications: true,
      };

      // Expand all sections if initialExpandedSection is provided
      if (initialExpandedSection) {
        Object.keys(initialState).forEach((key) => {
          initialState[key as keyof ExpandedSections] = true;
        });
      } else {
        // Default expanded sections
        initialState.reminders = true;
        initialState.tickets = true;
      }

      return initialState;
    });

  const [activeModal, setActiveModal] = React.useState<string | null>(null);
  const [editingTicket, setEditingTicket] = React.useState<TicketItem | null>(
    null
  );
  const [selectedTicket, setSelectedTicket] = React.useState<TicketItem | null>(
    null
  );
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>(
    {}
  );
  const [successMessage, setSuccessMessage] = React.useState<string>("");
  const [editingEngagement, setEditingEngagement] = React.useState<any>(null);
  const [editingFlag, setEditingFlag] = React.useState<any>(null);
  const [editingNotification, setEditingNotification] =
    React.useState<any>(null);
  const [editingReminder, setEditingReminder] = React.useState<any>(null);
  const [editingNote, setEditingNote] = React.useState<any>(null);

  // State for viewing details
  const [selectedNote, setSelectedNote] = React.useState<any>(null);
  const [selectedNotification, setSelectedNotification] = React.useState<any>(null);
  const [selectedReminder, setSelectedReminder] = React.useState<any>(null);

  // Tickets form state
  const [ticketForm, setTicketForm] = React.useState<TicketFormData & any>({
    title: "",
    description: "",
    customer: entityType === "customer" ? customerNo || "" : "",
    user: currentUser?.id || "", // Set current user as default assignee
    category: undefined,
    prospect: entityType === "prospect" ? customerNo : undefined,
    priority: "medium",
    status: "open",
    source: undefined,
    actions: [], // Initialize as empty array
    attachments: [], // Initialize as empty array
    messages: [], // Initialize as empty array
    // Reminder UI state for create flow
    add_reminder: "false",
    reminder_type: "General",
    reminder_date: "",
    reminder_time: "",
    reminder_priority: "Normal",
  });

  // File attachment state for tickets
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);

  // Ticket message form state
  const [messageForm, setMessageForm] = React.useState<TicketMessageFormData>({
    ticket: 0,
    sender: currentUser?.id || "",
    message: "",
  });

  // Category form state
  const [categoryForm, setCategoryForm] =
    React.useState<TicketCategoryFormData>({
      name: "",
      description: "",
    });

  // Source form state
  const [sourceForm, setSourceForm] = React.useState<TicketSourceFormData>({
    name: "",
    description: "",
  });

  // Action log form state
  const [actionLogForm, setActionLogForm] =
    React.useState<TicketActionLogFormData>({
      ticket: 0,
      action: "created",
      performed_by: undefined,
      comment: "",
    });

  // Additional state for editing
  const [editingCategory, setEditingCategory] =
    React.useState<TicketCategory | null>(null);
  const [editingSource, setEditingSource] = React.useState<TicketSource | null>(
    null
  );
  const [editingMessage, setEditingMessage] =
    React.useState<TicketMessage | null>(null);

  // Form state for engagements
  const [engagementForm, setEngagementForm] =
    React.useState<EngagementFormData>({
      client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
      engagement_type: "",
      subject: "",
      description: "",
      follow_up_required: false,
      follow_up_date: "",
      set_reminder: false,
      reminder_time: "",
      created_by: currentUser?.id || "",
      customer: entityType === "customer" ? customerNo : undefined,
      prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
      sale: entityType === "leadfile" ? leadfileNo : undefined,

      // Legacy fields for backward compatibility
      entity_type: entityType,
      entity_id: entityId,
      customer_no: entityType === "customer" ? customerNo : "",
      lead_file_no: entityType === "leadfile" ? leadfileNo : "",
      client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    });
  // Form state for notes - Updated to match new API structure
  const [noteForm, setNoteForm] = React.useState({
    client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
    note_type: "General",
    title: "",
    content: "",
    is_private: false,
    is_pinned: false,
    tags: "",
    follow_up_required: false,
    follow_up_date: "",
    set_reminder: false,
    reminder_time: "",
    created_by: currentUser?.id || "",
    customer: entityType === "customer" ? customerNo : undefined,
    prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
    sale: entityType === "leadfile" ? leadfileNo : undefined,

    // Legacy fields for backward compatibility
    reminder_date: "",
    is_active: true,
  });

  // Form state for flags - Updated to match new API structure
  const [flagForm, setFlagForm] = React.useState({
    client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
    flag_reason: "",
    description: "",
    is_resolved: false,
    resolution_date: "",
    resolution_notes: "",
    follow_up_required: false,
    follow_up_date: "",
    set_reminder: false,
    reminder_time: "",
    created_by: currentUser?.id || "",
    customer: entityType === "customer" ? customerNo : undefined,
    prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
    sale: entityType === "leadfile" ? leadfileNo : undefined,

    // Legacy fields for backward compatibility
    entity_type: entityType,
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? leadfileNo : "",
    client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
  });
  // Form state for notifications - Updated to match new API structure
  const [notificationForm, setNotificationForm] = React.useState({
    client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
    notification_type: "Info",
    priority: "Normal",
    title: "",
    message: "",
    is_read: false,
    read_at: "",
    action_url: "",
    recipient: "",
    sender: "",
    created_by: currentUser?.id || "",
    customer: entityType === "customer" ? customerNo : undefined,
    prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
    sale: entityType === "leadfile" ? leadfileNo : undefined,

    // Legacy fields for backward compatibility
    entity_type: entityType,
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? customerNo : "",
    client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    expires_at: "",
    is_active: true,
  });

  // Form state for reminders - Updated to match new API structure
  const [reminderForm, setReminderForm] = React.useState({
    // New API fields
    client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
    reminder_type: "General",
    reminder_date: "",
    reminder_time: "",
    priority: "Normal",
    reminder_notes: "",
    action_url: "",
    title: "",
    description: "",
    status: "Active",
    created_by: currentUser?.id || "",
    customer: entityType === "customer" ? customerNo : undefined,
    prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
    sale: entityType === "leadfile" ? leadfileNo : undefined,

    // Legacy fields for backward compatibility
    entity_type: entityType,
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? leadfileNo : "",
    client_status:
      entityType === "customer"
        ? "Customer"
        : entityType === "prospect"
        ? "Prospect"
        : "Lead File",
    remind_at: "",
    advance_notice_minutes: 15,
    repeat_pattern: "None",
    tags: "",
    is_active: true,
  });

  // API calls for tickets - Fetch all tickets and filter client-side
  // The API doesn't support customer/prospect filtering, so we fetch all and filter locally
  const {
    data: allTicketsData,
    isLoading: ticketsLoading,
    isError: ticketsError,
    refetch: refetchTickets,
  } = useGetCustomerTicketsQuery({});

  // Client-side filtering based on entity type
  const ticketsData = React.useMemo(() => {
    if (!allTicketsData?.results) return allTicketsData;

    const filteredResults = allTicketsData.results.filter((ticket: any) => {
      // For customer cards: filter by customer field
      if (customerNo && ticket.customer === customerNo) {
        return true;
      }
      // For sales/lead cards: filter by sales field (should match lead_file_no)
      if (leadfileNo && ticket.sales === leadfileNo) {
        return true;
      }
      return false;
    });

    console.log("Filtered Tickets:", filteredResults);

    return {
      ...allTicketsData,
      results: filteredResults,
      count: filteredResults.length,
      total_data: filteredResults.length,
    };
  }, [allTicketsData, customerNo, leadfileNo, entityType]);

  const [createTicket, { isLoading: isCreatingTicket }] =
    useCreateTicketMutation();
  const [updateTicket, { isLoading: isUpdatingTicket }] =
    useUpdateCustomerTicketMutation();
  const [deleteTicket, { isLoading: isDeletingTicket }] =
    useDeleteCustomerTicketMutation();

  // API calls for ticket categories and sources
  const { data: categoriesData } = useGetCustomerTicketCategoriesQuery({});
  const { data: sourcesData } = useGetCustomerTicketSourcesQuery({});

  // API calls for ticket messages
  const {
    data: ticketMessagesData,
    isLoading: messagesLoading,
    refetch: refetchMessages,
  } = useGetCustomerTicketMessagesQuery(
    { ticket: selectedTicket?.id || 0 },
    { skip: !selectedTicket }
  );

  const [createMessage, { isLoading: isSendingMessage }] =
    useCreateCustomerTicketMessageMutation();
  const [updateMessage] = useUpdateCustomerTicketMessageMutation();
  const [deleteMessage] = useDeleteCustomerTicketMessageMutation();

  // API calls for ticket attachments
  const [createAttachment, { isLoading: isUploadingAttachment }] =
    useCreateCustomerTicketAttachmentMutation();
  const [deleteAttachment] = useDeleteCustomerTicketAttachmentMutation();

  // API calls for action logs
  const { data: actionLogsData, refetch: refetchActionLogs } =
    useGetCustomerTicketActionLogsQuery(
      { ticket: selectedTicket?.id || 0 },
      { skip: !selectedTicket }
    );
  const [createActionLog] = useCreateCustomerTicketActionLogMutation();

  // API calls for categories CRUD
  const [createCategory, { isLoading: isCreatingCategory }] =
    useCreateCustomerTicketCategoryMutation();
  const [updateCategory] = useUpdateCustomerTicketCategoryMutation();
  const [deleteCategory] = useDeleteCustomerTicketCategoryMutation();

  // API calls for sources CRUD
  const [createSource, { isLoading: isCreatingSource }] =
    useCreateCustomerTicketSourceMutation();
  const [updateSource] = useUpdateCustomerTicketSourceMutation();
  const [deleteSource] = useDeleteCustomerTicketSourceMutation();

  // API calls for ticket attachments
  const { data: ticketAttachmentsData, isLoading: attachmentsLoading } =
    useGetCustomerTicketAttachmentsQuery(
      { ticket: selectedTicket?.id || 0 },
      { skip: !selectedTicket }
    );

  // API calls for engagements - Build parameters based on entity type and available data
  let engagementsParams: any = {};

  if (entityType === "customer" && customerNo) {
    engagementsParams = {
      client_type: "Customer",
      customer: customerNo
    };
  } else if (entityType === "leadfile" && leadfileNo) {
    engagementsParams = {
      client_type: "Sale",
      sale: leadfileNo
    };
  } else if (entityType === "prospect" && customerNo) {
    engagementsParams = {
      client_type: "Prospect",
      prospect: customerNo
    };
  }

  const {
    data: engagementsData,
    isLoading: engagementsLoading,
    isError: engagementsError,
    error: engagementsErrorDetails,
    refetch: refetchEngagements,
  } = useGetEngagementsQuery(engagementsParams, {
    skip: Object.keys(engagementsParams).length === 0,
    refetchOnMountOrArgChange: true, // Force fresh data on mount
  });

  const [createEngagement, { isLoading: isCreatingEngagement }] =
    useCreateEngagementMutation();
  const [updateEngagement] = usePartialUpdateEngagementMutation();
  const [deleteEngagement, { isLoading: isDeletingEngagement }] =
    useDeleteEngagementMutation();

  // API calls for flags - Build parameters based on entity type and available data
  let flagsParams: any = {};

  if (entityType === "customer" && customerNo) {
    flagsParams = {
      client_type: "Customer",
      customer: customerNo
    };
  } else if (entityType === "leadfile" && leadfileNo) {
    flagsParams = {
      client_type: "Sale",
      sale: leadfileNo
    };
  } else if (entityType === "prospect" && customerNo) {
    flagsParams = {
      client_type: "Prospect",
      prospect: customerNo
    };
  }

  const {
    data: flagsData,
    isLoading: flagsLoading,
    isError: flagsError,
    error: flagsErrorDetails,
    refetch: refetchFlags,
  } = useGetFlagsQuery(flagsParams, {
    skip: Object.keys(flagsParams).length === 0,
    refetchOnMountOrArgChange: true,
  });

  const [createFlag, { isLoading: isCreatingFlag }] = useCreateFlagMutation();
  const [updateFlag] = usePartialUpdateFlagMutation();
  const [deleteFlag, { isLoading: isDeletingFlag }] = useDeleteFlagMutation();

  // API calls for notifications - Build parameters based on entity type and available data
  let notificationsParams: any = {};

  if (entityType === "customer" && customerNo) {
    notificationsParams = {
      client_type: "Customer",
      customer: customerNo
    };
  } else if (entityType === "leadfile" && leadfileNo) {
    notificationsParams = {
      client_type: "Sale",
      sale: leadfileNo
    };
  } else if (entityType === "prospect" && customerNo) {
    notificationsParams = {
      client_type: "Prospect",
      prospect: customerNo
    };
  }

  const {
    data: notificationsData,
    isLoading: notificationsLoading,
    isError: notificationsError,
    error: notificationsErrorDetails,
    refetch: refetchNotifications,
  } = useGetNotificationsQuery(notificationsParams, {
    skip: Object.keys(notificationsParams).length === 0,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: unreadNotificationsData,
    isLoading: unreadNotificationsLoading,
    isError: unreadNotificationsError,
    refetch: refetchUnreadNotifications,
  } = useGetUnreadNotificationsQuery(notificationsParams, {
    skip: Object.keys(notificationsParams).length === 0,
    refetchOnMountOrArgChange: true,
  });

  const [createNotification, { isLoading: isCreatingNotification }] =
    useCreateNotificationMutation();
  const [updateNotification] = usePartialUpdateNotificationMutation();
  const [deleteNotification, { isLoading: isDeletingNotification }] =
    useDeleteNotificationMutation();
  const [markAsRead] = useMarkNotificationAsReadMutation();

  // API calls for reminders - Build parameters based on entity type and available data
  let remindersParams: any = {};

  if (entityType === "customer" && customerNo) {
    remindersParams = {
      client_type: "Customer",
      customer: customerNo
    };
  } else if (entityType === "leadfile" && leadfileNo) {
    remindersParams = {
      client_type: "Sale",
      sale: leadfileNo
    };
  } else if (entityType === "prospect" && customerNo) {
    remindersParams = {
      client_type: "Prospect",
      prospect: customerNo
    };
  }

  const {
    data: remindersData,
    isLoading: remindersLoading,
    isError: remindersError,
    error: remindersErrorDetails,
    refetch: refetchReminders,
  } = useGetRemindersQuery(remindersParams, {
    skip: Object.keys(remindersParams).length === 0,
    refetchOnMountOrArgChange: true,
  });

  // Note: Overdue and upcoming endpoints don't exist in the API, so we'll calculate these from the main reminders data

  const [createReminder, { isLoading: isCreatingReminder }] =
    useAddReminderMutation();
  const [updateReminder] = useUpdateReminderMutation();
  const [deleteReminder, { isLoading: isDeletingReminder }] =
    useDeleteReminderMutation();

  // Debug effect to track reminder data changes
  React.useEffect(() => {
    console.log("🔄 Reminder data changed:");
    console.log("- Reminders Data:", remindersData);
    console.log("- Loading:", remindersLoading);
    console.log("- Error:", remindersError);
    console.log("- Items count:", reminderItems?.length || 0);
  }, [remindersData, remindersLoading, remindersError]);

  // Debug effect to track component mount
  React.useEffect(() => {
    console.log("🚀 CustomerSidebar mounted with reminder system");
    console.log("- Entity Type:", entityType);
    console.log("- Customer No:", customerNo);
    console.log("- Lead File No:", leadfileNo);
  }, []);

  // API calls for notes - Build parameters based on entity type and available data
  let notesParams: any = {};

  if (entityType === "customer" && customerNo) {
    notesParams = {
      client_type: "Customer",
      customer: customerNo
    };
  } else if (entityType === "leadfile" && leadfileNo) {
    notesParams = {
      client_type: "Sale",
      sale: leadfileNo
    };
  } else if (entityType === "prospect" && customerNo) {
    notesParams = {
      client_type: "Prospect",
      prospect: customerNo
    };
  }

  const {
    data: notesData,
    isLoading: notesLoading,
    isError: notesError,
    error: notesErrorDetails,
    refetch: refetchNotes,
  } = useGetNotesQuery(notesParams, {
    skip: Object.keys(notesParams).length === 0,
    refetchOnMountOrArgChange: true,
  });

  const [createNote, { isLoading: isCreatingNote }] = useCreateNoteMutation();
  const [updateNote] = usePartialUpdateNoteMutation();
  const [deleteNote, { isLoading: isDeletingNote }] = useDeleteNoteMutation();
  const [toggleNotePin] = useToggleNotePinMutation();

  // Ticket handlers
  const handleTicketFormChange = (
    field: keyof TicketFormData,
    value: string | number
  ) => {
    setTicketForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateTicketForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!ticketForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!ticketForm.description.trim()) {
      errors.description = "Description is required";
    }

    // Check if at least one entity is specified (customer, prospect, or sales)
    const hasCustomer = ticketForm.customer.trim();
    const hasProspect = ticketForm.prospect?.trim();
    const hasSales = entityType === "leadfile" && leadfileNo;

    if (!hasCustomer && !hasProspect && !hasSales) {
      errors.customer = "Customer, prospect, or sales entity is required";
    }

    if (!ticketForm.user?.trim()) {
      errors.user = "User assignment is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateTicket = async () => {
    if (!validateTicketForm()) return;

    try {
      // Prepare ticket data using FormData for file upload support
      const formData = new FormData();

      // Add basic fields
      formData.append("title", ticketForm.title);
      formData.append("description", ticketForm.description);
      formData.append("user", ticketForm.user || currentUser?.id || "");
      formData.append("priority", ticketForm.priority);
      formData.append("status", ticketForm.status);

      // Add optional fields if they exist
      if (ticketForm.category) {
        formData.append("category", ticketForm.category.toString());
      }
      if (ticketForm.source) {
        formData.append("source", ticketForm.source.toString());
      }

      // Add entity-specific fields based on entity type
      if (entityType === "customer") {
        formData.append("customer", customerNo || "");
      } else if (entityType === "leadfile") {
        formData.append("sales", leadfileNo || "");
      } else if (entityType === "prospect") {
        formData.append("prospect", customerNo || "");
      }

      // Add reminder fields if enabled
      if ((ticketForm as any).add_reminder === 'true') {
        formData.append('add_reminder', 'true');
        formData.append('reminder_type', (ticketForm as any).reminder_type || 'General');
        formData.append('reminder_date', (ticketForm as any).reminder_date || '');
        // Combine date + time to match backend format YYYY-MM-DD HH:MM
        const rDate = (ticketForm as any).reminder_date;
        const rTime = (ticketForm as any).reminder_time;
        if (rDate && rTime) {
          formData.append('reminder_time', `${rDate} ${rTime}`);
        }
        formData.append('reminder_priority', (ticketForm as any).reminder_priority || 'Normal');
      }

      // Add file if selected
      if (selectedFile) {
        formData.append("file", selectedFile);
      }

      // Debug: Log the FormData contents
      console.log("=== Ticket Creation Debug ===");
      console.log("Entity Type:", entityType);
      console.log("Customer No:", customerNo);
      console.log("Lead File No:", leadfileNo);
      console.log("Current User:", currentUser);
      console.log("Ticket Form:", ticketForm);

      // Log FormData contents
      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      await createTicket(formData).unwrap();
      setSuccessMessage("Ticket created successfully!");
      refetchTickets();

      // Reset form and file
      setSelectedFile(null);

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create ticket - Full error:", error);
      console.error("Error data:", error?.data);
      console.error("Error status:", error?.status);
      console.error("Error message:", error?.message);

      let errorMessage = "Failed to create ticket. Please try again.";
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.data?.error) {
        errorMessage = error.data.error;
      } else if (error?.data) {
        errorMessage = JSON.stringify(error.data);
      }

      setFormErrors({
        submit: errorMessage,
      });
    }
  };

  const handleUpdateTicket = async () => {
    if (!validateTicketForm() || !editingTicket) return;

    try {
      // Prepare ticket data based on entity type
      const ticketData = {
        ...ticketForm,
        customer: entityType === "customer" ? customerNo || "" : "",
        sales: entityType === "leadfile" ? leadfileNo || "" : undefined,
        prospect: entityType === "prospect" ? customerNo : undefined,
      };

      await updateTicket({
        ticketId: editingTicket.id,
        data: ticketData,
      }).unwrap();
      setSuccessMessage("Ticket updated successfully!");
      refetchTickets();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update ticket:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to update ticket. Please try again.",
      });
    }
  };

  const handleDeleteTicket = async (ticketId: number) => {
    if (
      !confirm(
        "Are you sure you want to delete this ticket? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteTicket(ticketId).unwrap();
      refetchTickets();
      setSuccessMessage("Ticket deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete ticket:", error);
    }
  };

  const handleMessageFormChange = (
    field: keyof TicketMessageFormData,
    value: string | number
  ) => {
    setMessageForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSendMessage = async () => {
    if (!messageForm.message.trim() || !selectedTicket) return;

    try {
      await createMessage({
        ...messageForm,
        ticket: selectedTicket.id,
        sender: currentUser?.id || "",
      }).unwrap();
      setMessageForm((prev) => ({ ...prev, message: "" }));
      refetchMessages();
      setSuccessMessage("Message sent successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const openTicketDetails = (ticket: TicketItem) => {
    // Navigate to the ticket details page instead of opening modal
    navigate(`/ticketing/${ticket.id}`);
  };

  const openEditTicket = (ticket: TicketItem) => {
    setEditingTicket(ticket);
    setTicketForm({
      title: ticket.title,
      description: ticket.description,
      customer: ticket.customer,
      user: ticket.user || currentUser?.id || "",
      category: ticket.category,
      prospect: ticket.prospect,
      priority: ticket.priority,
      status: ticket.status,
      source: ticket.source,
      actions: ticket.actions || [],
      attachments: ticket.attachments || [],
      messages: ticket.messages || [],
    });
    openModal("editTicket");
  };

  const navigateToTicket = (ticketId: number) => {
    navigate(`/ticketing/${ticketId}`);
  };

  // Category handlers
  const handleCategoryFormChange = (
    field: keyof TicketCategoryFormData,
    value: string
  ) => {
    setCategoryForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCreateCategory = async () => {
    try {
      await createCategory(categoryForm).unwrap();
      setSuccessMessage("Category created successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to create category:", error);
      setFormErrors({
        submit:
          error?.data?.message ||
          "Failed to create category. Please try again.",
      });
    }
  };

  const handleUpdateCategory = async () => {
    if (!editingCategory) return;
    try {
      await updateCategory({
        categoryId: editingCategory.id,
        data: categoryForm,
      }).unwrap();
      setSuccessMessage("Category updated successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to update category:", error);
      setFormErrors({
        submit:
          error?.data?.message ||
          "Failed to update category. Please try again.",
      });
    }
  };

  const handleDeleteCategory = async (categoryId: number) => {
    if (!confirm("Are you sure you want to delete this category?")) return;
    try {
      await deleteCategory(categoryId).unwrap();
      setSuccessMessage("Category deleted successfully!");
      refetchTickets();
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete category:", error);
    }
  };

  // Source handlers
  const handleSourceFormChange = (
    field: keyof TicketSourceFormData,
    value: string
  ) => {
    setSourceForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCreateSource = async () => {
    try {
      await createSource(sourceForm).unwrap();
      setSuccessMessage("Source created successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to create source:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to create source. Please try again.",
      });
    }
  };

  const handleUpdateSource = async () => {
    if (!editingSource) return;
    try {
      await updateSource({
        sourceId: editingSource.id,
        data: sourceForm,
      }).unwrap();
      setSuccessMessage("Source updated successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to update source:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to update source. Please try again.",
      });
    }
  };

  const handleDeleteSource = async (sourceId: number) => {
    if (!confirm("Are you sure you want to delete this source?")) return;
    try {
      await deleteSource(sourceId).unwrap();
      setSuccessMessage("Source deleted successfully!");
      refetchTickets();
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete source:", error);
    }
  };

  // Message handlers
  const handleDeleteMessage = async (messageId: number) => {
    if (!confirm("Are you sure you want to delete this message?")) return;
    try {
      await deleteMessage(messageId).unwrap();
      refetchMessages();
      setSuccessMessage("Message deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete message:", error);
    }
  };

  const handleEditMessage = (message: TicketMessage) => {
    setEditingMessage(message);
    setMessageForm({
      ticket: message.ticket,
      sender: message.sender,
      message: message.message,
    });
    openModal("editMessage");
  };

  // Attachment handlers
  const handleUploadAttachment = async (file: File) => {
    if (!selectedTicket) return;

    const formData = new FormData();
    formData.append("ticket", selectedTicket.id.toString());
    formData.append("uploaded_by", currentUser?.id || "");
    formData.append("file", file);

    try {
      await createAttachment(formData).unwrap();
      setSuccessMessage("Attachment uploaded successfully!");
      // Refetch attachments
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to upload attachment:", error);
    }
  };

  const handleDeleteAttachment = async (attachmentId: number) => {
    if (!confirm("Are you sure you want to delete this attachment?")) return;
    try {
      await deleteAttachment(attachmentId).unwrap();
      setSuccessMessage("Attachment deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete attachment:", error);
    }
  };

  // Action log handlers
  const handleCreateActionLog = async (action: string, comment?: string) => {
    if (!selectedTicket) return;

    const actionLogData = {
      ticket: selectedTicket.id,
      action: action as TicketActionLogFormData["action"],
      performed_by: parseInt(currentUser?.id || "0"),
      comment: comment || "",
    };

    try {
      await createActionLog(actionLogData).unwrap();
      refetchActionLogs();
    } catch (error) {
      console.error("Failed to create action log:", error);
    }
  };

  const toggleSection = (section: keyof ExpandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const openModal = (modalName: string) => {
    console.log(`🔓 Opening modal: ${modalName}`);
    if (modalName.includes("reminder") || modalName.includes("Reminder")) {
      console.log("🎯 Reminder modal being opened");
      console.log("Current reminder form state:", reminderForm);
    }
    setActiveModal(modalName);
    setFormErrors({});
    setSuccessMessage("");
  };

  const closeModal = () => {
    console.log(`🔒 Closing modal: ${activeModal}`);
    if (
      activeModal &&
      (activeModal.includes("reminder") || activeModal.includes("Reminder"))
    ) {
      console.log("🎯 Reminder modal being closed");
    }
    setActiveModal(null);
    setEditingTicket(null);
    setSelectedTicket(null);
    setEditingEngagement(null);
    setEditingFlag(null);
    setEditingNotification(null);
    setEditingReminder(null);
    setEditingNote(null);
    resetForms();
  };

  const resetForms = () => {
    setTicketForm({
      title: "",
      description: "",
      customer: entityType === "customer" ? customerNo || "" : "",
      user: currentUser?.id || "",
      category: undefined,
      prospect:
        entityType === "prospect" ? customerNo : leadfileNo || undefined,
      priority: "medium",
      status: "open",
      source: undefined,
      actions: [], // Initialize as empty array
      attachments: [], // Initialize as empty array
      messages: [], // Initialize as empty array
    });

    setMessageForm({
      ticket: 0,
      sender: currentUser?.id || "",
      message: "",
    });

    setCategoryForm({
      name: "",
      description: "",
    });

    setSourceForm({
      name: "",
      description: "",
    });

    setActionLogForm({
      ticket: 0,
      action: "created",
      performed_by: undefined,
      comment: "",
    });

    setEngagementForm({
      client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
      engagement_type: "",
      subject: "",
      description: "",
      follow_up_required: false,
      follow_up_date: "",
      set_reminder: false,
      reminder_time: "",
      created_by: currentUser?.id || "",
      customer: entityType === "customer" ? customerNo : undefined,
      prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
      sale: entityType === "leadfile" ? leadfileNo : undefined,

      // Legacy fields for backward compatibility
      entity_type: entityType,
      entity_id: entityId,
      customer_no: entityType === "customer" ? customerNo : "",
      lead_file_no: entityType === "leadfile" ? leadfileNo : "",
      client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    });
    setNotificationForm({
      entity_type: entityType,
      entity_id: entityId,
      customer_no: entityType === "customer" ? customerNo : "",
      prospect_id: entityType === "prospect" ? customerNo : "",
      lead_file_no: entityType === "leadfile" ? customerNo : "",
      client_status:
        entityType === "customer"
          ? "Customer"
          : entityType === "prospect"
          ? "Prospect"
          : "Lead File",
      notification_type: "Info",
      priority: "Normal",
      title: "",
      message: "",
      recipient: "",
      sender: "",
      expires_at: "",
      action_url: "",
      is_active: true,
    });

    setFlagForm({
      client_type: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale",
      flag_reason: "",
      description: "",
      is_resolved: false,
      resolution_date: "",
      resolution_notes: "",
      follow_up_required: false,
      follow_up_date: "",
      set_reminder: false,
      reminder_time: "",
      created_by: currentUser?.id || "",
      customer: entityType === "customer" ? customerNo : undefined,
      prospect: entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined,
      sale: entityType === "leadfile" ? leadfileNo : undefined,

      // Legacy fields for backward compatibility
      entity_type: entityType,
      entity_id: entityId,
      customer_no: entityType === "customer" ? customerNo : "",
      prospect_id: entityType === "prospect" ? customerNo : "",
      lead_file_no: entityType === "leadfile" ? leadfileNo : "",
      client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    });

    setNoteForm({
      note_type: "General",
      title: "",
      content: "",
      is_private: false,
      is_pinned: false,
      tags: "",
      reminder_date: "",
      is_active: true,
    });

    setFormErrors({});
    setSuccessMessage("");
  };

  const validateEngagementForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!engagementForm.engagement_type) {
      errors.engagement_type = "Engagement type is required";
    }
    if (!engagementForm.subject.trim()) {
      errors.subject = "Subject is required";
    }
    if (!engagementForm.description.trim()) {
      errors.description = "Description is required";
    }
    if (!engagementForm.client_type) {
      errors.client_type = "Client type is required";
    }

    // Validate that the appropriate entity field is set based on client_type
    if (engagementForm.client_type === "Customer" && !engagementForm.customer) {
      errors.customer = "Customer is required for Customer client type";
    }
    if (engagementForm.client_type === "Prospect" && !engagementForm.prospect) {
      errors.prospect = "Prospect is required for Prospect client type";
    }
    if (engagementForm.client_type === "Sale" && !engagementForm.sale) {
      errors.sale = "Sale is required for Sale client type";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEngagementFormChange = (
    field: keyof EngagementFormData,
    value: string | boolean | number
  ) => {
    setEngagementForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const handleCreateEngagement = async () => {
    if (!validateEngagementForm()) return;
    console.log("Form data being sent:", engagementForm);

    try {
      // Process dates to ISO and drop empty date fields (match EngagementForm.tsx)
      const processed = { ...engagementForm } as any;
      if (processed.reminder_time) {
        processed.reminder_time = new Date(processed.reminder_time).toISOString();
      } else {
        delete processed.reminder_time;
      }
      if (processed.follow_up_date) {
        processed.follow_up_date = new Date(`${processed.follow_up_date}T00:00:00`).toISOString();
      } else {
        delete processed.follow_up_date;
      }

      // Format the data according to the new API structure
      const formattedData = {
        client_type: processed.client_type,
        engagement_type: processed.engagement_type,
        subject: processed.subject,
        description: processed.description,
        follow_up_required: processed.follow_up_required,
        follow_up_date: processed.follow_up_date,
        set_reminder: processed.set_reminder,
        reminder_time: processed.reminder_time,
        created_by: processed.created_by || currentUser?.id,
        // Set the appropriate entity field based on client_type
        customer: processed.client_type === "Customer" ? processed.customer : undefined,
        prospect: processed.client_type === "Prospect" ? processed.prospect : undefined,
        sale: processed.client_type === "Sale" ? processed.sale : undefined,
      } as any;

      // Remove undefined fields and empty strings
      Object.keys(formattedData).forEach((key) => {
        if ((formattedData as any)[key] === undefined || (formattedData as any)[key] === "") {
          delete (formattedData as any)[key];
        }
      });

      await createEngagement(formattedData).unwrap();
      setSuccessMessage("Engagement created successfully!");
      console.log("Engagement created:", formattedData);
      refetchEngagements();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create engagement:", error);
      setFormErrors({
        submit:
          error?.data?.message ||
          "Failed to create engagement. Please try again.",
      });
    }
  };

  const handleUpdateEngagement = async () => {
    if (!validateEngagementForm()) return;

    try {
      // Process dates to ISO and drop empty date fields (match EngagementForm.tsx)
      const processed = { ...engagementForm } as any;
      if (processed.reminder_time) {
        processed.reminder_time = new Date(processed.reminder_time).toISOString();
      } else {
        delete processed.reminder_time;
      }
      if (processed.follow_up_date) {
        processed.follow_up_date = new Date(`${processed.follow_up_date}T00:00:00`).toISOString();
      } else {
        delete processed.follow_up_date;
      }

      // Format the data according to the new API structure
      const formattedData = {
        client_type: processed.client_type,
        engagement_type: processed.engagement_type,
        subject: processed.subject,
        description: processed.description,
        follow_up_required: processed.follow_up_required,
        follow_up_date: processed.follow_up_date,
        set_reminder: processed.set_reminder,
        reminder_time: processed.reminder_time,
        created_by: processed.created_by || currentUser?.id,
        // Set the appropriate entity field based on client_type
        customer: processed.client_type === "Customer" ? processed.customer : undefined,
        prospect: processed.client_type === "Prospect" ? processed.prospect : undefined,
        sale: processed.client_type === "Sale" ? processed.sale : undefined,
      } as any;

      // Remove undefined fields and empty strings
      Object.keys(formattedData).forEach((key) => {
        if ((formattedData as any)[key] === undefined || (formattedData as any)[key] === "") {
          delete (formattedData as any)[key];
        }
      });

      // Use the existing slice structure: data with engagement_id inside
      await updateEngagement({
        ...formattedData,
        engagement_id: editingEngagement.engagement_id,
      }).unwrap();
      setSuccessMessage("Engagement updated successfully!");
      refetchEngagements();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update engagement:", error);
      setFormErrors({
        submit:
          error?.data?.message ||
          "Failed to update engagement. Please try again.",
      });
    }
  };

  const handleDeleteEngagement = async (
    engagementId: string,
    createdBy: string
  ) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete engagements that you created.");
      return;
    }

    if (
      !confirm(
        "Are you sure you want to delete this engagement? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteEngagement(engagementId).unwrap();
      refetchEngagements();
      setSuccessMessage("Engagement deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete engagement:", error);
    }
  };



  const openEditEngagement = (engagement: any) => {
    setEditingEngagement(engagement);
    setEngagementForm({
      client_type: engagement.client_type || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale"),
      engagement_type: engagement.engagement_type || "",
      subject: engagement.subject || "",
      description: engagement.description || "",
      follow_up_required: engagement.follow_up_required || false,
      follow_up_date: engagement.follow_up_date ? engagement.follow_up_date.split("T")[0] : "",
      set_reminder: engagement.set_reminder || false,
      reminder_time: engagement.reminder_time ? engagement.reminder_time.slice(0, 16) : "",
      created_by: engagement.created_by || currentUser?.id || "",
      customer: engagement.customer || (entityType === "customer" ? customerNo : undefined),
      prospect: engagement.prospect || (entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined),
      sale: engagement.sale || (entityType === "leadfile" ? leadfileNo : undefined),

      // Legacy fields for backward compatibility
      entity_type: engagement.entity_type || entityType,
      entity_id: engagement.entity_id || entityId,
      customer_no: engagement.customer_no || (entityType === "customer" ? customerNo : ""),
      lead_file_no: engagement.lead_file_no || (entityType === "leadfile" ? leadfileNo : ""),
      client_status: engagement.client_status || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File"),
    });
    openModal("editEngagement");
  };
  // Flag form validation
  const validateFlagForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!flagForm.flag_reason) {
      errors.flag_reason = "Flag reason is required";
    }
    if (!flagForm.description.trim()) {
      errors.description = "Description is required";
    }
    if (!flagForm.client_type) {
      errors.client_type = "Client type is required";
    }

    // Validate that the appropriate entity field is set based on client_type
    if (flagForm.client_type === "Customer" && !flagForm.customer) {
      errors.customer = "Customer is required for Customer client type";
    }
    if (flagForm.client_type === "Prospect" && !flagForm.prospect) {
      errors.prospect = "Prospect is required for Prospect client type";
    }
    if (flagForm.client_type === "Sale" && !flagForm.sale) {
      errors.sale = "Sale is required for Sale client type";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Flag form change handler
  const onFlagFormChange = (
    field: string,
    value: string | boolean | number
  ) => {
    setFlagForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // Create flag handler
  const onCreateFlag = async () => {
    if (!validateFlagForm()) return;

    try {
      // Format dates properly for API
      const formattedData = {
        ...flagForm,
        follow_up_date: formatDateForAPI(flagForm.follow_up_date),
        reminder_time: formatDateForAPI(flagForm.reminder_time, true),
        resolution_date: flagForm.is_resolved ? formatDateForAPI(flagForm.resolution_date) : "",
      };

      // Remove empty fields
      Object.keys(formattedData).forEach(key => {
        if ((formattedData as any)[key] === "" || (formattedData as any)[key] === undefined) {
          delete (formattedData as any)[key];
        }
      });

      await createFlag(formattedData).unwrap();
      setSuccessMessage("Flag created successfully!");
      refetchFlags();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create flag:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to create flag. Please try again.",
      });
    }
  };

  // Update flag handler
  const onUpdateFlag = async () => {
    if (!validateFlagForm()) return;

    try {
      // Only include fields that should be updated, exclude read-only and entity fields
      const updateData: any = {
        flag_reason: flagForm.flag_reason,
        description: flagForm.description,
        is_resolved: flagForm.is_resolved,
        follow_up_required: flagForm.follow_up_required,
        set_reminder: flagForm.set_reminder,
      };

      // Add optional fields only if they have values
      if (flagForm.follow_up_date) {
        updateData.follow_up_date = formatDateForAPI(flagForm.follow_up_date);
      }

      if (flagForm.reminder_time) {
        updateData.reminder_time = formatDateForAPI(flagForm.reminder_time, true);
      }

      if (flagForm.is_resolved && flagForm.resolution_date) {
        updateData.resolution_date = formatDateForAPI(flagForm.resolution_date);
      }

      if (flagForm.resolution_notes) {
        updateData.resolution_notes = flagForm.resolution_notes;
      }

      // Remove empty fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === "" || updateData[key] === undefined || updateData[key] === null) {
          delete updateData[key];
        }
      });

      await updateFlag({
        flagId: editingFlag?.flag_id || "",
        data: updateData,
      }).unwrap();
      setSuccessMessage("Flag updated successfully!");
      refetchFlags();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update flag:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to update flag. Please try again.",
      });
    }
  };

  // Delete flag handler
  const onDeleteFlag = async (flagId: string, createdBy: string) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete flags that you created.");
      return;
    }

    if (
      !confirm(
        "Are you sure you want to delete this flag? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteFlag(flagId).unwrap();
      refetchFlags();
      setSuccessMessage("Flag deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete flag:", error);
    }
  };

  // Resolve flag handler - Updated to use the new API structure
  const onResolveFlag = async (flagId: string, resolutionNotes: string) => {
    try {
      await updateFlag({
        flagId,
        data: {
          is_resolved: true,
          resolution_notes: resolutionNotes,
          resolution_date: new Date().toISOString(),
        },
      }).unwrap();
      refetchFlags();
      setSuccessMessage("Flag resolved successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to resolve flag:", error);
    }
  };

  // Helper function to format date for API
  const formatDateForAPI = (dateString: string, isDateTime: boolean = false) => {
    if (!dateString) return "";

    if (isDateTime) {
      // For datetime-local input, convert to ISO string
      return new Date(dateString).toISOString();
    } else {
      // For date input, add time and convert to ISO string
      return new Date(dateString + "T00:00:00").toISOString();
    }
  };

  // Open edit flag handler
  const onOpenEditFlag = (flag: any) => {
    setEditingFlag(flag);
    setFlagForm({
      client_type: flag.client_type || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale"),
      flag_reason: flag.flag_reason || "",
      description: flag.description || "",
      is_resolved: flag.is_resolved || false,
      resolution_date: flag.resolution_date ? flag.resolution_date.split("T")[0] : "",
      resolution_notes: flag.resolution_notes || "",
      follow_up_required: flag.follow_up_required || false,
      follow_up_date: flag.follow_up_date ? flag.follow_up_date.split("T")[0] : "",
      set_reminder: flag.set_reminder || false,
      reminder_time: flag.reminder_time ? flag.reminder_time.slice(0, 16) : "",
      created_by: flag.created_by || currentUser?.id || "",
      customer: flag.customer || (entityType === "customer" ? customerNo : undefined),
      prospect: flag.prospect || (entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined),
      sale: flag.sale || (entityType === "leadfile" ? leadfileNo : undefined),

      // Legacy fields for backward compatibility
      entity_type: flag.entity_type || entityType,
      entity_id: flag.entity_id || entityId,
      customer_no: flag.customer_no || "",
      prospect_id: flag.prospect_id || "",
      lead_file_no: flag.lead_file_no || "",
      client_status: flag.client_status || "",
    });
    openModal("editFlag");
  };

  const validateNotificationForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!notificationForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!notificationForm.message.trim()) {
      errors.message = "Message is required";
    }
    if (!notificationForm.recipient.trim()) {
      errors.recipient = "Recipient is required";
    }
    if (!notificationForm.client_type) {
      errors.client_type = "Client type is required";
    }

    // Validate that the appropriate entity field is set based on client_type
    if (notificationForm.client_type === "Customer" && !notificationForm.customer) {
      errors.customer = "Customer is required for Customer client type";
    }
    if (notificationForm.client_type === "Prospect" && !notificationForm.prospect) {
      errors.prospect = "Prospect is required for Prospect client type";
    }
    if (notificationForm.client_type === "Sale" && !notificationForm.sale) {
      errors.sale = "Sale is required for Sale client type";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const onNotificationFormChange = (
    field: string,
    value: string | boolean | number
  ) => {
    setNotificationForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const onReminderFormChange = (
    field: string,
    value: string | boolean | number
  ) => {
    console.log(`📝 Reminder form field changed: ${field} = ${value}`);

    setReminderForm((prev) => {
      const newForm = {
        ...prev,
        [field]: value,
      };
      console.log("Updated reminder form:", newForm);
      return newForm;
    });

    // Clear field-specific errors when user starts typing
    if (formErrors[field]) {
      console.log(`🧹 Clearing error for field: ${field}`);
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // notification handler
  const onCreateNotification = async () => {
    if (!validateNotificationForm()) return;

    try {
      // Format data properly for API
      const formattedData = {
        client_type: notificationForm.client_type,
        notification_type: notificationForm.notification_type,
        priority: notificationForm.priority,
        title: notificationForm.title,
        message: notificationForm.message,
        is_read: notificationForm.is_read,
        action_url: notificationForm.action_url || undefined,
        recipient: notificationForm.recipient,
        sender: notificationForm.sender || undefined,
        created_by: notificationForm.created_by || currentUser?.id,
        // Set the appropriate entity field based on client_type
        customer: notificationForm.client_type === "Customer" ? notificationForm.customer : undefined,
        prospect: notificationForm.client_type === "Prospect" ? notificationForm.prospect : undefined,
        sale: notificationForm.client_type === "Sale" ? notificationForm.sale : undefined,
      };

      // Remove undefined fields
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key as keyof typeof formattedData] === undefined) {
          delete formattedData[key as keyof typeof formattedData];
        }
      });

      await createNotification(formattedData).unwrap();
      setSuccessMessage("Notification created successfully!");
      refetchNotifications();
      refetchUnreadNotifications();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create notification:", error);
      setFormErrors({
        submit:
          error?.data?.message ||
          "Failed to create notification. Please try again.",
      });
    }
  };

  // Update notification handler
  const onUpdateNotification = async () => {
    if (!validateNotificationForm()) return;

    try {
      // Only include fields that should be updated, exclude read-only fields
      const updateData: any = {
        notification_type: notificationForm.notification_type,
        priority: notificationForm.priority,
        title: notificationForm.title,
        message: notificationForm.message,
        is_read: notificationForm.is_read,
        action_url: notificationForm.action_url || undefined,
        recipient: notificationForm.recipient,
        sender: notificationForm.sender || undefined,
      };

      // Remove empty fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === "" || updateData[key] === undefined || updateData[key] === null) {
          delete updateData[key];
        }
      });

      // Use the existing slice structure: data with notification_id inside
      await updateNotification({
        ...updateData,
        notification_id: editingNotification?.notification_id,
      }).unwrap();
      setSuccessMessage("Notification updated successfully!");
      refetchNotifications();
      refetchUnreadNotifications();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update notification:", error);
      setFormErrors({
        submit:
          error?.data?.message ||
          "Failed to update notification. Please try again.",
      });
    }
  };

  // Delete notification handler
  const onDeleteNotification = async (
    notificationId: string,
    createdBy: string
  ) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete notifications that you created.");
      return;
    }

    if (
      !confirm(
        "Are you sure you want to delete this notification? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteNotification(notificationId).unwrap();
      refetchNotifications();
      refetchUnreadNotifications();
      setSuccessMessage("Notification deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete notification:", error);
    }
  };

  // Mark notification as read handler
  const onMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId).unwrap();
      refetchNotifications();
      refetchUnreadNotifications();
      setSuccessMessage("Notification marked as read!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  // Reminder CRUD handlers
  const handleCreateReminder = async () => {
    console.log("=== CREATE REMINDER STARTED ===");
    console.log("Reminder Form Data:", reminderForm);

    if (!validateReminderForm()) {
      console.log("❌ Reminder form validation failed");
      return;
    }

    try {
      // Format data properly for API
      const formattedData = {
        client_type: reminderForm.client_type,
        reminder_type: reminderForm.reminder_type,
        reminder_date: reminderForm.reminder_date, // Required field
        priority: reminderForm.priority,
        title: reminderForm.title,
        status: reminderForm.status,
        created_by: reminderForm.created_by || currentUser?.id,
        // Set the appropriate entity field based on client_type
        customer: reminderForm.client_type === "Customer" ? reminderForm.customer : undefined,
        prospect: reminderForm.client_type === "Prospect" ? reminderForm.prospect : undefined,
        sale: reminderForm.client_type === "Sale" ? reminderForm.sale : undefined,
      };

      // Add optional fields only if they have values
      if (reminderForm.reminder_time && reminderForm.reminder_date) {
        // Combine date and time into proper datetime format
        const dateTimeString = `${reminderForm.reminder_date}T${reminderForm.reminder_time}:00`;
        (formattedData as any).reminder_time = dateTimeString;
      }

      if (reminderForm.reminder_notes) {
        (formattedData as any).reminder_notes = reminderForm.reminder_notes;
      }

      if (reminderForm.action_url) {
        (formattedData as any).action_url = reminderForm.action_url;
      }

      if (reminderForm.description) {
        (formattedData as any).description = reminderForm.description;
      }

      // Remove undefined fields
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key as keyof typeof formattedData] === undefined) {
          delete formattedData[key as keyof typeof formattedData];
        }
      });

      console.log("📤 Sending create reminder request...");
      const result = await createReminder(formattedData).unwrap();
      console.log("✅ Reminder created successfully:", result);

      setSuccessMessage("Reminder created successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
      closeModal();

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      console.log("=== CREATE REMINDER COMPLETED ===");
    } catch (error: any) {
      console.error("❌ Failed to create reminder:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create reminder. Please try again.",
      });
      console.log("=== CREATE REMINDER FAILED ===");
    }
  };

  const handleUpdateReminder = async () => {
    console.log("=== UPDATE REMINDER STARTED ===");
    console.log("Editing Reminder:", editingReminder);
    console.log("Updated Form Data:", reminderForm);

    if (!validateReminderForm() || !editingReminder) {
      console.log("❌ Reminder form validation failed or no editing reminder");
      return;
    }

    try {
      // Only include fields that should be updated, exclude read-only fields
      const updateData: any = {
        reminder_type: reminderForm.reminder_type,
        reminder_date: reminderForm.reminder_date, // Required field
        priority: reminderForm.priority,
        title: reminderForm.title,
        status: reminderForm.status,
      };

      // Add optional fields only if they have values

      if (reminderForm.reminder_time && reminderForm.reminder_date) {
        // Combine date and time into proper datetime format
        const dateTimeString = `${reminderForm.reminder_date}T${reminderForm.reminder_time}:00`;
        updateData.reminder_time = dateTimeString;
      }

      if (reminderForm.reminder_notes) {
        updateData.reminder_notes = reminderForm.reminder_notes;
      }

      if (reminderForm.action_url) {
        updateData.action_url = reminderForm.action_url;
      }

      if (reminderForm.description) {
        updateData.description = reminderForm.description;
      }

      // Remove empty fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === "" || updateData[key] === undefined || updateData[key] === null) {
          delete updateData[key];
        }
      });

      console.log("📤 Sending update reminder request...");
      const result = await updateReminder({
        id: editingReminder.reminder_id,
        ...updateData,
      }).unwrap();
      console.log("✅ Reminder updated successfully:", result);

      setSuccessMessage("Reminder updated successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
      closeModal();

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      console.log("=== UPDATE REMINDER COMPLETED ===");
    } catch (error: any) {
      console.error("❌ Failed to update reminder:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update reminder. Please try again.",
      });
      console.log("=== UPDATE REMINDER FAILED ===");
    }
  };

  const handleDeleteReminder = async (
    reminderId: string,
    createdBy: string
  ) => {
    console.log("=== DELETE REMINDER STARTED ===");
    console.log("Reminder ID:", reminderId);
    console.log("Created By:", createdBy);

    try {
      console.log("📤 Sending delete reminder request...");
      await deleteReminder(reminderId).unwrap();
      console.log("✅ Reminder deleted successfully");

      setSuccessMessage("Reminder deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      console.log("=== DELETE REMINDER COMPLETED ===");
    } catch (error) {
      console.error("❌ Failed to delete reminder:", error);
      console.log("=== DELETE REMINDER FAILED ===");
    }
  };

  // Note: Complete and snooze endpoints don't exist in the API, so these functions are removed

  // Open edit notification handler
  const onOpenEditNotification = (notification: any) => {
    setEditingNotification(notification);
    setNotificationForm({
      client_type: notification.client_type || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale"),
      notification_type: notification.notification_type || "Info",
      priority: notification.priority || "Normal",
      title: notification.title || "",
      message: notification.message || "",
      is_read: notification.is_read || false,
      read_at: notification.read_at || "",
      action_url: notification.action_url || "",
      recipient: notification.recipient || "",
      sender: notification.sender || "",
      created_by: notification.created_by || currentUser?.id || "",
      customer: notification.customer || (entityType === "customer" ? customerNo : undefined),
      prospect: notification.prospect || (entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined),
      sale: notification.sale || (entityType === "leadfile" ? leadfileNo : undefined),

      // Legacy fields for backward compatibility
      entity_type: notification.entity_type || entityType,
      entity_id: notification.entity_id || entityId,
      customer_no: notification.customer_no || "",
      prospect_id: notification.prospect_id || "",
      lead_file_no: notification.lead_file_no || "",
      client_status: notification.client_status || "",
      expires_at: notification.expires_at ? notification.expires_at.split("T")[0] : "",
      is_active: notification.is_active !== undefined ? notification.is_active : true,
    });
    openModal("editNotification");
  };

  // Note form validation
  const validateNoteForm = () => {
    const errors: Record<string, string> = {};

    if (!noteForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!noteForm.content.trim()) {
      errors.content = "Content is required";
    }
    if (!noteForm.client_type) {
      errors.client_type = "Client type is required";
    }

    // Validate that the appropriate entity field is set based on client_type
    if (noteForm.client_type === "Customer" && !noteForm.customer) {
      errors.customer = "Customer is required for Customer client type";
    }
    if (noteForm.client_type === "Prospect" && !noteForm.prospect) {
      errors.prospect = "Prospect is required for Prospect client type";
    }
    if (noteForm.client_type === "Sale" && !noteForm.sale) {
      errors.sale = "Sale is required for Sale client type";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reminder form validation
  const validateReminderForm = () => {
    console.log("=== VALIDATING REMINDER FORM ===");
    console.log("Form Data:", reminderForm);

    const errors: any = {};

    if (!reminderForm.title.trim()) {
      errors.title = "Title is required";
      console.log("❌ Title validation failed");
    }
    if (!reminderForm.reminder_type) {
      errors.reminder_type = "Reminder type is required";
      console.log("❌ Reminder type validation failed");
    }
    if (!reminderForm.priority) {
      errors.priority = "Priority is required";
      console.log("❌ Priority validation failed");
    }
    if (!reminderForm.client_type) {
      errors.client_type = "Client type is required";
      console.log("❌ Client type validation failed");
    }

    // Validate that the appropriate entity field is set based on client_type
    if (reminderForm.client_type === "Customer" && !reminderForm.customer) {
      errors.customer = "Customer is required for Customer client type";
    }
    if (reminderForm.client_type === "Prospect" && !reminderForm.prospect) {
      errors.prospect = "Prospect is required for Prospect client type";
    }
    if (reminderForm.client_type === "Sale" && !reminderForm.sale) {
      errors.sale = "Sale is required for Sale client type";
    }

    // Validate reminder_date is required
    if (!reminderForm.reminder_date) {
      errors.reminder_date = "Reminder date is required";
      console.log("❌ Reminder date validation failed");
    }

    console.log("Validation Errors:", errors);
    setFormErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    console.log("Form is valid:", isValid);
    console.log("=== REMINDER FORM VALIDATION COMPLETED ===");
    return isValid;
  };

  // Open edit reminder handler
  const onOpenEditReminder = (reminder: any) => {
    console.log("=== OPENING EDIT REMINDER ===");
    console.log("Reminder data:", reminder);
    console.log("Reminder ID:", reminder.reminder_id);

    setEditingReminder(reminder);
    setReminderForm({
      // New API fields
      client_type: reminder.client_type || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale"),
      reminder_type: reminder.reminder_type || "General",
      reminder_date: reminder.reminder_date ? reminder.reminder_date.split("T")[0] : "",
      reminder_time: reminder.reminder_time ? reminder.reminder_time.split("T")[1]?.substring(0, 5) || "" : "",
      priority: reminder.priority || "Normal",
      reminder_notes: reminder.reminder_notes || "",
      action_url: reminder.action_url || "",
      title: reminder.title || "",
      description: reminder.description || "",
      status: reminder.status || "Active",
      created_by: reminder.created_by || currentUser?.id || "",
      customer: reminder.customer || (entityType === "customer" ? customerNo : undefined),
      prospect: reminder.prospect || (entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined),
      sale: reminder.sale || (entityType === "leadfile" ? leadfileNo : undefined),

      // Legacy fields for backward compatibility
      entity_type: reminder.entity_type || entityType,
      entity_id: reminder.entity_id || entityId,
      customer_no: reminder.customer_no || "",
      prospect_id: reminder.prospect_id || "",
      lead_file_no: reminder.lead_file_no || "",
      client_status: reminder.client_status || "",
      remind_at: reminder.remind_at
        ? new Date(reminder.remind_at).toISOString().slice(0, 16)
        : "",
      advance_notice_minutes: reminder.advance_notice_minutes || 15,
      repeat_pattern: reminder.repeat_pattern || "None",
      tags: reminder.tags || "",
      is_active: reminder.is_active !== undefined ? reminder.is_active : true,
    });
    console.log("📝 Opening edit reminder modal...");
    openModal("editReminder");
    console.log("=== EDIT REMINDER SETUP COMPLETED ===");
  };

  // Note form change handler
  const onNoteFormChange = (field: any, value: any) => {
    setNoteForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // Create note handler
  const onCreateNote = async () => {
    console.log("Creating note with data:", noteForm);
    if (!validateNoteForm()) return;

    try {
      // Format dates properly for API
      const formattedData = {
        client_type: noteForm.client_type,
        note_type: noteForm.note_type,
        title: noteForm.title,
        content: noteForm.content,
        is_private: noteForm.is_private,
        is_pinned: noteForm.is_pinned,
        tags: noteForm.tags,
        follow_up_required: noteForm.follow_up_required,
        set_reminder: noteForm.set_reminder,
        created_by: noteForm.created_by || currentUser?.id,
        // Set the appropriate entity field based on client_type
        customer: noteForm.client_type === "Customer" ? noteForm.customer : undefined,
        prospect: noteForm.client_type === "Prospect" ? noteForm.prospect : undefined,
        sale: noteForm.client_type === "Sale" ? noteForm.sale : undefined,
      };

      // Add optional fields only if they have values
      if (noteForm.follow_up_date) {
        (formattedData as any).follow_up_date = new Date(noteForm.follow_up_date + "T00:00:00").toISOString();
      }

      if (noteForm.reminder_time) {
        (formattedData as any).reminder_time = new Date(noteForm.reminder_time).toISOString();
      }

      // Remove undefined fields
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key as keyof typeof formattedData] === undefined) {
          delete formattedData[key as keyof typeof formattedData];
        }
      });

      await createNote(formattedData).unwrap();
      setSuccessMessage("Note created successfully!");
      refetchNotes();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create note:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to create note. Please try again.",
      });
    }
  };

  // Update note handler
  const onUpdateNote = async () => {
    if (!validateNoteForm()) return;

    try {
      // Only include fields that should be updated, exclude read-only fields
      const updateData: any = {
        note_type: noteForm.note_type,
        title: noteForm.title,
        content: noteForm.content,
        is_private: noteForm.is_private,
        is_pinned: noteForm.is_pinned,
        tags: noteForm.tags,
        follow_up_required: noteForm.follow_up_required,
        set_reminder: noteForm.set_reminder,
      };

      // Add optional fields only if they have values
      if (noteForm.follow_up_date) {
        updateData.follow_up_date = new Date(noteForm.follow_up_date + "T00:00:00").toISOString();
      }

      if (noteForm.reminder_time) {
        updateData.reminder_time = new Date(noteForm.reminder_time).toISOString();
      }

      // Remove empty fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === "" || updateData[key] === undefined || updateData[key] === null) {
          delete updateData[key];
        }
      });

      // Use the existing slice structure: data with note_id inside
      await updateNote({
        ...updateData,
        note_id: editingNote?.note_id,
      }).unwrap();
      setSuccessMessage("Note updated successfully!");
      refetchNotes();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update note:", error);
      setFormErrors({
        submit:
          error?.data?.message || "Failed to update note. Please try again.",
      });
    }
  };

  // Delete note handler
  const onDeleteNote = async (noteId: any, createdBy: any) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete notes that you created.");
      return;
    }

    if (
      !confirm(
        "Are you sure you want to delete this note? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteNote(noteId).unwrap();
      refetchNotes();
      setSuccessMessage("Note deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete note:", error);
    }
  };

  // Toggle pin handler
  const onToggleNotePin = async (noteId: any, currentPinStatus: any) => {
    try {
      await toggleNotePin({
        noteId,
        data: { is_pinned: !currentPinStatus },
      }).unwrap();
      refetchNotes();
      setSuccessMessage(
        `Note ${!currentPinStatus ? "pinned" : "unpinned"} successfully!`
      );
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to toggle pin status:", error);
    }
  };

  // Open edit note handler
  const onOpenEditNote = (note: any) => {
    setEditingNote(note);
    setNoteForm({
      client_type: note.client_type || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Sale"),
      note_type: note.note_type || "General",
      title: note.title || "",
      content: note.content || "",
      is_private: note.is_private || false,
      is_pinned: note.is_pinned || false,
      tags: note.tags || "",
      follow_up_required: note.follow_up_required || false,
      follow_up_date: note.follow_up_date ? note.follow_up_date.split("T")[0] : "",
      set_reminder: note.set_reminder || false,
      reminder_time: note.reminder_time ? note.reminder_time.slice(0, 16) : "",
      created_by: note.created_by || currentUser?.id || "",
      customer: note.customer || (entityType === "customer" ? customerNo : undefined),
      prospect: note.prospect || (entityType === "prospect" ? parseInt(customerNo || "0") || undefined : undefined),
      sale: note.sale || (entityType === "leadfile" ? leadfileNo : undefined),

      // Legacy fields for backward compatibility
      reminder_date: note.reminder_date ? note.reminder_date.split("T")[0] : "",
      is_active: note.is_active !== undefined ? note.is_active : true,
    });
    openModal("editNote");
  };

  // Utility functions
  const renderPriorityBadge: BadgeRenderer = (priority) => {
    const variants: Record<string, string> = {
      High: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Medium:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      Low: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Critical: "bg-red-200 text-red-900 dark:bg-red-800/40 dark:text-red-200",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[priority] || variants.Medium
        }`}
      >
        {priority}
      </span>
    );
  };

  const renderNoteTypeBadge = (type: any) => {
    const variants = {
      General:
        "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
      Important: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Reminder:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Follow-up":
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      Internal:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      "Customer Facing":
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[type] || variants.General
        }`}
      >
        {type}
      </span>
    );
  };

  const renderNotificationTypeBadge: BadgeRenderer = (type) => {
    const variants = {
      Info: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Warning:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      Error: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Success:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Reminder:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Alert:
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[type] || variants.Info
        }`}
      >
        {type}
      </span>
    );
  };

  const renderEngagementTypeBadge: BadgeRenderer = (type) => {
    const variants: Record<string, string> = {
      Call: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Email:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Meeting:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      SMS: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      Chat: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
      Visit: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Event:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "Follow-up":
        "bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[type] || variants.Call
        }`}
      >
        {type}
      </span>
    );
  };

  const renderEngagementStatusBadge: BadgeRenderer = (status) => {
    const variants: Record<string, string> = {
      Scheduled:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "In Progress":
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      Completed:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Cancelled: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Rescheduled:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[status] || variants.Scheduled
        }`}
      >
        {status}
      </span>
    );
  };

  const renderStatusBadge: BadgeRenderer = (status) => {
    const variants: Record<string, string> = {
      Open: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "In Progress":
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      Resolved:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Closed:
        "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
      Escalated: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[status] || variants.Open
        }`}
      >
        {status}
      </span>
    );
  };

  const renderCategoryBadge: BadgeRenderer = (category) => {
    const variants: Record<string, string> = {
      Product:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Service:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Billing:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Technical:
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      General:
        "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[category] || variants.General
        }`}
      >
        {category}
      </span>
    );
  };

  const renderFeedbackTypeBadge: BadgeRenderer = (type) => {
    const variants: Record<string, string> = {
      General:
        "bg-gray-100 text-gray-800 dark:bg-gray-800/40 dark:text-gray-300",
      "Product Review":
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Service Review":
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Compliment:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Suggestion:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      Complaint: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[type] || variants.General
        }`}
      >
        {type}
      </span>
    );
  };

  const renderRatingStars = (rating?: number): JSX.Element => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${
              star <= (rating || 0)
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
        {rating && (
          <span className="ml-1 text-xs text-gray-500">({rating}/5)</span>
        )}
      </div>
    );
  };

  const renderFlagTypeBadge: BadgeRenderer = (type) => {
    const variants = {
      "Customer Issue":
        "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      "Data Quality":
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Security:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Compliance:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Performance:
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      Content:
        "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[type] || variants.Content
        }`}
      >
        {type}
      </span>
    );
  };

  const renderSeverityBadge = (severity: any) => {
    const variants = {
      Info: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Warning:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      Error:
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      Critical: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
    };

    return (
      <span
        className={`text-xs px-2 py-1 rounded-full ${
          variants[severity] || variants.Info
        }`}
      >
        {severity}
      </span>
    );
  };

  const formatDate: DateFormatter = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const timeAgo: DateFormatter = (dateString) => {
    if (!dateString) return "Unknown";
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return "Today";
    if (diffInDays === 1) return "1 day ago";
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  };

  // Reminder badge renderers
  const renderReminderTypeBadge = (type: string) => {
    const typeColors = {
      "General": "bg-gray-100 text-gray-800",
      "Follow-up Call": "bg-green-100 text-green-800",
      "Payment Reminder": "bg-yellow-100 text-yellow-800",
      "Document Collection": "bg-blue-100 text-blue-800",
      "Site Visit": "bg-purple-100 text-purple-800",
      "Meeting": "bg-indigo-100 text-indigo-800",
      "Email": "bg-cyan-100 text-cyan-800",
      "SMS": "bg-orange-100 text-orange-800",
    };
    return (
      <Badge
        className={`text-xs ${
          typeColors[type as keyof typeof typeColors] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {type}
      </Badge>
    );
  };

  const renderReminderPriorityBadge = (priority: string) => {
    const priorityColors = {
      Low: "bg-green-100 text-green-800",
      Normal: "bg-blue-100 text-blue-800",
      High: "bg-yellow-100 text-yellow-800",
      Urgent: "bg-red-100 text-red-800",
    };
    return (
      <Badge
        className={`text-xs ${
          priorityColors[priority as keyof typeof priorityColors] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {priority}
      </Badge>
    );
  };

  const renderReminderStatusBadge = (status: string) => {
    const statusColors = {
      Active: "bg-green-100 text-green-800",
      Completed: "bg-blue-100 text-blue-800",
      Cancelled: "bg-red-100 text-red-800",
      Snoozed: "bg-yellow-100 text-yellow-800",
    };
    return (
      <Badge
        className={`text-xs ${
          statusColors[status as keyof typeof statusColors] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </Badge>
    );
  };

  // Sample data for other sections
  const ticketItems: TicketItem[] = ticketsData?.results || [];
  const ticketMessages: TicketMessage[] = ticketMessagesData?.results || [];
  const ticketAttachments: TicketAttachment[] =
    ticketAttachmentsData?.results || [];
  const actionLogs: TicketActionLog[] = actionLogsData?.results || [];
  const categories: TicketCategory[] = categoriesData?.results || [];
  const sources: TicketSource[] = sourcesData?.results || [];
  // Handle both transformed and raw response formats for notes
  let noteItems: any[] = [];
  if (notesData?.results) {
    // Properly transformed data
    noteItems = notesData.results;
  } else if ((notesData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    noteItems = (notesData as any).data.results;
  }

  // Handle both transformed and raw response formats for flags
  let flagItems: any[] = [];
  if (flagsData?.results) {
    // Properly transformed data
    flagItems = flagsData.results;
  } else if ((flagsData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    flagItems = (flagsData as any).data.results;
  }

  // Handle both transformed and raw response formats
  let engagementItems: any[] = [];
  if (engagementsData?.results) {
    // Properly transformed data
    engagementItems = engagementsData.results;
  } else if ((engagementsData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    engagementItems = (engagementsData as any).data.results;
  }

  // Handle both transformed and raw response formats for notifications
  let notificationItems: any[] = [];
  if (notificationsData?.results) {
    // Properly transformed data
    notificationItems = notificationsData.results;
  } else if ((notificationsData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    notificationItems = (notificationsData as any).data.results;
  }

  // Handle both transformed and raw response formats for reminders
  let reminderItems: any[] = [];
  if (remindersData?.results) {
    // Properly transformed data
    reminderItems = remindersData.results;
  } else if ((remindersData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    reminderItems = (remindersData as any).data.results;
  }

  // Calculate overdue and upcoming counts
  const overdueCount = reminderItems.filter(
    (reminder) => reminder.is_overdue
  ).length;
  const upcomingCount = reminderItems.filter(
    (reminder) => reminder.is_due && !reminder.is_overdue
  ).length;

  // Debug logging for reminders - placed after all variables are initialized
  console.log("=== REMINDERS DEBUG INFO ===");
  console.log("Entity Type:", entityType);
  console.log("Customer No:", customerNo);
  console.log("Lead File No:", leadfileNo);
  console.log("Reminders Params:", remindersParams);
  console.log("Reminders Data:", remindersData);
  console.log("Reminders Loading:", remindersLoading);
  console.log("Reminders Error:", remindersError);
  console.log("Processed Reminder Items:", reminderItems);
  console.log("Overdue Count:", overdueCount);
  console.log("Upcoming Count:", upcomingCount);
  console.log("Expanded Sections:", expandedSections);
  console.log("Active Modal:", activeModal);
  console.log("=== END REMINDERS DEBUG ===");

  return (
    <>
      <ScrollArea className="h-full overflow-auto">
        <div className={`p-4 space-y-6 pb-16 ${className}`}>
          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <TicketsSection
            expanded={expandedSections.tickets}
            onToggle={() => toggleSection("tickets")}
            items={ticketItems}
            loading={ticketsLoading}
            error={ticketsError}
            onOpenList={() => openModal("ticketsList")}
            onOpenCreate={() => openModal("createTicket")}
            onOpenTicket={openTicketDetails}
            renderPriorityBadge={renderPriorityBadge}
            renderStatusBadge={renderStatusBadge}
            timeAgo={timeAgo}
          />

          <NotesSection
            expanded={expandedSections.notes}
            onToggle={() => toggleSection("notes")}
            items={noteItems}
            loading={notesLoading}
            error={notesError}
            onOpenList={() => openModal("notes")}
            onOpenCreate={() => openModal("createNote")}
            onOpenDetails={(note) => {
              setSelectedNote(note);
              openModal("noteDetails");
            }}
            onEdit={(note) => {
              onOpenEditNote(note);
            }}
            renderNoteTypeBadge={renderNoteTypeBadge}
            onTogglePin={onToggleNotePin}
            timeAgo={timeAgo}
          />

          <FlagsSection
            expanded={expandedSections.flags}
            onToggle={() => toggleSection("flags")}
            items={flagItems}
            loading={flagsLoading}
            error={flagsError}
            onAdd={() => openModal("createFlag")}
            onOpenList={() => openModal("flags")}
            onOpenDetails={(flag) => {
              setEditingFlag(flag);
              openModal("viewFlag");
            }}
            onEdit={(flag) => {
              onOpenEditFlag(flag);
            }}
            renderFlagTypeBadge={renderFlagTypeBadge}
            renderSeverityBadge={renderSeverityBadge}
            renderStatusBadge={renderStatusBadge}
            timeAgo={timeAgo}
          />

          <EngagementsSection
            expanded={expandedSections.engagements}
            onToggle={() => toggleSection("engagements")}
            items={engagementItems}
            loading={engagementsLoading}
            error={engagementsError}
            onOpenList={() => openModal("engagements")}
            onOpenCreate={() => openModal("createEngagement")}
            onOpenDetails={(engagement) => {
              setEditingEngagement(engagement);
              openModal("viewEngagement");
            }}
            onEdit={(engagement) => {
              setEditingEngagement(engagement);
              openModal("editEngagement");
            }}
            renderEngagementTypeBadge={renderEngagementTypeBadge}
            renderEngagementStatusBadge={renderEngagementTypeBadge}
            formatDate={formatDate}
          />

          <NotificationsSection
            expanded={expandedSections.notifications}
            onToggle={() => toggleSection("notifications")}
            items={notificationItems}
            loading={notificationsLoading}
            error={notificationsError}
            onOpenList={() => openModal("notifications")}
            onOpenCreate={() => openModal("createNotification")}
            onOpenDetails={(notification) => {
              setSelectedNotification(notification);
              openModal("notificationDetails");
            }}
            onEdit={(notification) => {
              onOpenEditNotification(notification);
            }}
            renderNotificationTypeBadge={renderNotificationTypeBadge}
            renderPriorityBadge={renderPriorityBadge}
            renderStatusBadge={renderStatusBadge}
            timeAgo={timeAgo}
            onMarkAsRead={onMarkAsRead}
            unreadCount={unreadNotificationsData?.count || 0}
          />

          <RemindersSection
            expanded={expandedSections.reminders}
            onToggle={() => {
              console.log("🔄 Toggling reminders section");
              toggleSection("reminders");
            }}
            items={reminderItems}
            loading={remindersLoading}
            error={remindersError}
            onOpenList={() => {
              console.log("📋 Opening reminders list modal");
              openModal("reminders");
            }}
            onOpenCreate={() => {
              console.log("➕ Opening create reminder modal");
              openModal("createReminder");
            }}
            onOpenDetails={(reminder) => {
              setSelectedReminder(reminder);
              openModal("reminderDetails");
            }}
            onEdit={(reminder) => {
              onOpenEditReminder(reminder);
            }}
            renderReminderTypeBadge={renderReminderTypeBadge}
            renderPriorityBadge={renderReminderPriorityBadge}
            renderStatusBadge={renderReminderStatusBadge}
            timeAgo={timeAgo}
            onCompleteReminder={() => {
              console.log("⚠️ Complete functionality not available - API endpoint doesn't exist");
            }}
            onSnoozeReminder={() => {
              console.log("⚠️ Snooze functionality not available - API endpoint doesn't exist");
            }}
            overdueCount={overdueCount}
            upcomingCount={upcomingCount}
          />
        </div>
      </ScrollArea>

      {/* Add EngagementsModals component here when created */}
      <EngagementsModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        engagementForm={engagementForm}
        onEngagementFormChange={handleEngagementFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingEngagement={editingEngagement}
        onOpenEditEngagement={openEditEngagement}
        onCreateEngagement={handleCreateEngagement}
        onUpdateEngagement={handleUpdateEngagement}
        onDeleteEngagement={handleDeleteEngagement}
        engagementItems={engagementItems}
        currentUser={currentUser}
        isCreating={isCreatingEngagement}
        isDeleting={isDeletingEngagement}
        renderEngagementTypeBadge={renderEngagementTypeBadge}
        renderEngagementStatusBadge={renderEngagementTypeBadge}
        formatDate={formatDate}
      />

      {/* Add TicketsModals component */}
      <TicketsModals
        activeModal={activeModal}
        onClose={closeModal}
        ticketForm={ticketForm}
        onTicketFormChange={handleTicketFormChange}
        onCreateTicket={handleCreateTicket}
        onUpdateTicket={handleUpdateTicket}
        onFileChange={setSelectedFile}
        selectedFile={selectedFile}
        messageForm={messageForm}
        onMessageFormChange={handleMessageFormChange}
        onSendMessage={handleSendMessage}
        onDeleteMessage={handleDeleteMessage}
        onEditMessage={handleEditMessage}
        categoryForm={categoryForm}
        onCategoryFormChange={handleCategoryFormChange}
        onCreateCategory={handleCreateCategory}
        onUpdateCategory={handleUpdateCategory}
        onDeleteCategory={handleDeleteCategory}
        editingCategory={editingCategory}
        sourceForm={sourceForm}
        onSourceFormChange={handleSourceFormChange}
        onCreateSource={handleCreateSource}
        onUpdateSource={handleUpdateSource}
        onDeleteSource={handleDeleteSource}
        editingSource={editingSource}
        onUploadAttachment={handleUploadAttachment}
        onDeleteAttachment={handleDeleteAttachment}
        actionLogs={actionLogs}
        onCreateActionLog={handleCreateActionLog}
        onNavigateToTicket={navigateToTicket}
        editingTicket={editingTicket}
        selectedTicket={selectedTicket}
        ticketMessages={ticketMessages}
        ticketAttachments={ticketAttachments}
        categories={categories}
        sources={sources}
        ticketItems={ticketItems}
        isCreating={isCreatingTicket}
        isUpdating={isUpdatingTicket}
        isSendingMessage={isSendingMessage}
        messagesLoading={messagesLoading}
        attachmentsLoading={attachmentsLoading}
        isDeleting={isDeletingTicket}
        isUploadingAttachment={isUploadingAttachment}
        formErrors={formErrors}
        successMessage={successMessage}
        renderPriorityBadge={renderPriorityBadge}
        renderStatusBadge={renderStatusBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
        currentUser={currentUser}
      />

      {/* Add FlagsModals component */}
      <FlagsModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        flagForm={flagForm}
        onFlagFormChange={onFlagFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingFlag={editingFlag}
        onOpenEditFlag={onOpenEditFlag}
        onCreateFlag={onCreateFlag}
        onUpdateFlag={onUpdateFlag}
        onDeleteFlag={onDeleteFlag}
        onResolveFlag={onResolveFlag}
        flagItems={flagItems}
        currentUser={currentUser}
        isCreating={isCreatingFlag}
        isDeleting={isDeletingFlag}
        renderFlagTypeBadge={renderFlagTypeBadge}
        renderSeverityBadge={renderSeverityBadge}
        renderStatusBadge={renderStatusBadge}
        formatDate={formatDate}
      />

      <NotificationsModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        notificationForm={notificationForm}
        onNotificationFormChange={onNotificationFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingNotification={editingNotification}
        onOpenEditNotification={onOpenEditNotification}
        onCreateNotification={onCreateNotification}
        onUpdateNotification={onUpdateNotification}
        onDeleteNotification={onDeleteNotification}
        onMarkAsRead={onMarkAsRead}
        notificationItems={notificationItems}
        currentUser={currentUser}
        isCreating={isCreatingNotification}
        isDeleting={isDeletingNotification}
        renderNotificationTypeBadge={renderNotificationTypeBadge}
        renderPriorityBadge={renderPriorityBadge}
        renderStatusBadge={renderStatusBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
        selectedNotification={selectedNotification}
      />
      <NotesModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        noteForm={noteForm}
        onNoteFormChange={onNoteFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingNote={editingNote}
        onOpenEditNote={onOpenEditNote}
        onCreateNote={onCreateNote}
        onUpdateNote={onUpdateNote}
        onDeleteNote={onDeleteNote}
        onToggleNotePin={onToggleNotePin}
        noteItems={noteItems}
        currentUser={currentUser}
        isCreating={isCreatingNote}
        isDeleting={isDeletingNote}
        renderNoteTypeBadge={renderNoteTypeBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
        selectedNote={selectedNote}
      />

      <RemindersModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        reminderForm={reminderForm}
        onReminderFormChange={onReminderFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingReminder={editingReminder}
        onOpenEditReminder={onOpenEditReminder}
        onCreateReminder={handleCreateReminder}
        onUpdateReminder={handleUpdateReminder}
        onDeleteReminder={handleDeleteReminder}
        onCompleteReminder={() => console.log("⚠️ Complete functionality not available")}
        onSnoozeReminder={() => console.log("⚠️ Snooze functionality not available")}
        reminderItems={reminderItems}
        currentUser={currentUser}
        isCreating={isCreatingReminder}
        isDeleting={isDeletingReminder}
        renderReminderTypeBadge={renderReminderTypeBadge}
        renderPriorityBadge={renderReminderPriorityBadge}
        renderStatusBadge={renderReminderStatusBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
        selectedReminder={selectedReminder}
      />
    </>
  );
};

export default CustomerSidebar;
