import React from 'react';
import { Outlet } from 'react-router-dom';
import PermissionGuard from './PermissionGuard';

/**
 * A route-compatible version of PermissionGuard that uses Outlet
 * This component automatically applies permission checking to all nested routes
 * based on the ROUTE_PERMISSIONS mapping in PermissionGuard
 */
const PermissionRouteGuard: React.FC = () => {
  return (
    <PermissionGuard>
      <Outlet />
    </PermissionGuard>
  );
};

export default PermissionRouteGuard;
