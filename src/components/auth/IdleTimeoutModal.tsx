import { useIdleTimeout } from "@/utils/userIdleTimeout";
import { toast } from "../custom/Toast/MyToast";
import { Outlet } from "react-router-dom";

const IdleTimeoutModal = () => {
  // idle timeout hook
  useIdleTimeout({
    idleTime: 3600, //60 min in seconds
    onIdle: () => {
      // console.log('Idle timeout triggered');
    },
  });

  // warning
  useIdleTimeout({
    idleTime: 3480, //58 min in seconds
    onIdle: () => {
      toast.success("Inactive user", {
        description: "You will be logged out in 2 minutes.",
        action: {
          label: "Keep Me On",
          onClick: () => {},
        },
        duration: 120000, // 20 seconds in milliseconds
        closeButton: true,
      });
    },
    redirectUrl: null,
  });
  return <Outlet />;
};

export default IdleTimeoutModal;
