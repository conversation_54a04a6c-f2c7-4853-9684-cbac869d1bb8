import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAuthHook } from '@/utils/useAuthHook';
import { useCreateUser2UserPermissionMutation, useGetUser2UserPermissionsQuery } from '@/redux/slices/permissions';
import { LOGISTICS_PERMISSIONS } from '@/hooks/useSidebarPermissions';
import { toast } from '@/components/custom/Toast/MyToast';

/**
 * Debug component to manually manage user permissions for testing
 * This component should only be used in development
 */
export const PermissionManager: React.FC = () => {
  const { user_details } = useAuthHook();
  const [selectedPermission, setSelectedPermission] = useState<number | ''>('');
  
  const [createUserPermission, { isLoading: isCreating }] = useCreateUser2UserPermissionMutation();
  
  const { data: userPermissions = [], refetch } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: user_details?.employee_no || '',
    },
    {
      skip: !user_details?.employee_no,
    }
  );

  const handleAddPermission = async () => {
    if (!selectedPermission || !user_details?.employee_no) {
      toast.error('Please select a permission and ensure user is logged in');
      return;
    }

    try {
      await createUserPermission({
        user: user_details.employee_no,
        permission: Number(selectedPermission),
      }).unwrap();
      
      toast.success(`Permission ${selectedPermission} added successfully!`);
      setSelectedPermission('');
      refetch();
    } catch (error: any) {
      console.error('Failed to add permission:', error);
      toast.error(`Failed to add permission: ${error?.data?.detail || 'Unknown error'}`);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Permission Manager (Debug)</CardTitle>
        <CardDescription>
          Manually add permissions for testing. Only use in development.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div>
          <strong>Current User:</strong> {user_details?.fullnames} ({user_details?.employee_no})
        </div>

        <div>
          <strong>Current User Permissions:</strong>
          <div className="flex flex-wrap gap-2 mt-2">
            {userPermissions.length > 0 ? (
              userPermissions.map((perm: any) => (
                <Badge key={perm.id} variant="default">
                  {perm.permission}
                </Badge>
              ))
            ) : (
              <span className="text-gray-500">No permissions found</span>
            )}
          </div>
        </div>

        <div className="border-t pt-4">
          <h4 className="font-medium mb-3">Add Logistics Permission</h4>
          
          <div className="grid grid-cols-2 gap-2 mb-4">
            {Object.entries(LOGISTICS_PERMISSIONS).map(([key, code]) => (
              <Button
                key={key}
                variant={selectedPermission === code ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedPermission(code)}
                className="text-xs"
              >
                {key} ({code})
              </Button>
            ))}
          </div>

          <div className="flex gap-2">
            <Input
              type="number"
              placeholder="Or enter permission code"
              value={selectedPermission}
              onChange={(e) => setSelectedPermission(e.target.value ? Number(e.target.value) : '')}
              className="flex-1"
            />
            <Button 
              onClick={handleAddPermission}
              disabled={!selectedPermission || isCreating}
            >
              {isCreating ? 'Adding...' : 'Add Permission'}
            </Button>
          </div>
        </div>

        <div className="border-t pt-4">
          <h4 className="font-medium mb-2">Quick Add All Logistics Permissions</h4>
          <Button
            variant="secondary"
            onClick={async () => {
              if (!user_details?.employee_no) {
                toast.error('User not logged in');
                return;
              }

              try {
                const promises = Object.values(LOGISTICS_PERMISSIONS).map(code =>
                  createUserPermission({
                    user: user_details.employee_no!,
                    permission: code,
                  }).unwrap()
                );

                await Promise.all(promises);
                toast.success('All logistics permissions added!');
                refetch();
              } catch (error: any) {
                console.error('Failed to add permissions:', error);
                toast.error('Failed to add some permissions');
              }
            }}
            disabled={isCreating}
          >
            Add All Logistics Permissions
          </Button>
        </div>

        <div className="text-xs text-gray-500 border-t pt-4">
          <strong>Available Logistics Permissions:</strong>
          <ul className="mt-1 space-y-1">
            {Object.entries(LOGISTICS_PERMISSIONS).map(([key, code]) => (
              <li key={key}>
                {code} - {key.replace(/_/g, ' ').toLowerCase()}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default PermissionManager;
