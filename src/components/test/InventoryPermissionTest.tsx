import React from 'react';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';

/**
 * Test component to verify inventory permissions are working correctly
 * This component can be temporarily added to any page to test permissions
 */
const InventoryPermissionTest: React.FC = () => {
  const { hasInventoryPermission, userPermissionCodes } = useSidebarPermissions();

  const inventoryPermissions = [
    { key: 'VIEW_INVENTORY_FULL_ACCESS', code: 7112, description: 'View Inventory Full Access (All Features)' },
    { key: 'VIEW_INVENTORY_MARKETER', code: 7113, description: 'View Inventory Marketer (My Booking)' },
    { key: 'VIEW_INVENTORY_GM_HQ', code: 7114, description: 'View Inventory GM HQ (Special Bookings)' },
    { key: 'VIEW_INVENTORY_GM_KAREN', code: 7115, description: 'View Inventory GM KAREN (Special Bookings)' },
    { key: 'VIEW_INVENTORY_ACCOUNTS', code: 7116, description: 'View Inventory Accounts' },
    { key: 'VIEW_INVENTORY_DIASPORA', code: 7117, description: 'View Inventory Diaspora (My Booking + Plot Features)' },
    { key: 'VIEW_INVENTORY_REPORTS', code: 7118, description: 'View Inventory Reports' },
    { key: 'VIEW_INVENTORY_PRICING', code: 7119, description: 'View Inventory Pricing' },
    { key: 'VIEW_INVENTORY_MPESA_TRANSACTIONS', code: 7120, description: 'View Inventory Mpesa Transactions' },
    { key: 'VIEW_INVENTORY_LOGS', code: 7121, description: 'View Inventory Logs' },
    { key: 'VIEW_INVENTORY_MAPS', code: 7122, description: 'View Inventory Maps (Quick Links)' },
  ];

  return (
    <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
      <h3 className="text-lg font-semibold mb-4">Inventory Permissions Test</h3>
      
      <div className="mb-4">
        <h4 className="font-medium mb-2">All User Permission Codes:</h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {userPermissionCodes.join(', ') || 'No permissions found'}
        </p>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">Inventory Permission Status:</h4>
        {inventoryPermissions.map((permission) => {
          const hasPermission = hasInventoryPermission(permission.key as any);
          const hasCode = userPermissionCodes.includes(permission.code);
          
          return (
            <div key={permission.key} className="flex items-center justify-between p-2 border rounded">
              <span className="text-sm">
                {permission.description} ({permission.code})
              </span>
              <div className="flex gap-2">
                <span className={`px-2 py-1 text-xs rounded ${
                  hasCode ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
                }`}>
                  Code: {hasCode ? 'Yes' : 'No'}
                </span>
                <span className={`px-2 py-1 text-xs rounded ${
                  hasPermission ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  Access: {hasPermission ? 'Granted' : 'Denied'}
                </span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded">
        <p className="text-sm text-yellow-800 dark:text-yellow-200">
          <strong>⚠️ Important:</strong> The entire Inventory section is hidden if user has NO inventory permissions.
          My Booking requires Marketer (7113) OR Diaspora (7117) permission. Special Bookings requires
          either GM HQ (7114) or GM KAREN (7115) permission. Other sections require their specific permissions.
        </p>
      </div>

      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
        <h4 className="font-medium mb-2 text-blue-800 dark:text-blue-200">Action-Level Permissions:</h4>
        <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <p><strong>7112 (Full Access):</strong> Overrides all restrictions, shows everything</p>
          <p><strong>7113 (Marketer):</strong> My Booking sidebar, Special/M-Pesa/Other/Plot Payment buttons, Open plots only</p>
          <p><strong>7117 (Diaspora):</strong> My Booking sidebar (NO Diaspora sidebar), Reserve Plot/Diaspora Receipting/Plot Payment buttons, Open plots only</p>
          <p><strong>7122 (Maps):</strong> Quick Links section (Project Visibility, Map Management, Forex)</p>
        </div>
      </div>
    </div>
  );
};

export default InventoryPermissionTest;
