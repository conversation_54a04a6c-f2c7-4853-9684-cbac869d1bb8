import { useState, useEffect } from "react";
import { 
  Calendar,
  Clock,
  DollarSign, 
  Search,
  ChevronDown,
  FileBarChart,
  CalendarDays,
  CheckCircle2,
  XCircle,
  AlertCircle,
  ArrowUpDown,
  Filter,
  Download,
  CreditCard,
  TrendingUp
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useGetCustomerInstallmentsQuery } from "@/redux/slices/customersApiSlice";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";

// Define interfaces for the component props and data structures
interface InstallmentsTabProps {
  customerId: string;
}

interface InstallmentSchedule {
  id: number;
  member_no: string;
  leadfile_no: string;
  line_no: number;
  installment_no: number;
  installment_amount: string;
  remaining_Amount: string;
  due_date: string;
  paid: string;
  plot_No: string;
  plot_Name: string;
  amount_Paid: string;
  penaties_Accrued: number;
  created_at: string;
  updated_at: string;
}

interface InstallmentsData {
  results: InstallmentSchedule[];
  count: number;
  next: string | null;
  previous: string | null;
}

interface InstallmentsSummary {
  totalScheduled: number;
  totalPaid: number;
  totalPending: number;
  totalOverdue: number;
  totalPenalties: number;
  paidCount: number;
  pendingCount: number;
  overdueCount: number;
  totalInstallments: number;
  nextDueAmount: number;
  nextDueDate: string | null;
}

interface FilterState {
  paid: string;
  plot_No: string;
  plot_Name: string;
  leadfile_no: string;
}

const CustomerInstallmentsTab = ({ customerId }: InstallmentsTabProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    paid: "",
    plot_No: "",
    plot_Name: "",
    leadfile_no: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  useEffect(() => {
    setIsSearching(true);
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setIsSearching(false);
    }, 500);

    return () => {
      clearTimeout(timer);
      setIsSearching(false);
    };
  }, [searchTerm]);

  useEffect(() => {
    setCurrentPage(1); // Reset to first page when search term changes
  }, [debouncedSearchTerm, filters]);



  // Construct query parameters for installments endpoint
  const queryParams = {
    member_no: customerId, // Filter by customer member number
    search: debouncedSearchTerm || undefined,
    paid: filters.paid || undefined,
    plot_No: filters.plot_No || undefined,
    plot_Name: filters.plot_Name || undefined,
    page: currentPage,
    page_size: pageSize
  };

  // Fetch customer installments data
  const {
    data: installmentsData,
    isLoading,
    isError
  } = useGetCustomerInstallmentsQuery(queryParams);



  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleNextPage = () => {
    if (installmentsData && installmentsData.next) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (installmentsData && installmentsData.previous && currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  // Compute summary statistics from installments data
  const calculateSummary = (): InstallmentsSummary => {
    if (!installmentsData || !installmentsData.results || installmentsData.results.length === 0) {
      return {
        totalScheduled: 0,
        totalPaid: 0,
        totalPending: 0,
        totalOverdue: 0,
        totalPenalties: 0,
        paidCount: 0,
        pendingCount: 0,
        overdueCount: 0,
        totalInstallments: 0,
        nextDueAmount: 0,
        nextDueDate: null
      };
    }

    const results = installmentsData.results;
    const today = new Date();
    
    let totalScheduled = 0;
    let totalPaid = 0;
    let totalPending = 0;
    let totalOverdue = 0;
    let totalPenalties = 0;
    let paidCount = 0;
    let pendingCount = 0;
    let overdueCount = 0;
    let nextDueAmount = 0;
    let nextDueDate: string | null = null;

    const upcomingDue = results
      .filter((installment: InstallmentSchedule) => 
        installment.paid.toLowerCase() !== 'yes' && 
        new Date(installment.due_date) >= today
      )
      .sort((a: InstallmentSchedule, b: InstallmentSchedule) => 
        new Date(a.due_date).getTime() - new Date(b.due_date).getTime()
      );

    if (upcomingDue.length > 0) {
      nextDueAmount = parseFloat((upcomingDue[0].installment_amount || "0").replace(/,/g, ''));
      nextDueDate = upcomingDue[0].due_date;
    }

    results.forEach((installment: InstallmentSchedule) => {
      const installmentAmount = parseFloat((installment.installment_amount || "0").replace(/,/g, ''));
      const amountPaid = parseFloat((installment.amount_Paid || "0").replace(/,/g, ''));
      const penalties = installment.penaties_Accrued || 0;
      const dueDate = new Date(installment.due_date);
      const isPaid = installment.paid.toLowerCase() === 'yes';

      totalScheduled += installmentAmount;
      totalPaid += amountPaid;
      totalPenalties += penalties;

      if (isPaid) {
        paidCount++;
      } else {
        const remainingAmount = parseFloat((installment.remaining_Amount || "0").replace(/,/g, ''));
        totalPending += remainingAmount;

        if (dueDate < today) {
          totalOverdue += remainingAmount;
          overdueCount++;
        } else {
          pendingCount++;
        }
      }
    });

    return {
      totalScheduled,
      totalPaid,
      totalPending,
      totalOverdue,
      totalPenalties,
      paidCount,
      pendingCount,
      overdueCount,
      totalInstallments: installmentsData?.count || 0,
      nextDueAmount,
      nextDueDate
    };
  };

  const summary = calculateSummary();

  // Determine installment status
  const getInstallmentStatus = (installment: InstallmentSchedule): string => {
    const isPaid = installment.paid.toLowerCase() === 'yes';
    const dueDate = new Date(installment.due_date);
    const today = new Date();

    if (isPaid) return "Paid";
    if (dueDate < today) return "Overdue";
    if (dueDate.toDateString() === today.toDateString()) return "Due Today";
    return "Pending";
  };

  const getStatusColor = (status: string): string => {
    const statusColors: Record<string, string> = {
      "Paid": "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800",
      "Pending": "bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",
      "Overdue": "bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800",
      "Due Today": "bg-amber-50 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800"
    };

    return statusColors[status] || "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700";
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Paid":
        return <CheckCircle2 className="h-3 w-3" />;
      case "Overdue":
        return <XCircle className="h-3 w-3" />;
      case "Due Today":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  // Extract unique values for filter dropdowns
  const getUniqueValues = (field: keyof InstallmentSchedule): string[] => {
    if (!installmentsData || !installmentsData.results) return [];
    const uniqueValues = new Set<string>(
      installmentsData.results
        .map((item: InstallmentSchedule) => item[field])
        .filter(Boolean)
        .map((value: unknown) => String(value))
    );
    return Array.from(uniqueValues);
  };

  // Format currency values
  const formatCurrency = (value: string | number): string => {
    let numValue: number;

    if (typeof value === 'string') {
      // Remove commas from string before parsing
      const cleanValue = value.replace(/,/g, '');
      numValue = parseFloat(cleanValue);
    } else {
      numValue = value;
    }

    return isNaN(numValue) ? "0" : numValue.toLocaleString();
  };

  // Format date values
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "N/A";
    }
  };

  // Check if due date is approaching (within 7 days)
  const isDueApproaching = (dueDateString: string): boolean => {
    const dueDate = new Date(dueDateString);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays >= 0;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <SpinnerTemp type="spinner-double" size="lg" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading installments data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Installments Summary Cards */}
     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Total Paid</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">
                  KES {formatCurrency(summary.totalPaid)}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center text-green-600 dark:text-green-400">
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    <span>{summary.paidCount} Paid</span>
                  </div>
                </div>
              </div>
              <div className="bg-green-100 p-2 rounded-lg dark:bg-green-900/30 ml-3">
                <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Pending Amount</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">
                  KES {formatCurrency(summary.totalPending)}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{summary.pendingCount} Pending</span>
                  {summary.overdueCount > 0 && (
                    <>
                      <span className="mx-1">•</span>
                      <span className="text-red-600 dark:text-red-400">{summary.overdueCount} Overdue</span>
                    </>
                  )}
                </div>
              </div>
              <div className="bg-amber-100 p-2 rounded-lg dark:bg-amber-900/30 ml-3">
                <Clock className="h-4 w-4 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Total Installments</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">
                  {summary.totalInstallments}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{summary.paidCount} Completed</span>
                  {summary.pendingCount + summary.overdueCount > 0 && (
                    <>
                      <span className="mx-1">•</span>
                      <span>{summary.pendingCount + summary.overdueCount} Remaining</span>
                    </>
                  )}
                </div>
              </div>
              <div className="bg-blue-100 p-2 rounded-lg dark:bg-blue-900/30 ml-3">
                <FileBarChart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Installments Table */}
      <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
        <CardHeader className="border-b bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <CardTitle className="text-lg font-medium">Installment Schedule</CardTitle>
            <div className="flex flex-wrap gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search records..."
                  className="pl-8 h-9 w-48 md:w-64 bg-secondary"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                {isSearching && (
                  <div className="absolute right-2.5 top-2.5">
                    <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                  </div>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-9 w-9 p-0 bg-secondary" 
                    title="Export"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Export as PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Print Schedule
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-secondary dark:bg-gray-800/50">
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Installment #
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">Lead File</TableHead>
                  <TableHead className="font-medium">Plot</TableHead>
                  <TableHead className="font-medium text-right">Amount</TableHead>
                  <TableHead className="font-medium text-right">Paid</TableHead>
                  <TableHead className="font-medium text-right">Remaining</TableHead>
                  <TableHead className="font-medium">Due Date</TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="font-medium text-right">Penalties</TableHead>
                  <TableHead className="sr-only">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {installmentsData && installmentsData.results && installmentsData.results.length > 0 ? (
                  installmentsData.results.map((installment: InstallmentSchedule) => {
                    const status = getInstallmentStatus(installment);
                    const isDueApproach = isDueApproaching(installment.due_date);
                    return (
                      <TableRow 
                        key={`${installment.leadfile_no}-${installment.installment_no}`} 
                        className={`hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors ${
                          isDueApproach && status !== 'Paid' ? 'bg-amber-50/50 dark:bg-amber-900/10' : ''
                        }`}
                      >
                        <TableCell className="font-medium text-blue-600 dark:text-blue-400">
                          #{installment.installment_no}
                        </TableCell>
                        <TableCell className="font-medium">{installment.leadfile_no}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{installment.plot_No}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{installment.plot_Name}</div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          KES {formatCurrency(installment.installment_amount)}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          KES {formatCurrency(installment.amount_Paid)}
                        </TableCell>
                        <TableCell className="text-right">
                          {parseFloat((installment.remaining_Amount || "0").replace(/,/g, '')) === 0 ? (
                            <span className="text-green-600 dark:text-green-400 font-medium">Cleared</span>
                          ) : (
                            `KES ${formatCurrency(installment.remaining_Amount)}`
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <span className="text-sm">{formatDate(installment.due_date)}</span>
                            {isDueApproach && status !== 'Paid' && (
                              <AlertCircle className="ml-2 h-4 w-4 text-amber-500" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={`inline-flex items-center gap-1 ${getStatusColor(status)}`}
                          >
                            {getStatusIcon(status)}
                            {status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {installment.penaties_Accrued > 0 ? (
                            <span className="text-red-600 dark:text-red-400 font-medium">
                              KES {formatCurrency(installment.penaties_Accrued)}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <CreditCard className="h-4 w-4 mr-2" />
                                Record Payment
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <FileBarChart className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="h-4 w-4 mr-2" />
                                Download Receipt
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center space-y-2">
                        <FileBarChart className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500 dark:text-gray-400">No installments found</p>
                        <p className="text-sm text-gray-400 dark:text-gray-500">
                          {searchTerm || Object.values(filters).some(Boolean) 
                            ? "Try adjusting your search or filters" 
                            : "This customer has no installment records"
                          }
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        
        {/* Pagination */}
        {installmentsData && installmentsData.results && installmentsData.results.length > 0 && (
          <CardFooter className="border-t bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 w-full">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, installmentsData.count)} of {installmentsData.count} installments
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousPage}
                  disabled={!installmentsData.previous || currentPage === 1}
                  className="h-8"
                >
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Page {currentPage} of {Math.ceil(installmentsData.count / pageSize)}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={!installmentsData.next}
                  className="h-8"
                >
                  Next
                </Button>
              </div>
            </div>
          </CardFooter>
        )}
      </Card>

      {/* Additional Summary Information */}
      {summary.totalPenalties > 0 && (
        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-red-100 p-3 rounded-full dark:bg-red-900/30">
                  <AlertCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Total Penalties Accrued
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Late payment penalties across all installments
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                  KES {formatCurrency(summary.totalPenalties)}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  From {summary.overdueCount} overdue installments
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CustomerInstallmentsTab;