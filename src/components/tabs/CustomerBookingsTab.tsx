import { useState, useEffect } from "react";
import {
  FileText,
  DollarSign,
  Search,
  ChevronDown,
  FileBarChart,
  CheckCircle2,
  ArrowUpDown,
  Download,
  Clock
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { <PERSON><PERSON>, <PERSON>ertDescription } from "@/components/ui/alert";
import { useGetCustomerBookingsQuery } from "@/redux/slices/customersApiSlice";

interface BookingsTabProps {
  customerId: string;
  customerPhone?: string;
}

interface BookingItem {
  plots: string;
  booking_type: string;
  status: string;
  amount: number;
  creation_date: string;
  customer_id: string;
  lead_id: string | null;
  marketer_id: string;
  fullnames: string;
  name: string | null;
  customer_name: string;
}



interface BookingsSummary {
  totalBookings: number;
  totalAmount: number;
  avgBookingAmount: number;
  doneBookings: number;
  waitingBookings: number;
  timedBookings: number;
  specialBookings: number;
  otherBookings: number;
}

interface FilterState {
  BOOKING_TYPE: string;
  STATUS: string;
  MARKETER_EMPLOYEE_NO: string;
  search: string;
}

const CustomerBookingsTab = ({ customerId, customerPhone }: BookingsTabProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<FilterState>({
    BOOKING_TYPE: "ALL",
    STATUS: "ALL",
    MARKETER_EMPLOYEE_NO: "ALL",
    search: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  // Build query parameters - use customer phone as search to filter bookings for this specific customer
  // If user has entered additional search terms, combine them with customer phone
  const searchQuery = customerPhone ?
    (searchTerm ? `${customerPhone} ${searchTerm}` : customerPhone) :
    searchTerm;

  const queryParams = {
    BOOKING_TYPE: filters.BOOKING_TYPE,
    STATUS: filters.STATUS,
    MARKETER_EMPLOYEE_NO: filters.MARKETER_EMPLOYEE_NO,
    search: searchQuery, // Use customer phone to filter bookings for this specific customer
    page: currentPage,
    page_size: pageSize
  };

  // Fetch customer bookings using the new API
  const {
    data: bookingsData,
    isLoading,
    isError,
    refetch
  } = useGetCustomerBookingsQuery(queryParams);

  // Handle search input changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: searchTerm }));
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const calculateSummary = (): BookingsSummary => {
    if (!bookingsData || !bookingsData.results || bookingsData.results.length === 0) {
      return {
        totalBookings: 0,
        totalAmount: 0,
        avgBookingAmount: 0,
        doneBookings: 0,
        waitingBookings: 0,
        timedBookings: 0,
        specialBookings: 0,
        otherBookings: 0
      };
    }

    const results = bookingsData.results;
    const totalAmount = results.reduce((sum: number, booking: BookingItem) => sum + (booking.amount || 0), 0);
    const doneBookings = results.filter((booking: BookingItem) => booking.status === "DONE").length;
    const waitingBookings = results.filter((booking: BookingItem) => booking.status === "WAITING").length;
    const timedBookings = results.filter((booking: BookingItem) => booking.status === "TIMED").length;
    const specialBookings = results.filter((booking: BookingItem) => booking.booking_type === "SPECIAL").length;
    const otherBookings = results.filter((booking: BookingItem) => booking.booking_type === "OTHER").length;

    return {
      totalBookings: results.length,
      totalAmount,
      avgBookingAmount: results.length > 0 ? totalAmount / results.length : 0,
      doneBookings,
      waitingBookings,
      timedBookings,
      specialBookings,
      otherBookings
    };
  };

  const summary = calculateSummary();

  const getStatusColor = (status: string): string => {
    const statusColors: Record<string, string> = {
      "DONE": "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800",
      "WAITING": "bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800",
      "TIMED": "bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800"
    };

    return statusColors[status] || "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700";
  };

  const getBookingTypeColor = (type: string): string => {
    const typeColors: Record<string, string> = {
      "SPECIAL": "bg-purple-50 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800",
      "OTHER": "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-800",
      "MPESA": "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800"
    };

    return typeColors[type] || "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700";
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <SpinnerTemp type="spinner-double" size="lg" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading bookings data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Bookings Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Total Bookings</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">{summary.totalBookings}</h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>Value: {formatCurrency(summary.totalAmount)}</span>
                </div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full dark:bg-blue-900/30">
                <FileBarChart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Done</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">{summary.doneBookings}</h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>Completed bookings</span>
                </div>
              </div>
              <div className="bg-green-100 p-2 rounded-full dark:bg-green-900/30">
                <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Waiting</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">{summary.waitingBookings}</h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>Pending approval</span>
                </div>
              </div>
              <div className="bg-yellow-100 p-2 rounded-full dark:bg-yellow-900/30">
                <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Average Value</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">{formatCurrency(summary.avgBookingAmount)}</h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>Per booking</span>
                </div>
              </div>
              <div className="bg-purple-100 p-2 rounded-full dark:bg-purple-900/30">
                <DollarSign className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bookings Table */}
      <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
        <CardHeader className="border-b bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-medium">Booking Records</CardTitle>
              {customerPhone && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Filtered by customer phone: {customerPhone}
                </p>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <Input
                  type="search"
                  placeholder="Additional search..."
                  className="pl-8 h-9 w-48 md:w-64 bg-secondary"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>

              <Select value={filters.BOOKING_TYPE} onValueChange={(value) => setFilters(prev => ({ ...prev, BOOKING_TYPE: value }))}>
                <SelectTrigger className="h-9 w-32 bg-secondary">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="SPECIAL">Special</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                  <SelectItem value="MPESA">Mpesa</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.STATUS} onValueChange={(value) => setFilters(prev => ({ ...prev, STATUS: value }))}>
                <SelectTrigger className="h-9 w-32 bg-secondary">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Status</SelectItem>
                  <SelectItem value="DONE">Done</SelectItem>
                  <SelectItem value="WAITING">Waiting</SelectItem>
                  <SelectItem value="TIMED">Timed</SelectItem>
                </SelectContent>
              </Select>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                      variant="outline"
                      size="sm"
                      className="h-9 w-9 p-0 bg-secondary"
                      title="Export"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    Print Records
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-secondary dark:bg-gray-800/50">
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Plot
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Booking Type
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">Amount</TableHead>
                  <TableHead className="font-medium">Date Created</TableHead>
                  <TableHead className="font-medium">Marketer</TableHead>
                  <TableHead className="font-medium">Customer</TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="sr-only">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bookingsData?.results?.length > 0 ? (
                  bookingsData.results.map((booking: BookingItem, index: number) => (
                    <TableRow key={`${booking.customer_id}-${booking.plots}-${index}`} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <TableCell className="font-medium text-blue-600 dark:text-blue-400">{booking.plots}</TableCell>
                      <TableCell>
                        <Badge className={`${getBookingTypeColor(booking.booking_type)}`}>
                          {booking.booking_type}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(booking.amount)}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{formatDate(booking.creation_date)}</span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(booking.creation_date).toLocaleTimeString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium text-sm">{booking.fullnames}</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">{booking.marketer_id}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium text-sm">{booking.customer_name}</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">{booking.customer_id}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <ChevronDown className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Update Status</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>Export Record</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      No booking records found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between border-t p-4 bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">{bookingsData?.results?.length || 0}</span> of{" "}
            <span className="font-medium">{bookingsData?.count || 0}</span> records
            {bookingsData?.title && (
              <span className="ml-2 text-xs">({bookingsData.title})</span>
            )}
          </div>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              disabled={!bookingsData?.previous || currentPage <= 1}
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            >
              Previous
            </Button>
            <span className="flex items-center px-3 text-sm text-gray-500">
              Page {currentPage} of {bookingsData?.num_pages || 1}
            </span>
            <Button
              variant="outline"
              size="sm"
              disabled={!bookingsData?.next || currentPage >= (bookingsData?.num_pages || 1)}
              onClick={() => setCurrentPage(prev => prev + 1)}
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CustomerBookingsTab;