import { useState } from "react";
import DrawerModal from "../custom/modals/DrawerModal";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CustomerAvatar from "../customer-section/CustomerAvatar";
import { useForm } from "react-hook-form";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Customer } from "../customer-section/CustomerInfoHeader";

interface EditCustomerDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer;
  onUpdate: (data: Customer) => void;
}

interface CustomerFormValues {
  customer_name: string;
  national_id: string;
  passport_no: string;
  kra_pin: string;
  dob: string | undefined;
  gender: string | null;
  marital_status: string | null;
  phone: string;
  alternative_phone: string;
  primary_email: string;
  alternative_email: string;
  address: string;
  customer_type: "Individual" | "Group";
  country_of_residence: string;
  lead_source: number | null;
  marketer: string;
}

const EditCustomerDrawer = ({
  isOpen,
  onClose,
  customer,
  onUpdate,
}: EditCustomerDrawerProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const form = useForm<CustomerFormValues>({
    defaultValues: {
      customer_name: customer.name || "",
      national_id: customer.nationalId || "",
      passport_no: customer.passportNo || "",
      kra_pin: customer.kraPin || "",
      dob: customer.dob || undefined,
      gender: customer.gender || null,
      marital_status: customer.maritalStatus || null,
      phone: customer.phone || "",
      alternative_phone: customer.alternativePhone || "",
      primary_email: customer.email || "",
      alternative_email: customer.alternativeEmail || "",
      address: customer.address || "",
      customer_type: customer.customerType,
      country_of_residence: customer.countryOfResidence || "",
      lead_source: customer.leadSource,
      marketer: customer.marketer || "",
    },
  });

  const handleSubmit = async (data: CustomerFormValues) => {
    setIsSaving(true);

    try {
      console.log("Updating customer:", data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Convert form values to match Customer type
      // The key is to convert null values to undefined where needed
      const updatedCustomer: Customer = {
        ...customer,
        name: data.customer_name,
        nationalId: data.national_id,
        passportNo: data.passport_no,
        kraPin: data.kra_pin,
        dob: data.dob,
        // Convert null to undefined for gender
        gender: data.gender === null ? undefined : data.gender,
        // Convert null to undefined for maritalStatus
        maritalStatus:
          data.marital_status === null ? undefined : data.marital_status,
        phone: data.phone,
        alternativePhone: data.alternative_phone,
        email: data.primary_email,
        alternativeEmail: data.alternative_email,
        address: data.address,
        customerType: data.customer_type,
        countryOfResidence: data.country_of_residence,
        leadSource: data.lead_source ?? undefined, // Convert null to undefined
        marketer: data.marketer,
      };

      onUpdate(updatedCustomer);

      toast({
        title: "Customer updated",
        description: "Customer information has been updated successfully.",
      });

      onClose();
    } catch (error) {
      console.error("Error updating customer:", error);
      toast({
        title: "Error",
        description: "There was a problem updating the customer information.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DrawerModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Edit Customer"
      description="Update customer information"
      size="lg"
    >
      <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 mb-6">
        <CustomerAvatar
          name={customer.name}
          image={customer.avatar}
          size="lg"
          className="shrink-0"
        />
        <div className="text-center sm:text-left min-w-0 flex-1">
          <h3 className="font-medium text-lg truncate">{customer.name}</h3>
          <p className="text-gray-500 text-sm truncate">
            {customer.email || "No email available"}
          </p>
          <p className="text-gray-500 text-sm">
            Customer No: {customer.customer_no}
          </p>
        </div>
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="mb-4 w-full grid grid-cols-3 sm:w-auto sm:flex">
          <TabsTrigger value="basic" className="text-xs sm:text-sm">Basic Info</TabsTrigger>
          <TabsTrigger value="additional" className="text-xs sm:text-sm">Additional Info</TabsTrigger>
          <TabsTrigger value="contact" className="text-xs sm:text-sm">Contact Details</TabsTrigger>
        </TabsList>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <TabsContent value="basic" className="space-y-4 mt-0">
              <FormField
                control={form.control}
                name="customer_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="marital_status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marital Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select marital status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Single">Single</SelectItem>
                        <SelectItem value="Married">Married</SelectItem>
                        <SelectItem value="Divorced">Divorced</SelectItem>
                        <SelectItem value="Widowed">Widowed</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date of Birth</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(new Date(field.value), "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={
                            field.value ? new Date(field.value) : undefined
                          }
                          onSelect={(date) =>
                            field.onChange(
                              date ? format(date, "yyyy-MM-dd") : undefined
                            )
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customer_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select customer type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Individual">Individual</SelectItem>
                        <SelectItem value="Group">Group</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </TabsContent>

            <TabsContent value="additional" className="space-y-4 mt-0">
              <FormField
                control={form.control}
                name="national_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>National ID</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="passport_no"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Passport Number</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="kra_pin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>KRA PIN</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country_of_residence"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country of Residence</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="marketer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marketer</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </TabsContent>

            <TabsContent value="contact" className="space-y-4 mt-0">
              <FormField
                control={form.control}
                name="primary_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Primary Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="alternative_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alternative Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="alternative_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alternative Phone</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </TabsContent>

            <div className="flex flex-col sm:flex-row justify-end gap-3 mt-6 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onClose} className="w-full sm:w-auto">
                Cancel
              </Button>
              <Button type="submit" disabled={isSaving} className="w-full sm:w-auto">
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </Form>
      </Tabs>
    </DrawerModal>
  );
};

export default EditCustomerDrawer;
