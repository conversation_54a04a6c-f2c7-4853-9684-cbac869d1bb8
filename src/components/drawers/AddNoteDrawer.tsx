import { useState } from "react";
import { useForm } from "react-hook-form";
import DrawerModal from "../custom/modals/DrawerModal";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

interface AddNoteDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  customerId: string;
  onAddNote?: (data: NoteFormValues) => void;
}

interface NoteFormValues {
  title: string;
  content: string;
}

const AddNoteDrawer = ({ isOpen, onClose, customerId, onAddNote }: AddNoteDrawerProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<NoteFormValues>({
    defaultValues: {
      title: '',
      content: ''
    }
  });

  const handleSubmit = async (data: NoteFormValues) => {
    setIsSaving(true);
    
    try {
      console.log("Adding note:", data, "for customer:", customerId);
      
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (onAddNote) {
        onAddNote(data);
      }
      
      toast({
        title: "Note added",
        description: "Your note has been successfully added."
      });
      
      onClose();
      form.reset();
    } catch (error) {
      console.error("Error adding note:", error);
      toast({
        title: "Error",
        description: "There was a problem adding your note.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DrawerModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Add Note"
      description="Create a new note for this customer"
      size="md"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 sm:space-y-6 h-full flex flex-col">
          <div className="flex-1 space-y-4 sm:space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Note title" 
                      {...field} 
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem className="flex-1 flex flex-col">
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter your note here..." 
                      className="min-h-[150px] sm:min-h-[200px] flex-1 resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving} className="w-full sm:w-auto">
              {isSaving ? "Saving..." : "Save Note"}
            </Button>
          </div>
        </form>
      </Form>
    </DrawerModal>
  );
};

export default AddNoteDrawer;