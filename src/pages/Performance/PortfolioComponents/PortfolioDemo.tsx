import React from 'react';
import PortfolioMarketersList from './PortfolioMarketersList';

const PortfolioDemo: React.FC = () => {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Portfolio Marketers Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          This demo shows how to use the portfolio headers data with the marketer profile modal.
          Click on any marketer card to view their detailed profile.
        </p>
      </div>
      
      <PortfolioMarketersList />
    </div>
  );
};

export default PortfolioDemo;