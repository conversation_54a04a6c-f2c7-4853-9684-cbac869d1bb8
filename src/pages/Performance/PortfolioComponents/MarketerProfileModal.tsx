import React, { useState } from "react";
import { User, Target, Calendar } from "lucide-react";
import { Badge } from "@/components/custom/badges/badges";
import { MarketerReport, PortfolioHeader, MarketingPeriod } from "@/types/marketer";
import { cn } from "@/lib/utils";
import { useGetPortfolioHeadersQuery, useGetPortfolioLinesQuery } from "@/redux/slices/profile";
import { useGetPeriodsQuery } from "@/redux/slices/hrDashboardApiSlice";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { formatDate } from "@/utils/formatDate";
import { useAuthHook } from "@/utils/useAuthHook";
import { Screen } from "@/app-components/layout/screen";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";

interface MarketerProfileProps {
  marketerData: MarketerReport | PortfolioHeader | null;
}

// Portfolio Lines columns definition for DataTable
const portfolioLinesColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'booking_date',
    header: 'Booking Date',
    cell: ({ getValue }) => formatDate(getValue()),
    meta: { filterVariant: 'date' },
  },
  {
    accessorKey: 'completion_date',
    header: 'Completion Date',
    cell: ({ getValue }) => formatDate(getValue()),
    meta: { filterVariant: 'date' },
  },
  {
    accessorKey: 'current_balance',
    header: 'Current Balance',
    cell: ({ getValue }) => {
      const value = getValue();
      const num = typeof value === 'number' ? value : parseFloat(value ?? '0');
      return num.toLocaleString('en-KE', { style: 'currency', currency: 'KES' });
    },
  },
  {
    accessorKey: 'customer_number',
    header: 'Customer Number',
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ getValue }) => formatDate(getValue()),
    meta: { filterVariant: 'date' },
  },
  {
    accessorKey: 'installments_due',
    header: 'Installments Due',
  },
  {
    accessorKey: 'installments_due_collected',
    header: 'Installments Collected',
  },
  {
    accessorKey: 'lead_file_no',
    header: 'Lead File No',
  },
  {
    accessorKey: 'marketer_no',
    header: 'Marketer No',
  },
  {
    accessorKey: 'plot_name',
    header: 'Plot Name',
  },
  {
    accessorKey: 'total_to_collect',
    header: 'Total To Collect',
    cell: ({ getValue }) => {
      const value = getValue();
      const num = typeof value === 'number'
        ? value
        : typeof value === 'string'
          ? parseFloat(value)
          : 0;
      return num.toLocaleString('en-KE', { style: 'currency', currency: 'KES' });
    },
  },
  {
    accessorKey: 'total_collected',
    header: 'Total Collected',
    cell: ({ getValue }) => parseFloat(getValue() || '0').toLocaleString('en-KE', { style: 'currency', currency: 'KES' }),
  },
  {
    accessorKey: 'total_previously_collected',
    header: 'Total Previously Collected',
    cell: ({ getValue }) => parseFloat(getValue() || '0').toLocaleString('en-KE', { style: 'currency', currency: 'KES' }),
  },
  {
    accessorKey: 'overdue_collections',
    header: 'Overdue Collections',
    cell: ({ getValue }) => parseFloat(getValue() || '0').toLocaleString('en-KE', { style: 'currency', currency: 'KES' }),
  },
  {
    accessorKey: 'overdue_collections_collected',
    header: 'Overdue Collected',
    cell: ({ getValue }) => parseFloat(getValue() || '0').toLocaleString('en-KE', { style: 'currency', currency: 'KES' }),
  },
  {
    accessorKey: 'penalties_accrued',
    header: 'Penalties Accrued',
    cell: ({ getValue }) => parseFloat(getValue() || '0').toLocaleString('en-KE', { style: 'currency', currency: 'KES' }),
  },
  {
    accessorKey: 'previous_unpaid',
    header: 'Previous Unpaid',
    cell: ({ getValue }) => parseFloat(getValue() || '0').toLocaleString('en-KE', { style: 'currency', currency: 'KES' }),
  },
  {
    accessorKey: 'period_start_date',
    header: 'Period Start',
    cell: ({ getValue }) => formatDate(getValue()),
    meta: { filterVariant: 'date' },
  },
  {
    accessorKey: 'period_end_date',
    header: 'Period End',
    cell: ({ getValue }) => formatDate(getValue()),
    meta: { filterVariant: 'date' },
  },
];

const MarketerProfile: React.FC<MarketerProfileProps> = ({ marketerData }) => {
  const { user_details } = useAuthHook();
  const currentUserEmployeeNo = user_details?.employee_no;

  // Period selection state and fetch
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedPeriod, setSelectedPeriod] = useState<MarketingPeriod | null>(null);
  const { data: periodsData } = useGetPeriodsQuery({ page: currentPage, page_size: 50 });

  // Modal state for Portfolio Information details
  const [isPortfolioModalOpen, setIsPortfolioModalOpen] = useState(false);

  // Determine current/active period defaults
  const currentPeriod = periodsData?.results?.[0];
  const activePeriodStart = selectedPeriod?.period_start_date || currentPeriod?.period_start_date || '';
  const activePeriodEnd = selectedPeriod?.period_end_date || currentPeriod?.period_end_date || '';

  // Determine marketer number for filtering (strictly use logged-in employee no if available)
  const marketerNoParam: string = currentUserEmployeeNo ? String(currentUserEmployeeNo) : "";

  // Fetch portfolio header for this marketer and active period
  const { data: portfolioData, error: portfolioError } = useGetPortfolioHeadersQuery(
    {
      ...(marketerNoParam ? { marketer_no: marketerNoParam } : {}),
      ...(activePeriodStart ? { period_start_date: activePeriodStart } : {}),
      ...(activePeriodEnd ? { period_end_date: activePeriodEnd } : {}),
      page: 1,
      page_size: 1,
    },
    { skip: !marketerNoParam }
  );

  // Fetch only portfolio lines for this marketer (and period if available)
  const { data: portfolioLine, error: portfolioLineError } = useGetPortfolioLinesQuery(
    {
      ...(marketerNoParam ? { marketer_no: marketerNoParam } : {}),
      ...(activePeriodStart ? { period_start_date: activePeriodStart } : {}),
      ...(activePeriodEnd ? { period_end_date: activePeriodEnd } : {}),
      page: 1,
      page_size: 100,
    },
    { skip: !marketerNoParam }
  );

  // Use the first header result as active portfolio marketer
  const portfolioMarketer = React.useMemo(() => {
    try {
      const res = portfolioData?.data?.results;
      if (!res || !Array.isArray(res) || res.length === 0) return null;
      return res[0];
    } catch (e) {
      console.error('Error reading portfolio header:', e);
      return null;
    }
  }, [portfolioData]);

  // Client-side safety filter in case API returns unfiltered data
  const filteredLines = React.useMemo(() => {
    try {
      const res = portfolioLine?.data?.results;
      if (!res || !Array.isArray(res)) return [];

      return res.filter((l: any) => {
        const marketerMatch = marketerNoParam ? String(l.marketer_no) === String(marketerNoParam) : true;
        const startMatch = !activePeriodStart || l.period_start_date === activePeriodStart;
        const endMatch = !activePeriodEnd || l.period_end_date === activePeriodEnd;
        return marketerMatch && startMatch && endMatch;
      });
    } catch (error) {
      console.error('Error filtering portfolio lines:', error);
      return [];
    }
  }, [portfolioLine, marketerNoParam, activePeriodStart, activePeriodEnd]);

  // Helper functions to handle different data structures
  const getMarketerNumber = (data: MarketerReport | PortfolioHeader | null): string => {
    if (!data) return '';
    if ('marketer_no' in data) {
      return data.marketer_no;
    }
    return data.marketer_no_id || data.id?.toString() || '';
  };

  const getMarketerName = (data: MarketerReport | PortfolioHeader | null): string => {
    if (!data) return '';
    return data.marketer_name || '';
  };

  const getMonthlyTarget = (data: MarketerReport | PortfolioHeader | null): number => {
    if (!data) return 0;
    if ('monthly_target' in data && data.monthly_target) {
      return data.monthly_target;
    }
    return 0;
  };

  const getDailyTarget = (data: MarketerReport | PortfolioHeader | null): number => {
    if (!data) return 0;
    if ('daily_target' in data && data.daily_target) {
      return data.daily_target;
    }
    // Calculate daily target from monthly (assuming 22 working days)
    const monthlyTarget = getMonthlyTarget(data);
    return monthlyTarget > 0 ? monthlyTarget / 22 : 0;
  };

  const getAchievedAmount = (data: MarketerReport | PortfolioHeader | null): number => {
    if (!data) return 0;
    if ('MIB_achieved' in data && data.MIB_achieved) {
      return data.MIB_achieved;
    }
    if ('total_purchases' in data) {
      return parseFloat(data.total_purchases) || 0;
    }
    return 0;
  };



  const getPerformancePercentage = (data: MarketerReport | PortfolioHeader | null): number => {
    if (!data) return 0;
    if ('MIB_Perfomance' in data && data.MIB_Perfomance) {
      return data.MIB_Perfomance;
    }
    // Calculate performance percentage
    const achieved = getAchievedAmount(data);
    const target = getMonthlyTarget(data);
    return target > 0 ? (achieved / target) * 100 : 0;
  };

  // Enhanced helper functions that use portfolio data when available
  const getEnhancedMonthlyTarget = (): number => {
    if (portfolioMarketer?.marketer_target) return parseFloat(portfolioMarketer.marketer_target) || 0;
    return getMonthlyTarget(marketerData);
  };

  const getEnhancedAchievedAmount = (): number => {
    if (portfolioMarketer?.total_purchases) return parseFloat(portfolioMarketer.total_purchases) || 0;
    return getAchievedAmount(marketerData);
  };

  const getEnhancedPerformancePercentage = (): number => {
    if (portfolioMarketer?.MIB_perfomance) return parseFloat(portfolioMarketer.MIB_perfomance) || 0;
    return getPerformancePercentage(marketerData);
  };

  // Calculate enhanced values first
  const targetAmount = getEnhancedMonthlyTarget();
  const achievementAmount = getEnhancedAchievedAmount();
  const performancePercentage = getEnhancedPerformancePercentage();
  const dailyTargetAmount = targetAmount > 0 ? targetAmount / 22 : 0; // 22 working days assumption

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 100) return "text-green-600 bg-green-50 border-green-200";
    if (percentage >= 70) return "text-orange-600 bg-orange-50 border-orange-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getPerformanceStatus = (percentage: number) => {
    if (percentage >= 100) return "Excellent";
    if (percentage >= 80) return "Good";
    if (percentage >= 60) return "Average";
    return "Needs Improvement";
  };

  // Safe date formatting
  const formatDateSafe = (dateString: string | undefined) => {
    if (!dateString) return 'Not specified';
    try {
      return new Date(dateString).toLocaleDateString('en-KE', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const displayMarketerName = getMarketerName(marketerData) || portfolioMarketer?.marketer_name || user_details?.fullnames || '';

  return (
    <Screen>
      <div className="p-4 md:p-6 space-y-4 md:space-y-6 max-w-[1200px] mx-auto">
        {/* Compact Header */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3">
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-3 min-w-0">
              <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white text-base font-semibold shrink-0">
                {(getMarketerName(marketerData) || portfolioMarketer?.marketer_name || user_details?.fullnames || 'M').charAt(0).toUpperCase()}
              </div>
              <div className="min-w-0">
                <div className="flex items-center gap-2 min-w-0">
                  <h3 className="text-base font-semibold text-gray-900 dark:text-white truncate">
                    {getMarketerName(marketerData) || portfolioMarketer?.marketer_name || user_details?.fullnames || 'My Portfolio'}
                  </h3>
                  <span className="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-200 border-gray-200 dark:border-gray-700">
                    ID: {marketerNoParam || user_details?.employee_no || 'N/A'}
                  </span>
                </div>
                <div className="mt-1 flex flex-wrap items-center gap-2 text-[11px] text-gray-600 dark:text-gray-300">
                  {(marketerData && 'title' in marketerData && marketerData.title) || portfolioMarketer?.title ? (
                    <span className="truncate">{(marketerData && 'title' in marketerData && marketerData.title) || portfolioMarketer?.title}</span>
                  ) : null}
                  {portfolioMarketer?.team && (
                    <span className="px-1.5 py-0.5 rounded bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300">Team: {portfolioMarketer.team}</span>
                  )}
                  {portfolioMarketer?.region && (
                    <span className="px-1.5 py-0.5 rounded bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">Region: {portfolioMarketer.region}</span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2 shrink-0">
              <span className={cn("inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium border", getPerformanceColor(performancePercentage))}>
                {getPerformanceStatus(performancePercentage)}
              </span>
            </div>
          </div>
        </div>

        {/* Period Toolbar */}
        <div className="sticky top-2 z-10 bg-white/70 dark:bg-gray-900/70 backdrop-blur border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2">
          <div className="flex items-center justify-between gap-3">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-300">Period</span>
            <div className="w-56">
              <Select
                value={selectedPeriod ? `${selectedPeriod.period_start_date}|${selectedPeriod.period_end_date}` : ''}
                onValueChange={(val) => {
                  const [start, end] = val.split("|");
                  setSelectedPeriod({ period_start_date: start, period_end_date: end });
                }}
              >
                <SelectTrigger className="h-8 px-2 text-sm">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent className="max-h-60 text-sm">
                  {periodsData?.results?.map((p: MarketingPeriod, idx: number) => (
                    <SelectItem key={`${p.period_start_date}-${p.period_end_date}-${idx}`} value={`${p.period_start_date}|${p.period_end_date}`}>
                      {new Date(p.period_start_date).toLocaleDateString('en-KE', { month: 'short', year: 'numeric' })}
                      {" - "}
                      {new Date(p.period_end_date).toLocaleDateString('en-KE', { month: 'short', year: 'numeric' })}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Quick Stats Grid - Only show when marketer data exists */}
        {marketerData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Monthly Target</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {new Intl.NumberFormat('en-KE', {
                      style: 'currency',
                      currency: 'KES',
                      minimumFractionDigits: 0,
                    }).format(targetAmount)}
                  </p>
                </div>
                <Target className="h-8 w-8 text-blue-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Achieved</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {new Intl.NumberFormat('en-KE', {
                      style: 'currency',
                      currency: 'KES',
                      minimumFractionDigits: 0,
                    }).format(achievementAmount)}
                  </p>
                </div>
                <User className="h-8 w-8 text-green-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Performance</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {performancePercentage.toFixed(1)}%
                  </p>
                </div>
                <div className={cn(
                  "h-8 w-8 rounded-full flex items-center justify-center text-white text-sm font-bold",
                  performancePercentage >= 100 ? "bg-green-600" : 
                  performancePercentage >= 70 ? "bg-orange-500" : "bg-red-500"
                )}>
                  %
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Daily Target</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {new Intl.NumberFormat('en-KE', {
                      style: 'currency',
                      currency: 'KES',
                      minimumFractionDigits: 0,
                    }).format(dailyTargetAmount)}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>
        )}

        {/* Period Information - Show when a period is selected */}
        {selectedPeriod && (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Period Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Period Start</p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {formatDateSafe(selectedPeriod?.period_start_date)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Period End</p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {formatDateSafe(selectedPeriod?.period_end_date)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Portfolio Information */}
        {portfolioMarketer && (
          <div
            role="button"
            tabIndex={0}
            onClick={() => setIsPortfolioModalOpen(true)}
            onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') setIsPortfolioModalOpen(true) }}
            className="group cursor-pointer bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500/30"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Portfolio Information</h4>
              <span className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">Click to view details</span>
            </div>

            {/* Top tags like in Commissions header */}
            <div className="flex flex-wrap items-center gap-2 mb-4">
              <span className="px-2 py-1 text-xs rounded bg-gray-100 dark:bg-gray-700/50">Emp No: {marketerNoParam}</span>
              {portfolioMarketer.team && (
                <span className="px-2 py-1 text-xs rounded bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">Team: {portfolioMarketer.team}</span>
              )}
              {portfolioMarketer.region && (
                <span className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">Region: {portfolioMarketer.region}</span>
              )}
              {portfolioMarketer.locality && (
                <span className="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">Locality: {portfolioMarketer.locality}</span>
              )}
            </div>

            {/* Period info */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-xs text-gray-600 dark:text-gray-400">Period Start</p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">{formatDateSafe(portfolioMarketer.period_start_date)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-600 dark:text-gray-400">Period End</p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">{formatDateSafe(portfolioMarketer.period_end_date)}</p>
              </div>
            </div>

            {/* KPI summary row - compact */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                <p className="text-[11px] text-gray-600 dark:text-gray-400">Line Number</p>
                <p className="text-base font-semibold">{portfolioMarketer.line_no || '—'}</p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                <p className="text-[11px] text-gray-600 dark:text-gray-400">Monthly Target</p>
                <p className="text-base font-semibold">
                  {Number(portfolioMarketer.marketer_target || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                <p className="text-[11px] text-gray-600 dark:text-gray-400">Total Purchases</p>
                <p className="text-base font-semibold">
                  {Number(portfolioMarketer.total_purchases || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                <p className="text-[11px] text-gray-600 dark:text-gray-400">Performance</p>
                <p className="text-base font-semibold">
                  {Number(portfolioMarketer.MIB_perfomance || 0).toLocaleString()}%
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Portfolio Details Modal */}
        <Dialog open={isPortfolioModalOpen} onOpenChange={setIsPortfolioModalOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Portfolio Details</DialogTitle>
              <DialogDescription>Deeper view of your current portfolio metrics and period info.</DialogDescription>
            </DialogHeader>

            {portfolioMarketer && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Emp No</p>
                    <p className="font-medium">{marketerNoParam}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Team / Region</p>
                    <p className="font-medium">{portfolioMarketer.team || '—'}{portfolioMarketer.region ? ` / ${portfolioMarketer.region}` : ''}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Locality</p>
                    <p className="font-medium">{portfolioMarketer.locality || '—'}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Line Number</p>
                    <p className="font-medium">{portfolioMarketer.line_no || '—'}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Period Start</p>
                    <p className="font-medium">{formatDateSafe(portfolioMarketer.period_start_date)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Period End</p>
                    <p className="font-medium">{formatDateSafe(portfolioMarketer.period_end_date)}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Monthly Target</p>
                    <p className="text-base font-semibold">
                      {Number(portfolioMarketer.marketer_target || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}
                    </p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Total Purchases</p>
                    <p className="text-base font-semibold">
                      {Number(portfolioMarketer.total_purchases || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}
                    </p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Performance</p>
                    <p className="text-base font-semibold">{Number(portfolioMarketer.MIB_perfomance || 0).toLocaleString()}%</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Total Collections</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_collections || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Total To Collect</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_to_collect || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Installments Due</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_installments_due || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Installments Collected</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_installments_collected || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Overdue</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_overdue || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Overdue Collected</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_overdue_collected || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Previous Unpaid</p>
                    <p className="font-medium">{Number(portfolioMarketer.total_previous_unpaid || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-white dark:bg-gray-800">
                    <p className="text-[11px] text-gray-600 dark:text-gray-400">Portfolio Balance</p>
                    <p className="font-medium">{Number(portfolioMarketer.portfolio_balance || 0).toLocaleString('en-KE', { style: 'currency', currency: 'KES', minimumFractionDigits: 0 })}</p>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Portfolio Lines DataTable */}
        {(portfolioMarketer || marketerData) && (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Portfolio Lines</h4>
              <div className="text-xs text-gray-500">{filteredLines?.length || 0} rows</div>
            </div>
            {filteredLines && filteredLines.length > 0 ? (
              <DataTable
                data={filteredLines}
                columns={portfolioLinesColumns}
                title="Portfolio Lines"
                enableToolbar={true}
                enablePagination={true}
                enableColumnFilters={true}
                enableSorting={true}
                tableClassName="text-xs md:text-sm"
                pageSize={10}
                paginationOptions={[10, 25, 50]}
              />
            ) : (
              <div className="text-center py-8 text-gray-600 dark:text-gray-300">
                No portfolio lines found for your account in the selected period.
              </div>
            )}
          </div>
        )}

        {/* No marketer selection block removed: page should always reflect current user context */}
      </div>
    </Screen>
  );
};

// Utility function to convert PortfolioHeader to MarketerReport format
export const createMarketerReportFromPortfolio = (portfolioHeader: PortfolioHeader): MarketerReport => {
  const totalPurchases = parseFloat(portfolioHeader.total_purchases) || 0;
  const defaultMonthlyTarget = 1000000; // Default target, should be configurable
  const performancePercentage = defaultMonthlyTarget > 0 ? (totalPurchases / defaultMonthlyTarget) * 100 : 0;

  return {
    id: portfolioHeader.line_no,
    marketer_no_id: portfolioHeader.marketer_no,
    period_start_date: portfolioHeader.period_start_date,
    period_end_date: portfolioHeader.period_end_date,
    monthly_target: portfolioHeader.monthly_target || defaultMonthlyTarget,
    daily_target: portfolioHeader.daily_target || (defaultMonthlyTarget / 22),
    MIB_achieved: totalPurchases,
    MIB_Perfomance: performancePercentage,
    
    // Optional fields
    line_no: portfolioHeader.line_no,
    marketer_no: portfolioHeader.marketer_no,
    marketer_name: portfolioHeader.marketer_name,
    title: portfolioHeader.title,
  };
};

export default MarketerProfile;