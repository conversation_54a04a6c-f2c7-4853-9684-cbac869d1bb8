import React, { useState } from 'react';
import { useGetPortfolioHeadersQuery } from '@/redux/slices/profile';
import { PortfolioHeader, MarketerReport } from '@/types/marketer';
import MarketerProfileModal, { createMarketerReportFromPortfolio } from './MarketerProfileModal';
import { PrimaryButton } from '@/components/custom/buttons/buttons';
import { User, TrendingUp, Target } from 'lucide-react';

const PortfolioMarketersList: React.FC = () => {
  const { data: portfolioData, isLoading, error } = useGetPortfolioHeadersQuery({});
  const [selectedMarketer, setSelectedMarketer] = useState<MarketerReport | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  const handleViewProfile = (portfolioHeader: PortfolioHeader) => {
    const marketerReport = createMarketerReportFromPortfolio(portfolioHeader);
    setSelectedMarketer(marketerReport);
    setModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600 dark:text-gray-400">Loading marketers...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-red-600 dark:text-red-400">Error loading marketers data</div>
      </div>
    );
  }

  if (!portfolioData?.data?.results?.length) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600 dark:text-gray-400">No marketers data available</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Portfolio Marketers
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {portfolioData.data.total_data} total marketers
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {portfolioData.data.results.map((marketer) => {
          const totalPurchases = parseFloat(marketer.total_purchases) || 0;
          const defaultTarget = 1000000; // This should be configurable
          const performance = defaultTarget > 0 ? (totalPurchases / defaultTarget) * 100 : 0;

          return (
            <div
              key={marketer.line_no}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold">
                  {(marketer.marketer_name || marketer.marketer_no).charAt(0).toUpperCase()}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {marketer.marketer_name || 'Unknown Marketer'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {marketer.marketer_no}
                  </p>
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Total Purchases</span>
                  </div>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {new Intl.NumberFormat('en-KE', {
                      style: 'currency',
                      currency: 'KES',
                      notation: 'compact',
                      maximumFractionDigits: 1,
                    }).format(totalPurchases)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Performance</span>
                  </div>
                  <span className={`font-semibold ${
                    performance >= 100 ? 'text-green-600' :
                    performance >= 70 ? 'text-orange-600' : 'text-red-600'
                  }`}>
                    {performance.toFixed(1)}%
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-purple-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Line No</span>
                  </div>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {marketer.line_no}
                  </span>
                </div>
              </div>

              <PrimaryButton
                onClick={() => handleViewProfile(marketer)}
                className="w-full"
                size="sm"
              >
                View Profile
              </PrimaryButton>
            </div>
          );
        })}
      </div>

      {/* Pagination Info */}
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <div>
          Page {portfolioData.data.current_page} of {portfolioData.data.last_page}
        </div>
        <div>
          Showing {portfolioData.data.results.length} of {portfolioData.data.total_data} marketers
        </div>
      </div>

      {/* Marketer Profile Modal */}
      {selectedMarketer && (
        <MarketerProfileModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          marketerData={selectedMarketer}
        />
      )}
    </div>
  );
};

export default PortfolioMarketersList;