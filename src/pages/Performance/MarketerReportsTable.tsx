import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { AlertCircle } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetMarketerPerformanceByPeriodQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { MarketerReport, MarketingPeriod } from "@/types/marketer";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { format } from "date-fns";
import BaseModal from "@/components/custom/modals/BaseModal";

interface MarketerReportsTableProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    selectedPeriod: MarketingPeriod | null;
    employeeNo: string | null;
}

export default function MarketerReportsTable({
    open,
    onOpenChange,
    selectedPeriod,
    employeeNo
}: MarketerReportsTableProps) {
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);

    // Fetch marketer performance data filtered by employee number
    const {
        data: reportsData,
        isLoading,
        error
    } = useGetMarketerPerformanceByPeriodQuery(
        {
            marketing_period: selectedPeriod?.period_start_date || "",
            marketer_employee_no: employeeNo || "ALL",
            page: currentPage,
            page_size: pageSize
        },
        {
            skip: !selectedPeriod || !employeeNo
        }
    );

    if (error) {
        console.error("API Error Details:", error);
    }

    const data: MarketerReport[] = reportsData?.results || [];
    const totalRecords = reportsData?.count || 0;
    const totalPages = reportsData?.num_pages || 1;

    const formatPeriodName = (period: MarketingPeriod | null) => {
        if (!period) return "";
        const startDate = new Date(period.period_start_date);
        const endDate = new Date(period.period_end_date);
        return `${format(startDate, "MMM yyyy")} - ${format(endDate, "MMM yyyy")}`;
    };

    // Columns without marketer name and ID
    const columns: ColumnDef<MarketerReport>[] = [
        {
            accessorKey: "period_start_date",
            header: "Period Start",
            cell: (info) => {
                const value = info.getValue() as string;
                return new Date(value).toLocaleDateString('en-KE');
            },
            enableColumnFilter: true,
            filterFn: "includesString",
        },
        {
            accessorKey: "period_end_date",
            header: "Period End",
            cell: (info) => {
                const value = info.getValue() as string;
                return new Date(value).toLocaleDateString('en-KE');
            },
            enableColumnFilter: true,
            filterFn: "includesString",
        },
        {
            accessorKey: "monthly_target",
            header: "Monthly Target",
            cell: (info) => {
                const value = info.getValue() as number;
                return new Intl.NumberFormat('en-KE', {
                    style: 'currency',
                    currency: 'KES',
                    minimumFractionDigits: 0,
                }).format(value);
            },
            enableColumnFilter: true,
            filterFn: "includesString",
        },
        {
            accessorKey: "daily_target",
            header: "Daily Target",
            cell: (info) => {
                const value = info.getValue() as number;
                return new Intl.NumberFormat('en-KE', {
                    style: 'currency',
                    currency: 'KES',
                    minimumFractionDigits: 2,
                }).format(value);
            },
            enableColumnFilter: true,
            filterFn: "includesString",
        },
        {
            accessorKey: "MIB_achieved",
            header: "MIB Achieved",
            cell: (info) => {
                const value = info.getValue() as number;
                return new Intl.NumberFormat('en-KE', {
                    style: 'currency',
                    currency: 'KES',
                    minimumFractionDigits: 0,
                }).format(value);
            },
            enableColumnFilter: true,
            filterFn: "includesString",
        },
        {
            accessorKey: "MIB_Perfomance",
            header: "Performance %",
            cell: (info) => {
                const value = info.getValue() as number;
                return (
                    <Badge
                        variant={value >= 100 ? "default" : "secondary"}
                        className={cn(
                            value >= 100
                                ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400"
                                : value >= 70
                                    ? "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                        )}
                    >
                        {value.toFixed(2)}%
                    </Badge>
                );
            },
            enableColumnFilter: true,
            filterFn: "includesString",
        },
    ];

    return (
        <BaseModal
            open={open}
            onOpenChange={onOpenChange}
            title="My Performance Report"
            description={selectedPeriod ? formatPeriodName(selectedPeriod) : "View your performance data"}
            className="max-w-[95vw] w-full"
            size="full"
        >
            <div className="p-4 bg-white dark:bg-gray-900 rounded-md max-w-full overflow-hidden">
                {/* Content */}
                {isLoading ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
                            <p className="text-gray-600 dark:text-gray-400">Loading your performance data...</p>
                        </div>
                    </div>
                ) : error ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
                            <p className="text-red-600 dark:text-red-400">Failed to load performance data</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                Error: {error && 'status' in error ? `${error.status}` : 'Unknown error'}
                            </p>
                        </div>
                    </div>
                ) : !employeeNo ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <AlertCircle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
                            <p className="text-orange-600 dark:text-orange-400">Unable to identify user</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Please log in again</p>
                        </div>
                    </div>
                ) : data.length === 0 ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-600 dark:text-gray-400">No performance data found</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                No records available for the selected period
                            </p>
                        </div>
                    </div>
                ) : (
                    <div className="w-full max-w-full overflow-hidden">
                        <div className="table-mobile-scroll">
                            <DataTable<MarketerReport>
                                data={data}
                                columns={columns}
                                title={`Performance Report - ${formatPeriodName(selectedPeriod)}`}
                                enableExportToExcel={true}
                                enablePrintPdf={true}
                                enableColumnFilters={true}
                                enableSorting={true}
                                enableToolbar={true}
                                containerClassName="w-full bg-white dark:bg-gray-900 rounded-lg shadow-md"
                                tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300"
                                tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
                                tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
                                tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
                                tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                tBodyCellsClassName="px-4 py-2"
                            />
                        </div>

                        {/* Pagination Controls */}
                        {totalPages > 1 && (
                            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 my-6 px-2">
                                <div className="flex items-center gap-2">
                                    <PrimaryButton
                                        onClick={() => setCurrentPage(1)}
                                        disabled={currentPage === 1}
                                        className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === 1
                                            ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                                            : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/30 border-emerald-200 dark:border-emerald-700"
                                            }`}
                                        aria-label="First page"
                                    >
                                        First
                                    </PrimaryButton>
                                    <PrimaryButton
                                        onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                                        disabled={currentPage === 1}
                                        className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === 1
                                            ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                                            : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/30 border-emerald-200 dark:border-emerald-700"
                                            }`}
                                        aria-label="Previous page"
                                    >
                                        Previous
                                    </PrimaryButton>
                                    <span className="mx-2 text-sm text-gray-700 dark:text-gray-300">
                                        Page <span className="font-semibold">{currentPage}</span> of <span className="font-semibold">{totalPages}</span>
                                    </span>
                                    <PrimaryButton
                                        onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                                        disabled={currentPage === totalPages}
                                        className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === totalPages
                                            ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                                            : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/30 border-emerald-200 dark:border-emerald-700"
                                            }`}
                                        aria-label="Next page"
                                    >
                                        Next
                                    </PrimaryButton>
                                    <PrimaryButton
                                        onClick={() => setCurrentPage(totalPages)}
                                        disabled={currentPage === totalPages}
                                        className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === totalPages
                                            ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                                            : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/30 border-emerald-200 dark:border-emerald-700"
                                            }`}
                                        aria-label="Last page"
                                    >
                                        Last
                                    </PrimaryButton>
                                </div>
                                <div className="flex items-center gap-2">
                                    <label htmlFor="pageSize" className="text-sm text-gray-700 dark:text-gray-300">
                                        Records per page:
                                    </label>
                                    <select
                                        id="pageSize"
                                        value={pageSize}
                                        onChange={(e) => {
                                            setPageSize(Number(e.target.value));
                                            setCurrentPage(1);
                                        }}
                                        className="px-3 py-1 rounded-lg border border-emerald-200 dark:border-emerald-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-sm focus:ring-2 focus:ring-emerald-500"
                                    >
                                        {[10, 20, 50, 100].map((size) => (
                                            <option key={size} value={size}>{size} / page</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </BaseModal>
    );
}