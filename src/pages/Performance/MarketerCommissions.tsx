import React, { useState, useMemo, useEffect } from 'react';
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuthHook } from '@/utils/useAuthHook';
import { useGetIndividualCommissionHeadersQuery, useGetIndividualCommissionLinesQuery } from '@/redux/slices/commissionApiSlice';
import { useGetMarketingPeriodsQuery } from '@/redux/slices/hrDashboardApiSlice';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import {
  DollarSign,
  Calendar,
  BarChart3,
  Award,
  Clock,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  
  CreditCard,
  Receipt,
  Banknote,
  Percent
} from 'lucide-react';

const MarketerCommissions = () => {
  const { user_details } = useAuthHook();
  const currentUserEmployeeNo = user_details?.employee_no;

  // State management
  const [selectedPeriod, setSelectedPeriod] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);

  // Fetch marketing periods
  const {
    data: periodsData,
    isLoading: periodsLoading,
    refetch: refetchPeriods
  } = useGetMarketingPeriodsQuery();

  // Get current period (first in the array)
  const currentPeriod = periodsData?.results?.[0];

  // Use selected period or default to current period
  const activePeriod = selectedPeriod || currentPeriod?.period_start_date || "";
  const activePeriodEnd = selectedPeriod ?
    periodsData?.results?.find(p => p.period_start_date === selectedPeriod)?.period_end_date :
    currentPeriod?.period_end_date || "";

  // Fetch commission headers for the logged-in user
  const {
    data: commissionHeadersData,
    isLoading: headersLoading,
    error: headersError,
    refetch: refetchHeaders
  } = useGetIndividualCommissionHeadersQuery({
    emp_no: currentUserEmployeeNo || "",
    period_start_date: activePeriod,
    period_end_date: activePeriodEnd,
    page: currentPage,
    page_size: pageSize,
    search: searchTerm || undefined,
  }, {
    skip: !currentUserEmployeeNo || !activePeriod
  });

  // Fetch commission lines for the logged-in user
  const {
    data: commissionLinesData,
    isLoading: linesLoading,
    error: linesError,
    refetch: refetchLines
  } = useGetIndividualCommissionLinesQuery({
    marketer_no: currentUserEmployeeNo || "",
    period_start_date: activePeriod,
    period_end_date: activePeriodEnd,
    page: 1,
    page_size: 100, // Get more lines for analysis
    search: searchTerm || undefined,
  }, {
    skip: !currentUserEmployeeNo || !activePeriod
  });

  // Fetch all commission headers for history (without period filter)
  const {
    data: allCommissionHeadersData,
    isLoading: allHeadersLoading,
    refetch: refetchAllHeaders
  } = useGetIndividualCommissionHeadersQuery({
    emp_no: currentUserEmployeeNo || "",
    page: 1,
    page_size: 100, // Get more for history
  }, {
    skip: !currentUserEmployeeNo
  });

  // Fetch all commission lines (without period filter) to test if any data exists
  const {
    data: allCommissionLinesData,
    isLoading: allLinesLoading,
    refetch: refetchAllLines
  } = useGetIndividualCommissionLinesQuery({
    marketer_no: currentUserEmployeeNo || "",
    page: 1,
    page_size: 100,
  }, {
    skip: !currentUserEmployeeNo
  });

  // Test with no parameters to see if ANY commission lines exist in the system
  const {
    data: testAllLinesData,
    isLoading: testLinesLoading,
    refetch: refetchTestLines
  } = useGetIndividualCommissionLinesQuery({
    page: 1,
    page_size: 10, // Just get a few to test
  }, {
    skip: !currentUserEmployeeNo
  });

  // Get commission data
  const commissionHeaders = commissionHeadersData?.data?.results || [];
  const commissionLines = commissionLinesData?.data?.results || [];
  const allCommissionHeaders = allCommissionHeadersData?.data?.results || [];
  const allCommissionLines = allCommissionLinesData?.data?.results || [];
  const testAllLines = testAllLinesData?.data?.results || [];
  const currentCommission = commissionHeaders?.[0];

  // Check if we have any historical data even if current period data is missing
  const hasHistoricalData = allCommissionHeaders.length > 0 || allCommissionLines.length > 0;

  // Keep initial tab on "overview"; disable auto-switching to history to preserve default UX
  // useEffect(() => {
  //   if (!currentCommission && hasHistoricalData && activeTab === "overview") {
  //     setActiveTab("history");
  //   }
  // }, [currentCommission, hasHistoricalData, activeTab]);

  // Debug logging
  console.log("=== COMMISSION DEBUG INFO ===");
  console.log("Commission Headers Data:", commissionHeadersData);
  console.log("Commission Lines Data:", commissionLinesData);
  console.log("All Commission Lines Data (no period filter):", allCommissionLinesData);
  console.log("Commission Headers:", commissionHeaders);
  console.log("Commission Lines (filtered):", commissionLines);
  console.log("All Commission Lines (unfiltered):", allCommissionLines);
  console.log("Current Commission:", currentCommission);
  console.log("API Parameters for Headers:", {
    emp_no: currentUserEmployeeNo,
    period_start_date: activePeriod,
    period_end_date: activePeriodEnd
  });
  console.log("API Parameters for Lines:", {
    marketer_no: currentUserEmployeeNo,
    period_start_date: activePeriod,
    period_end_date: activePeriodEnd
  });
  console.log("Total lines without period filter:", allCommissionLines.length);
  console.log("Test All Lines Data (no filters):", testAllLinesData);
  console.log("Test All Lines (any in system):", testAllLines);
  console.log("Test All Lines count:", testAllLines.length);
  if (testAllLines.length > 0) {
    console.log("Sample commission line structure:", testAllLines[0]);
    console.log("Sample marketer_no values:", testAllLines.map((line: any) => line.marketer_no));
  }
  console.log("=== END DEBUG INFO ===");

  // Calculate summary metrics
  const summaryMetrics = useMemo(() => {
    if (!currentCommission) return null;

    const totalCommission = parseFloat(currentCommission.Total_commission || "0");
    const depositCommission = parseFloat(currentCommission.deposit_commission || "0");
    const installmentCommission = parseFloat(currentCommission.installment_commission || "0");
    const payableCommission = parseFloat(currentCommission.commisison_payable_TL || "0");

    const totalDeposits = commissionLines.reduce((sum: number, line: any) =>
      sum + parseFloat(line.new_deposits_collected || "0"), 0);
    const totalInstallments = commissionLines.reduce((sum: number, line: any) =>
      sum + parseFloat(line.installments_collected || "0"), 0);

    return {
      totalCommission,
      depositCommission,
      installmentCommission,
      payableCommission,
      totalDeposits,
      totalInstallments,
      totalTransactions: commissionLines.length,
      depositPercentage: parseFloat(currentCommission.deposit_perc || "0"),
      installmentPercentage: parseFloat(currentCommission.installment_perc || "0"),
    };
  }, [currentCommission, commissionLines]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refetchPeriods(),
        refetchHeaders(),
        refetchLines(),
        refetchAllHeaders(),
        refetchAllLines(),
        refetchTestLines()
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  const isLoading = periodsLoading || headersLoading || linesLoading;

  // More debug logging
  console.log("Loading states:", { periodsLoading, headersLoading, linesLoading, allHeadersLoading });
  console.log("Errors:", { headersError, linesError });
  console.log("User Employee No:", currentUserEmployeeNo);
  console.log("Active Period:", activePeriod, "End:", activePeriodEnd);

  // Add this near the top of your component, after the hooks
  useEffect(() => {
    console.group('=== MARKETER COMMISSIONS DEBUG ===');
    
    // Log API call parameters
    console.log('API Request Parameters:', {
      employee_no: currentUserEmployeeNo,
      period_start_date: activePeriod,
      period_end_date: activePeriodEnd,
      page: currentPage,
      page_size: pageSize
    });

    // Log Commission Data
    console.log('Commission Data:', {
      headers: commissionHeadersData?.data,
      lines: commissionLinesData?.data,
      allHeaders: allCommissionHeadersData?.data,
      allLines: allCommissionLinesData?.data
    });

    // Log Computed Values
    console.log('Computed Values:', {
      currentPeriod,
      selectedPeriod,
      hasHistoricalData,
      summaryMetrics
    });

    // Log Loading States and Errors
    console.log('Loading States:', {
      periodsLoading,
      headersLoading,
      linesLoading,
      allHeadersLoading,
      allLinesLoading
    });

    console.log('Errors:', {
      headersError,
      linesError
    });

    console.groupEnd();
  }, [
    currentUserEmployeeNo,
    activePeriod,
    activePeriodEnd,
    commissionHeadersData,
    commissionLinesData,
    allCommissionHeadersData,
    allCommissionLinesData,
    currentPeriod,
    selectedPeriod,
    hasHistoricalData,
    summaryMetrics
  ]);

  // Add API response logging
  useEffect(() => {
    if (commissionHeadersData || commissionLinesData) {
      console.group('=== API RESPONSES ===');
      console.log('Headers Response:', commissionHeadersData);
      console.log('Lines Response:', commissionLinesData);
      console.groupEnd();
    }
  }, [commissionHeadersData, commissionLinesData]);

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      </Screen>
    );
  }

  if (headersError || linesError) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500 mb-2">Error loading commission data</p>
            <p className="text-sm text-gray-500 mb-4">Please try again later</p>
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </Screen>
    );
  }



  return (
    <Screen>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-600 rounded-lg">
                <DollarSign className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Commissions</h1>
                <p className="text-gray-600 mt-1">Track your commission earnings and transaction details</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    Employee: {currentUserEmployeeNo}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {user_details?.fullnames}
                  </Badge>
                  {currentCommission && (
                    <Badge className="bg-green-100 text-green-800 text-xs">
                      Role: {currentCommission.role}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              {/* Period Selector */}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-[220px]">
                    <SelectValue placeholder="Select Period" />
                  </SelectTrigger>
                  <SelectContent>
                    {periodsData?.results?.map((period, index) => (
                      <SelectItem key={period.period_start_date} value={period.period_start_date}>
                        {index === 0 && "(Current) "}
                        {new Date(period.period_start_date).toLocaleDateString()} - {new Date(period.period_end_date).toLocaleDateString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Navigation */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" defaultValue='overview'>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="transactions" className="flex items-center gap-2">
              <Receipt className="h-4 w-4" />
              Transactions
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              History
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {currentCommission ? (
              <>
                {/* Current Period Info */}
                {currentPeriod && (
                  <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-blue-900">
                        <Clock className="h-5 w-5" />
                        Current Commission Period
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm text-blue-700">Period Duration</p>
                          <p className="font-semibold text-blue-900">
                            {new Date(currentCommission.period_start_date).toLocaleDateString()} - {new Date(currentCommission.period_end_date).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-blue-700">Role</p>
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                            {currentCommission.role}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm text-blue-700">Commission Status</p>
                          <Badge className="bg-green-100 text-green-800 border-0">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Commission Overview Cards */}
                {summaryMetrics && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Total Commission */}
                    <Card className="hover:shadow-lg transition-shadow duration-200">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-700">Total Commission</CardTitle>
                        <div className="p-2 bg-green-100 rounded-lg">
                          <DollarSign className="h-4 w-4 text-green-600" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-gray-900">
                          KSh {formatNumberWithCommas(summaryMetrics.totalCommission)}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Total earnings this period
                        </p>
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs text-green-700 border-green-200">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Commission Earned
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Deposit Commission */}
                    <Card className="hover:shadow-lg transition-shadow duration-200">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-700">Deposit Commission</CardTitle>
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Banknote className="h-4 w-4 text-blue-600" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-gray-900">
                          KSh {formatNumberWithCommas(summaryMetrics.depositCommission)}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {summaryMetrics.depositPercentage}% of KSh {formatNumberWithCommas(summaryMetrics.totalDeposits)}
                        </p>
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs text-blue-700 border-blue-200">
                            <Percent className="h-3 w-3 mr-1" />
                            Deposit Rate: {summaryMetrics.depositPercentage}%
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Installment Commission */}
                    <Card className="hover:shadow-lg transition-shadow duration-200">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-700">Installment Commission</CardTitle>
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <CreditCard className="h-4 w-4 text-purple-600" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-gray-900">
                          KSh {formatNumberWithCommas(summaryMetrics.installmentCommission)}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {summaryMetrics.installmentPercentage}% of KSh {formatNumberWithCommas(summaryMetrics.totalInstallments)}
                        </p>
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs text-purple-700 border-purple-200">
                            <Percent className="h-3 w-3 mr-1" />
                            Installment Rate: {summaryMetrics.installmentPercentage}%
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Payable Commission */}
                    <Card className="hover:shadow-lg transition-shadow duration-200">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-700">Payable Commission</CardTitle>
                        <div className="p-2 bg-orange-100 rounded-lg">
                          <Award className="h-4 w-4 text-orange-600" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-gray-900">
                          KSh {formatNumberWithCommas(summaryMetrics.payableCommission)}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Net commission payable
                        </p>
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs text-orange-700 border-orange-200">
                            <Award className="h-3 w-3 mr-1" />
                            Final Payout
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Commission Breakdown */}
                {summaryMetrics && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Commission Breakdown
                      </CardTitle>
                      <CardDescription>
                        Detailed analysis of your commission structure
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {/* Commission Progress */}
                        <div>
                          <div className="flex justify-between items-center mb-3">
                            <span className="text-sm font-semibold text-gray-700">Commission Distribution</span>
                            <span className="text-lg font-bold text-gray-900">
                              KSh {formatNumberWithCommas(summaryMetrics.totalCommission)}
                            </span>
                          </div>

                          {/* Deposit Commission Bar */}
                          <div className="mb-4">
                            <div className="flex justify-between text-sm mb-1">
                              <span>Deposit Commission</span>
                              <span>KSh {formatNumberWithCommas(summaryMetrics.depositCommission)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{
                                  width: `${summaryMetrics.totalCommission > 0 ?
                                    (summaryMetrics.depositCommission / summaryMetrics.totalCommission) * 100 : 0}%`
                                }}
                              ></div>
                            </div>
                          </div>

                          {/* Installment Commission Bar */}
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>Installment Commission</span>
                              <span>KSh {formatNumberWithCommas(summaryMetrics.installmentCommission)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-purple-600 h-2 rounded-full"
                                style={{
                                  width: `${summaryMetrics.totalCommission > 0 ?
                                    (summaryMetrics.installmentCommission / summaryMetrics.totalCommission) * 100 : 0}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Transaction Summary */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                            <Receipt className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                            <p className="text-sm text-blue-700 font-medium">Total Transactions</p>
                            <p className="font-bold text-blue-900 text-lg">{summaryMetrics.totalTransactions}</p>
                          </div>
                          <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
                            <Banknote className="h-6 w-6 text-green-600 mx-auto mb-2" />
                            <p className="text-sm text-green-700 font-medium">Total Deposits</p>
                            <p className="font-bold text-green-900 text-lg">KSh {formatNumberWithCommas(summaryMetrics.totalDeposits)}</p>
                          </div>
                          <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                            <CreditCard className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                            <p className="text-sm text-purple-700 font-medium">Total Installments</p>
                            <p className="font-bold text-purple-900 text-lg">KSh {formatNumberWithCommas(summaryMetrics.totalInstallments)}</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <DollarSign className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Current Commission Data</h3>
                    <p className="text-gray-500 mb-4">
                      No commission data available for the current period.
                      {hasHistoricalData && " Check the History tab to view past commissions."}
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        Refresh Data
                      </Button>
                      {hasHistoricalData && (
                        <Button variant="default" onClick={() => setActiveTab("history")}>
                          <Clock className="h-4 w-4 mr-2" />
                          View History
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Transactions Tab */}
          <TabsContent value="transactions" className="space-y-6">
            {currentCommission && commissionLines.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Receipt className="h-5 w-5" />
                    Transaction Details
                  </CardTitle>
                  <CardDescription>
                    Individual transactions contributing to your commission
                  </CardDescription>
                </CardHeader>
                <CardContent>

                  {/* Show filtered lines first, fallback to all lines if filtered is empty */}
                  {(commissionLines && commissionLines.length > 0) || (allCommissionLines && allCommissionLines.length > 0) ? (
                    <div className="space-y-4">
                      {/* Search and Filter */}
                      {/* <div className="flex items-center gap-4">
                        <div className="relative flex-1">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <Input
                            placeholder="Search by plot number..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </div> */}

                      {/* Transactions Table */}
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="bg-gray-50 border-b">
                              <th className="text-left p-3 font-semibold text-gray-700">Date</th>
                              <th className="text-left p-3 font-semibold text-gray-700">Plot Number</th>
                              <th className="text-right p-3 font-semibold text-gray-700">New Deposits</th>
                              <th className="text-right p-3 font-semibold text-gray-700">Installments</th>
                              <th className="text-right p-3 font-semibold text-gray-700">Total</th>
                              <th className="text-center p-3 font-semibold text-gray-700">Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {/* Use filtered lines if available, otherwise show all lines */}
                            {(commissionLines.length > 0 ? commissionLines : allCommissionLines).map((transaction: any) => {
                              const deposits = parseFloat(transaction.new_deposits_collected || "0");
                              const installments = parseFloat(transaction.installments_collected || "0");
                              const total = deposits + installments;

                              return (
                                <tr key={transaction.id} className="border-b hover:bg-gray-50">
                                  <td className="p-3">
                                    <div className="font-medium text-gray-900">
                                      {new Date(transaction.transaction_date).toLocaleDateString()}
                                    </div>
                                  </td>
                                  <td className="p-3">
                                    <div className="font-medium text-gray-900">
                                      {transaction.plot_number}
                                    </div>
                                  </td>
                                  <td className="p-3 text-right">
                                    <div className="font-medium text-gray-900">
                                      {deposits > 0 ? `KSh ${formatNumberWithCommas(deposits)}` : '-'}
                                    </div>
                                  </td>
                                  <td className="p-3 text-right">
                                    <div className="font-medium text-gray-900">
                                      {installments > 0 ? `KSh ${formatNumberWithCommas(installments)}` : '-'}
                                    </div>
                                  </td>
                                  <td className="p-3 text-right">
                                    <div className="font-bold text-gray-900">
                                      KSh {formatNumberWithCommas(total)}
                                    </div>
                                  </td>
                                  <td className="p-3 text-center">
                                    <Button variant="ghost" size="sm">
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>

                      {/* Pagination */}
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-500">
                          Showing {commissionLines.length > 0 ? commissionLines.length : allCommissionLines.length} transactions
                          {commissionLines.length === 0 && allCommissionLines.length > 0 && (
                            <span className="text-orange-600 ml-2">(showing all periods - no data for selected period)</span>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" disabled>
                            Previous
                          </Button>
                          <Button variant="outline" size="sm" disabled>
                            Next
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-700 mb-2">No Transactions Found</h3>
                      <p className="text-gray-500 mb-4">
                        No commission transactions available for the selected period
                      </p>
                      <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        Refresh Data
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <Receipt className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Transaction Data</h3>
                    <p className="text-gray-500 mb-4">
                      No transaction details available for the current period.
                      {hasHistoricalData && " Check the History tab to view past transactions."}
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        Refresh Data
                      </Button>
                      {hasHistoricalData && (
                        <Button variant="default" onClick={() => setActiveTab("history")}>
                          <Clock className="h-4 w-4 mr-2" />
                          View History
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Commission History
                </CardTitle>
                <CardDescription>
                  View your commission history across different periods
                </CardDescription>
              </CardHeader>
              <CardContent>
                {allCommissionHeaders && allCommissionHeaders.length > 0 ? (
                  <div className="space-y-4">
                    {/* Summary Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="text-2xl font-bold text-blue-900">
                          {allCommissionHeaders.length}
                        </div>
                        <div className="text-sm text-blue-700">Total Periods</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                        <div className="text-2xl font-bold text-green-900">
                          KSh {formatNumberWithCommas(
                            allCommissionHeaders.reduce((sum: number, header: any) =>
                              sum + parseFloat(header.Total_commission || "0"), 0)
                          )}
                        </div>
                        <div className="text-sm text-green-700">Total Earned</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div className="text-2xl font-bold text-purple-900">
                          KSh {formatNumberWithCommas(
                            allCommissionHeaders.reduce((sum: number, header: any) =>
                              sum + parseFloat(header.deposit_commission || "0"), 0)
                          )}
                        </div>
                        <div className="text-sm text-purple-700">Deposit Commissions</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                        <div className="text-2xl font-bold text-orange-900">
                          KSh {formatNumberWithCommas(
                            allCommissionHeaders.reduce((sum: number, header: any) =>
                              sum + parseFloat(header.installment_commission || "0"), 0)
                          )}
                        </div>
                        <div className="text-sm text-orange-700">Installment Commissions</div>
                      </div>
                    </div>

                    {/* Historical Commission Table */}
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50 border-b">
                            <th className="text-left p-3 font-semibold text-gray-700">Period</th>
                            <th className="text-left p-3 font-semibold text-gray-700">Role</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Total Commission</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Deposit Comm.</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Installment Comm.</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Payable</th>
                          </tr>
                        </thead>
                        <tbody>
                          {[...allCommissionHeaders]
                            .sort((a: any, b: any) => new Date(b.period_start_date).getTime() - new Date(a.period_start_date).getTime())
                            .map((commission: any) => {
                              const isCurrentPeriod = commission.period_start_date === currentPeriod?.period_start_date;

                              return (
                                <tr key={commission.id} className={`border-b hover:bg-gray-50 ${isCurrentPeriod ? 'bg-blue-50' : ''}`}>
                                  <td className="p-3">
                                    <div>
                                      <div className="font-medium text-gray-900">
                                        {new Date(commission.period_start_date).toLocaleDateString()} - {new Date(commission.period_end_date).toLocaleDateString()}
                                      </div>
                                      {isCurrentPeriod && (
                                        <Badge variant="secondary" className="mt-1 text-xs bg-blue-100 text-blue-800">
                                          Current Period
                                        </Badge>
                                      )}
                                    </div>
                                  </td>
                                  <td className="p-3">
                                    <Badge variant="outline" className="text-xs">
                                      {commission.role}
                                    </Badge>
                                  </td>
                                  <td className="p-3 text-right font-medium text-gray-900">
                                    KSh {formatNumberWithCommas(parseFloat(commission.Total_commission || "0"))}
                                  </td>
                                  <td className="p-3 text-right font-medium text-gray-900">
                                    KSh {formatNumberWithCommas(parseFloat(commission.deposit_commission || "0"))}
                                  </td>
                                  <td className="p-3 text-right font-medium text-gray-900">
                                    KSh {formatNumberWithCommas(parseFloat(commission.installment_commission || "0"))}
                                  </td>
                                  <td className="p-3 text-right font-bold text-green-600">
                                    KSh {formatNumberWithCommas(parseFloat(commission.commisison_payable_TL || "0"))}
                                  </td>
                                </tr>
                              );
                            })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Historical Data</h3>
                    <p className="text-gray-500 mb-4">
                      No historical commission data available yet
                    </p>
                    <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                      <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                      Refresh Data
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};

export default MarketerCommissions;
