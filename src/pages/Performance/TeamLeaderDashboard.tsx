import React, { useEffect, useMemo, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Users, ChevronLeft, RefreshCw, Target, DollarSign, LineChart } from 'lucide-react';
import { useGetMarketingPeriodsQuery } from '@/redux/slices/hrDashboardApiSlice';
import { useGetTeamMarketerTargetsQuery } from '@/redux/slices/teamLeaderApiSlice';
import { useAuthHook } from '@/utils/useAuthHook';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';

const TeamLeaderDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user_details } = useAuthHook();

  // URL params: team (name) and period (start date or ALL)
  const initialTeam = searchParams.get('team') || '';
  const initialPeriod = searchParams.get('period') || '';

  const [selectedTeam, setSelectedTeam] = useState<string>(initialTeam);
  const [selectedPeriod, setSelectedPeriod] = useState<string>(initialPeriod);

  // Fetch periods
  const { data: periodsData, isLoading: periodsLoading } = useGetMarketingPeriodsQuery();
  const currentPeriod = periodsData?.results?.[0];

  useEffect(() => {
    if (!selectedPeriod && currentPeriod) {
      setSelectedPeriod(currentPeriod.period_start_date);
    }
  }, [currentPeriod, selectedPeriod]);

  // Determine leader employee number from logged-in user; fall back to ALL
  const leaderEmployeeNo = user_details?.employee_no || 'ALL';

  // The Team Leader API filters by TEAM_LEADER_EMPLOYEE_NO; prefer logged-in leader number
  const apiPeriod = selectedPeriod || currentPeriod?.period_start_date || 'ALL';
  const { data: teamTargets, isLoading: targetsLoading, refetch } = useGetTeamMarketerTargetsQuery({
    leader_employee_no: leaderEmployeeNo,
    marketing_period: apiPeriod,
  });

  const allTeams = teamTargets || [];

  // Auto-select team based on logged-in user's team if none selected in URL
  useEffect(() => {
    if (!selectedTeam && user_details?.team) {
      setSelectedTeam(String(user_details.team));
    }
  }, [selectedTeam, user_details?.team]);

  const selectedTeamData = useMemo(() => {
    if (!selectedTeam) return null;
    return allTeams.find(t => (t.team || '').toLowerCase() === selectedTeam.toLowerCase()) || null;
  }, [allTeams, selectedTeam]);

  // Aggregate metrics for the selected team
  const teamSummary = useMemo(() => {
    const t = selectedTeamData;
    if (!t) return null;
    const members = t.members || [];
    const totalTarget = members.reduce((s, m) => s + (Number(m.monthly_target) || 0), 0);
    const totalAchieved = members.reduce((s, m) => s + (Number(m.MIB_achieved) || 0), 0);
    const avgPerformance = members.length > 0
      ? members.reduce((s, m) => s + (Number(m.MIB_Perfomance) || 0), 0) / members.length
      : 0;
    return {
      leader: { name: t.leader_fullnames, employee_no: t.leader_employee_no },
      period_start_date: t.period_start_date,
      period_end_date: t.period_end_date,
      monthly_target: Number(t.monthly_target) || 0,
      MIB_achieved: Number(t.MIB_achieved) || 0,
      membersCount: members.length,
      totalTarget,
      totalAchieved,
      avgPerformance,
    };
  }, [selectedTeamData]);

  // Keep URL in sync
  useEffect(() => {
    const params: any = {};
    if (selectedTeam) params.team = selectedTeam;
    if (selectedPeriod) params.period = selectedPeriod;
    setSearchParams(params, { replace: true });
  }, [selectedTeam, selectedPeriod, setSearchParams]);

  const handleRefresh = async () => {
    await refetch();
  };

  const isLoading = periodsLoading || targetsLoading;

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-xl border border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" onClick={() => navigate(-1)} className="flex items-center gap-2">
                <ChevronLeft className="h-4 w-4" /> Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Team Leader Dashboard</h1>
                <p className="text-gray-600 text-sm">View your team members' targets and achievements</p>
                {teamSummary && (
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="secondary" className="text-xs">Leader: {teamSummary.leader.name}</Badge>
                    <Badge variant="outline" className="text-xs">Period: {new Date(teamSummary.period_start_date).toLocaleDateString()} - {new Date(teamSummary.period_end_date).toLocaleDateString()}</Badge>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Team selector */}
              <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                <SelectTrigger className="w-56">
                  <SelectValue placeholder="Select Team" />
                </SelectTrigger>
                <SelectContent className="max-h-72">
                  {allTeams.map((t) => (
                    <SelectItem key={`${t.leader_employee_no}-${t.team}`} value={t.team}>
                      {t.team}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Period selector */}
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-56">
                  <SelectValue placeholder="Select Period" />
                </SelectTrigger>
                <SelectContent className="max-h-72">
                  <SelectItem value="ALL">All Periods</SelectItem>
                  {periodsData?.results?.map((p, idx) => (
                    <SelectItem key={p.period_start_date} value={p.period_start_date}>
                      {idx === 0 && '(Current) '} {new Date(p.period_start_date).toLocaleDateString()} - {new Date(p.period_end_date).toLocaleDateString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button variant="outline" onClick={handleRefresh} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" /> Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Summary cards */}
        {teamSummary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Team Target</CardTitle>
                <div className="p-2 bg-indigo-100 rounded-lg"><Target className="h-4 w-4 text-indigo-600" /></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">KSh {formatNumberWithCommas(teamSummary.monthly_target)}</div>
                <p className="text-xs text-gray-500 mt-1">Leader monthly target</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Team Achieved</CardTitle>
                <div className="p-2 bg-green-100 rounded-lg"><DollarSign className="h-4 w-4 text-green-600" /></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">KSh {formatNumberWithCommas(teamSummary.MIB_achieved)}</div>
                <p className="text-xs text-gray-500 mt-1">Leader-level achieved</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Members Achieved</CardTitle>
                <div className="p-2 bg-teal-100 rounded-lg"><DollarSign className="h-4 w-4 text-teal-600" /></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">KSh {formatNumberWithCommas(teamSummary.totalAchieved)}</div>
                <p className="text-xs text-gray-500 mt-1">Sum of members' MIB</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Avg Performance</CardTitle>
                <div className="p-2 bg-pink-100 rounded-lg"><LineChart className="h-4 w-4 text-pink-600" /></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{teamSummary.avgPerformance.toFixed(1)}%</div>
                <p className="text-xs text-gray-500 mt-1">Across team members</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Members table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2"><Users className="h-5 w-5" /> Team Members</CardTitle>
            <CardDescription>Member performance for the selected period and team</CardDescription>
          </CardHeader>
          <CardContent>
            {!selectedTeamData ? (
              <div className="text-gray-500">Select a team to view members.</div>
            ) : selectedTeamData.members.length === 0 ? (
              <div className="text-gray-500">No members found for this team/period.</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm">
                  <thead>
                    <tr className="text-left text-gray-600">
                      <th className="p-2">Member</th>
                      <th className="p-2 text-right">Target</th>
                      <th className="p-2 text-right">MIB Achieved</th>
                      <th className="p-2 text-right">Performance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedTeamData.members
                      .slice()
                      .sort((a, b) => (b.MIB_Perfomance || 0) - (a.MIB_Perfomance || 0))
                      .map((m) => {
                        const perf = Number(m.MIB_Perfomance) || 0;
                        const color = perf >= 100 ? 'text-green-600' : perf >= 75 ? 'text-blue-600' : perf >= 50 ? 'text-yellow-600' : 'text-red-600';
                        return (
                          <tr key={m.employee_no} className="border-t">
                            <td className="p-2">
                              <div className="font-medium text-gray-900">{m.fullnames}</div>
                              <div className="text-xs text-gray-500">{m.employee_no}</div>
                            </td>
                            <td className="p-2 text-right">KSh {formatNumberWithCommas(m.monthly_target)}</td>
                            <td className="p-2 text-right">KSh {formatNumberWithCommas(m.MIB_achieved)}</td>
                            <td className={`p-2 text-right font-semibold ${color}`}>{perf.toFixed(1)}%</td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
};

export default TeamLeaderDashboard;