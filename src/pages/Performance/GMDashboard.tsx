import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuthHook } from '@/utils/useAuthHook';
import { useGetHQKarenQuery, useGetTeamsPerformanceQuery, useGetMarketingPeriodsQuery } from '@/redux/slices/hrDashboardApiSlice';
import { useGetTeamMarketerTargetsQuery } from '@/redux/slices/teamLeaderApiSlice';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import {
  TrendingUp,
  TrendingDown,
  Target,
  DollarSign,
  Calendar,
  BarChart3,
  Award,
  Clock,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  AlertCircle,
  CheckCircle,
  XCircle,
  Activity,
  TrendingUpIcon,
  Building2,
  Users,
  Crown,
  Star,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  Briefcase,
  PieChart,
  LineChart
} from 'lucide-react';

interface TeamPerformanceCardProps {
  team: any;
  office: string;
  onClick: () => void;
}

const TeamPerformanceCard: React.FC<TeamPerformanceCardProps> = ({ team, office, onClick }) => {
  const performancePercentage = team.MIB_Perfomance || 0;
  const targetAchievement = performancePercentage >= 100 ? "Target Exceeded" :
    performancePercentage >= 75 ? "On Track" :
      performancePercentage >= 50 ? "Needs Improvement" : "Below Expectations";

  const getPerformanceStatus = () => {
    if (performancePercentage >= 100) return { color: "text-green-600", bg: "bg-green-50", icon: CheckCircle, status: "Excellent", border: "border-green-200" };
    if (performancePercentage >= 75) return { color: "text-blue-600", bg: "bg-blue-50", icon: TrendingUp, status: "Good", border: "border-blue-200" };
    if (performancePercentage >= 50) return { color: "text-yellow-600", bg: "bg-yellow-50", icon: AlertCircle, status: "Fair", border: "border-yellow-200" };
    return { color: "text-red-600", bg: "bg-red-50", icon: XCircle, status: "Poor", border: "border-red-200" };
  };

  const status = getPerformanceStatus();

  return (
    <Card 
      className={`hover:shadow-lg transition-all duration-200 cursor-pointer hover:scale-[1.02] ${status.border} ${status.bg} group`}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`p-2 rounded-lg ${status.bg} ${status.color}`}>
              <Users className="h-5 w-5" />
            </div>
            <div>
              <CardTitle className="text-lg font-bold text-gray-900">{team.team}</CardTitle>
              <p className="text-sm text-gray-500">{office} Office</p>
            </div>
          </div>
          <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Performance Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Performance Status</span>
          <Badge className={`${status.bg} ${status.color} border-0`}>
            <status.icon className="h-3 w-3 mr-1" />
            {status.status}
          </Badge>
        </div>

        {/* Performance Percentage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Achievement</span>
            <span className={`font-semibold ${status.color}`}>{performancePercentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                performancePercentage >= 100 ? 'bg-green-500' :
                performancePercentage >= 75 ? 'bg-blue-500' :
                performancePercentage >= 50 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${Math.min(performancePercentage, 100)}%` }}
            />
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div>
            <p className="text-gray-500">Monthly Target</p>
            <p className="font-semibold text-gray-900">
              KSh {formatNumberWithCommas(team.monthly_target)}
            </p>
          </div>
          <div>
            <p className="text-gray-500">MIB Achieved</p>
            <p className="font-semibold text-gray-900">
              KSh {formatNumberWithCommas(team.MIB_achieved)}
            </p>
          </div>
        </div>

        {/* Target Achievement Message */}
        <div className={`text-xs p-2 rounded-lg ${status.bg} ${status.color}`}>
          {targetAchievement}
        </div>
      </CardContent>
    </Card>
  );
};

const GMDashboard = () => {
  const { user_details } = useAuthHook();
  const userOffice = user_details?.office; // "HQ" or "KAREN"
  const userPermissions = user_details?.user_permissions || [];

  // Use centralized permission checker from sidebar hook
  const { hasPerformancePermission } = useSidebarPermissions();

  // Check if user has GM permissions via hook (handles arrays/strings/numbers)
  const hasGMHQPermission = hasPerformancePermission("VIEW_PERFORMANCE_GM_HQ");
  const hasGMKarenPermission = hasPerformancePermission("VIEW_PERFORMANCE_GM_KAREN");

  const hasGMPermissions = hasGMHQPermission || hasGMKarenPermission;

  // State management
  const [selectedPeriod, setSelectedPeriod] = useState<string>(""); // holds period_start_date or "ALL"
  const [currentPeriodStart, setCurrentPeriodStart] = useState<string>("");
  const [currentPeriodEnd, setCurrentPeriodEnd] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [performanceFilter, setPerformanceFilter] = useState<string>("ALL");
  const [refreshing, setRefreshing] = useState<boolean>(false);
  // Prefer an office the user actually has GM permission for
  const initialOffice = hasGMKarenPermission && !hasGMHQPermission
    ? "KAREN"
    : hasGMHQPermission && !hasGMKarenPermission
    ? "HQ"
    : (userOffice || "ALL");
  const [selectedOfficeFilter, setSelectedOfficeFilter] = useState<string>(initialOffice);
  const [teamPage, setTeamPage] = useState<number>(1);
  const itemsPerPage = 20;

  // Determine which offices the user can view
  const allowedOffices = useMemo(() => {
    const offices = [];
    if (hasGMHQPermission) offices.push("HQ");
    if (hasGMKarenPermission) offices.push("KAREN");
    return offices;
  }, [hasGMHQPermission, hasGMKarenPermission]);

  // Ensure office filter aligns with permissions
  useEffect(() => {
    const upperSelected = (selectedOfficeFilter || '').toUpperCase();
    if (allowedOffices.length === 1) {
      // Force the only allowed office (e.g., KAREN)
      if (upperSelected !== allowedOffices[0]) {
        setSelectedOfficeFilter(allowedOffices[0]);
      }
      return;
    }
    if (allowedOffices.length > 1) {
      // If current selection is not in allowed list, default to ALL
      if (upperSelected !== 'ALL' && !allowedOffices.includes(upperSelected)) {
        setSelectedOfficeFilter('ALL');
      }
    }
  }, [allowedOffices, selectedOfficeFilter]);
  
  // Fetch marketing periods
  const {
    data: periodsData,
    isLoading: periodsLoading,
    refetch: refetchPeriods
  } = useGetMarketingPeriodsQuery();

  // Get current period (first in the array)
  const currentPeriod = periodsData?.results?.[0];

  // Keep current period start/end in local state for easy access
  useEffect(() => {
    if (currentPeriod) {
      setCurrentPeriodStart(currentPeriod.period_start_date);
      setCurrentPeriodEnd(currentPeriod.period_end_date);
      // Default selection to current period when user lands (not ALL)
      if (!selectedPeriod) {
        setSelectedPeriod(currentPeriod.period_start_date);
      }
    }
  }, [currentPeriod]);

  // Reset teams page to 1 when filters change
  useEffect(() => {
    setTeamPage(1);
  }, [searchTerm, performanceFilter, selectedOfficeFilter, selectedPeriod]);

  // Active period semantics
  const isAllPeriods = selectedPeriod === "ALL";
  const activePeriod = isAllPeriods ? "ALL" : (selectedPeriod || currentPeriodStart || "ALL");
  const activePeriodStart = isAllPeriods ? undefined : (selectedPeriod || currentPeriodStart || undefined);
  const activePeriodEnd = isAllPeriods
    ? undefined
    : (periodsData?.results?.find(p => p.period_start_date === activePeriod)?.period_end_date
        || currentPeriodEnd
        || undefined);

  // Fetch GM performance data
  const {
    data: gmPerformanceData,
    isLoading: gmPerformanceLoading,
    error: gmPerformanceError,
    refetch: refetchGMPerformance
  } = useGetHQKarenQuery({
    office: selectedOfficeFilter,
    period: activePeriod,
    period_start: activePeriodStart,
    period_end: activePeriodEnd,
    page: 1,
    page_size: 100
  });

  // Fetch teams performance data
  const {
    data: teamsData,
    isLoading: teamsLoading,
    error: teamsError,
    refetch: refetchTeams
  } = useGetTeamsPerformanceQuery({
    period: activePeriod,
    period_start: activePeriodStart,
    period_end: activePeriodEnd,
    office: selectedOfficeFilter,
    page: 1,
    page_size: 100
  });

  // Filter teams by office
  const filteredTeams = useMemo(() => {
    if (!teamsData?.results) return [];
    
    // For now, we'll filter teams based on common naming patterns
    // You might need to adjust this logic based on actual team-office relationships
    const teams = teamsData.results.filter((team: any) => {
      if (selectedOfficeFilter === "ALL") return true;
      
      // Add your logic here to determine which teams belong to which office
      // This is a placeholder - you might need to adjust based on actual data structure
      return true; // For now, show all teams
    });

    return teams;
  }, [teamsData?.results, selectedOfficeFilter]);

  // Calculate GM statistics
  const gmStats = useMemo(() => {
    if (!gmPerformanceData?.results) return null;

    const baseData = gmPerformanceData.results;

    // Semantics:
    // - If ALL periods selected: aggregate across ALL periods returned by API
    // - If a specific period is selected: summarize only that period
    const gmData = isAllPeriods
      ? baseData
      : baseData.filter((r: any) => r.period_start_date === activePeriodStart);

    const totalTarget = gmData.reduce((sum: number, gm: any) => sum + (gm.monthly_target || 0), 0);
    const totalAchieved = gmData.reduce((sum: number, gm: any) => sum + (gm.MIB_achieved || 0), 0);
    const totalCommission = gmData.reduce((sum: number, gm: any) => sum + (gm.commission_payable || 0), 0);
    const overallPerformance = totalTarget > 0 ? (totalAchieved / totalTarget) * 100 : 0;

    return {
      totalGMs: gmData.length,
      totalTarget,
      totalAchieved,
      totalCommission,
      overallPerformance,
      gmData
    };
  }, [gmPerformanceData?.results, isAllPeriods, activePeriodStart]);

  // Calculate team statistics
  const teamStats = useMemo(() => {
    if (!filteredTeams) return null;

    const totalTeams = filteredTeams.length;
    const totalTeamTarget = filteredTeams.reduce((sum: number, team: any) => sum + (team.monthly_target || 0), 0);
    const totalTeamAchieved = filteredTeams.reduce((sum: number, team: any) => sum + (team.MIB_achieved || 0), 0);
    const avgTeamPerformance = filteredTeams.reduce((sum: number, team: any) => sum + (team.MIB_Perfomance || 0), 0) / Math.max(totalTeams, 1);

    const topPerformingTeams = [...filteredTeams]
      .sort((a: any, b: any) => (b.MIB_Perfomance || 0) - (a.MIB_Perfomance || 0))
      .slice(0, 3);

    const underPerformingTeams = filteredTeams.filter((team: any) => (team.MIB_Perfomance || 0) < 50);

    return {
      totalTeams,
      totalTeamTarget,
      totalTeamAchieved,
      avgTeamPerformance,
      topPerformingTeams,
      underPerformingTeams: underPerformingTeams.length
    };
  }, [filteredTeams]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([refetchPeriods(), refetchGMPerformance(), refetchTeams()]);
    } finally {
      setRefreshing(false);
    }
  };

  // Modal state for viewing team members inline
  const [teamModalOpen, setTeamModalOpen] = useState(false);
  const [selectedTeamForModal, setSelectedTeamForModal] = useState<any | null>(null);

  // Fetch team-leader targets for modal (ALL leaders; we'll filter client-side by team name)
  const apiPeriod = isAllPeriods ? 'ALL' : (selectedPeriod || currentPeriodStart || 'ALL');
  const { data: teamLeaderTargets = [], isLoading: teamLeaderTargetsLoading } = useGetTeamMarketerTargetsQuery({
    leader_employee_no: 'ALL',
    marketing_period: apiPeriod,
  });

  // Handle team card click: open modal with team members for the selected period
  const handleTeamClick = (team: any) => {
    setSelectedTeamForModal(team);
    setTeamModalOpen(true);
  };

  const isLoading = periodsLoading || gmPerformanceLoading || teamsLoading;

  // Show loading if user_details is not yet available
  if (!user_details) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <SpinnerTemp type="spinner-double" size="lg" />
          <p className="mt-4 text-gray-500">Loading user details...</p>
        </div>
      </Screen>
    );
  }

  // Check permissions only after user_details is loaded
  if (!hasGMPermissions) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500 mb-2">Access Denied</p>
            <p className="text-sm text-gray-500">You don't have permission to view GM dashboards</p>
            <div className="mt-4 text-xs text-gray-400">
              <p>Office: {user_details.office}</p>
              <p>Permissions: {JSON.stringify(userPermissions.slice(0, 5))}...</p>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      </Screen>
    );
  }

  if (gmPerformanceError || teamsError) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500 mb-2">Error loading dashboard data</p>
            <p className="text-sm text-gray-500 mb-4">Please try again later</p>
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </Screen>
    );
  }

  // Compute selected team data without hooks to avoid hook order issues with early returns
  const selectedTeamData = (() => {
    if (!selectedTeamForModal) return null as any;
    const teamName = (selectedTeamForModal.team || '').toLowerCase();
    return (teamLeaderTargets || []).find((t: any) => (t.team || '').toLowerCase() === teamName) || null;
  })();

  return (
    <Screen>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-xl border border-purple-200">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-600 rounded-lg">
                <Crown className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">General Manager Dashboard</h1>
                <p className="text-gray-600 mt-1">Monitor overall performance and team achievements</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    Office: {selectedOfficeFilter === "ALL" ? "All Offices" : selectedOfficeFilter}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {user_details?.fullnames}
                  </Badge>
                  <Badge className="bg-purple-100 text-purple-800 text-xs">
                    <Briefcase className="h-3 w-3 mr-1" />
                    GM Access
                  </Badge>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              {/* Office Filter */}
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-gray-500" />
                <Select value={selectedOfficeFilter} onValueChange={setSelectedOfficeFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Office" />
                  </SelectTrigger>
                  <SelectContent>
                    {allowedOffices.length > 1 && (
                      <SelectItem value="ALL">All Offices</SelectItem>
                    )}
                    {allowedOffices.map((office) => (
                      <SelectItem key={office} value={office}>{office}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Period Selector */}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-48 md:w-[220px]">
                    <SelectValue placeholder="Select Period" />
                  </SelectTrigger>
                  <SelectContent className="max-h-72 w-[calc(100vw-2rem)] md:w-auto">
                    <SelectItem value="ALL">All Periods</SelectItem>
                    {periodsData?.results?.map((period, index) => (
                      <SelectItem key={period.period_start_date} value={period.period_start_date}>
                        {index === 0 && "(Current) "}
                        {new Date(period.period_start_date).toLocaleDateString()} - {new Date(period.period_end_date).toLocaleDateString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Team Members Modal */}
        <Dialog open={teamModalOpen} onOpenChange={setTeamModalOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Team Members - {selectedTeamForModal?.team || ''}</DialogTitle>
              <DialogDescription>
                {isAllPeriods ? 'All periods' : `Period: ${new Date(currentPeriodStart || '').toLocaleDateString()} - ${new Date(currentPeriodEnd || '').toLocaleDateString()}`}
              </DialogDescription>
            </DialogHeader>

            {teamLeaderTargetsLoading ? (
              <div className="py-6 flex items-center justify-center">
                <SpinnerTemp type="spinner-double" size="lg" />
              </div>
            ) : !selectedTeamData ? (
              <div className="text-gray-500">No data found for this team/period.</div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-gray-700">Team Target</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-xl font-bold text-gray-900">KSh {formatNumberWithCommas(selectedTeamData.monthly_target)}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-gray-700">Team Achieved</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-xl font-bold text-gray-900">KSh {formatNumberWithCommas(selectedTeamData.MIB_achieved)}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-gray-700">Members</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-xl font-bold text-gray-900">{selectedTeamData.members?.length || 0}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-gray-700">Avg Performance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-xl font-bold text-gray-900">{(() => {
                        const m = selectedTeamData.members || [];
                        const avg = m.length ? m.reduce((s: number, x: any) => s + (Number(x.MIB_Perfomance) || 0), 0) / m.length : 0;
                        return avg.toFixed(1);
                      })()}%</div>
                    </CardContent>
                  </Card>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="text-left text-gray-600">
                        <th className="p-2">Member</th>
                        <th className="p-2 text-right">Target</th>
                        <th className="p-2 text-right">MIB Achieved</th>
                        <th className="p-2 text-right">Performance</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedTeamData.members
                        .slice()
                        .sort((a: any, b: any) => (b.MIB_Perfomance || 0) - (a.MIB_Perfomance || 0))
                        .map((m: any) => {
                          const perf = Number(m.MIB_Perfomance) || 0;
                          const color = perf >= 100 ? 'text-green-600' : perf >= 75 ? 'text-blue-600' : perf >= 50 ? 'text-yellow-600' : 'text-red-600';
                          return (
                            <tr key={m.employee_no} className="border-t">
                              <td className="p-2">
                                <div className="font-medium text-gray-900">{m.fullnames}</div>
                                <div className="text-xs text-gray-500">{m.employee_no}</div>
                              </td>
                              <td className="p-2 text-right">KSh {formatNumberWithCommas(m.monthly_target)}</td>
                              <td className="p-2 text-right">KSh {formatNumberWithCommas(m.MIB_achieved)}</td>
                              <td className={`p-2 text-right font-semibold ${color}`}>{perf.toFixed(1)}%</td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* GM Overview Statistics */}
        {gmStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Current GM(s) */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Current GM{selectedOfficeFilter === 'ALL' ? 's' : ''}</CardTitle>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Crown className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                {(() => {
                  const office = (selectedOfficeFilter || 'ALL').toUpperCase();
                  // Prefer filtered gmData, but gracefully fall back to raw API results when empty
                  const gmList = (gmStats.gmData && gmStats.gmData.length > 0)
                    ? gmStats.gmData
                    : (gmPerformanceData?.results || []);
                  const findByEmp = (empNo: string) => gmList.find((gm: any) => (gm.marketer_no || '').toUpperCase() === empNo);
                  if (office === 'ALL') {
                    const hq = findByEmp('OL/HR/408');
                    const karen = findByEmp('OL/HR/548');
                    if (!hq && !karen) {
                      return <div className="text-sm text-gray-500">No active GM data for the selected period.</div>;
                    }
                    return (
                      <div className="space-y-1">
                        {hq && <div className="text-2xl font-bold text-gray-900 truncate">{hq.marketer_name}<span className="ml-2 text-xs text-gray-500">• HQ</span></div>}
                        {karen && <div className="text-2xl font-bold text-gray-900 truncate">{karen.marketer_name}<span className="ml-2 text-xs text-gray-500">• KAREN</span></div>}
                        <p className="text-xs text-gray-500 mt-1">Active GMs</p>
                      </div>
                    );
                  }
                  const pick = gmList.find((gm: any) => {
                    const emp = (gm.marketer_no || '').toUpperCase();
                    if (office === 'HQ') return emp === 'OL/HR/408';
                    if (office === 'KAREN') return emp === 'OL/HR/548';
                    return emp === 'OL/HR/408';
                  }) || gmList[0];
                  if (!pick) {
                    return <div className="text-sm text-gray-500">No active GM data for the selected period.</div>;
                  }
                  return (
                    <div>
                      <div className="text-2xl font-bold text-gray-900 truncate">{pick.marketer_name}</div>
                      <p className="text-xs text-gray-500 mt-1">Active GM</p>
                    </div>
                  );
                })()}
              </CardContent>
            </Card>

            {/* Total Target */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Combined Target</CardTitle>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Target className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  KSh {formatNumberWithCommas(gmStats.totalTarget)}
                </div>
                <p className="text-xs text-gray-500 mt-1">Monthly target</p>
              </CardContent>
            </Card>

            {/* Total Achieved */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Achieved</CardTitle>
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  KSh {formatNumberWithCommas(gmStats.totalAchieved)}
                </div>
                <p className="text-xs text-gray-500 mt-1">MIB achieved</p>
              </CardContent>
            </Card>

            {/* Overall Performance */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Overall Performance</CardTitle>
                <div className="p-2 bg-orange-100 rounded-lg">
                  <BarChart3 className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {gmStats.overallPerformance.toFixed(1)}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {gmStats.overallPerformance >= 100 ? 'Exceeding targets' :
                   gmStats.overallPerformance >= 75 ? 'On track' :
                   gmStats.overallPerformance >= 50 ? 'Needs improvement' : 'Below expectations'}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Current GM Performance (single) */}
        {gmStats && gmStats.gmData.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                Current GM Performance
              </CardTitle>
              <CardDescription>Current period and office selection</CardDescription>
            </CardHeader>
            <CardContent>
              {(() => {
                const office = (selectedOfficeFilter || 'ALL').toUpperCase();
                const gmList = (gmStats.gmData && gmStats.gmData.length > 0)
                  ? gmStats.gmData
                  : (gmPerformanceData?.results || []);
                if (!gmList || gmList.length === 0) {
                  return <div className="text-sm text-gray-500">No GM performance data for the selected period.</div>;
                }
                const byEmp = (empNo: string) => gmList.find((gm: any) => (gm.marketer_no || '').toUpperCase() === empNo);
                let currentGM = gmList.find((gm: any) => {
                  const emp = (gm.marketer_no || '').toUpperCase();
                  if (office === 'HQ') return emp === 'OL/HR/408';
                  if (office === 'KAREN') return emp === 'OL/HR/548';
                  return false;
                });
                if (!currentGM) {
                  // Fallbacks: prefer known mapping, else first available
                  currentGM = (office === 'KAREN' ? byEmp('OL/HR/548') : byEmp('OL/HR/408')) || gmList[0];
                }
                if (!currentGM) {
                  return <div className="text-sm text-gray-500">No GM performance data for the selected period.</div>;
                }

                const monthlyTarget = Number(currentGM.monthly_target) || 0;
                const achieved = Number(currentGM.MIB_achieved) || 0;
                const performance = monthlyTarget > 0 ? (achieved / monthlyTarget) * 100 : 0;
                const isPerforming = performance >= 75;

                return (
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${isPerforming ? 'bg-green-100' : 'bg-red-100'}`}>
                        <Crown className={`h-4 w-4 ${isPerforming ? 'text-green-600' : 'text-red-600'}`} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{currentGM.marketer_name}</h4>
                        <p className="text-sm text-gray-500">{currentGM.title} • {currentGM.marketer_no}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2">
                        <span className={`font-semibold ${isPerforming ? 'text-green-600' : 'text-red-600'}`}>
                          {performance.toFixed(1)}%
                        </span>
                        {isPerforming ? <ArrowUp className="h-4 w-4 text-green-600" /> : <ArrowDown className="h-4 w-4 text-red-600" />}
                      </div>
                      <p className="text-xs text-gray-500">
                        KSh {formatNumberWithCommas(achieved)} / {formatNumberWithCommas(monthlyTarget)}
                      </p>
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}

        {/* Team Statistics Overview */}
        {teamStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Teams */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Active Teams</CardTitle>
                <div className="p-2 bg-cyan-100 rounded-lg">
                  <Users className="h-4 w-4 text-cyan-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{teamStats.totalTeams}</div>
                <p className="text-xs text-gray-500 mt-1">Teams in office</p>
              </CardContent>
            </Card>

            {/* Team Target */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Team Targets</CardTitle>
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <Target className="h-4 w-4 text-indigo-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  KSh {formatNumberWithCommas(teamStats.totalTeamTarget)}
                </div>
                <p className="text-xs text-gray-500 mt-1">Combined target</p>
              </CardContent>
            </Card>

            {/* Team Achievement */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Team Achieved</CardTitle>
                <div className="p-2 bg-teal-100 rounded-lg">
                  <DollarSign className="h-4 w-4 text-teal-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  KSh {formatNumberWithCommas(teamStats.totalTeamAchieved)}
                </div>
                <p className="text-xs text-gray-500 mt-1">Total achieved</p>
              </CardContent>
            </Card>

            {/* Average Performance */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Avg Performance</CardTitle>
                <div className="p-2 bg-pink-100 rounded-lg">
                  <LineChart className="h-4 w-4 text-pink-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {teamStats.avgTeamPerformance.toFixed(1)}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {teamStats.underPerformingTeams > 0 && `${teamStats.underPerformingTeams} under 50%`}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Teams Performance Grid */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                <div>
                  <CardTitle>Team Performance</CardTitle>
                  <CardDescription>Click on any team to view detailed performance</CardDescription>
                </div>
              </div>
              
              {/* Search Teams */}
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search teams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters and pagination controls */}
            <div className="flex flex-col md:flex-row md:items-center gap-3 mb-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <Select
                  onValueChange={(v) => setPerformanceFilter(v)}
                  value={performanceFilter}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by performance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All</SelectItem>
                    <SelectItem value=":gte:75">75% and above</SelectItem>
                    <SelectItem value=":gte:50">50% - 74%</SelectItem>
                    <SelectItem value=":lt:50">Below 50%</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {filteredTeams && filteredTeams.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {[...filteredTeams]
                    .sort((a: any, b: any) => (b.MIB_Perfomance || 0) - (a.MIB_Perfomance || 0))
                    .filter((team: any) => {
                      const nameMatch = team.team.toLowerCase().includes(searchTerm.toLowerCase());
                      const perf = team.MIB_Perfomance || 0;
                      if (performanceFilter.startsWith(":gte:")) {
                        const min = parseFloat(performanceFilter.split(":gte:")[1] || '0');
                        return nameMatch && perf >= min;
                      }
                      if (performanceFilter.startsWith(":lt:")) {
                        const max = parseFloat(performanceFilter.split(":lt:")[1] || '0');
                        return nameMatch && perf < max;
                      }
                      return nameMatch;
                    })
                    .slice((teamPage - 1) * itemsPerPage, teamPage * itemsPerPage)
                    .map((team: any, index: number) => (
                      <TeamPerformanceCard
                        key={`${team.team}-${index}`}
                        team={team}
                        office={selectedOfficeFilter === "ALL" ? "Multi" : selectedOfficeFilter}
                        onClick={() => handleTeamClick(team)}
                      />
                    ))}
                </div>

                {/* Pagination controls */}
                <div className="flex items-center justify-between mt-4">
                  <div>
                    <Button size="sm" variant="outline" onClick={() => refetchTeams()}>
                      Refresh Teams
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      disabled={teamPage === 1}
                      onClick={() => setTeamPage((p) => Math.max(1, p - 1))}
                    >
                      Prev
                    </Button>
                    <span className="text-sm text-gray-600">Page {teamPage}</span>
                    <Button
                      size="sm"
                      variant="outline"
                      disabled={teamPage * itemsPerPage >= (filteredTeams?.length || 0)}
                      onClick={() => setTeamPage((p) => p + 1)}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Teams Found</h3>
                <p className="text-gray-500">No teams available for the selected criteria</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
};

export default GMDashboard;