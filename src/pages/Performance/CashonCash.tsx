import { Screen } from "@/app-components/layout/screen";
import { useGetAllcashoncashSalesQuery } from "@/redux/slices/cashoncash";
import { useState, useEffect, useMemo, useCallback } from "react";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { useAuthHook } from "@/utils/useAuthHook";
import { formatDate } from "@/utils/formatDate";
import { addComma } from "@/utils/helpers";
import { Link } from "react-router-dom";
import { searchDebouncer } from "@/utils/debouncers";

interface CashOnCashTransaction {
  id: number;
  Period: string;
  Regional_Category: string;
  Transaction_date: string;
  amount: string;
  bonus: string;
  client_name: string;
  client_no: string;
  marketer: string;
  plot_no: string;
}

interface CashOnCashData {
  current_page: number;
  last_page: number;
  per_page: number;
  total_data: number;
  results: CashOnCashTransaction[];
  links: {
    next: string | null;
    previous: string | null;
  };
}

interface ApiResponse {
  message: string;
  data: CashOnCashData;
}

// Stat Card Component
const StatCard = ({
  label,
  value,
  color = "blue",
  isLoading = false,
  icon,
}: {
  label: string;
  value: string;
  color?: "blue" | "green" | "purple" | "gray";
  isLoading?: boolean;
  icon?: React.ReactNode;
}) => {
  const colorClasses = {
    blue: "text-blue-700 bg-blue-50 border-blue-200",
    green: "text-green-700 bg-green-50 border-green-200",
    purple: "text-purple-700 bg-purple-50 border-purple-200",
    gray: "text-gray-700 bg-gray-50 border-gray-200",
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 p-4 relative overflow-hidden`}
    >
      {isLoading && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500">
          <div className="h-full bg-gradient-to-r from-blue-600 to-purple-600 animate-pulse"></div>
        </div>
      )}

      <div className="flex items-center justify-between mb-2">
        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
          {label}
        </span>
        {icon && (
          <div className={`p-1 rounded ${colorClasses[color]}`}>{icon}</div>
        )}
      </div>

      <div className="flex items-center">
        {isLoading ? (
          <div className="h-6 bg-gray-200 rounded animate-pulse w-24"></div>
        ) : (
          <span
            className={`text-2xl font-bold ${
              colorClasses[color].split(" ")[0]
            }`}
          >
            {value}
          </span>
        )}
      </div>
    </div>
  );
};

function CashOnCash() {
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(20);
  const { user_details } = useAuthHook();
  const marketerEmployeeNo = user_details?.employee_no || "";

  const { data, isLoading, error } = useGetAllcashoncashSalesQuery(
    {
      ordering: "-id",
      page,
      per_page: perPage,
      marketer: marketerEmployeeNo,
      search: searchValue,
    },
    { skip: !marketerEmployeeNo }
  ) as {
    data: ApiResponse | undefined;
    isLoading: boolean;
    error: any;
  };

  // Current page data
  const transactions = data?.data?.results || [];
  const totalData = data?.data?.total_data || 0;
  const currentPage = data?.data?.current_page || 1;
  const lastPage = data?.data?.last_page || 1;

  // Define columns for DataTable
  const columns: ColumnDef<CashOnCashTransaction>[] = useMemo(
    () => [
      {
        accessorKey: "client_name",
        header: "Client Name",
        cell: (info) => (
          <Link
            to={`/customer/${info?.row?.original?.client_no}`}
            className="font-medium underline capitalize text-blue-400"
          >
            {info.getValue() as string}
          </Link>
        ),
      },
      {
        accessorKey: "plot_no",
        header: "Plot No",
        cell: ({ getValue }) => (
          <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            {getValue() as string}
          </span>
        ),
      },
      {
        accessorKey: "amount",
        header: "Amount",
        cell: ({ getValue }) => (
          <span className="font-semibold text-green-700">
            Kshs. {addComma(getValue() as string)}
          </span>
        ),
      },
      {
        accessorKey: "bonus",
        header: "Bonus",
        cell: ({ getValue }) => (
          <span className="font-semibold text-purple-700">
            Kshs. {addComma(getValue() as string)}
          </span>
        ),
      },
      {
        accessorKey: "Period",
        header: "Period",
        cell: ({ getValue }) => (
          <span className="text-sm">{formatDate(getValue() as string)}</span>
        ),
      },
      {
        accessorKey: "Transaction_date",
        header: "Transaction Date",
        cell: ({ getValue }) => (
          <span className="text-sm">{formatDate(getValue() as string)}</span>
        ),
      },
      {
        accessorKey: "Regional_Category",
        header: "Region",
        cell: ({ getValue }) => (
          <span className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            {getValue() as string}
          </span>
        ),
      },
    ],
    [addComma, formatDate]
  );

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handlePerPageChange = useCallback((newPerPage: number) => {
    setPerPage(newPerPage);
    setPage(1); // Reset to first page when changing items per page
  }, []);

  return (
    <Screen>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-sm p-6 text-white">
          <h1 className="text-3xl font-bold mb-2">Cash on Cash Transactions</h1>
          <p className="text-blue-100 text-lg">
            My cash transactions and performance metrics
          </p>
          {user_details && (
            <div className="mt-3 flex items-center gap-3 text-sm text-blue-100">
              <span className="px-2 py-1 bg-white/10 rounded">
                Emp No: {user_details.employee_no}
              </span>
              <span className="px-2 py-1 bg-white/10 rounded">
                {user_details.fullnames}
              </span>
            </div>
          )}
        </div>

        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Current Page Statistics
            </h2>
            <div className="flex flex-wrap gap-4 text-sm text-gray-600">
              <span>Total Records: {totalData.toLocaleString()}</span>
              <span>
                Page {currentPage} of {lastPage}
              </span>
              <span>Showing {transactions.length} records</span>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatCard
              label="Page Amount"
              value={addComma(
                transactions
                  ?.reduce((sum, t) => sum + parseFloat(t.amount), 0)
                  .toString()
              )}
              color="blue"
            />
            <StatCard
              label="Page Bonus"
              value={addComma(
                transactions
                  .reduce((sum, t) => sum + parseFloat(t.bonus), 0)
                  .toString()
              )}
              color="green"
            />
            <StatCard
              label="Page Transactions"
              value={transactions.length.toLocaleString()}
              color="gray"
            />
            <StatCard
              label="Page Average"
              value={addComma(
                (transactions.length
                  ? transactions.reduce(
                      (sum, t) => sum + parseFloat(t.amount),
                      0
                    ) / transactions.length
                  : 0
                ).toString()
              )}
              color="purple"
            />
          </div>
        </div>

        {/* DataTable */}
        <div className="bg-white rounded-lg shadow-sm border px-3">
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary my-3"
            placeholder="Search using plot no..."
          />
          {isLoading ? (
            <div className="flex justify-center items-center min-h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 m-4 text-red-700">
              <h3 className="font-semibold text-lg mb-2">Error Loading Data</h3>
              <p>Unable to fetch cash on cash data. Please try again later.</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : (
            <div className="">
              {transactions.length === 0 ? (
                <div className="flex items-center justify-center min-h-48 text-gray-600">
                  No transactions found for your account.
                </div>
              ) : (
                <DataTable
                  data={transactions}
                  columns={columns}
                  title="Transaction Details"
                  enableToolbar={true}
                  enableColumnFilters={false}
                  enableExportToExcel={true}
                  enablePrintPdf={true}
                  enablePagination={true}
                  currentPage={currentPage}
                  setCurrentPage={handlePageChange}
                  itemsPerPage={perPage}
                  setItemsPerPage={handlePerPageChange}
                  totalItems={totalData}
                  itemsPerPageOptions={[10, 20, 50, 100]}
                  paginationClassName="mt-6 border-t pt-4"
                  tHeadClassName="bg-secondary"
                />
              )}
            </div>
          )}
        </div>
      </div>
    </Screen>
  );
}

export default CashOnCash;
