import { Screen } from "@/app-components/layout/screen";
import { useState } from "react";
import { useGetPeriodsQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Activity, BarChart3, Calendar, CheckCircle, AlertCircle } from "lucide-react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { MarketingPeriod } from "@/types/marketer";
import { useSelector } from "react-redux";
import { selectCurrentUserDetails } from "@/redux/authSlice";
import MarketerReportsTable from "./MarketerReportsTable";

function MarketersPerfomance() {
  const [selectedPeriod, setSelectedPeriod] = useState<MarketingPeriod | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [showReportsTable, setShowReportsTable] = useState(false);

  // Get logged-in user details
  const userDetails = useSelector(selectCurrentUserDetails);
  const employeeNo = userDetails?.employee_no || null;

  // Fetch marketing periods from API on page load
  const { data: periodsData, isLoading: isLoadingPeriods, error: periodsError } = useGetPeriodsQuery({
    page: currentPage,
    page_size: 20
  });

  const handlePeriodSelect = (period: MarketingPeriod) => {
    setSelectedPeriod(period);
    setShowReportsTable(true);
  };

  const formatPeriodName = (period: MarketingPeriod) => {
    const startDate = new Date(period.period_start_date);
    const endDate = new Date(period.period_end_date);
    return `${format(startDate, "MMM yyyy")} - ${format(endDate, "MMM yyyy")}`;
  };

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center gap-3">
          <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
            <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              My Performance
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-base sm:text-lg">
              View your MIB and achievements
            </p>
          </div>
        </div>

        {/* Period Selection */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
              <Activity className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Select Marketing Period
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Choose a period to view your performance
              </p>
            </div>
          </div>

          {/* Period Selection Cards */}
          <div className="space-y-4">
            {isLoadingPeriods ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Array.from({ length: 6 }).map((_, idx) => (
                  <Card key={idx} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="h-20 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : periodsError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
                <p className="text-red-600">Failed to load periods. Please try again.</p>
              </div>
            ) : periodsData?.results && periodsData.results.length > 0 ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  {periodsData.results.map((period: MarketingPeriod, index: number) => (
                    <Card
                      key={`${period.period_start_date}-${period.period_end_date}-${index}`}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
                        selectedPeriod?.period_start_date === period.period_start_date && selectedPeriod?.period_end_date === period.period_end_date
                          ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                          : "border-gray-200 hover:border-emerald-300"
                      )}
                      onClick={() => handlePeriodSelect(period)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
                              <Calendar className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {formatPeriodName(period)}
                              </h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {format(new Date(period.period_start_date), "MMM dd")} - {format(new Date(period.period_end_date), "MMM dd, yyyy")}
                              </p>
                            </div>
                          </div>
                          {selectedPeriod?.period_start_date === period.period_start_date && selectedPeriod?.period_end_date === period.period_end_date && (
                            <CheckCircle className="w-5 h-5 text-emerald-600" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Pagination */}
                {periodsData && periodsData.num_pages > 1 && (
                  <div className="flex items-center justify-between pt-4">
                    <PrimaryButton
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      size="sm"
                    >
                      Previous
                    </PrimaryButton>

                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Page {currentPage} of {periodsData.num_pages}
                    </span>

                    <PrimaryButton
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.min(periodsData.num_pages, prev + 1))}
                      disabled={currentPage === periodsData.num_pages}
                      size="sm"
                    >
                      Next
                    </PrimaryButton>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">No periods available.</p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Reports Modal */}
        <MarketerReportsTable
          open={showReportsTable}
          onOpenChange={setShowReportsTable}
          selectedPeriod={selectedPeriod}
          employeeNo={employeeNo}
        />
      </div>
    </Screen>
  );
}

export default MarketersPerfomance;