import { Screen } from "@/app-components/layout/screen";
import { useState } from "react";
import ReportsModal from "../CustomDshboard/ReportsModal";
import { useGetMarketerPerformanceQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Activity, BarChart3 } from "lucide-react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

function MarketersPerfomance() {
  const [showReports, setShowReports] = useState(false);
  const { data, isLoading } = useGetMarketerPerformanceQuery({});
  
  console.log(data, isLoading);

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center gap-3">
          <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
            <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              Marketers Performance
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-base sm:text-lg">
              Check your marketers performance
            </p>
          </div>
        </div>

        {/* Performance Card */}
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
              <Activity className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Marketers Performance
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                View MIB and achievements
              </p>
            </div>
          </div>
          
          <PrimaryButton
            onClick={() => setShowReports(true)}
            className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
            size="sm"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            View Reports
          </PrimaryButton>
        </div>
      </div>

      {/* Reports Modal */}
      <ReportsModal 
        open={showReports} 
        onOpenChange={setShowReports} 
      />
    </Screen>
  );
}

export default MarketersPerfomance;