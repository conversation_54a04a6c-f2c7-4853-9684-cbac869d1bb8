import { useState } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";
import EscalateComplaintModal from "./escalation";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useCreateComplaintMutation } from "@/redux/slices/complaintsApiSlice";

interface ComplaintData {
  complaint_id: string;
  title: string;
  category: string;
  priority: string;
  status?: string;
  created_at?: string;
  entity_type?: string;
  entity_name?: string;
}

interface ViewComplaintModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  complaintData: ComplaintData | null;
}

export default function ViewComplaintModal({
  isOpen,
  onOpenChange,
  complaintData,
}: ViewComplaintModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [escalateOpen, setEscalateOpen] = useState(false);
  const [newAssignee, setNewAssignee] = useState("");
  const { toast } = useToast();
  const [updateComplaint] = useCreateComplaintMutation();

  // Mock assignees; replace with API fetch
  const assignees = ["John Doe", "Jane Smith", "Alex Johnson", "Unassigned"];

  const handleMarkAsSolved = async () => {
    if (!complaintData) return;
    try {
      await updateComplaint({
        complaint_id: complaintData.complaint_id,
        status: "Resolved",
      }).unwrap();
      toast({
        title: "Success",
        description: `${complaintData.title} marked as solved.`,
        duration: 3000,
      });
      onOpenChange(false);
      setCurrentStep(0);
    } catch (err) {
      console.error("Failed to mark as solved:", err);
      toast({
        title: "Error",
        description: "Failed to mark complaint as solved.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleEscalationSubmit = async (data: {
    reason: string;
    priority: string;
  }) => {
    if (!complaintData) return;
    try {
      await updateComplaint({
        complaint_id: complaintData.complaint_id,
        status: "Escalated",
        priority: data.priority,
        // Assume API accepts reason as a note
        note: data.reason,
      }).unwrap();
      toast({
        title: "Success",
        description: `${complaintData.title} escalated.`,
        duration: 3000,
      });
      setEscalateOpen(false);
      onOpenChange(false);
      setCurrentStep(0);
    } catch (err) {
      console.error("Failed to escalate:", err);
      toast({
        title: "Error",
        description: "Failed to escalate complaint.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleReassign = async () => {
    if (!complaintData || !newAssignee) return;
    try {
      await updateComplaint({
        complaint_id: complaintData.complaint_id,
        assigned_to: newAssignee,
      }).unwrap();
      toast({
        title: "Success",
        description: `Complaint reassigned to ${newAssignee}.`,
        duration: 3000,
      });
      setNewAssignee("");
      setCurrentStep(0);
    } catch (err) {
      console.error("Failed to reassign:", err);
      toast({
        title: "Error",
        description: "Failed to reassign complaint.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <>
      <MultiStepModal
        isOpen={isOpen}
        onOpenChange={(open) => {
          onOpenChange(open);
          if (!open) {
            setCurrentStep(0);
            setNewAssignee("");
          }
        }}
        title="View Complaint Details"
        description="Review the details of the selected complaint"
        currentStep={currentStep}
        onStepChange={setCurrentStep}
        onComplete={() => {
          toast({
            title: "Review Complete",
            description: "Complaint review completed.",
            duration: 3000,
          });
          onOpenChange(false);
          setCurrentStep(0);
        }}
        steps={[
          {
            title: "Basic Info",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label>Complaint ID</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.complaint_id || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Title</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.title || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Category</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.category || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Priority</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.priority || "N/A"}
                  </p>
                </div>
              </div>
            ),
          },
          {
            title: "Complaint Info",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.status || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Entity</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.entity_name || "N/A"} (
                    {complaintData?.entity_type || "N/A"})
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Created At</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.created_at
                      ? new Date(complaintData.created_at).toLocaleString()
                      : "N/A"}
                  </p>
                </div>
              </div>
            ),
          },
          {
            title: "Reassign",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="reassign">Reassign To</Label>
                  <Select value={newAssignee} onValueChange={setNewAssignee}>
                    <SelectTrigger id="reassign">
                      <SelectValue placeholder="Select an assignee" />
                    </SelectTrigger>
                    <SelectContent>
                      {assignees.map((assignee) => (
                        <SelectItem key={assignee} value={assignee}>
                          {assignee}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <PrimaryButton
                  onClick={handleReassign}
                  disabled={!newAssignee}
                  className="w-full"
                >
                  Reassign Complaint
                </PrimaryButton>
              </div>
            ),
          },
          {
            title: "Confirmation",
            content: (
              <div className="py-6 text-center space-y-4">
                <AlertCircle className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium">Review Complete</h3>
                <p className="text-muted-foreground">
                  You have reviewed all complaint details.
                </p>
                <div className="flex justify-center gap-4 mt-6">
                  <PrimaryButton variant="outline" onClick={handleMarkAsSolved}>
                    Mark as Solved
                  </PrimaryButton>
                  <PrimaryButton
                    variant="default"
                    onClick={() => setEscalateOpen(true)}
                  >
                    Escalate
                  </PrimaryButton>
                  {/* Placeholder for Add Reminder */}
                </div>
              </div>
            ),
          },
        ]}
      />

      <EscalateComplaintModal
        isOpen={escalateOpen}
        onOpenChange={setEscalateOpen}
        onSubmit={handleEscalationSubmit}
        complaint={{
          id: complaintData?.complaint_id || "",
          title: complaintData?.title || "N/A",
          description: "N/A", // Schema has no description
        }}
      />
    </>
  );
}
