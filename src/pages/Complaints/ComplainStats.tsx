import PieChart from '@/components/custom/charts/PieChart';

const complaintStats = [
  { label: "Open", value: 12, color: "#22c55e" },
  { label: "Resolved", value: 8, color: "#3b82f6" },
  { label: "Pending", value: 3, color: "#facc15" },
  { label: "Urgent", value: 1, color: "#ef4444" },
];

export default function ComplaintsAnalytics() {
  return (
    <div className="bg-white rounded-lg shadow p-6 flex flex-col md:flex-row gap-8 items-center mb-8">
      <div className="w-full md:w-1/3 flex justify-center">
        <PieChart
          data={complaintStats.map(({ label, value, color }) => ({
            name: label,
            value,
            color,
          }))}
        />
      </div>
      <div className="flex-1 grid grid-cols-2 md:grid-cols-4 gap-4">
        {complaintStats.map(stat => (
          <div
            key={stat.label}
            className="flex flex-col items-center bg-gray-50 rounded p-4"
          >
            <span
              className="w-3 h-3 rounded-full mb-2"
              style={{ background: stat.color }}
            />
            <div className="text-2xl font-bold">{stat.value}</div>
            <div className="text-gray-500 text-sm">{stat.label}</div>
          </div>
        ))}
      </div>
    </div>
  );
}