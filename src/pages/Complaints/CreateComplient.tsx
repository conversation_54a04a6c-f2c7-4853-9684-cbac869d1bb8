import { useState } from "react";
import { AlertCircle } from "lucide-react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { useToast } from "@/hooks/use-toast";

interface FormData {
  complaint_id: string;
  title: string;
  category: string;
  priority: string;
}

interface AddComplaintModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSubmit: (formData: FormData) => void;
}

export default function AddComplaintModal({
  isOpen,
  onOpenChange,
  onSubmit,
}: AddComplaintModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    complaint_id: "",
    title: "",
    category: "Product",
    priority: "Medium",
  });
  const { toast } = useToast();

  const categories = ["Product", "Service", "Billing", "Technical", "Other"];
  const priorities = ["Low", "Medium", "High", "Urgent"];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const validateStep = () => {
    if (currentStep === 0) {
      if (formData.complaint_id.length < 1 || formData.complaint_id.length > 50) {
        toast({
          title: "Error",
          description: "Complaint ID must be 1–50 characters.",
          variant: "destructive",
          duration: 3000,
        });
        return false;
      }
      if (formData.title.length < 1 || formData.title.length > 255) {
        toast({
          title: "Error",
          description: "Title must be 1–255 characters.",
          variant: "destructive",
          duration: 3000,
        });
        return false;
      }
    }
    return true;
  };

  const handleStepChange = (step: number) => {
    if (step > currentStep && !validateStep()) return;
    setCurrentStep(step);
  };

  const handleComplete = () => {
    if (!validateStep()) return;
    onSubmit(formData);
    setCurrentStep(0);
    setFormData({
      complaint_id: "",
      title: "",
      category: "Product",
      priority: "Medium",
    });
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Add New Complaint"
      description="Complete all steps to submit a new complaint"
      currentStep={currentStep}
      onStepChange={handleStepChange}
      onComplete={handleComplete}
      steps={[
        {
          title: "Basic Info",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="complaint_id">Complaint ID</Label>
                <Input
                  id="complaint_id"
                  placeholder="e.g., CMP-123"
                  value={formData.complaint_id}
                  onChange={handleInputChange}
                  maxLength={50}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="Enter complaint title"
                  value={formData.title}
                  onChange={handleInputChange}
                  maxLength={255}
                  required
                />
              </div>
            </div>
          ),
        },
        {
          title: "Complaint Details",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((cat) => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => setFormData({ ...formData, priority: value })}
                >
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {priorities.map((pri) => (
                      <SelectItem key={pri} value={pri}>{pri}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6 text-center">
              <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Almost Done!</h3>
              <p className="text-muted-foreground mt-2">
                Please review your information before submitting.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}