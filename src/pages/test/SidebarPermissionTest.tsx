import React from 'react';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLogisticsPermissions } from '@/hooks/useLogisticsPermissions';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import { useAuthHook } from '@/utils/useAuthHook';

const SidebarPermissionTest: React.FC = () => {
  const { user_details } = useAuthHook();
  const {
    canAccessLogisticsReports,
    canAccessSitevisitReports,
    userPermissionCodes,
    isLoading: logisticsLoading,
    LOGISTICS_PERMISSIONS,
  } = useLogisticsPermissions();

  const { userPermissionCodes: allPermissionCodes, isLoading: sidebarLoading } = useSidebarPermissions();

  const isLoading = logisticsLoading || sidebarLoading;

  if (isLoading) {
    return (
      <Screen>
        <div className="p-6">
          <p>Loading permissions...</p>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Sidebar Permission Test</h1>
          <Badge variant="outline">Test Page</Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User Info */}
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div><strong>Name:</strong> {user_details?.fullnames || "Unknown"}</div>
              <div><strong>Employee No:</strong> {user_details?.employee_no || "Unknown"}</div>
              <div><strong>Department:</strong> {user_details?.department || "Unknown"}</div>
            </CardContent>
          </Card>

          {/* Reports Permission Status */}
          <Card>
            <CardHeader>
              <CardTitle>Reports Permission Status</CardTitle>
              <CardDescription>Check if user can see reports in sidebar</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className={`p-3 rounded border ${canAccessLogisticsReports ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                <div className="font-medium">Logistics Reports (210)</div>
                <div className={`text-sm ${canAccessLogisticsReports ? 'text-green-600' : 'text-red-600'}`}>
                  {canAccessLogisticsReports ? '✓ Should appear in sidebar' : '✗ Should NOT appear in sidebar'}
                </div>
              </div>
              
              <div className={`p-3 rounded border ${canAccessSitevisitReports ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                <div className="font-medium">Site Visit Reports (211)</div>
                <div className={`text-sm ${canAccessSitevisitReports ? 'text-green-600' : 'text-red-600'}`}>
                  {canAccessSitevisitReports ? '✓ Should appear in sidebar' : '✗ Should NOT appear in sidebar'}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* All Permission Codes */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>All Permission Codes</CardTitle>
              <CardDescription>All permission codes the user has access to</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {allPermissionCodes.length > 0 ? (
                  allPermissionCodes.map(code => (
                    <Badge 
                      key={code} 
                      variant={[210, 211].includes(code) ? "default" : "secondary"}
                      className={[210, 211].includes(code) ? "bg-blue-600" : ""}
                    >
                      {code}
                      {code === 210 && " (Logistics Reports)"}
                      {code === 211 && " (Site Visit Reports)"}
                    </Badge>
                  ))
                ) : (
                  <span className="text-red-600">No permissions found</span>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Testing Instructions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium mb-2">How to Test:</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Check the permission status above</li>
                  <li>Look at the sidebar on the left</li>
                  <li>Navigate to Logistics section in sidebar</li>
                  <li>Check if "Logistics Reports" and "Site Visit Reports" links appear</li>
                  <li>Navigate to Reports section in sidebar</li>
                  <li>Check if "Logistics Reports" link appears</li>
                </ol>
              </div>
              
              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-medium mb-2">Expected Behavior:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>If you have permission 210: "Logistics Reports" should appear in both Logistics and Reports sections</li>
                  <li>If you have permission 211: "Site Visit Reports" should appear in Logistics section</li>
                  <li>If you don't have these permissions: The links should NOT appear in the sidebar</li>
                  <li>Users without permissions should not be able to access the routes directly either</li>
                </ul>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium mb-2">To Add Permissions:</h4>
                <p className="text-sm">
                  Go to User List → Find your user → Assign Permissions → Logistics Tab → 
                  Add permissions 210 (Logistics Reports) and/or 211 (Site Visit Reports)
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default SidebarPermissionTest;
