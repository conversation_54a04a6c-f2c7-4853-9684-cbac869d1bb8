import React from 'react';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useLogisticsPermissions } from '@/hooks/useLogisticsPermissions';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import { useAuthHook } from '@/utils/useAuthHook';
import { useGetUser2UserPermissionsQuery, useGetTeams2TeamsPermissionsQuery } from '@/redux/slices/permissions';
import { useGetDepartmentsQuery } from '@/redux/slices/user';
import PermissionManager from '@/components/debug/PermissionManager';
import SalesPermissionIndicator from '@/components/sales/SalesPermissionIndicator';
import CustomerPermissionIndicator from '@/components/customer/CustomerPermissionIndicator';
import ProspectPermissionIndicator from '@/components/prospect/ProspectPermissionIndicator';
import withPreloadedPermissions from '@/components/hoc/withPreloadedPermissions';

// Define the props interface for the component with preloaded permissions
interface ModulePermissionTestProps {
  salesPermissions?: any;
  customerPermissions?: any;
  prospectPermissions?: any;
}

const ModulePermissionTest: React.FC<ModulePermissionTestProps> = ({
  salesPermissions,
  customerPermissions,
  prospectPermissions
}) => {
  const { user_details } = useAuthHook();
  
  // Get permissions from all hooks
  const {
    hasLogisticsPermission,
    userLogisticsPermissions,
    userPermissionCodes: logisticsPermissionCodes,
    isLoading: logisticsLoading,
    LOGISTICS_PERMISSIONS,
  } = useLogisticsPermissions();

  // Extract sales permissions from the preloaded props
  const {
    hasAnySalesAccess,
    canViewHQSales: canViewSalesHQ,
    canViewKarenSales: canViewSalesKaren,
    canViewAllOffices: canViewSalesAllOffices,
    canViewAllMarketers: canViewSalesAllMarketers,
    canViewDiasporaTeam: canViewSalesDiasporaTeam,
    canViewDigitalTeam: canViewSalesDigitalTeam,
    canViewTelemarketingTeam: canViewSalesTelemarketingTeam,
    canViewOtherTeam: canViewSalesOtherTeam,
    canViewAllTeams: canViewSalesAllTeams,
    canViewDiasporaRegion: canViewSalesDiasporaRegion,
    canViewAllDiasporaRegions: canViewSalesAllDiasporaRegions,
    permissionLevel: salesPermissionLevel,
    userPermissions: salesPermissionsData,
    isLoading: salesLoading,
  } = salesPermissions || {};

  // Extract customer permissions from the preloaded props
  const {
    hasAnyCustomerAccess,
    canViewHQCustomers,
    canViewKarenCustomers,
    canViewAllOfficesCustomers,
    canViewOwnCustomers,
    canViewAllMarketersCustomers,
    canViewDiasporaTeamCustomers,
    canViewDigitalTeamCustomers,
    canViewTelemarketingTeamCustomers,
    canViewOtherTeamCustomers,
    canViewAllTeamsCustomers,
    canViewDiasporaRegionCustomers,
    canViewAllDiasporaRegionsCustomers,
    permissionLevel: customerPermissionLevel,
    userPermissions: customerPermissionsData,
  } = customerPermissions || {};

  // Extract prospect permissions from the preloaded props
  const {
    hasAnyProspectAccess,
    canViewHQProspects,
    canViewKarenProspects,
    canViewAllOfficesProspects,
    canViewOwnProspects,
    canViewAllMarketersProspects,
    canViewDiasporaTeamProspects,
    canViewDigitalTeamProspects,
    canViewTelemarketingTeamProspects,
    canViewOtherTeamProspects,
    canViewAllTeamsProspects,
    canViewDiasporaRegionProspects,
    canViewAllDiasporaRegionsProspects,
    permissionLevel: prospectPermissionLevel,
    userPermissions: prospectPermissionsData,
  } = prospectPermissions || {};

  const { userPermissionCodes: allPermissionCodes, isLoading: sidebarLoading } = useSidebarPermissions();

  // Fetch departments to see user's department ID
  const { data: departments = [] } = useGetDepartmentsQuery({
    page: 1,
    page_size: 1000,
  });

  // Find user's department ID
  const userDepartmentId = React.useMemo(() => {
    if (!user_details?.department || !departments.length) return null;
    const userDept = departments.find((dept: any) => dept.dp_name === user_details.department);
    return userDept?.dp_id || null;
  }, [user_details?.department, departments]);

  // Fetch user-specific permissions directly
  const { data: userSpecificPermissions = [], isLoading: loadingUserPerms } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: user_details?.employee_no || '',
    },
    {
      skip: !user_details?.employee_no,
    }
  );

  // Fetch team permissions
  const { data: teamPermissions = [], isLoading: loadingTeamPerms } = useGetTeams2TeamsPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      team: userDepartmentId,
    },
    {
      skip: !userDepartmentId,
    }
  );

  const isLoading = logisticsLoading || sidebarLoading || loadingUserPerms || loadingTeamPerms || salesLoading;

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
        <div className="prose dark:prose-invert max-w-none">
          <h1>Module Permission Test</h1>
          <p>This page helps debug permissions for the current user across all modules.</p>
        </div>

        {isLoading && (
          <Card>
            <CardContent className="p-6">
              <p>Loading permissions...</p>
            </CardContent>
          </Card>
        )}

        {!isLoading && (
          <div className="grid grid-cols-1 gap-6">
            {/* User Info */}
            <Card>
              <CardHeader>
                <CardTitle>User Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div><strong>Name:</strong> {user_details?.fullnames || "Unknown"}</div>
                <div><strong>Employee No:</strong> {user_details?.employee_no || "Unknown"}</div>
                <div><strong>Department:</strong> {user_details?.department || "Unknown"}</div>
                <div><strong>Department ID:</strong> {userDepartmentId || "Not found"}</div>
                <div><strong>Team:</strong> {user_details?.team || "Unknown"}</div>
                <div><strong>User Group:</strong> {user_details?.user_group || "Unknown"}</div>
              </CardContent>
            </Card>

            {/* Permission Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <SalesPermissionIndicator />
              </div>
              <div>
                <CustomerPermissionIndicator />
              </div>
              <div>
                <ProspectPermissionIndicator />
              </div>
            </div>

            {/* All Permission Codes */}
            <Card>
              <CardHeader>
                <CardTitle>All Permission Codes</CardTitle>
                <CardDescription>All permission codes the user has access to</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {allPermissionCodes.length > 0 ? (
                    allPermissionCodes.map(code => (
                      <Badge 
                        key={code} 
                        variant="secondary"
                      >
                        {code}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-red-600">No permissions found</span>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Raw Permission Data */}
            <Card>
              <CardHeader>
                <CardTitle>Raw Permission Data</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <strong>User Permissions from Login:</strong>
                  <div className="text-sm text-gray-600 mt-1">
                    {user_details?.user_permissions ? 
                      JSON.stringify(user_details.user_permissions, null, 2) : 
                      "None"
                    }
                  </div>
                </div>

                <div>
                  <strong>User-Specific Permissions (API):</strong>
                  <div className="text-sm text-gray-600 mt-1">
                    {userSpecificPermissions.length > 0 ? 
                      JSON.stringify(userSpecificPermissions, null, 2) : 
                      "None found"
                    }
                  </div>
                </div>

                <div>
                  <strong>Team Permissions (API):</strong>
                  <div className="text-sm text-gray-600 mt-1">
                    {teamPermissions.length > 0 ? 
                      JSON.stringify(teamPermissions, null, 2) : 
                      "None found"
                    }
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Permission Manager */}
            <Card>
              <CardHeader>
                <CardTitle>Permission Manager</CardTitle>
                <CardDescription>Add permissions for testing (Development only)</CardDescription>
              </CardHeader>
              <CardContent>
                <PermissionManager />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Screen>
  );
};

// Wrap the component with the HOC to preload permissions
export default withPreloadedPermissions(ModulePermissionTest);