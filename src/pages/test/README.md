# Permission System Testing

This directory contains test pages for the permission system.

## Available Test Pages

1. **Permission Test** - `/test/permissions`

   - Basic permission testing

2. **Permission System Test** - `/test/permission-system`

   - Tests the overall permission system with route guards

3. **Logistics Permission Test** - `/test/logistics-permissions`

   - Tests logistics-specific permissions

4. **Route Permission Test** - `/test/route-permissions`

   - Tests route-based permissions

5. **Module Permission Test** - `/test/module-permissions`
   - Tests module-specific permissions for Sales, Customers, and Prospects

## How to Use

To use the Module Permission Test page, you need to manually add the route to App.tsx:

```jsx
<Route path="/test/module-permissions" element={<ModulePermissionTest />} />
```

This page will show you all your current permissions for Sales, Customers, and Prospects modules, and help you debug any permission issues.

## Permission Codes

### Sales Permissions

- 1001: VIEW_SALES_HQ - Permission to view sales data from HQ office
- 1002: VIEW_SALES_KAREN - Permission to view sales data from KAREN office
- 1003: VIEW_SALES_ALL_OFFICES - Permission to view sales data from all offices
- 1004: VIEW_SALES_OWN_MARKETER - Permission to view sales data for the logged-in marketer only
- 1005: VIEW_SALES_ALL_MARKETERS - Permission to view sales data for all marketers
- 1006: VIEW_SALES_DIASPORA_TEAM - Permission to view sales data from DIASPORA team
- 1007: VIEW_SALES_DIGITAL_TEAM - Permission to view sales data from DIGITAL team
- 1008: VIEW_SALES_TELEMARKETING_TEAM - Permission to view sales data from TELEMARKETING team
- 1009: VIEW_SALES_OTHER_TEAM - Permission to view sales data from OTHER team
- 1010: VIEW_SALES_ALL_TEAMS - Permission to view sales data from all teams
- 1011: VIEW_SALES_DIASPORA_REGION - Permission to view sales data filtered by diaspora region
- 1012: VIEW_SALES_ALL_DIASPORA_REGIONS - Permission to view sales data from all diaspora regions

### Customer Permissions

- 2001: VIEW_CUSTOMER_HQ - View customers from HQ office
- 2002: VIEW_CUSTOMER_KAREN - View customers from Karen office
- 2003: VIEW_CUSTOMER_ALL_OFFICES - View customers from all offices
- 2004: VIEW_CUSTOMER_OWN_MARKETER - View only customers assigned to the current user
- 2005: VIEW_CUSTOMER_ALL_MARKETERS - View customers assigned to all marketers
- 2006: VIEW_CUSTOMER_DIASPORA_TEAM - View customers from Diaspora team
- 2007: VIEW_CUSTOMER_DIGITAL_TEAM - View customers from Digital team
- 2008: VIEW_CUSTOMER_TELEMARKETING_TEAM - View customers from Telemarketing team
- 2009: VIEW_CUSTOMER_OTHER_TEAM - View customers from Other teams
- 2010: VIEW_CUSTOMER_ALL_TEAMS - View customers from all teams
- 2011: VIEW_CUSTOMER_DIASPORA_REGION - View customers from user's diaspora region only
- 2012: VIEW_CUSTOMER_ALL_DIASPORA_REGIONS - View customers from all diaspora regions

### Prospect Permissions

- 3001: VIEW_PROSPECT_HQ - View prospects from HQ office
- 3002: VIEW_PROSPECT_KAREN - View prospects from Karen office
- 3003: VIEW_PROSPECT_ALL_OFFICES - View prospects from all offices
- 3004: VIEW_PROSPECT_OWN_MARKETER - View only prospects assigned to the current user
- 3005: VIEW_PROSPECT_ALL_MARKETERS - View prospects assigned to all marketers
- 3006: VIEW_PROSPECT_DIASPORA_TEAM - View prospects from Diaspora team
- 3007: VIEW_PROSPECT_DIGITAL_TEAM - View prospects from Digital team
- 3008: VIEW_PROSPECT_TELEMARKETING_TEAM - View prospects from Telemarketing team
- 3009: VIEW_PROSPECT_OTHER_TEAM - View prospects from Other teams
- 3010: VIEW_PROSPECT_ALL_TEAMS - View prospects from all teams
- 3011: VIEW_PROSPECT_DIASPORA_REGION - View prospects from user's diaspora region only
- 3012: VIEW_PROSPECT_ALL_DIASPORA_REGIONS - View prospects from all diaspora regions
