import React from 'react';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import { usePermissionNavigation } from '@/hooks/usePermissionNavigation';
import PermissionAwareLink from '@/components/navigation/PermissionAwareLink';
import { PrimaryButton } from '@/components/custom/buttons/buttons';
import { Shield, Lock, CheckCircle, XCircle } from 'lucide-react';

const PermissionSystemTest: React.FC = () => {
  const { hasPermission, userPermissionCodes } = useSidebarPermissions();
  const { navigateWithPermissionCheck, canAccessRoute } = usePermissionNavigation();

  const testRoutes = [
    { path: '/', name: 'Dashboard', requiredPermission: 111 },
    { path: '/logistics-dash', name: 'Logistics Dashboard', requiredPermission: 111 },
    { path: '/admin/services', name: 'Admin Services', requiredPermission: 116 },
    { path: '/permissions', name: 'Permissions Management', requiredPermission: 117 },
    { path: '/accounts-dashboard', name: 'Accounts Dashboard', requiredPermission: 113 },
    { path: '/marketerreport', name: 'Marketer Reports', requiredPermission: 114 },
    { path: '/performance', name: 'Performance', requiredPermission: 112 },
  ];

  const handleTestNavigation = (path: string) => {
    navigateWithPermissionCheck(path);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 flex items-center">
          <Shield className="w-8 h-8 mr-3 text-blue-600" />
          Permission System Test
        </h1>
        <p className="text-gray-600">
          This page demonstrates the permission-based 503 system. Test different navigation methods
          and see how the system handles unauthorized access.
        </p>
      </div>

      {/* Current User Permissions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-blue-900 mb-4 flex items-center">
          <CheckCircle className="w-5 h-5 mr-2" />
          Your Current Permissions
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[111, 112, 113, 114, 115, 116, 117].map((permCode) => (
            <div
              key={permCode}
              className={`p-3 rounded-md text-center ${
                hasPermission(permCode)
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-gray-100 text-gray-500 border border-gray-200'
              }`}
            >
              <div className="font-medium">Permission {permCode}</div>
              <div className="text-sm">
                {permCode === 111 && 'Main'}
                {permCode === 112 && 'Performance'}
                {permCode === 113 && 'Teams'}
                {permCode === 114 && 'Reports'}
                {permCode === 115 && 'Analytics'}
                {permCode === 116 && 'Services'}
                {permCode === 117 && 'Admin'}
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4 text-sm text-blue-700">
          <strong>Permission Codes:</strong> {userPermissionCodes.join(', ') || 'None'}
        </div>
      </div>

      {/* Test Routes */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Lock className="w-5 h-5 mr-2" />
          Test Route Access
        </h2>
        <div className="space-y-4">
          {testRoutes.map((route) => {
            const hasAccess = canAccessRoute(route.path);
            return (
              <div
                key={route.path}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-md"
              >
                <div className="flex items-center">
                  {hasAccess ? (
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500 mr-3" />
                  )}
                  <div>
                    <div className="font-medium">{route.name}</div>
                    <div className="text-sm text-gray-500">
                      {route.path} (Requires permission {route.requiredPermission})
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <PermissionAwareLink
                    to={route.path}
                    className={`px-3 py-1 rounded text-sm ${
                      hasAccess
                        ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                        : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                    }`}
                  >
                    Link Navigate
                  </PermissionAwareLink>
                  <button
                    onClick={() => handleTestNavigation(route.path)}
                    className={`px-3 py-1 rounded text-sm ${
                      hasAccess
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : 'bg-red-100 text-red-700 hover:bg-red-200'
                    }`}
                  >
                    Programmatic Navigate
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-yellow-900 mb-4">
          How to Test
        </h2>
        <div className="space-y-3 text-yellow-800">
          <div className="flex items-start">
            <div className="w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
              1
            </div>
            <div>
              <strong>Test with your current permissions:</strong> Try clicking the navigation buttons above.
              Routes you have access to will work normally, while restricted routes will show the 503 page.
            </div>
          </div>
          <div className="flex items-start">
            <div className="w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
              2
            </div>
            <div>
              <strong>Test direct URL access:</strong> Try typing restricted URLs directly in the browser
              (e.g., /admin/services if you don't have permission 116).
            </div>
          </div>
          <div className="flex items-start">
            <div className="w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
              3
            </div>
            <div>
              <strong>Test sidebar navigation:</strong> Check if sidebar links for restricted sections
              redirect to the 503 page when clicked.
            </div>
          </div>
          <div className="flex items-start">
            <div className="w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
              4
            </div>
            <div>
              <strong>Contact information:</strong> The 503 page includes contact details for
              <EMAIL>.<NAME_EMAIL> for users to request access.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermissionSystemTest;
