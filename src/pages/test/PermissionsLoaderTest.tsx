import React, { useState } from 'react';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PermissionsLoader } from '@/components/permissions/PermissionsLoader';
import { useSalesPermissions } from '@/hooks/useSalesPermissions';
import { useCustomerPermissions } from '@/hooks/useCustomerPermissions';
import { useProspectPermissions } from '@/hooks/useProspectPermissions';
import SalesPermissionIndicator from '@/components/sales/SalesPermissionIndicator';
import CustomerPermissionIndicator from '@/components/customer/CustomerPermissionIndicator';
import ProspectPermissionIndicator from '@/components/prospect/ProspectPermissionIndicator';

const PermissionsLoaderTest: React.FC = () => {
  const [permissionsLoaded, setPermissionsLoaded] = useState(false);

  // These hooks will only be called after permissions are loaded
  const salesPermissions = useSalesPermissions();
  const customerPermissions = useCustomerPermissions();
  const prospectPermissions = useProspectPermissions();

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
        <div className="prose dark:prose-invert max-w-none">
          <h1>Permissions Loader Test</h1>
          <p>This page demonstrates how to use the PermissionsLoader component to ensure permissions are loaded before rendering content.</p>
        </div>

        <PermissionsLoader
          onPermissionsLoaded={() => {
            console.log('Permissions loaded successfully');
            setPermissionsLoaded(true);
          }}
        >
          {/* This content will only be rendered after permissions are loaded */}
          <Card>
            <CardHeader>
              <CardTitle>Permissions Loaded: {permissionsLoaded ? 'Yes' : 'No'}</CardTitle>
              <CardDescription>
                The content below is only rendered after permissions have been loaded from the server.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <SalesPermissionIndicator />
                <CustomerPermissionIndicator />
                <ProspectPermissionIndicator />
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Sales Permissions</h3>
                <p>Has Any Sales Access: {salesPermissions.hasAnySalesAccess ? 'Yes' : 'No'}</p>
                <p>Permission Level: {salesPermissions.permissionLevel}</p>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Customer Permissions</h3>
                <p>Has Any Customer Access: {customerPermissions.hasAnyCustomerAccess ? 'Yes' : 'No'}</p>
                <p>Permission Level: {customerPermissions.permissionLevel}</p>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Prospect Permissions</h3>
                <p>Has Any Prospect Access: {prospectPermissions.hasAnyProspectAccess ? 'Yes' : 'No'}</p>
                <p>Permission Level: {prospectPermissions.permissionLevel}</p>
              </div>
            </CardContent>
          </Card>
        </PermissionsLoader>
      </div>
    </Screen>
  );
};

export default PermissionsLoaderTest;