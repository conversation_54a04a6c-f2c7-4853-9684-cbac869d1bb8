import { useState, useEffect } from "react";
import { Search, FileText, Eye } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import SimpleTable, { ColumnDefinitionST } from "@/components/custom/tables/SimpleTable";
import MarketerDetailsModal from "./tablemodal";

const MarketerReport = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [selectedMarketer, setSelectedMarketer] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const bookingsData = [
    {
      id: "1",
      clientName: "<PERSON>",
      marketer: "<PERSON>",
      plotNo: "A-123",
      projectName: "Sunset Estates",
      totalPlots: 100,
      openPlots: 40,
      reservedPlots: 20,
      soldPlots: 40,
      bank: "KCB Bank",
    },
    {
      id: "2",
      clientName: "<PERSON>",
      marketer: "Bob <PERSON>",
      plotNo: "B-456",
      projectName: "Coastal Heights",
      totalPlots: 150,
      openPlots: 60,
      reservedPlots: 30,
      soldPlots: 60,
      bank: "Equity Bank",
    },
    {
      id: "3",
      clientName: "Michael Brown",
      marketer: "Alice Johnson",
      plotNo: "C-789",
      projectName: "Lakeview Gardens",
      totalPlots: 80,
      openPlots: 30,
      reservedPlots: 15,
      soldPlots: 35,
      bank: "Co-operative Bank",
    },
  ];

  const filteredBookings = bookingsData.filter((booking) =>
    booking.marketer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredBookings.slice(indexOfFirstItem, indexOfLastItem);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleViewMarketer = (marketer: string) => {
    setSelectedMarketer(marketer);
    setIsModalOpen(true);
  };

  const columns: ColumnDefinitionST<typeof bookingsData[0]>[] = [
    {
      key: "clientName",
      header: "Client Name",
      headerClassName: "font-normal px-6 py-5",
      cellClassName: "px-6 py-3",
    },
    {
      key: "marketer",
      header: "Marketer",
      headerClassName: "font-normal px-6 py-5",
      cellClassName: "px-6 py-3",
    },
    {
      key: "plotNo",
      header: "Plot No",
      headerClassName: "font-normal px-6 py-5",
      cellClassName: "px-6 py-3",
    },
    {
      key: "actions",
      header: "Actions",
      headerClassName: "font-normal text-center px-6 py-5",
      cellClassName: "text-center px-6 py-3",
      renderCell: (row) => (
        <div className="flex justify-center space-x-2">
          <button
            onClick={() => handleViewMarketer(row.marketer)}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
          >
            <Eye className="w-3 h-3 inline-block mr-1" />
            View
          </button>
        </div>
      ),
    },
  ];

  const renderPaginationItems = () => {
    const items = [];
    const maxPagesToShow = 3;
    let startPage = Math.max(1, currentPage - 1);
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
      items.push(
        <PaginationItem key="first">
          <PaginationLink onClick={() => setCurrentPage(1)}>1</PaginationLink>
        </PaginationItem>
      );
      if (startPage > 2) {
        items.push(<PaginationItem key="ellipsis-1"><PaginationEllipsis /></PaginationItem>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => setCurrentPage(i)}
            isActive={i === currentPage}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        items.push(<PaginationItem key="ellipsis-2"><PaginationEllipsis /></PaginationItem>);
      }
      items.push(
        <PaginationItem key="last">
          <PaginationLink onClick={() => setCurrentPage(totalPages)}>
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="min-h-screen py-6 px-4 sm:px-6 lg:px-8 space-y-4">
     
      

      {/* Search Bar */}
      <div className="relative max-w-sm">
        <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search by marketer name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        />
      </div>

      {/* Table */}
      <div className="bg-white shadow-md rounded-md p-4 border border-gray-200">
        {filteredBookings.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-sm text-gray-500">No bookings found matching your search.</p>
          </div>
        ) : (
          <SimpleTable
            data={currentItems}
            columns={columns}
            containerClassName="rounded-lg"
            tableClassName="border-collapse w-full"
            tHeaderClassName="border-b border-gray-200"
            tBodyClassName="divide-y divide-gray-100"
            tRowClassName="hover:bg-gray-50 transition-colors"
            headerCellsClassName=""
            bodyCellsClassName=""
            hoverable={true}
            striped={false}
          />
        )}
      </div>

      {/* Pagination */}
      {filteredBookings.length > 0 && (
        <div className="flex items-center justify-between text-sm">
          <p className="text-gray-500">
            Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredBookings.length)} of {filteredBookings.length} bookings
          </p>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              {renderPaginationItems()}
              <PaginationItem>
                <PaginationNext
                  onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Modal */}
      <MarketerDetailsModal
        marketer={selectedMarketer}
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
      />
    </div>
  );
};

export default MarketerReport;


