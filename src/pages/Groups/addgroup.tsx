import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface AddGroupProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (newGroup: { groupName: string; groupHead: string; description: string }) => void;
}

export default function AddGroup({ isOpen, onClose, onAdd }: AddGroupProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [groupData, setGroupData] = useState<{
    groupName: string;
    groupHead: string;
    description: string;
  }>({
    groupName: "",
    groupHead: "",
    description: "",
  });

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0); 
      setGroupData({ groupName: "", groupHead: "", description: "" }); 
    }
  }, [isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setGroupData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddGroup = () => {
    onAdd(groupData); // Pass the new group data to the parent component
    onClose(); // Close the modal
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Add New Group"
      description="Complete all steps to add a new group"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleAddGroup}
      steps={[
        {
          title: "Basic Information",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="groupName">Group Name</Label>
                <Input
                  id="groupName"
                  name="groupName"
                  value={groupData.groupName}
                  onChange={handleInputChange}
                  placeholder="Enter group name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="groupHead">Group Head</Label>
                <Input
                  id="groupHead"
                  name="groupHead"
                  value={groupData.groupHead}
                  onChange={handleInputChange}
                  placeholder="Enter group head name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={groupData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the group"
                  rows={3}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="space-y-4 py-2">
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Group Name:</span>
                  <span className="ml-2">{groupData.groupName}</span>
                </div>
                <div>
                  <span className="font-medium">Group Head:</span>
                  <span className="ml-2">{groupData.groupHead}</span>
                </div>
                <div>
                  <span className="font-medium">Description:</span>
                  <span className="ml-2">{groupData.description}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review the information above before adding this group.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}