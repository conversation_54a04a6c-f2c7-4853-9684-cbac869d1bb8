import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp'
import { useGetAccountsCreditsDashboardQuery } from '@/redux/slices/teams'
import { formatNumberWithCommas } from '@/utils/salesDataFormatter'
import { BarChartBig, Calendar, ChartArea, ChartGantt, ChartLine, ChartNoAxesCombined, ChartPie, ChartSpline, Globe, TrendingUp } from 'lucide-react'
import { NavLink } from 'react-router-dom'

const CreditsTeamMetrics = () => {
    const { data, isLoading, isFetching } = useGetAccountsCreditsDashboardQuery({})

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    return (
        <div className='relative space-y-4'>
            {showLoader && (
                <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                    <SpinnerTemp type="spinner-double" size="md" />
                </div>
            )}

            <div className='grid grid-cols-1 md:grid-cols-4 gap-5'>
                <div className='bg-emerald-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-emerald-800 text-white p-2 rounded-lg'>
                            <ChartLine size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.Collections?.ALL_Overdue_Collections) || 0}</p>
                    <p className='text-sm'>All Overdue Collections</p>
                </div>
                <div className='bg-cyan-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-cyan-800 text-white p-2 rounded-lg'>
                            <ChartNoAxesCombined size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.Collections?.Overdue_Collections_Collected) || 0}</p>
                    <p className='text-sm trancate'>Overdue Collections Collected</p>
                </div>
                <div className='bg-red-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-red-800 text-white p-2 rounded-lg'>
                            <ChartGantt size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.Collections?.Overdue_Collections) || 0}</p>
                    <p className='text-sm trancate'>Overdue Collections</p>
                </div>
                <div className='bg-blue-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-blue-800 text-white p-2 rounded-lg'>
                            <ChartPie size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.Collections?.Sales_Deposits_Below_Threshold) || 0}</p>
                    <p className='text-sm trancate'>Deposits Below Threshold</p>
                </div>
            </div>

            {/* desktop  */}
            <div className='hidden md:contents'>
                <div className='grid grid-cols-4 py-14 '>
                    <div className='flex flex-col items-center border-r'>
                        <Calendar size={40} className='text-emerald-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Installments_Due_Today) || 0}</p>
                        <p className='text-sm'>Installments due today</p>
                    </div>
                    <div className='flex flex-col items-center border-r'>
                        <BarChartBig size={40} className='text-cyan-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Overdue_Below_Threshold) || 0}</p>
                        <p className='text-sm'>Overdue Below Threshold</p>
                    </div>
                    <div className='flex flex-col items-center border-r'>
                        <ChartArea size={40} className='text-red-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Expected_Monthly_Installments) || 0}</p>
                        <p className='text-sm'>Expected Monthly Installments</p>
                    </div>
                    <div className='flex flex-col items-center'>
                        <ChartSpline size={40} className='text-blue-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.EXPECTED_Monthly_installments_collected) || 0}</p>
                        <p className='text-sm'>Monthly Installments Collected</p>
                    </div>
                </div>
            </div>

            {/* mobile  */}
            <div className='md:hidden overflow-x-auto scrollbar-hide'>
                <div className='flex py-14'>
                    <div className='flex flex-col items-center flex-shrink-0 w-48 border-r'>
                        <Calendar size={40} className='text-emerald-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Installments_Due_Today) || 0}</p>
                        <p className='text-sm text-center'>Installments due today</p>
                    </div>
                    <div className='flex flex-col items-center flex-shrink-0 w-48 border-r'>
                        <BarChartBig size={40} className='text-cyan-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Overdue_Below_Threshold) || 0}</p>
                        <p className='text-sm text-center'>Overdue Below Threshold</p>
                    </div>
                    <div className='flex flex-col items-center flex-shrink-0 w-48 border-r'>
                        <ChartArea size={40} className='text-red-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Expected_Monthly_Installments) || 0}</p>
                        <p className='text-sm text-center'>Expected Monthly Installments</p>
                    </div>
                    <div className='flex flex-col items-center flex-shrink-0 w-48'>
                        <ChartSpline size={40} className='text-blue-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.EXPECTED_Monthly_installments_collected) || 0}</p>
                        <p className='text-sm text-center'>Monthly Installments Collected</p>
                    </div>
                </div>
            </div>


            <div className='border border-border/60 rounded-xl'>
                <div className='py-6 border-b border-border/60 px-4'>
                    <p className='font-bold text-sm'>Team Performance</p>
                </div>
                <div>
                    {/* Diaspora Regions */}
                    <div className="p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {data?.Credits_Teams_Performance?.map((region: any, index: number) => (
                                <RegionCard key={index} region={region} />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
            <div className='h-5'></div>
        </div>

    )
}

export default CreditsTeamMetrics

const RegionCard = ({ region }: any) => (
    <div className="rounded-xl px-2 py-6 border border-border/60 hover:shadow-lg transition-shadow">
        <div className="flex items-center justify-between  mb-2">
            <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                    <Globe className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                    <h3 className="font-semibold text-sm ">{region?.fullnames}</h3>
                    <p className="text-xs">{region?.credit_officer_id}</p>
                </div>
            </div>
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-3 py-1 rounded-full cursor-pointer hover:underline">
                <NavLink to={`/credits-team-dashboard/${region.credit_officer_id}`}> Details</NavLink>
            </span>
        </div>
    </div>
);