import { Screen } from "@/app-components/layout/screen"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button"
import { ChartLine, ChartNoAxesCombined, EllipsisVertical } from "lucide-react"
import { useState } from "react";
import AccountsMetrics from "./AccountsMetrics";
import NewSalesReport from "../../Reports/SalesReports/NewSalesReport";


const Accounts = ({ }) => {


    return (
        <Screen>
            <div className='space-y-4'>
                <CustomButtons />
                <AccountsMetrics />
            </div>
        </Screen >
    )
}

export default Accounts

interface CustomButtonsProps {

}
const CustomButtons = ({ }: CustomButtonsProps) => {
    const [isNewSalesModalOpen, setIsNewSalesModalOpen] = useState<boolean>(false);


    return <>
        <div className=''>
            {/* Desktop Buttons */}
            <div className=' w-full'>
                <div className='hidden sm:flex justify-between items-center'>
                    <p className='font-bold text-xl'>Accounts Dashboard</p>
                    <div className='flex gap-3'>

                        <DropdownMenu>
                            <DropdownMenuTrigger>
                                <span className="flex items-center gap-1 h-10 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                                    <ChartNoAxesCombined size={17} />Reports
                                </span>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-96 h-[65vh] overflow-y-scroll">
                                <DropdownMenuItem onClick={() => setIsNewSalesModalOpen(true)} className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>New Sales</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Money IN Reports</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Installments Reports</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Marketers Performance</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Offices Performance</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Projects Summary Report</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Cash On Cash Report</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>PROJECTS - Below Threshold Tally</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>PROJECTS - Overdue Below Threshold Tally</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MARKETERS - Overdue Collections Summary</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MARKETERS - Below Threshold Tally</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MARKETERS - Overdue Below Threshold Tally</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Threshold Attainment Tally</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Plot Tracking summary Report</DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        <DropdownMenu>
                            <DropdownMenuTrigger>
                                <span className="flex items-center gap-1 h-10 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                                    <ChartNoAxesCombined size={17} />Statistics
                                </span>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-56">
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Receivables Statistics</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MIB Stats Reports</DropdownMenuItem>
                                <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Stock Availability Stats</DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        <button className="flex items-center gap-1 h-10 text-green-600 text-sm  hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                            <ChartLine size={17} />Collection Feedback
                        </button>
                    </div>
                </div>
            </div>
            {/* Mobile Dropdown */}
            <div className='flex sm:hidden justify-between py-2 w-full'>
                <p className='font-bold text-xl'>Accounts Dashboard</p>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button size='icon' variant='outline'><EllipsisVertical /></Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-96 h-[65vh] overflow-y-scroll">
                        <DropdownMenuLabel className='py-3'>Reports</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>New Sales Reports</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Money IN Reports</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Installments Reports</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Marketers Performance</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Offices Performance</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Projects Summary Report</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Cash On Cash Report</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>PROJECTS - Below Threshold Tally</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>PROJECTS - Overdue Below Threshold Tally</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MARKETERS - Overdue Collections Summary</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MARKETERS - Below Threshold Tally</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MARKETERS - Overdue Below Threshold Tally</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Threshold Attainment Tally</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Plot Tracking summary Report</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel className='py-3'>Statistics</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Receivables Statistics</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MIB Stats Reports</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Stock Availability Stats</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel className='py-3'>Feedback</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Collection Feedback</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>


        <NewSalesReport isModalOpen={isNewSalesModalOpen} setIsModalOpen={setIsNewSalesModalOpen} />
    </>
}




