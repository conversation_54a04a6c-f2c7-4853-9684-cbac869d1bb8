import { Screen } from "@/app-components/layout/screen"
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Calendar, EllipsisVertical, ListFilter, Plus } from "lucide-react";
import MarketerMetrics from "./MarketerMetrics";

const MarketerDashboard = () => {
    return (
        <Screen>
            <div className='space-y-4'>
                <CustomButtons />
                <MarketerMetrics />
            </div>
        </Screen>
    )
}

export default MarketerDashboard


const CustomButtons = () => {
    return <div className=''>
        {/* Desktop Buttons */}
        <div className=' w-full'>
            <div className='hidden sm:flex justify-between items-center'>
                <p className='font-bold text-xl'>Marketer Dashboard</p>
                <div className='flex gap-3'>
                    <span className="flex items-center gap-1 h-10 text-green-600 text-sm cursor-pointer hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                        <Plus size={17} />Create New Lead
                    </span>
                    <span className="flex items-center gap-1 h-10 text-green-600 text-sm cursor-pointer hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                        <ListFilter size={17} />Tasks
                    </span>
                    <span className="flex items-center gap-1 h-10 text-green-600 text-sm cursor-pointer hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                        <Calendar size={17} />Calendar
                    </span>
                </div>
            </div>
            {/* Mobile Dropdown */}
            <div className='flex sm:hidden justify-between w-full'>
                <p className='font-bold text-xl'>Marketer Dashboard</p>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button size='icon' variant='outline'><EllipsisVertical /></Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-72 h-[65vh] overflow-y-scroll">
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Create Lead</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Tasks</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Calendar</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>
    </div>
}