import { Screen } from "@/app-components/layout/screen"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChartNoAxesCombined, EllipsisVertical } from "lucide-react";
import DiasporaMetrics from "./DiasporaMetrics";

const DiasporaDashboard = () => {

    return (
        <Screen>
            <div className='space-y-4'>
                <CustomButtons />
                <DiasporaMetrics />
            </div>
        </Screen >
    )
}

export default DiasporaDashboard

const CustomButtons = () => {
    return <div className=''>
        {/* Desktop Buttons */}
        <div className=' w-full'>
            

            <div className='hidden sm:flex justify-between gap-3'>
                <p className='font-bold text-xl'>Diaspora Dashboard</p>
                <DropdownMenu>
                    <DropdownMenuTrigger>
                        <span className="flex items-center gap-1 h-10 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                            <ChartNoAxesCombined size={17} />Performance Reports
                        </span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56">
                        <DropdownMenuLabel className='py-3'>Global Office</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Regions Portfolio</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Lead Sources Portfolio</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Trips Portfolio</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MIB Reports</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>New Sales Reports</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
            {/* Mobile Dropdown */}
            <div className='flex sm:hidden justify-between  w-full'>
                <p className='font-bold text-xl'>Diaspora Dashboard</p>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button size='icon' variant='outline'><EllipsisVertical /></Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56">
                        <DropdownMenuLabel className='py-3'>Reports</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Regions Portfolio</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Lead Sources Portfolio</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Trips Portfolio</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MIB Reports</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>New Sales Reports</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>
    </div>
}