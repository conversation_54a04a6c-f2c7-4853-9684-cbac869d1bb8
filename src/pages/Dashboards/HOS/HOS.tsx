import { Screen } from "@/app-components/layout/screen"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChartColumnBig, ChartLine, ChartNoAxesCombined, EllipsisVertical } from "lucide-react";
import HOSMetrics from "./HOS-Metrics";

const HOS = () => {
   return (
      <Screen>
         <div className='space-y-4'>
            <CustomButtons />
            <HOSMetrics />
         </div>
      </Screen>
   )
}

export default HOS

const CustomButtons = () => {
   return <div className=''>
      {/* Desktop Buttons */}
      <div className=' w-full'>
         <div className='hidden sm:flex justify-between'>
            <p className='font-bold text-xl'>HQ HOS Dashboard</p>
            <div className='flex gap-3'>
               <DropdownMenu>
                  <DropdownMenuTrigger>
                     <span className="flex items-center gap-1 h-10 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                        <ChartNoAxesCombined size={17} />Performance Reports
                     </span>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56">
                     <DropdownMenuLabel className='py-3'>Kenya Office</DropdownMenuLabel>
                     <DropdownMenuSeparator />
                     <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Monthly Performance</DropdownMenuItem>
                  </DropdownMenuContent>
               </DropdownMenu>
               <span className="flex items-center gap-1 h-10 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                  <ChartColumnBig size={17} />New Sales Report
               </span>
               <span className="flex items-center gap-1 h-10 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                  <ChartLine size={17} />Cash on Cash Report
               </span>
            </div>
         </div>
         {/* Mobile Dropdown */}
         <div className='flex sm:hidden justify-between py-2 w-full'>
            <p className='font-bold text-xl'>HQ HOS Dashboard</p>
            <DropdownMenu>
               <DropdownMenuTrigger asChild>
                  <Button size='icon' variant='outline'><EllipsisVertical /></Button>
               </DropdownMenuTrigger>
               <DropdownMenuContent className="w-96 h-[48vh] overflow-y-scroll">
                  <DropdownMenuLabel className='py-3'>Reports</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Monthly Performance</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel className='py-3'>Other</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>New Sales Report</DropdownMenuItem>
                  <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Cash on Cash Report</DropdownMenuItem>
               </DropdownMenuContent>
            </DropdownMenu>
         </div>
      </div>
   </div>
}