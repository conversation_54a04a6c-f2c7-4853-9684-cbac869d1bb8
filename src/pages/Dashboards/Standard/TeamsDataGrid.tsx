import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { formatNumberWithCommas, formatShortDate } from "@/utils/salesDataFormatter";

const TeamsDataGrid = ({
    gridItems
}: {
    gridItems: []
}) => {
    const renderItem = (item: any, index: number) => {
        const baseClasses = `border border-purple-900/30 p-6 rounded-2xl relative overflow-hidden transition-transform hover:scale-105 min-h-32`;

        return (
            <div key={item.id || index} className={baseClasses}>
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-4 right-4 w-8 h-8 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                        </svg>
                    </div>
                </div>

                {/* Date Badge */}
                <div className="absolute top-4 left-4">
                    <span className="text-xs font-medium bg-opacity-20 px-2 py-1 rounded">
                        {formatShortDate(item.period_start_date)}
                    </span>
                </div>

                {/* Content */}
                <div className="relative z-10 h-full flex flex-col justify-between">
                    <div className="mt-8">
                        <h3 className="font-bold mb-2 text-xl">
                            {item.team}
                        </h3>
                        <p className="!leading-2 text-opacity-90 text-xs">
                            Monthly Target: Ksh {formatNumberWithCommas(item.monthly_target)}
                        </p>
                        <p className="!leading-4 text-opacity-90 text-xs">
                            MIB Achieved: Ksh {formatNumberWithCommas(item.MIB_achieved)}
                        </p>
                        <p className="text-opacity-90 text-xs">
                            MIB Performance: {formatNumberWithCommas(item.MIB_Perfomance)}
                        </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between mt-4">
                        <PrimaryButton className="text-foreground px-3 py-1.5 text-xs rounded-md">
                            Performance
                        </PrimaryButton>
                        <DropdownMenu>
                            <DropdownMenuTrigger>
                                <PrimaryButton className="text-foreground px-3 py-1.5 text-xs rounded-md">
                                    Reports
                                </PrimaryButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-56">
                                <DropdownMenuLabel className="py-3">Team reports</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer">
                                    Installments Exp Today
                                </DropdownMenuItem>
                                <DropdownMenuItem className="py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer">
                                    Overdue Collections
                                </DropdownMenuItem>
                                <DropdownMenuItem className="py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer">
                                    Below Threshold
                                </DropdownMenuItem>
                                <DropdownMenuItem className="py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer">
                                    Monthly Exp Installments
                                </DropdownMenuItem>
                                <DropdownMenuItem className="py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer">
                                    New Sales Reports
                                </DropdownMenuItem>
                                <DropdownMenuItem className="py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer">
                                    MIB Reports
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="px-2 min-h-screen">
            <div className="max-w-6xl mx-auto">
                <div className="grid grid-cols-1 [@media(min-width:550px)]:grid-cols-4 gap-4 auto-rows-min">
                    {gridItems.map((item, index) => renderItem(item, index))}
                </div>
            </div>
        </div>
    );
};

export default TeamsDataGrid;