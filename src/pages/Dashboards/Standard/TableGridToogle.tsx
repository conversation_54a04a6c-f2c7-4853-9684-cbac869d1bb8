import { Grid, Table } from "lucide-react";
import { ReactNode, useState } from "react";

export default function TableGridToogle({
    grid,
    table,
}: {
    grid:ReactNode
    table:ReactNode
}) {
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'table'


    const toggleView = () => {
        setViewMode(viewMode === 'grid' ? 'table' : 'grid');
    };

    return (
        <div className="py-6  min-h-screen">
            {/* Header with Title and Toggle */}
            <div className="flex items-center justify-between mb-6">
                {/* Title */}
                <div>
                    <h1 className="text-2xl font-bold">Teams Performance</h1>
                </div>

                {/* Toggle Button */}
                <div className="flex items-center  rounded-lg border shadow-sm">
                    <button
                        onClick={toggleView}
                        className={`flex items-center px-4 py-2 rounded-l-lg transition-colors duration-200 text-xs ${viewMode === 'grid'
                                ? 'bg-primary text-white'
                                : 'hover:text-white hover:bg-primary'
                            }`}
                    >
                        <Grid className="w-4 h-4 mr-2" />
                        Grid
                    </button>
                    <button
                        onClick={toggleView}
                        className={`flex items-center px-4 py-2 rounded-r-lg transition-colors duration-200 text-xs ${viewMode === 'table'
                                ? 'bg-primary text-white'
                                : 'hover:text-white hover:bg-primary'
                            }`}
                    >
                        <Table className="w-4 h-4 mr-2" />
                        Table
                    </button>
                </div>
            </div>

            {/* Dynamic Content */}
            <div className="transition-all duration-300">
                {viewMode === 'grid' ? (
                    grid
                    // <TeamsDataGrid gridItems={data ?? []} />
                ) : (
                    table
                    // <TeamsTable data={data ?? []} />
                )}
            </div>
        </div>
    );
};