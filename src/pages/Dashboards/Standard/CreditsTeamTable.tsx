import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { NavLink } from "react-router-dom";

interface Team {
    fullnames: string,
    credit_officer_id: string,
}

const columns: ColumnDef<Team>[] = [
    {
        accessorKey: 'fullnames',
        header: 'Name',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'actions',
        header: 'Actions',
        cell: info => {
            const rowData = info.row.original;
            return (
                <div className='flex gap-3'>
                    <PrimaryButton className="text-foreground px-3 py-1.5 text-xs rounded-md">
                        <NavLink to={`/credits-team-dashboard/${rowData.credit_officer_id}`}> Details</NavLink>
                    </PrimaryButton>
                </div>
            );
        },
        enableColumnFilter: false,
        enableSorting: false,
    },
]
const CreditsTeamsTable = ({ data }: { data: [] }) => {
    return (
        <div className='border border-purple-900/30 rounded-lg px-4 py-4'>
            <DataTable<Team>
                data={data}
                columns={columns}
                title="Global Office"
                enableSelectColumn={false}
                enableColumnFilters={false}
                enableSorting={true}
                enableToolbar={true}
                tableClassName='border-none'
                containerClassName=' py-2 '
                tHeadClassName='border-t'
                tHeadCellsClassName=" px-2 py-6 "
                tBodyCellsClassName="text-xs  px-2"
                tBodyTrClassName='hover:!bg-transparent'
            />
        </div>
    )
}

export default CreditsTeamsTable

