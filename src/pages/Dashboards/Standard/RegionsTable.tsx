import { DataTable } from "@/components/custom/tables/Table1";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";

interface Region {
    region: string,
    leadSources: number,
    collectedMBI: string,
    by: string,
}

const data: Region[] = [
    {
        region: 'Europe',
        leadSources: 21,
        collectedMBI: '431,999,000',
        by: '<PERSON>',
    }
]


const columns: ColumnDef<Region>[] = [
    {
        accessorKey: 'name',
        header: 'Region',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'manager_name',
        header: 'Manager',
        cell: info => String(info.getValue()).slice(0, 20) + '...',
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'leadsource_count',
        header: 'Lead Sources',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'total_paid_sum',
        header: 'Total Paid Sum',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'actions',
        header: 'Actions',
        cell: info => {
            const rowData = info.row.original;
            return (
                <div className='flex gap-3'>
                   <DropdownMenu>
                        <DropdownMenuTrigger className='border border-purple-900 bg-transparent text-foreground  hover:bg-transparent px-4 py-1.5 rounded-md'>
                            Reports
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-56">
                            <DropdownMenuLabel className='py-3'>Team reports</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>View Region</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Open Lead Sources</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Open Trips</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MIB Report</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            );
        },
        enableColumnFilter: false,
        enableSorting: false,
    },
]
const RegionsTable = ({data}:{data:[]}) => {
    return (
        <div className='border border-purple-900/30 rounded-lg px-4 py-4'>
            <DataTable<Region>
                data={data}
                columns={columns}
                title="Global Office"
                enableSelectColumn={false}
                enableColumnFilters={false}
                enableSorting={true}
                enableToolbar={true}
                tableClassName='border-none'
                containerClassName=' py-2 '
                tHeadClassName='border-t'
                tHeadCellsClassName=" px-2 py-6 "
                tBodyCellsClassName="text-xs  px-2"
                tBodyTrClassName='hover:!bg-transparent'
            />
        </div>
    )
}

export default RegionsTable

