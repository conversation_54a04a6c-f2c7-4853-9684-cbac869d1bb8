import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp'
import { Badge } from '@/components/ui/badge'
import { useGetAccountsCreditTeamDetailsQuery } from '@/redux/slices/teams'
import { formatNumberWithCommas } from '@/utils/salesDataFormatter'
import { BarChartBig, Calendar, ChartArea, ChartColumnBig, ChartColumnIncreasing, ChartGantt, ChartNoAxesCombined, ChartPie, ChartSpline, CircleCheck, TrendingUp } from 'lucide-react'
import { useParams } from 'react-router-dom'

const CreditTeamDetailsMetrics = () => {
    const { credit_officer_id } = useParams<{ credit_officer_id: string }>()

    const { data, isLoading, isFetching } = useGetAccountsCreditTeamDetailsQuery({ Credit_officer_erp_id: credit_officer_id })

    const expectedCollectionRate = 10

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;
    
    return (
        <div className='relative space-y-4 mt-2'>
            {showLoader && (
                <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                    <SpinnerTemp type="spinner-double" size="md" />
                </div>
            )}
            <div className='font-bold text-xl '>Credit Team Details</div>
            <div className='grid grid-cols-1 md:grid-cols-6 gap-5'>
                <div className='bg-emerald-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-emerald-800 text-white p-2 rounded-lg'>
                            <Calendar size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Count</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.installments_due_today) || 0}</p>
                    <p className='text-sm'>Installments Due Today</p>
                </div>
                <div className='bg-cyan-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-cyan-800 text-white p-2 rounded-lg'>
                            <ChartNoAxesCombined size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.installments_due_today_total) || 0}</p>
                    <p className='text-sm '>Installments Due Today</p>
                </div>
                <div className='bg-red-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-red-800 text-white p-2 rounded-lg'>
                            <ChartGantt size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.all_overdue_collections) || 0}</p>
                    <p className='text-sm '>All Overdue Collections</p>
                </div>
                <div className='bg-blue-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-blue-800 text-white p-2 rounded-lg'>
                            <ChartPie size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.overdue_collections_collected) || 0}</p>
                    <p className='text-sm'>Overdue Collections Collected</p>
                </div>
                <div className='bg-teal-50 cyan-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-teal-800 text-white p-2 rounded-lg'>
                            <ChartColumnBig size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.overdue_collections) || 0}</p>
                    <p className='text-sm '>Overdue Collections</p>
                </div>
                <div className='bg-fuchsia-50 -50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-fuchsia-800 text-white p-2 rounded-lg'>
                            <ChartColumnIncreasing size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.total_expexted_installments) || 0}</p>
                    <p className='text-sm '>Expected Installments</p>
                </div>
            </div>

            <div className='grid grid-cols-3 py-14  '>
                <div className='flex flex-col items-center border-r  '>
                    <BarChartBig size={40} className='text-cyan-600' />
                    <p className='text-lg md:text-[25px] font-bold'>{formatNumberWithCommas(data?.Portfolio?.all_customers) || 0}</p>
                    <p className='text-xs md:text-sm'>Customers</p>
                </div>
                <div className='flex flex-col items-center border-r '>
                    <ChartArea size={40} className='text-red-600' />
                    <p className='text-lg md:text-[25px] font-bold'>{formatNumberWithCommas(data?.Portfolio?.all_sales) || 0}</p>
                    <p className='text-xs md:text-sm'>Sales</p>
                </div>
                <div className='flex flex-col items-center'>
                    <ChartSpline size={40} className='text-blue-600' />
                    <p className='text-lg md:text-[25px] font-bold'>{formatNumberWithCommas(data?.Portfolio?.portfolio_total_paid) || 0}</p>
                    <p className='text-xs md:text-sm'>Portfolio Total Paid</p>
                </div>
            </div>


            <div className='grid grid-cols-1 md:grid-cols-3  gap-5'>

                {/* collections  */}
                <div className='border border-border/60   px-2 py-4 rounded-xl space-y-3 '>
                    <div className='flex items-center gap-2 mb-4'>
                        <div className='bg-emerald-600 p-1.5 rounded-lg text-white'>
                            <CircleCheck size={24} />
                        </div>
                        <div>
                            <p className='font-bold text-sm'>Sales Overview</p>
                            <p className='text-xs'>Details</p>
                        </div>
                        <Badge className='bg-emerald-600 ml-auto px-4'>
                            <p>4/4</p>
                        </Badge>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-red-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Sales Below Threshold</p>
                            </div>
                            <Badge className='bg-red-600 ml-auto px-4'>
                                <p className='font-bold'>{formatNumberWithCommas(data?.sales_below_threshold_count) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-cyan-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Overdue Below Threshold</p>
                            </div>
                            <Badge className='bg-cyan-600 ml-auto px-4'>
                                <p className='font-bold'>{formatNumberWithCommas(data?.overdue_below_threshold_count) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-fuchsia-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Monthly Installments Due</p>
                            </div>
                            <Badge className='bg-fuchsia-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.monthly_installments_due) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-blue-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Monthly Installments Collected</p>
                            </div>
                            <Badge className='bg-blue-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.SalesOverview?.monthly_installments_due_collected) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                </div>

                {/* Expected Monthly Installments Card */}
                <div className="border border-border/60 rounded-xl p-6 ">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className=" text-sm font-bold">Collections</h3>
                        <div className="flex items-center gap-2">
                            <ChartGantt className={`w-5 h-5 ${getStatusColor(expectedCollectionRate)}`} />

                        </div>
                    </div>

                    <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-xs">
                            <span>Overdue Collections</span> <span className='font-bold'> Ksh {formatNumberWithCommas(data?.all_overdue_collections) || 0}</span>
                        </div>
                        <div className="flex justify-between text-xs">
                            <span>Overdue Collected</span> <span className='font-bold'> Ksh {formatNumberWithCommas(data?.overdue_collections_collected) || 0}</span>
                        </div>
                    </div>

                    <div className="mt-4">
                        <div className="w-full bg-slate-200  rounded-full h-1.5">
                            <div
                                className={`h-1.5 rounded-full transition-all duration-500 ${expectedCollectionRate >= 80 ? 'bg-green-400' :
                                    expectedCollectionRate >= 60 ? 'bg-yellow-400' :
                                        expectedCollectionRate >= 40 ? 'bg-orange-400' : 'bg-red-400'
                                    }`}
                                style={{ width: `${Math.min(expectedCollectionRate, 100)}%` }}
                            />
                        </div>
                        <div className="flex justify-between text-xs  mt-1">
                            <span>0%</span>
                            <span>100%</span>
                        </div>
                    </div>

                    <div className="mt-4 flex items-center justify-center">
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${expectedCollectionRate >= 80 ? 'bg-green-400/20 text-green-400' :
                            expectedCollectionRate >= 60 ? 'bg-yellow-400/20 text-yellow-400' :
                                expectedCollectionRate >= 40 ? 'bg-orange-400/20 text-orange-400' :
                                    'bg-red-400/20 text-red-400'
                            }`}>
                            {getStatusText(expectedCollectionRate).toUpperCase()} PERFORMANCE
                        </div>
                    </div>
                </div>

                {/* collections  */}
                <div className=' border border-border/60  px-2 py-4 rounded-xl space-y-3'>
                    <div className='flex items-center gap-2 mb-4'>
                        <div className='bg-emerald-600 p-1.5 rounded-lg text-white'>
                            <CircleCheck size={24} />
                        </div>
                        <div>
                            <p className='font-bold text-sm'>Collection Overview</p>
                            <p className='text-xs'>Details</p>
                        </div>
                        <Badge className='bg-emerald-600 ml-auto px-4'>
                            <p>4/4</p>
                        </Badge>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-red-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Installments Collected Today</p>
                            </div>
                            <Badge className='bg-red-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.installments_collected_today) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-cyan-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Deposit Installments Collected</p>
                            </div>
                            <Badge className='bg-cyan-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.additionaldeposits_installments_collected) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-fuchsia-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Final Payments Collected</p>
                            </div>
                            <Badge className='bg-fuchsia-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.finalpaymentscollected_mib) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-blue-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Final Payments Collected Count</p>
                            </div>
                            <Badge className='bg-blue-600 ml-auto px-4'>
                                <p className='font-bold'>{formatNumberWithCommas(data?.finalpaymentscollected_no_of_payments) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                </div>
            </div>

            <div className='h-5'></div>
        </div>
    )
}

export default CreditTeamDetailsMetrics

const getStatusText = (percentage: any) => {
    if (percentage >= 80) return 'excellent';
    if (percentage >= 60) return 'good';
    if (percentage >= 40) return 'fair';
    return 'critical';
};

const getStatusColor = (percentage: any) => {
    if (percentage >= 80) return 'text-green-400';
    if (percentage >= 60) return 'text-yellow-400';
    if (percentage >= 40) return 'text-orange-400';
    return 'text-red-400';
};
