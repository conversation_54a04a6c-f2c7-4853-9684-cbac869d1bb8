import CustomSelectField from "@/components/CustomSelectField";
import LazyModal, { TableColumn } from "@/components/reports/ReportsModal";
import LazyRModal from "@/components/reports/RModal";
import { useLazyGetLeadSourceQuery } from "@/redux/slices/propects";
import {
  formatNumberWithCommas,
  formatShortDate,
} from "@/utils/salesDataFormatter";
import { useState } from "react";
import { Link } from "react-router-dom";

interface NewSale {
  prospect_id: number;
  name: string | null;
  phone_number: string | null;
  email: string | null;
  city: string | null;
  country: string | null;
  leadsource_title: string | null;
  category: string | null;
  pipeline_level: string | null;
  created_date: string | null;
  comment: string | null;
}

interface NewSalesReportProps {
  isModalOpen: boolean;
  setIsModalOpen: (state: boolean) => void;
}

interface FilterComponentProps {
  setLeadSource: React.Dispatch<React.SetStateAction<string>>;
}

// filter component
const FilterComponent = ({ setLeadSource }: FilterComponentProps) => {
  const [
    fetchLeadSource,
    { data: leadSourcesData, isLoading: loadingLeadSources },
  ] = useLazyGetLeadSourceQuery({});

  return (
    <div className="flex items-center justify-start ">
      <div className="space-y-2 flex flex-col my-4">
        <CustomSelectField
          valueField="id"
          labelField="name"
          data={leadSourcesData?.data?.results}
          queryFunc={fetchLeadSource}
          setValue={setLeadSource}
          loader={loadingLeadSources}
          useSearchField={true}
          placeholder="Search Lead Source"
        />
      </div>
    </div>
  );
};

const UnallocatedLeadsReport = ({
  isModalOpen,
  setIsModalOpen,
}: NewSalesReportProps) => {
  const [lead_source, setLeadSource] = useState("");

  // Define table columns with proper typing
  const columns: TableColumn<NewSale>[] = [
    {
      key: "name",
      title: "Name",
      render: (value: string, row: any) => {
        console.log("object", row);
        return (
          <Link to={`/prospects/${row?.prospect_id}`} title="View Prospect">
            <span className="text-blue-400 underline">
              {value.toUpperCase().slice(0, 30)}
            </span>
          </Link>
        );
      },
    },
    {
      key: "phone_number",
      title: "Phone Number",
    },
    {
      key: "leadsource_title",
      title: "Leadsource Title",
    },
    {
      key: "email",
      title: "Email",
    },
    {
      key: "city",
      title: "City",
    },
    {
      key: "country",
      title: "Country",
    },
    {
      key: "category",
      title: "Category",
    },
    {
      key: "pipeline_level",
      title: "Pipeline Level",
    },
    {
      key: "created_date",
      title: "Date Created",
      render: (value: string) => {
        return <span>{formatShortDate(value)}</span>;
      },
    },
  ];

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <LazyRModal<NewSale>
      isOpen={isModalOpen}
      onClose={handleCloseModal}
      title="Unallocated Leads Report"
      url="/unallocated-leads/" // Your actual API endpoint
      params={{ leadsource_title: lead_source }}
      columns={columns}
      size="full"
      enableBackendSearch={true}
      //   FilterComponents={<FilterComponent setLeadSource={setLeadSource} />}
    />
  );
};

export default UnallocatedLeadsReport;
