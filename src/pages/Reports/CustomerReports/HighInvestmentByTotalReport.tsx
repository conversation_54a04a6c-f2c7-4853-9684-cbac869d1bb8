import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { Badge } from '@/components/ui/badge';

interface HighInvestmentByTotal {
    customer_no: string;
    customer_name: string;
    customer_type: string;
    phone: string;
    primary_email: string;
    total_investment: number;
}

interface HighInvestmentByTotalReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const HighInvestmentByTotalReport = ({ isModalOpen, setIsModalOpen }: HighInvestmentByTotalReportProps) => {

    // Define table columns with proper typing
    const columns: TableColumn<HighInvestmentByTotal>[] = [
        {
            key: 'customer_no',
            title: 'Customer No',
            render: (value: string) => (
                <span className="font-medium text-blue-600">{value}</span>
            )
        },
        {
            key: 'customer_name',
            title: 'Customer Name',
            render: (value: string) => (
                <span className="font-medium">{value}</span>
            )
        },
        {
            key: 'customer_type',
            title: 'Type',
            render: (value: string) => (
                <Badge variant="outline" className="text-xs">
                    {value}
                </Badge>
            )
        },
        {
            key: 'phone',
            title: 'Phone',
            render: (value: string) => (
                <span className="text-sm">{value || 'N/A'}</span>
            )
        },
        {
            key: 'primary_email',
            title: 'Email',
            render: (value: string) => (
                <span className="text-sm text-gray-600">{value}</span>
            )
        },
        {
            key: 'total_investment',
            title: 'Total Investment',
            render: (value: number) => {
                const getInvestmentLevel = (amount: number) => {
                    if (amount >= 1000000) return { level: 'Very High', color: 'bg-red-100 text-red-800' };
                    if (amount >= 500000) return { level: 'High', color: 'bg-orange-100 text-orange-800' };
                    if (amount >= 250000) return { level: 'Medium', color: 'bg-yellow-100 text-yellow-800' };
                    return { level: 'Standard', color: 'bg-green-100 text-green-800' };
                };

                const { level, color } = getInvestmentLevel(value);

                return (
                    <div className="flex flex-col gap-1">
                        <span className="font-bold text-lg text-green-600">
                            KES {formatNumberWithCommas(value.toString())}
                        </span>
                        <Badge variant="outline" className={`text-xs ${color} w-fit`}>
                            {level}
                        </Badge>
                    </div>
                );
            }
        },
    ];

    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    return (
        <LazyModal<HighInvestmentByTotal>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Customer High Investment Report by Sum of Total"
            url="/customer-high-invest_sum_of_total/"
            params={{}}
            columns={columns}
            size="xl"
        />
    );
};

export default HighInvestmentByTotalReport;
