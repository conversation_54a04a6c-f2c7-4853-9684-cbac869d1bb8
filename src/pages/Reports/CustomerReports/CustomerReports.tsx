import { Screen } from "@/app-components/layout/screen"
import { Badge } from "@/components/ui/badge"
import { ChartArea, ChartBarBig, FileChartColumnIncreasing, Users, Star } from "lucide-react"
import { useState } from "react";
import CustomerCategoryReport from "./CustomerCategoryReport";
import HighInvestmentByLeadFilesReport from "./HighInvestmentByLeadFilesReport";
import HighInvestmentByTotalReport from "./HighInvestmentByTotalReport";
import CustomerRatingReport from "./CustomerRatingReport";

const CustomerReports = () => {
    const [isCustomerCategoryModalOpen, setIsCustomerCategoryModalOpen] = useState(false);
    const [isHighInvestmentLeadFilesModalOpen, setIsHighInvestmentLeadFilesModalOpen] = useState(false);
    const [isHighInvestmentTotalModalOpen, setIsHighInvestmentTotalModalOpen] = useState(false);
    const [isCustomerRatingModalOpen, setIsCustomerRatingModalOpen] = useState(false);

    const items = [
        {
            color: "emerald",
            icon: <Users size={24} />,
            title: "Customer Category",
            subtitle: "Active, Completed, Dropped",
            badgeText: "View",
            onClick: () => setIsCustomerCategoryModalOpen(true),
        },
        {
            color: "blue",
            icon: <ChartBarBig size={24} />,
            title: "High Investment by Lead Files",
            subtitle: "Customers with multiple lead files",
            badgeText: "View",
            onClick: () => setIsHighInvestmentLeadFilesModalOpen(true),
        },
        {
            color: "purple",
            icon: <ChartArea size={24} />,
            title: "High Investment by Total",
            subtitle: "Customers with high total investments",
            badgeText: "View",
            onClick: () => setIsHighInvestmentTotalModalOpen(true),
        },
        {
            color: "yellow",
            icon: <Star size={24} />,
            title: "Customer Rating",
            subtitle: "Silver, Bronze, Sapphire ratings",
            badgeText: "View",
            onClick: () => setIsCustomerRatingModalOpen(true),
        },
    ]

    return (
        <Screen>
            <div className="min-h-screen rounded-lg">
                <div className='flex items-center gap-2 py-6'>
                    <FileChartColumnIncreasing className='text-emerald-800 dark:text-foreground' size={40} />
                    <div>
                        <p className='font-bold text-1xl'>Customer Reports</p>
                        <p className='text-xs'>Quick links</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {items.map((item, index) => (
                        <div
                            key={index}
                            className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 cursor-pointer border border-gray-200 dark:border-gray-700"
                            onClick={item.onClick}
                        >
                            <div className="p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className={`p-3 rounded-lg bg-${item.color}-100 dark:bg-${item.color}-900/20`}>
                                        <div className={`text-${item.color}-600 dark:text-${item.color}-400`}>
                                            {item.icon}
                                        </div>
                                    </div>
                                    <Badge 
                                        variant="outline" 
                                        className={`text-${item.color}-600 border-${item.color}-200 hover:bg-${item.color}-50`}
                                    >
                                        {item.badgeText}
                                    </Badge>
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                    {item.title}
                                </h3>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    {item.subtitle}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Modals */}
            <CustomerCategoryReport 
                isModalOpen={isCustomerCategoryModalOpen} 
                setIsModalOpen={setIsCustomerCategoryModalOpen} 
            />
            <HighInvestmentByLeadFilesReport 
                isModalOpen={isHighInvestmentLeadFilesModalOpen} 
                setIsModalOpen={setIsHighInvestmentLeadFilesModalOpen} 
            />
            <HighInvestmentByTotalReport
                isModalOpen={isHighInvestmentTotalModalOpen}
                setIsModalOpen={setIsHighInvestmentTotalModalOpen}
            />
            <CustomerRatingReport
                isModalOpen={isCustomerRatingModalOpen}
                setIsModalOpen={setIsCustomerRatingModalOpen}
            />
        </Screen>
    )
}

export default CustomerReports
