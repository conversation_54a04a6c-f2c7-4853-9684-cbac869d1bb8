import { Screen } from "@/app-components/layout/screen";
import { Badge } from "@/components/ui/badge";
import NewSalesReport from "@/pages/Reports/SalesReports/NewSalesReport";
import {
  AlignEndHorizontal,
  ChartArea,
  ChartBarBig,
  ChartPie,
  FileChartColumnIncreasing,
} from "lucide-react";
import { useState } from "react";
import CashOnCashReport from "./CashOnCashReport";
import CompletedSalesReport from "./CompletedSalesReport";
import OnGoingSalesReport from "./OnGoingSales";
import SalesViewsReport from "./SalesViewsReport";

const SalesReports = () => {
  const [isNewSalesModalOpen, setIsNewSalesModalOpen] =
    useState<boolean>(false);
  const [isCashOnCashModalOpen, setIsCashOnCashModalOpen] =
    useState<boolean>(false);
  const [isCompletedSalesModalOpen, setIsCompletedSalesModalOpen] =
    useState<boolean>(false);
  const [isOnGoingSalesModalOpen, setIsOnGoingSalesModalOpen] =
    useState<boolean>(false);
  const [isSalesViewsModalOpen, setIsSalesViewsModalOpen] =
    useState<boolean>(false);

  const items = [
    {
      color: "emerald",
      icon: <ChartBarBig size={24} />,
      title: "New Sales",
      subtitle: "Details",
      badgeText: "View",
      onClick: () => setIsNewSalesModalOpen(true),
    },
    {
      color: "blue",
      icon: <ChartArea size={24} />,
      title: "Cash On Cash",
      subtitle: "Overview",
      badgeText: "View",
      onClick: () => setIsCashOnCashModalOpen(true),
    },
    {
      color: "purple",
      icon: <ChartPie size={24} />,
      title: "Completed Sales",
      subtitle: "Summary",
      badgeText: "View",
      onClick: () => setIsCompletedSalesModalOpen(true),
    },
    {
      color: "red",
      icon: <ChartPie size={24} />,
      title: "Ongoing Sales",
      subtitle: "Summary",
      badgeText: "View",
      onClick: () => setIsOnGoingSalesModalOpen(true),
    },
    {
      color: "fuchsia",
      icon: <AlignEndHorizontal size={24} />,
      title: "Sales Views",
      subtitle: "Summary",
      badgeText: "View",
      onClick: () => setIsSalesViewsModalOpen(true),
    },
  ];

  return (
    <Screen>
      <div className="min-h-screen rounded-lg">
        <div className="flex items-center gap-2 py-6">
          <FileChartColumnIncreasing
            className="text-emerald-800 dark:text-foreground"
            size={40}
          />
          <div>
            <p className="font-bold text-1xl">Sales Reports</p>
            <p className="text-xs">Quick links</p>
          </div>
        </div>

        <div className="grid md:grid-cols-3 grid-cols-1 gap-4 px-3">
          {items.map((item, idx) => (
            <div
              key={idx}
              className={`flex items-center gap-2 mb-4 bg-${item.color}-50 dark:bg-${item.color}-800/60 px-3 py-5 rounded-xl cursor-pointer`}
              onClick={item.onClick}
            >
              <div className={`bg-${item.color}-600 p-2 rounded-lg text-white`}>
                {item.icon}
              </div>
              <div>
                <p className="font-bold text-sm">{item.title}</p>
                <p className="text-xs">{item.subtitle}</p>
              </div>
              <Badge
                className={`bg-${item.color}-600 hover:bg-${item.color}-600 ml-auto px-4 hover:underline`}
              >
                <p>{item.badgeText}</p>
              </Badge>
            </div>
          ))}
        </div>
      </div>

      <NewSalesReport
        isModalOpen={isNewSalesModalOpen}
        setIsModalOpen={setIsNewSalesModalOpen}
      />
      <CashOnCashReport
        isModalOpen={isCashOnCashModalOpen}
        setIsModalOpen={setIsCashOnCashModalOpen}
      />
      <CompletedSalesReport
        isModalOpen={isCompletedSalesModalOpen}
        setIsModalOpen={setIsCompletedSalesModalOpen}
      />
      <OnGoingSalesReport
        isModalOpen={isOnGoingSalesModalOpen}
        setIsModalOpen={setIsOnGoingSalesModalOpen}
      />
      <SalesViewsReport
        isModalOpen={isSalesViewsModalOpen}
        setIsModalOpen={setIsSalesViewsModalOpen}
      />
    </Screen>
  );
};

export default SalesReports;
