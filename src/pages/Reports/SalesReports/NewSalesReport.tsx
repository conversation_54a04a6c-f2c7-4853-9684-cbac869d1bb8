import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';

interface NewSale {
    lead_file_no: number;
    customer_name: string;
    selling_price: string;
    total_paid: string;
    balance_lcy: string;
    marketer_id: string;
}

interface NewSalesReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const NewSalesReport = ({ isModalOpen, setIsModalOpen }: NewSalesReportProps) => {


    // Define table columns with proper typing
    const columns: TableColumn<NewSale>[] = [
        {
            key: 'customer_name',
            title: 'Customer',
        },
        {
            key: 'selling_price',
            title: 'Selling Price',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'total_paid',
            title: 'Total Paid',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
        {
            key: 'balance_lcy',
            title: 'Balance',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    return (
        <LazyModal<NewSale>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="New Sales Report"
            url="/New-Sales"  // Your actual API endpoint
            params={{}}
            columns={columns}
            size="lg"
        />
    )
}

export default NewSalesReport