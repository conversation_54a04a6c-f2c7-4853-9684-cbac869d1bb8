import { TableColumn } from '@/components/reports/ReportsModal';
import LazyRModal from '@/components/reports/RModal';
import { Badge } from '@/components/ui/badge';
import { formatNumberWithCommas, formatShortDate } from '@/utils/salesDataFormatter';
import { ReactNode, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface NewSale {
    lead_file_no: number;
    customer_no: string | null;
    customer_name: string | null;
    selling_price: string | null;
    total_paid: string | null;
    balance: string | null;
    booking_date: string | null;
    marketer_name: string | null;
    plot_number: string | null;
    project_name: string | null;
    status: string | null;
}

interface NewSalesReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const DigitalTelemarketingSalesReport = ({ isModalOpen, setIsModalOpen }: NewSalesReportProps) => {


    // Define table columns with proper typing
    const columns: TableColumn<NewSale>[] = [
        {
            key: 'customer_name',
            title: 'Customer Name',
            render: (value: string) => {
                return <span>{value.toUpperCase().slice(0, 30)}</span>
            }
        },
        {
            key: 'selling_price',
            title: 'Selling Price',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'total_paid',
            title: 'Total Paid',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'balance',
            title: 'Balance',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'marketer_name',
            title: 'Marketer Name',
            render: (value: string) => {
                return <span>{value.toUpperCase()}</span>
            }
        },
        {
            key: 'status',
            title: 'Status',
            render: (value: string) => {
                return <Badge className='px-4 py-0.5'>{value.toUpperCase()}</Badge>
            }
        },
        {
            key: 'booking_date',
            title: 'Booking Date',
            render: (value: string) => {
                return <span>{formatShortDate(value)}</span>
            }
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    const [universalSearchValue, setuniversalSearchValue] = useState('')

    return (
        <LazyRModal<NewSale>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Digital Sales Report"
            url="/ digital team sales/"  // Your actual API endpoint
            params={{ status: universalSearchValue }}
            columns={columns}
            size="full"
            enableBackendSearch={true}
            FilterComponents={<FilterComponents setuniversalSearchValue={setuniversalSearchValue} />} // Optional: Add filter components if needed
        />
    )
}


export default DigitalTelemarketingSalesReport

interface SearchComponentProps {
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function FilterComponents({ setuniversalSearchValue }: SearchComponentProps) {
    return (
        <Select onValueChange={(value) => setuniversalSearchValue(value)}>
            <SelectTrigger className="w-[120px] focus:ring-0 border-gray-300 dark:border-gray-700 bg-transparent h-11">
                <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="dropped">Dropped</SelectItem>
            </SelectContent>
        </Select>
    );
}