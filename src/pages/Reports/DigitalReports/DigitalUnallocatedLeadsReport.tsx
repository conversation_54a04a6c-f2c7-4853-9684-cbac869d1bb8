import  { TableColumn } from '@/components/reports/ReportsModal';
import LazyRModal from '@/components/reports/RModal';
import {  formatShortDate } from '@/utils/salesDataFormatter';

interface NewSale {
    prospect_id: number;
    name: string | null;
    phone_number: string | null;
    email: string | null;
    city: string | null;
    country: string | null;
    leadsource_title: string | null;
    category: string | null;
    pipeline_level: string | null;
    created_date: string | null;
    comment: string | null;
}


interface NewSalesReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const DigitalUnallocatedLeadsReport = ({ isModalOpen, setIsModalOpen }: NewSalesReportProps) => {


    // Define table columns with proper typing
    const columns: TableColumn<NewSale>[] = [
        {
            key: 'name',
            title: 'Name',
            render: (value: string) => {
                return <span>{value.toUpperCase().slice(0, 30)}</span>
            }
        },
        {
            key: 'phone_number',
            title: 'Phone Number',
        },
        {
            key: 'email',
            title: 'Email',
        },
        {
            key: 'city',
            title: 'City',
        },
        {
            key: 'country',
            title: 'Country',
        },
        {
            key: 'leadsource_title',
            title: 'Leadsource Title',
        },
        {
            key: 'category',
            title: 'Category',
        },
        {
            key: 'pipeline_level',
            title: 'Pipeline Level',
        },
        {
            key: 'created_date',
            title: 'Date Created',
            render: (value: string) => {
                return <span>{formatShortDate(value)}</span>
            }
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    return (
        <LazyRModal<NewSale>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Unallocated Leads Report"
            url="/digital-unallocated-leads/"  // Your actual API endpoint
            params={{}}
            columns={columns}
            size="full"
            enableBackendSearch={true}
        />
    )
}

export default DigitalUnallocatedLeadsReport