import  { TableColumn } from '@/components/reports/ReportsModal';
import LazyRModal from '@/components/reports/RModal';

interface NewSale {
    name: string | null;
    phone_number: string | null;
    email: string | null;
    national_id: string | null;
    kra: string | null;
    marketer: string | null;
}

interface NewSalesReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const DigitalCustomersReport = ({ isModalOpen, setIsModalOpen }: NewSalesReportProps) => {


    // Define table columns with proper typing
    const columns: TableColumn<NewSale>[] = [
        {
            key: 'name',
            title: 'Name',
            render: (value: string) => {
                return <span>{value.toUpperCase().slice(0, 30)}</span>
            }
        },
        {
            key: 'phone_number',
            title: 'Phone Number',
            render: (value: string) => {
                return <span>{value?.split(',')[0]?.trim()}</span>
            }
        },
        {
            key: 'email',
            title: 'Email',
        },
        {
            key: 'national_id',
            title: 'National ID',
        },
        {
            key: 'kra',
            title: 'KRA',
        },
        {
            key: 'marketer',
            title: 'Marketer',
            render: (value: string) => {
                return <span>{value.toUpperCase()}</span>
            }
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    return (
        <LazyRModal<NewSale>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Digital Customers Report"
            url="/ digital customers/"  // Your actual API endpoint
            params={{}}
            columns={columns}
            size="full"
            enableBackendSearch={true}
        />
    )
}

export default DigitalCustomersReport