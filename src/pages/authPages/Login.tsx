import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Eye, Loader, LucideEyeOff } from "lucide-react";
import { useState } from "react";
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import { usePostLoginMutation } from "@/redux/slices/auth";
import { toast } from "@/components/custom/Toast/MyToast";

type Props = {};

const Login = ({ }: Props) => {
  // const form = useForm();
  const [showPassword, setShowPassword] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination from location state
  const from = location.state?.from || '/';

// form
  const formSchema = z.object({
    username: z.string().min(1, { message: "Email is required.", }),
    password: z.string().min(1, { message: "Password is required.", }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  })

  const [postLogin, { isLoading }] = usePostLoginMutation();

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      const response = await postLogin(values).unwrap();
      toast.success('Login successful');
      navigate(from, { replace: true }); 
      // console.log('Login successful:', response);
    } catch (err: any) {
      const { data } = err
      const { non_field_errors, username } = data
      toast.error(Array.isArray(non_field_errors) && non_field_errors[0] || Array.isArray(username) && username[0] || 'Something went wrong!')
       // console.error('Login failed:', err);
    }
  }



  return (
    <>
      <h1 className="text-center text-2xl font-semibold text-gray-800  dark:text-gray-100 ">
        Login
      </h1>
      <p className="text-center text-gray-600 w-[80%] mx-auto text-sm dark:text-gray-300">
        Enter your email and password below
      </p>
      <div className="flex flex-col gap-4 mt-4 w-[80%] mx-auto">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="ml-2">Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="Email Address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-12 items-end relative ">
              <div className="col-span-12">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="ml-2">Password</FormLabel>
                      <FormControl>
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div
                className="absolute right-1 top-[50%] p-1 cursor-pointer"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <LucideEyeOff className=" text-gray-500   " />
                ) : (
                  <Eye className=" text-gray-500 " />
                )}
              </div>
            </div>
            <PrimaryButton variant="primary" type="submit" disabled={isLoading} className="w-full">
              {isLoading ? (
                <span className='flex gap-2 items-center justify-center'>
                  <Loader className="fas fa-spinner animate-spin" size={20} />
                  Loading...
                </span>
              ) : ('Submit')}
            </PrimaryButton>
          </form>
        </Form>

        <div className="flex items-center justify-between flex-wrapforgot mt-1 dark:text-gray-50">
          <Link
            to="/auth/forgot-password"
            className="text-xs text-primary hover:underline dark:text-gray-50"
          >
            Forgot Password?
          </Link>
          <Link
            to="/auth/reset-password"
            className="text-xs text-accent hover:underline dark:text-gray-50"
          >
            Reset Password
          </Link>
        </div>
      </div>
    </>
  );
};

export default Login;
