import { Outlet } from "react-router-dom";
import Vid from '@/assets/optiven-background.mp4'
import Logo from '@/assets/logo.svg';
import { useEffect, useRef } from "react";

type Props = {};

const AuthIndex = ({ }: Props) => {

const videoRef = useRef<HTMLVideoElement | null>(null);

  // Handle seamless video looping
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      // Set playback speedto 0.5x (slower)
      video.playbackRate = 0.5;

      // Set up seamless looping
      video.addEventListener('ended', () => {
        video.currentTime = 0;
        video.play();
      });

      // Ensure video plays
      video.play().catch(error => {
        console.log('Video autoplay failed:', error);
      });
    }
  }, []);

  return (
    <div className="min-h-screen bg-black flex">
      {/* Left Panel - Video Background */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        {/* Video Background */}
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          preload="auto"
        >
          {/* Replace 'your-video-url.mp4' with your actual video URL */}
          <source src={Vid} type="video/mp4" />
          {/* Fallback for browsers that don't support video */}
          Your browser does not support the video tag.
        </video>

        {/* Video Overlay */}
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Logo/Brand on video */}
        <div className="absolute top-6 left-6 z-10 bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded">
          <img src={Logo} alt="Optiven Logo" className="w-32 h-auto" />
        </div>

        {/* Fallback background for when video is loading */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600 -z-10"></div>
      </div>

      <Outlet />

    </div>
  );
};

export default AuthIndex;

// import loginBg from "../../assets/loginBg.svg";
// import logo from "../../assets/logo.svg";
// const AuthIndex = ({}: Props) => {
//   return (
//     <div className="flex flex-col items-center justify-start h-screen bg-gray-200 relative p-2 ">
//       <div className="w-full h-2/5 rounded-lg">
//         <img
//           src={loginBg}
//           alt="optiven login bg"
//           className="w-full h-full object-cover rounded-xl"
//         />
//       </div>
//       <div className="p-3 bg-white  dark:bg-[#152238] md:w-1/3 sm:w-4/5 min-h-[400px] absolute top-[25%] rounded-xl">
//         <div className="flex items-center justify-center py-4">
//           <img src={logo} alt="Optiven Logo" />
//         </div>
//         <Outlet />

//         <div className="w-full text-center mt-4">
//           <Link to="/" className=" text-center text-xs hover:underline">
//             Navigate Home
//           </Link>
//         </div>
//       </div>
//     </div>
//   );
// };