import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ff, <PERSON><PERSON>, <PERSON>, Mail, Play } from "lucide-react";
import Logo from "@/assets/logo.svg";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { usePostLoginMutation } from "@/redux/slices/auth";
import { toast } from "@/components/custom/Toast/MyToast";
import { Link, useLocation, useNavigate } from "react-router-dom";

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  // Get the intended destination from location state
  const from = location.state?.from || "/";

  // form
  const formSchema = z.object({
    username: z
      .string()
      .min(1, { message: "Email is required." })
      .email({ message: "Invalid email format." }),
    password: z.string().min(1, { message: "Password is required." }),
  });

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });
  const [postLogin, { isLoading: isSignInLoading }] = usePostLoginMutation();
  const onSubmit = async (data: any) => {
    try {
      await postLogin(data).unwrap();
      toast.success("Login successful");
      navigate(from, { replace: true });
      // console.log('Login successful:', response);
    } catch (err: any) {
      const { data } = err;
      const { non_field_errors, username } = data;
      toast.error(
        (Array.isArray(non_field_errors) && non_field_errors[0]) ||
          (Array.isArray(username) && username[0]) ||
          "Something went wrong!"
      );
      // console.error('Login failed:', err);
    }
  };

  return (
    <>
      {/* Right Panel - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-900">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile Logo */}
          <div className="lg:hidden flex justify-center">
            <div className="absolute top-6 left-6 z-10   px-3 py-1.5 rounded">
              <img src={Logo} alt="Optiven Logo" className="w-32 h-auto" />
            </div>
          </div>

          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold text-white">
              Timua Kivumbi Na 5 Billion
              <br />
              By Optiven
            </h1>
          </div>

          {/* Login Link */}
          <div className="text-center">
            <span className="text-gray-400 text-sm">Lost you password? </span>
            <a
              href="https://staff.optiven.co.ke/auth/forgot-password"
              target="_blank"
              className="text-green-400 hover:text-green-300 text-sm font-medium"
            >
              Recover account
            </a>
          </div>
          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-6">
              {/* Email Field */}
              <div>
                <div className="relative">
                  <Mail className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    {...register("username")}
                    placeholder="Your Email"
                    className="w-full pl-14 pr-4 py-3 !bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                  />
                </div>
                {errors.username && (
                  <p className="text-red-500 text-xs pt-2 pl-3">
                    {errors.username.message}
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div>
                <div className="relative">
                  <Lock className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    placeholder="Password"
                    className="w-full pl-14 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-xs pt-2 pl-3">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <button
                className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors  group"
                type="submit"
                disabled={isSignInLoading}
              >
                {isSignInLoading ? (
                  <span className="flex items-center justify-center space-x-2 ">
                    <Loader className="fas fa-spinner animate-spin" size={22} />
                    <span>SIGNING IN...</span>
                  </span>
                ) : (
                  <span className="flex items-center justify-center space-x-2 ">
                    <span>SIGN IN</span>
                    <Play className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </span>
                )}
              </button>
            </div>
          </form>

          {/* Terms */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              By creating your Gen account, you agree to our{" "}
              <button className="text-green-400 hover:text-green-300 underline">
                Terms of Service
              </button>{" "}
              and{" "}
              <button className="text-green-400 hover:text-green-300 underline">
                Privacy Policy
              </button>
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default LoginPage;
