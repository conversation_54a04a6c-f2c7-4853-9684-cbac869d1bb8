import { Link } from "react-router-dom";
import loginBg from "../../assets/loginBg.svg";
import logo from "../../assets/logo.svg";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { ArrowLeft, Eye, LucideEyeOff } from "lucide-react";
import { useState } from "react";

type Props = {};

const Reset = ({}: Props) => {
  const form = useForm();
  const [showPassword, setShowPassword] = useState(false);
  const [showCPassword, setShowCPassword] = useState(false);

  return (
    <>
      <h1 className="text-center text-2xl font-semibold text-gray-800  dark:text-gray-100 ">
        Reset Password
      </h1>
      <p className="text-center text-gray-600 w-[80%] mx-auto text-sm dark:text-gray-300">
        Enter your email and password below
      </p>
      <div className="flex flex-col gap-4 mt-4 w-[80%] mx-auto">
        <Form {...form}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="ml-2">Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="Email Address" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-12 items-end relative ">
            <div className="col-span-12">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="ml-2">New Password</FormLabel>
                    <FormControl>
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="New Password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div
              className="absolute right-1 top-[50%] p-1 cursor-pointer"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <LucideEyeOff className=" text-gray-500   " />
              ) : (
                <Eye className=" text-gray-500 " />
              )}
            </div>
          </div>
          <div className="grid grid-cols-12 items-end relative ">
            <div className="col-span-12">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="ml-2">Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        type={showCPassword ? "text" : "password"}
                        placeholder="Confirm Password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div
              className="absolute right-1 top-[50%] p-1 cursor-pointer"
              onClick={() => setShowCPassword(!showCPassword)}
            >
              {showCPassword ? (
                <LucideEyeOff className=" text-gray-500   " />
              ) : (
                <Eye className=" text-gray-500 " />
              )}
            </div>
          </div>
          <PrimaryButton variant="primary" type="submit">
            Submit
          </PrimaryButton>
        </Form>

        <div className="flex items-center justify-between flex-wrap mt-1">
          <Link
            to="/auth/login"
            className="text-xs text-primary flex items-center hover:scale-105 transition-all duration-300 ease-in-out dark:text-gray-50"
          >
            <ArrowLeft size={12} /> Back to Login
          </Link>
        </div>
      </div>
    </>
  );
};

export default Reset;
