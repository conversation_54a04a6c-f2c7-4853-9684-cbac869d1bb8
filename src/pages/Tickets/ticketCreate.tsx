import { useState } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { AlertCircle, Ticket } from "lucide-react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

interface TicketData {
  id: number;
  title: string;
  value: string;
  icon: typeof Ticket;
  change: string;
  changeLabel: string;
  description: string;
  assignee: string; // Added assignee field
}

interface CreateTicketProps {
  onTicketCreated: (ticket: TicketData) => void;
}

export default function CreateTicket({ onTicketCreated }: CreateTicketProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    value: "Open",
    assignee: "",
  });

  const assignees = ["<PERSON>", "<PERSON>", "<PERSON>", "Unassigned"]; // Sample assignees

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (!formData.title || !formData.description || !formData.assignee) {
      alert("Please fill in all required fields");
      return;
    }
    const newTicket: TicketData = {
      id: Date.now(),
      title: formData.title,
      value: formData.value,
      icon: Ticket,
      change: "Just now",
      changeLabel: "Created recently",
      description: formData.description,
      assignee: formData.assignee,
    };
    onTicketCreated(newTicket);
    console.log("Ticket created:", newTicket);
    setIsOpen(false);
    setCurrentStep(0);
    setFormData({ title: "", description: "", value: "Open", assignee: "" });
  };

  return (
    <>
      <PrimaryButton onClick={() => setIsOpen(true)}>Create Ticket</PrimaryButton>
      
      <MultiStepModal
        isOpen={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) {
            setCurrentStep(0);
            setFormData({ title: "", description: "", value: "Open", assignee: "" });
          }
        }}
        title="Create New Ticket"
        description="Complete all steps to create a new support ticket"
        currentStep={currentStep}
        onStepChange={setCurrentStep}
        onComplete={handleSubmit}
        steps={[
          {
            title: "Ticket Info",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="title">Ticket Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter ticket title"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="value">Status</Label>
                  <select
                    id="value"
                    name="value"
                    value={formData.value}
                    onChange={handleInputChange}
                    className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  >
                    <option value="Open">Open</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Closed">Closed</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assignee">Assignee</Label>
                  <select
                    id="assignee"
                    name="assignee"
                    value={formData.assignee}
                    onChange={handleInputChange}
                    className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                    required
                  >
                    <option value="" disabled>Select an assignee</option>
                    {assignees.map((assignee) => (
                      <option key={assignee} value={assignee}>{assignee}</option>
                    ))}
                  </select>
                </div>
              </div>
            ),
          },
          {
            title: "Details",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter ticket description"
                    required
                    className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 min-h-[100px]"
                  />
                </div>
              </div>
            ),
          },
          {
            title: "Confirmation",
            content: (
              <div className="py-6 text-center space-y-4">
                <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium">Ready to Submit!</h3>
                <p className="text-muted-foreground mt-2">
                  Please review your ticket details before submitting.
                </p>
                <div className="space-y-2 text-left">
                  <p><strong>Title:</strong> {formData.title || "N/A"}</p>
                  <p><strong>Status:</strong> {formData.value || "N/A"}</p>
                  <p><strong>Assignee:</strong> {formData.assignee || "N/A"}</p>
                  <p><strong>Description:</strong> {formData.description || "N/A"}</p>
                </div>
              </div>
            ),
          },
        ]}
      />
    </>
  );
}