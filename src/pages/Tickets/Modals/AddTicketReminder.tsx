import React, { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { toast } from "sonner";
import { useUpdateTicketMutation } from "@/redux/slices/tickets";

interface AddTicketReminderProps {
  isOpen: boolean;
  onClose: () => void;
  ticketId: number;
  onSuccess?: () => void;
}

const AddTicketReminder: React.FC<AddTicketReminderProps> = ({
  isOpen,
  onClose,
  ticketId,
  onSuccess,
}) => {
  const [patchTicket, { isLoading }] = useUpdateTicketMutation();

  const [formData, setFormData] = useState({
    reminder_type: "General",
    reminder_date: "",
    reminder_time: "",
    priority: "Normal",
    description: "",
  });

  const reminderTypes = [
    "General",
    "Follow-up Call",
    "Payment Reminder",
    "Document Collection",
    "Site Visit",
    "Meeting",
    "Email",
    "SMS",
  ];

  const priorities = ["Low", "Normal", "High", "Urgent"];

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const resetForm = () => {
    setFormData({
      reminder_type: "General",
      reminder_date: "",
      reminder_time: "",
      priority: "Normal",
      description: "",
    });
  };

  const handleSubmit = async () => {
    if (!formData.reminder_date) {
      toast.error("Reminder date is required");
      return;
    }

    if (!formData.reminder_time) {
      toast.error("Reminder time is required");
      return;
    }

    try {
      // Combine date and time for backend format: YYYY-MM-DD HH:MM
      const combinedDateTime = `${formData.reminder_date}T${formData.reminder_time}:00`;

      const payload = new FormData();
      payload.append("ticket", ticketId.toString());
      payload.append("reminder_type", formData.reminder_type);
      payload.append("reminder_date", formData.reminder_date);
      payload.append("reminder_time", combinedDateTime);
      payload.append("reminder_priority", formData.priority);
      payload.append("reminder_descriptions", formData.description);
      payload.append("add_reminder", "true");

      await patchTicket({ id: ticketId, data: payload }).unwrap();

      toast.success("Reminder added successfully");
      resetForm();
      onClose();
      onSuccess?.();
    } catch (error: any) {
      console.error("Failed to add reminder:", error);
      toast.error(error?.data?.error || "Failed to add reminder");
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={handleClose}
      title="Add Ticket Reminder"
      description="Set a reminder for this ticket"
      size="md"
    >
      <div className="space-y-4">
        {/* Reminder Type */}
        <div className="space-y-2">
          <Label htmlFor="reminder_type">Reminder Type</Label>
          <Select
            value={formData.reminder_type}
            onValueChange={(value) => handleInputChange("reminder_type", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select reminder type" />
            </SelectTrigger>
            <SelectContent>
              {reminderTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date and Time */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="reminder_date">
              Reminder Date <span className="text-destructive">*</span>
            </Label>
            <Input
              id="reminder_date"
              type="date"
              value={formData.reminder_date}
              onChange={(e) =>
                handleInputChange("reminder_date", e.target.value)
              }
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="reminder_time">
              Reminder Time <span className="text-destructive">*</span>
            </Label>
            <Input
              id="reminder_time"
              type="time"
              value={formData.reminder_time}
              onChange={(e) =>
                handleInputChange("reminder_time", e.target.value)
              }
              required
            />
          </div>
        </div>

        {/* Priority */}
        <div className="space-y-2">
          <Label htmlFor="priority">Priority</Label>
          <Select
            value={formData.priority}
            onValueChange={(value) => handleInputChange("priority", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              {priorities.map((priority) => (
                <SelectItem key={priority} value={priority}>
                  {priority}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Enter reminder description (optional)"
            rows={3}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <SpinnerTemp type="spinner-double" size="sm" />
                Adding...
              </>
            ) : (
              "Add Reminder"
            )}
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export default AddTicketReminder;
