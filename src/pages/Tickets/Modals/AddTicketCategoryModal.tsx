import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { useCreateTicketsCategoriesMutation } from "@/redux/slices/tickets";
import { toast } from "sonner";

interface categoryTypes {
  id: number;
  name: string;
  description: string;
}

interface Props {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  updateData?: categoryTypes | null;
}

const AddTicketCategoryModal = ({
  isOpen,
  onOpenChange,
  updateData,
}: Props) => {
  const [name, setname] = useState(updateData ? updateData?.name : "");
  const [description, setDescription] = useState(
    updateData ? updateData?.description : ""
  );

  const [createCategory, { isLoading: creating }] =
    useCreateTicketsCategoriesMutation();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !description) {
      toast.error("All field are required");
    }

    try {
      const res = await createCategory({ name, description }).unwrap();
      if (res) {
        toast.success("Ticket Category created successfully");
        onOpenChange(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`Error creating ticket category`);
      }
      return;
    }
  };
  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`${
        updateData
          ? "Update " + updateData?.name + " ticket category"
          : "Create Ticket Category"
      }`}
      size="lg"
      //   showClose={true}
      position="center"
    >
      <form onSubmit={onSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 items-center text-sm">
          <div className="space-y-2">
            <label>
              {" "}
              Name <span className="text-destructive">*</span>
            </label>
            <input
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              type="text"
              name="name"
              value={name}
              onChange={(e) => setname(e.target.value)}
              placeholder="Enter name"
              required
            />
          </div>

          <div className="space-y-2">
            <label>
              {" "}
              Description <span className="text-destructive">*</span>
            </label>
            <textarea
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              name="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter Description"
              required
            ></textarea>
          </div>
        </div>
        <div className="w-full flex justify-end">
          {creating ? (
            <SpinnerTemp type="spinner-double" size="sm" />
          ) : (
            <Button type="submit" className="justify-end">
              Submit
            </Button>
          )}
        </div>
      </form>
    </BaseModal>
  );
};

export default AddTicketCategoryModal;
