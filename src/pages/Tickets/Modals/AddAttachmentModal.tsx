import React, { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileText, Image, Upload } from "lucide-react";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useUploadTicketAttachmentMutation } from "@/redux/slices/tickets";

interface AddAttachmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  refetch: () => void;
  ticketId: number;
  uploadedBy: string;
}

const AddAttachmentModal: React.FC<AddAttachmentModalProps> = ({
  isOpen,
  onClose,
  refetch,
  ticketId,
  uploadedBy,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);
  const [UploadAtchment, { isLoading }] = useUploadTicketAttachmentMutation();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      setSelectedFile(file);
      setIsPdf(file.type === "application/pdf");

      if (file.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(file));
      } else {
        setPreviewUrl(null);
      }
    } else {
      resetForm();
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setIsPdf(false);
  };

  const handleSubmit = async () => {
    if (!selectedFile) return;

    const formData = new FormData();
    formData.append("ticket", ticketId.toString());
    formData.append("uploaded_by", uploadedBy);
    formData.append("file", selectedFile as string | Blob);

    try {
      await UploadAtchment(formData);
      refetch();
      resetForm();
      onClose();
    } catch (error) {
      console.error("Failed to upload attachment:", error);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={handleClose}
      title="Add Attachment"
      description="Upload a file attachment to this ticket"
      size="md"
    >
      <form className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="file-upload">Select File</Label>
          <label
            htmlFor="file-upload"
            className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
          >
            <Input
              id="file-upload"
              type="file"
              accept="image/*,application/pdf,.doc,.docx,.txt"
              onChange={handleFileChange}
              className="hidden"
              required
            />

            {previewUrl && !isPdf ? (
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-32 object-contain rounded-lg"
              />
            ) : selectedFile && isPdf ? (
              <div className="flex flex-col items-center">
                <FileText className="h-12 w-12 text-gray-400 mb-2" />
                <p className="text-gray-700 font-medium">PDF Selected</p>
              </div>
            ) : selectedFile ? (
              <div className="flex flex-col items-center">
                <FileText className="h-12 w-12 text-gray-400 mb-2" />
                <p className="text-gray-700 font-medium">File Selected</p>
              </div>
            ) : (
              <>
                <Upload className="h-12 w-12 text-gray-400 mb-2" />
                <p className="text-gray-500">Click to upload file</p>
                <p className="text-xs text-gray-400 mt-1">
                  Supports images, PDF, and documents
                </p>
              </>
            )}
          </label>

          {selectedFile && (
            <p className="text-sm text-gray-600 text-center">
              Selected: <span className="font-medium">{selectedFile.name}</span>
            </p>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={!selectedFile || isLoading}
          >
            {isLoading ? (
              <>
                <SpinnerTemp type="spinner-double" size="sm" />
                Uploading...
              </>
            ) : (
              "Upload Attachment"
            )}
          </Button>
        </div>
      </form>
    </BaseModal>
  );
};

export default AddAttachmentModal;
