import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { useFetchTicketLogsQuery } from "@/redux/slices/tickets";
import { formatDateTime } from "@/utils/formatDate";
import { ColumnDef } from "@tanstack/react-table";
import { GitForkIcon, Plus } from "lucide-react";
import { useState } from "react";

type Props = {};
interface LogTypes {
  id: number;
  ticket: string;
  action: string;
  performed_by: string;
  comment: string;
  timestamp: string;
}

const index = () => {
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const { data: ticketLogs, isLoading: loading } = useFetchTicketLogsQuery({
    ticket: searchValue,
    page: currentPage,
    page_size: itemsPerPage,
  });

  const columns: ColumnDef<LogTypes>[] = [
    {
      accessorKey: "ticket",
      header: "Ticket",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "performed_by",
      header: "Action By",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "action",
      header: "Action Type",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "Create"
              ? "bg-primary text-white"
              : info?.getValue() === "Edit"
              ? "bg-blue-400 text-white"
              : "bg-destructive text-white"
          } text-center px-2 pt-1 pb-1.5 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "comment",
      header: "Task",
      cell: (info) => `${info.getValue() as string}`,
      enableColumnFilter: false,
    },
    {
      accessorKey: "timestamp",
      header: "Time",
      cell: (info) => formatDateTime(info.getValue()),
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      {/* header */}
      <div className="flex justify-between items-center mb-4">
        <div className="">
          <h1 className="text-3xl font-bold">Ticketing Logs</h1>
          {/* <p>View Ticket Logs</p> */}
        </div>
      </div>

      {/* table  */}
      <div className="">
        {loading ? (
          <div className="w-full flex items-center justify-center">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : (
          // ) : ticketLogs && ticketLogs?.data?.results?.length === 0 ? (
          //   <div className="w-full flex flex-col justify-center items-center  shadow-inner my-3 rounded py-4 text-destructive ">
          //     <GitForkIcon size={24} /> No Tickets Logs found!!
          //   </div>
          <DataTable<LogTypes>
            data={ticketLogs?.data?.results}
            columns={columns}
            enableToolbar={true}
            enablePagination={true}
            enableColumnFilters={true}
            enableSorting={true}
            enableExportToExcel={true}
            enablePrintPdf={true}
            tableClassName="border-collapse"
            tHeadClassName="bg-gray-50"
            tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
            tBodyTrClassName="hover:bg-gray-50"
            tBodyCellsClassName="border-t"
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            totalItems={ticketLogs?.data?.total_data || 0}
            searchInput={
              <input
                value={searchValue}
                name="searchValue"
                type="search"
                onChange={(e) => setSearchValue(e.target.value)}
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Search logs..."
              />
            }
          />
        )}
      </div>
    </Screen>
  );
};

export default index;
