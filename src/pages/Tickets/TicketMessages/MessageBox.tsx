import React from "react";
import { ticketTypes } from "..";
import { Button } from "@/components/ui/button";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Inbox, MessageCircleMore } from "lucide-react";
import AddTicketMessage from "./AddTicketMessage";
import { formatDateTime } from "@/utils/formatDate";

type Props = {
  ticket: ticketTypes;
  messages: any;
  currentUser: string;
  mloading: boolean;
};

const MessageBox = ({ ticket, mloading, messages, currentUser }: Props) => {
  return (
    <div className=" rounded ">
      <div className="py-2 px-5 rounded-t bg-primary shadow-lg flex justify-between items-center flex-wrap">
        <div className="text-lg font-bold text-white flex items-center gap-2">
          {" "}
          <MessageCircleMore /> Ticket Messages
        </div>
        {/* <Button variant="secondary">Add Message</Button> */}
      </div>

      {mloading ? (
        <div className="w-full flex items-center justify-center p-6">
          <SpinnerTemp type="spinner-double" size="sm" />
        </div>
      ) : messages?.data?.results?.length < 1 ? (
        <p className="flex items-center justify-center gap-2 py-4">
          <Inbox /> No Messages Found
        </p>
      ) : (
        <div className=" border py-2 max-h-[70vh] overflow-y-scroll">
          {messages?.data?.results?.map((msg: any, index: number) => {
            const isCurrentUser = msg?.sender === currentUser; // replace currentUser with your logged-in user ID/username
            return (
              <div
                key={index}
                className={`m-3 flex ${
                  isCurrentUser ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`max-w-[70%] px-2 py-2 rounded-lg text-white break-words ${
                    isCurrentUser
                      ? "bg-primary rounded-br-none"
                      : "bg-[dodgerblue] rounded-bl-none"
                  }`}
                >
                  {!isCurrentUser && (
                    <div className="flex text-sm items-center gap-1  py-1 mb-1">
                      <div className="p-2 text-xs bg-amber-600 rounded-full">
                        {msg?.sender_name?.substring(0, 2)}
                      </div>
                      <p className="text-[10px] capitalize">
                        {msg?.sender_name.toLowerCase()}
                      </p>
                    </div>
                  )}

                  <p className="px-3 py-2">{msg?.message}</p>

                  <p className="text-[9px] text-end text-secondary px-3">
                    {formatDateTime(msg?.created_at)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="px-3 py-2 bg-secondary rounded-b border">
        <AddTicketMessage
          ticket={ticket?.id?.toString()}
          currentUser={currentUser}
        />
      </div>
    </div>
  );
};

export default MessageBox;
