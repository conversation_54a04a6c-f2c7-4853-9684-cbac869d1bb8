import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { useFetchTicketsCategoriesQuery } from "@/redux/slices/tickets";
import { ColumnDef } from "@tanstack/react-table";
import { GitForkIcon, Plus } from "lucide-react";
import { useState } from "react";
import AddTicketCategoryModal from "../Modals/AddTicketCategoryModal";

interface categoryTypes {
  id: number;
  name: string;
  description: string;
}

const index = () => {
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { data: ticketsCategories, isLoading: loading } =
    useFetchTicketsCategoriesQuery({
      search: searchValue,
      page: currentPage,
      page_size: itemsPerPage,
    });

  const columns: ColumnDef<categoryTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <span className="font-medium capitalize ">
          {info.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: (info) => (
        <span className="font-medium capitalize">
          {info.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const ticketCats = row.original;
        return (
          <div className="flex space-x-2 justify-start">
            {/* <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewProspect(prospect.id)}
                  className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
                >
                  <span title="Edit">
                    <Edit />
                  </span>
                </PrimaryButton> */}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      {/* header */}
      <div className="flex justify-between items-center mb-4">
        <div className="">
          <h1 className="text-3xl font-bold">Ticketing Categories</h1>
          <p>View Ticket Categories</p>
        </div>
        <Button
          variant="default"
          className="flex items-center"
          onClick={() => setShowCreateModal(true)}
        >
          <Plus /> Create Ticket Category
        </Button>
      </div>

      {/* table  */}
      <div className="">
        {loading ? (
          <div className="w-full flex items-center justify-center">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : ticketsCategories &&
          ticketsCategories?.data?.results?.length === 0 ? (
          <div className="w-full flex flex-col justify-center items-center shadow my-3 rounded py-4 text-destructive">
            <GitForkIcon size={24} /> No Tickets Categories found!!
          </div>
        ) : (
          <DataTable<categoryTypes>
            data={ticketsCategories?.data?.results}
            columns={columns}
            enableToolbar={true}
            enablePagination={true}
            enableColumnFilters={true}
            enableSorting={true}
            enablePrintPdf={true}
            tableClassName="border-collapse"
            tHeadClassName="bg-gray-50"
            tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
            tBodyTrClassName="hover:bg-gray-50"
            tBodyCellsClassName="border-t"
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            totalItems={ticketsCategories?.data?.total_data || 0}
            searchInput={
              <input
                value={searchValue}
                name="searchValue"
                type="search"
                onChange={(e) => setSearchValue(e.target.value)}
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Search categories..."
              />
            }
          />
        )}
      </div>

      {/* create modal  */}
      {showCreateModal && (
        <AddTicketCategoryModal
          isOpen={showCreateModal}
          onOpenChange={setShowCreateModal}
        />
      )}
    </Screen>
  );
};

export default index;
