import { TrendingUp, TrendingDown, Clock, CheckCircle2, Al<PERSON><PERSON>riangle, XCircle } from 'lucide-react';
import React from 'react';

interface TicketStatsProps {
  tickets?: Array<{
    id: string;
    value: string; // status
    priority?: string;
    created_at: string;
  }>;
  onStatusFilter?: (status: string) => void;
  currentFilter?: string;
}

export default function TicketsStats({ tickets = [], onStatusFilter, currentFilter }: TicketStatsProps) {
  // Calculate real stats from tickets data
  const calculateStats = () => {
    const openTickets = tickets.filter(t => t.value === "Open").length;
    const pendingTickets = tickets.filter(t => t.value === "Pending").length;
    const resolvedTickets = tickets.filter(t => t.value === "Resolved").length;
    const closedTickets = tickets.filter(t => t.value === "Closed").length;

    // Calculate trends (mock data for now - in real app, compare with previous period)
    const totalTickets = tickets.length;
    const previousTotal = Math.max(1, totalTickets - 5); // Mock previous period

    return [
      {
        label: "Open",
        value: openTickets,
        color: "#ef4444",
        gradient: "from-green-500 to-emerald-500",
        icon: AlertTriangle,
        change: openTickets > previousTotal * 0.3 ? `+${Math.floor(openTickets * 0.2)}` : `${Math.floor(openTickets * 0.1)}`,
        trend: openTickets > previousTotal * 0.3 ? "up" : "down",
        status: "Open"
      },
      {
        label: "Pending",
        value: pendingTickets,
        color: "#f59e0b",
        gradient: "from-yellow-500 to-orange-500",
        icon: Clock,
        change: pendingTickets > previousTotal * 0.2 ? `+${Math.floor(pendingTickets * 0.3)}` : `-${Math.floor(pendingTickets * 0.1)}`,
        trend: pendingTickets > previousTotal * 0.2 ? "up" : "down",
        status: "Pending"
      },
      {
        label: "Resolved",
        value: resolvedTickets,
        color: "#10b981",
        gradient: "from-green-500 to-emerald-500",
        icon: CheckCircle2,
        change: `+${Math.floor(resolvedTickets * 0.25)}`,
        trend: "up",
        status: "Resolved"
      },
      {
        label: "Closed",
        value: closedTickets,
        color: "#6b7280",
        gradient: "from-gray-500 to-slate-500",
        icon: XCircle,
        change: closedTickets > 0 ? `+${Math.floor(closedTickets * 0.1)}` : "0",
        trend: closedTickets > 0 ? "up" : "neutral",
        status: "Closed"
      }
    ];
  };

  const ticketStats = calculateStats();
  const totalTickets = ticketStats.reduce((sum, stat) => sum + stat.value, 0);

  return (
    <div className="bg-white rounded-2xl shadow-xl border-0 overflow-hidden">
      {/* Header with gradient */}
      <div className="bg-gradient-to-r from-green-600 via-emerald-600 to-pink-500 p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">Ticket Analytics</h2>
        <p className="text-blue-100">Real-time overview of support tickets</p>
      </div>

      <div className="p-6">
        <div className="flex flex-col lg:flex-row gap-8 items-center">
          {/* Chart Section - Simple Visual Representation */}
          <div className="w-full lg:w-1/3 flex flex-col items-center">
            <div className="relative w-48 h-48 flex items-center justify-center">
              {/* Simple Donut Chart using CSS */}
              <div className="relative w-40 h-40 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                <div className="w-24 h-24 bg-white rounded-full flex flex-col items-center justify-center shadow-inner">
                  <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    {totalTickets}
                  </div>
                  <div className="text-xs text-gray-500">Total</div>
                </div>
              </div>

              {/* Status indicators around the circle */}
              <div className="absolute inset-0">
                {ticketStats.map((stat, index) => {
                  const angle = (index * 90) - 45; // Distribute around circle
                  const radius = 100;
                  const x = Math.cos((angle * Math.PI) / 180) * radius;
                  const y = Math.sin((angle * Math.PI) / 180) * radius;

                  return (
                    <div
                      key={stat.label}
                      className="absolute w-3 h-3 rounded-full shadow-lg"
                      style={{
                        backgroundColor: stat.color,
                        left: `calc(50% + ${x}px - 6px)`,
                        top: `calc(50% + ${y}px - 6px)`,
                      }}
                    />
                  );
                })}
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="flex-1 grid grid-cols-2 lg:grid-cols-4 gap-4 w-full">
            {ticketStats.map((stat) => {
              const Icon = stat.icon;
              const isActive = currentFilter === stat.status;

              return (
                <div
                  key={stat.label}
                  onClick={() => onStatusFilter?.(stat.status)}
                  className={`group relative overflow-hidden bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border transition-all duration-300 transform hover:-translate-y-1 cursor-pointer ${
                    isActive
                      ? `border-2 shadow-lg ${stat.gradient.includes('red') ? 'border-red-300' : stat.gradient.includes('yellow') ? 'border-yellow-300' : stat.gradient.includes('green') ? 'border-green-300' : 'border-gray-300'}`
                      : 'border-gray-200 hover:shadow-lg'
                  }`}
                >
                  {/* Gradient overlay on hover or active */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} transition-opacity duration-300 ${
                    isActive ? 'opacity-10' : 'opacity-0 group-hover:opacity-5'
                  }`}></div>

                  {/* Active indicator */}
                  {isActive && (
                    <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${stat.gradient}`}></div>
                  )}

                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-3">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.gradient} shadow-lg ${
                        isActive ? 'scale-110' : ''
                      } transition-transform duration-200`}>
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex items-center gap-1 text-xs">
                        {stat.trend === "up" && <TrendingUp className="h-3 w-3 text-green-500" />}
                        {stat.trend === "down" && <TrendingDown className="h-3 w-3 text-red-500" />}
                        <span className={`font-medium ${
                          stat.trend === "up" ? "text-green-600" :
                          stat.trend === "down" ? "text-red-600" : "text-gray-500"
                        }`}>
                          {stat.change}
                        </span>
                      </div>
                    </div>

                    <div className={`text-3xl font-bold mb-1 transition-colors duration-200 ${
                      isActive ? 'text-gray-900' : 'text-gray-800'
                    }`}>
                      {stat.value}
                    </div>

                    <div className={`text-sm font-medium transition-colors duration-200 ${
                      isActive ? 'text-gray-700' : 'text-gray-600'
                    }`}>
                      {stat.label}
                    </div>

                    <div className="mt-2 text-xs text-gray-500">
                      {totalTickets > 0 ? ((stat.value / totalTickets) * 100).toFixed(1) : '0'}% of total
                    </div>

                    {/* Click indicator */}
                    <div className="mt-2 text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      Click to filter
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}