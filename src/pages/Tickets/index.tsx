import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { useFetchTicketsQuery } from "@/redux/slices/tickets";
import { ColumnDef } from "@tanstack/react-table";
import { Filter, Plus, Ticket, TicketXIcon } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import AddTicketModal from "./Modals/AddTicketModal";

type Props = {};

export interface ticketTypes {
  id: number;
  ticket_id: string;
  title: string;
  description: string;
  customer: string;
  user: string;
  category: string;
  department: string;
  team: string;
  prospect: string;
  priority: string;
  status: string;
  created_at: string;
  attachments: string;
  messages: string;
  actions: string;
  source_name: string;
  category_name: string;
  customer_name: string;
  user_name: string;
  sales_number: string;
  prospect_name: string;
  escalations: {
    id: string;
    user: string;
    user_name: string;
    ticket: number;
    ticket_id: string;
    created_at: string;
  }[];
}

const ticketStatus = [
  {
    label: "ALL TICKETS",
    value: "",
  },
  {
    label: "OPEN TICKETS",
    value: "open",
  },
  {
    label: "IN PROGESS TICKETS",
    value: "in_progress",
  },
  {
    label: "ESCALATED TICKETS",
    value: "escalated",
  },
  {
    label: "RESOLVED TICKETS",
    value: "resolved",
  },
  {
    label: "CLOSED TICKETS",
    value: "closed",
  },
];

const index = ({}: Props) => {
  const [searchValue, setSearchValue] = useState("");
  const [currentStatus, setCurrentStatus] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const searchPlaceholder =
    currentStatus === ""
      ? "Search all tickets"
      : `Search ${currentStatus.replace("_", " ")} tickets`;

  const {
    data: ticketsData,
    isLoading: loading,
    isFetching,
  } = useFetchTicketsQuery({
    status: currentStatus,
    page: currentPage,
    page_size: itemsPerPage,
    ticket_id: searchValue,
  });

  const columns: ColumnDef<ticketTypes>[] = [
    {
      accessorKey: "ticket_id",
      header: "Ticket ID",
      cell: (info) => (
        <Link to={`/ticketing/${info?.row?.original?.id}`}>
          <span className="font-medium text-[dodgerblue] underline">
            {info.getValue() as string}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: (info) => (
        <span className="font-medium ">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "customer",
      header: "RAISED FOR",
      cell: (info) => {
        const customer = info.row.original.customer;
        const salesNumber = info.row.original.sales_number;
        if (customer) {
          return (
            <Link to={`/customer/${customer}`} title="View Customer">
              <span className="font-medium underline capitalize text-blue-400">
                {info.row.original.customer_name}
              </span>
            </Link>
          );
        } else if (salesNumber) {
          return (
            <Link
              to={`/sales/sales-card/${salesNumber}`}
              title="View Sales Card"
            >
              <span className="font-medium underline capitalize text-green-400">
                {"Lead File No"} ({salesNumber})
              </span>
            </Link>
          );
        } else {
          return "N/A";
        }
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "source_name",
      header: "Ticket Source",
      cell: (info) =>
        (info.getValue() as string) ? (
          <span className="font-medium">{info.getValue() as string}</span>
        ) : (
          "N/A"
        ),
      enableColumnFilter: false,
    },

    {
      accessorKey: "category_name",
      header: "Category",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },

    {
      accessorKey: "priority",
      header: "Priority",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "low"
              ? "bg-primary text-white px-3"
              : info?.getValue() === "medium"
              ? "bg-yellow-400 text-black px-3"
              : info?.getValue() === "high"
              ? "bg-orange-400 text-black px-3"
              : "bg-destructive text-white"
          } text-center px-2 pt-1 pb-1.5 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "open"
              ? "bg-primary text-white px-3"
              : info?.getValue() === "in_progress"
              ? "bg-yellow-400 text-black px-3"
              : info?.getValue() === "resolved"
              ? "bg-gray-500 text-black px-3"
              : "bg-destructive text-white"
          } text-center px-2 pt-1 pb-1.5 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      {/* header */}
      <div className="flex justify-between items-center mb-4">
        <div className="">
          <h1 className="text-3xl font-bold">Ticketing Module</h1>
          <p>Manage and track customer support requests</p>
        </div>
      </div>

      {/* stats cards section */}
      <div className="grid lg:grid-cols-5 md:grid-cols-3 grid-cols-2 gap-2">
        <div className="p-3 flex items-center justify-start bg-primary/10 gap-2 rounded-lg shadow-md">
          <div className="p-2 rounded-full bg-primary text-gray-300">
            <Ticket />
          </div>
          <div className="text-primary text-xs">
            <p>Open Tickets</p>
            <p className="font-bold">{ticketsData?.tickets_status?.open}</p>
          </div>
        </div>{" "}
        <div className="p-3 flex items-center justify-start bg-amber-600/10 gap-2 rounded-lg shadow-md">
          <div className="p-2 rounded-full bg-amber-600 text-gray-300">
            <Ticket />
          </div>
          <div className="text-amber-500 text-xs">
            <p>In progress</p>
            <p className="font-bold">
              {ticketsData?.tickets_status?.in_progress}
            </p>
          </div>
        </div>{" "}
        <div className="p-3 flex items-center justify-start bg-green-500/10 gap-2 rounded-lg shadow-md">
          <div className="p-2 rounded-full bg-green-500 text-gray-300">
            <Ticket />
          </div>
          <div className="text-green-500 text-xs">
            <p>Escalated</p>
            <p className="font-bold">
              {ticketsData?.tickets_status?.escalated}
            </p>
          </div>
        </div>{" "}
        <div className="p-3 flex items-center justify-start bg-black/10 gap-2 rounded-lg shadow-md">
          <div className="p-2 rounded-full bg-black dark:bg-gray-700 text-gray-300">
            <Ticket />
          </div>
          <div className="text-black dark:text-gray-300 text-xs">
            <p>Resolved</p>
            <p className="font-bold">{ticketsData?.tickets_status?.resolved}</p>
          </div>
        </div>{" "}
        <div className="p-3 flex items-center justify-start bg-destructive/10 gap-2 rounded-lg shadow-md">
          <div className="p-2 rounded-full bg-destructive text-gray-300">
            <Ticket />
          </div>
          <div className="text-destructive text-xs">
            <p>Reopened</p>
            <p className="font-bold">{ticketsData?.tickets_status?.closed}</p>
          </div>
        </div>
        <div className="lg:hidden p-3 flex items-center justify-start bg-cyan-400/10 gap-2 rounded-lg shadow-md">
          <div className="p-2 rounded-full bg-cyan-400 text-gray-300">
            <Ticket />
          </div>
          <div className="text-cyan-bg-cyan-400 text-xs">
            <p>Total</p>
            <p className="font-bold">{ticketsData?.tickets_status?.total}</p>
          </div>
        </div>
      </div>

      {/* filter section  */}
      <div className="w-full flex justify-between items-center flex-wrap pt-6 gap-2">
        <input
          value={searchValue}
          name="searchValue"
          type="search"
          onChange={(e) => setSearchValue(e.target.value)}
          className="px-4 py-2 w-full md:w-1/2  border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder={searchPlaceholder}
        />
        <div className="flex gap-2">
          <Button
            variant="secondary"
            className="flex items-center justify-center gap-1"
          >
            <Filter /> Filter
          </Button>
          <Button
            variant="secondary"
            className="flex items-center justify-center gap-1"
          >
            <Filter /> Sort
          </Button>
          <Button
            variant="default"
            className="flex items-center justify-center gap-1"
            onClick={() => setShowCreateModal(true)}
          >
            <Plus /> Create Ticket
          </Button>
        </div>
      </div>

      {/* filters status  */}
      <div className="grid lg:grid-cols-6 md:grid-cols-3 grid-cols-2 gap-2 my-4">
        {ticketStatus?.map((ts: { label: string; value: string }) => (
          <div
            onClick={() => setCurrentStatus(ts.value)}
            className={`${
              currentStatus == ts.value
                ? "bg-primary text-white"
                : " bg-primary/10 text-dark"
            } px-3 py-2 flex items-center justify-center gap-2 text-sm rounded-sm shadow-md cursor-pointer`}
            key={ts.value}
          >
            {ts.label}
          </div>
        ))}
      </div>

      {/* table  */}
      <div className=" mt-4">
        {loading || isFetching ? (
          <div className="w-full flex items-center justify-center p-4">
            <SpinnerTemp type="spinner-double" size="sm" />
          </div>
        ) : ticketsData && ticketsData?.data?.results?.length === 0 ? (
          <div className="w-full flex flex-col justify-center items-center shadow my-3 rounded py-4 text-destructive">
            <TicketXIcon size={24} /> No Tickets found!!
          </div>
        ) : (
          <DataTable<ticketTypes>
            data={ticketsData?.data?.results || []}
            columns={columns}
            enableToolbar={true}
            enablePagination={true}
            enableColumnFilters={true}
            enableSorting={true}
            enablePrintPdf={false}
            tableClassName="border-collapse"
            tHeadClassName="bg-gray-50"
            tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
            tBodyTrClassName="hover:bg-gray-50"
            tBodyCellsClassName="border-t"
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            totalItems={ticketsData?.data?.total_data || 0}
          />
        )}
      </div>

      {/* create modal  */}
      {showCreateModal && (
        <AddTicketModal
          isOpen={showCreateModal}
          onOpenChange={setShowCreateModal}
        />
      )}
    </Screen>
  );
};

export default index;
