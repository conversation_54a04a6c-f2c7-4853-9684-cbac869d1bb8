import { Badge } from "@/components/custom/badges/badges";
import BaseModal from "@/components/custom/modals/BaseModal";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useLazyRefreshUserDataQuery } from "@/redux/slices/auth";
import {
  useCreateUser2UserPermissionMutation,
  useDeleteUser2UserPermissionMutation,
  useGetUser2UserPermissionsQuery,
  useLazyGetUserPermissionsQuery,
} from "@/redux/slices/permissions";
import { useSelector } from "react-redux";
import { selectCurrentUserDetails } from "@/redux/authSlice";
import { Square, SquareCheckBigIcon, TriangleAlert, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Tabs, TabsContent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface TableUser {
  id: number;
  employee_no: string;
  email: string;
  fullnames: string;
  department: string | null;
  designation: string | null;
  status: string | null;
  team: string | null;
  region: string | null;
  manager: string | null;
  phone: string | null;
  gender: string | null;
  created_date: string;
  category: string | null;
}

interface PermType {
  id: number;
  permission: string;
  permission_name: string;
  user: string;
}

// Permission category types
type PermissionCategory = 'prospects' | 'customers' | 'sales' | 'logistics' | 'other';

// Function to categorize permissions based on their ID prefix
const categorizePermission = (permission: string): PermissionCategory => {
  const permId = permission.toString();
  
  // Prospects: 30xx
  if (permId.startsWith('30')) return 'prospects';
  
  // Customers: 20xx (but not 202-209 which are logistics)
  if (permId.startsWith('20') && !(permId >= '202' && permId <= '209')) return 'customers';
  
  // Sales: 10xx or 100x (including 1001-1010)
  if (permId.startsWith('10') || permId.match(/^100\d$/) || (permId >= '1001' && permId <= '1010')) return 'sales';
  
  // Logistics: 2xx or specific 202-209 range
  if ((permId.startsWith('2') && permId.length === 3) || (permId >= '202' && permId <= '209')) return 'logistics';
  
  // Everything else
  return 'other';
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  user: TableUser;
};

const getAvatarUrl = (name: string) => {
  const initials = name
    ?.split(" ")
    .map((n) => n[0])
    .join("");
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(
    initials
  )}&background=random`;
};
const getStatusBadge = (status: string) => {
  const statusLower = status?.toLowerCase();
  if (statusLower === "active") {
    return (
      <Badge
        variant="primary"
        className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      >
        Active
      </Badge>
    );
  } else if (statusLower === "inactive") {
    return (
      <Badge
        variant="destructive"
        className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
      >
        Inactive
      </Badge>
    );
  } else {
    return (
      <Badge
        variant="outline"
        className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
      >
        {status}
      </Badge>
    );
  }
};

const UserPermissionsModal = ({ isOpen, onClose, user }: Props) => {
  const [
    fetchUserPerms,
    { data: userPermsResponse, isLoading: loadingUserPerms },
  ] = useLazyGetUserPermissionsQuery();

  const [refreshUserData] = useLazyRefreshUserDataQuery();
  const currentUser = useSelector(selectCurrentUserDetails);
  const isCurrentUser = currentUser?.employee_no === user.employee_no;

  // Extract the results array from the response
  const userPerms = Array.isArray(userPermsResponse)
    ? userPermsResponse
    : userPermsResponse?.results || [];
  const {
    data: userMaps = [],
    isLoading: loadingUserMaps,
    refetch,
  } = useGetUser2UserPermissionsQuery({
    page: 1,
    page_size: 1000,
    user: user?.employee_no ?? "",
  });
  const [deletePerm, { isLoading: deleting }] =
    useDeleteUser2UserPermissionMutation();
  const [createPerm, { isLoading: creating }] =
    useCreateUser2UserPermissionMutation();

  const [showDeleteModal, setShowDeleteModal] = useState<PermType | null>(null);
  const [showAddPermissionModal, setShowAddPermissionModal] =
    useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<PermissionCategory>('prospects');
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // Group permissions by category
  const categorizedPermissions = userMaps.reduce((acc, permission) => {
    const category = categorizePermission(permission.permission);
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {} as Record<PermissionCategory, PermType[]>);
  
  // Group all available permissions by category for the add modal
  interface AvailablePermission {
    permission_id: number;
    permission_name: string;
    [key: string]: any; // For any additional properties
  }

  type CategorizedAvailablePermissions = Record<PermissionCategory, AvailablePermission[]>;

  const categorizedAvailablePermissions: CategorizedAvailablePermissions = userPerms.reduce(
    (acc: CategorizedAvailablePermissions, perm: AvailablePermission) => {
      const category: PermissionCategory = categorizePermission(perm.permission_id.toString());
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(perm);
      return acc;
    },
    {} as CategorizedAvailablePermissions
  );

  const handleRemovePermission = async (permissionId: number) => {
    try {
      const res = await deletePerm(permissionId).unwrap();
      if (!res) {
        toast.success("Permission removed successfully");
        
        // If this is the current user, refresh their data to update permissions
        if (isCurrentUser) {
          await refreshUserData(currentUser?.employee_no ?? "");
          toast.success("Your permissions have been updated");
        }
      } else {
        toast.error("Failed to remove permission");
      }
    } catch (error) {
      toast.error("Something went wrong");
      console.error("Error removing permission:", error);
    }
  };

  const handleAddPermission = async (permission: number) => {
    try {
      const res = await createPerm({
        permission,
        user: user?.employee_no ?? "",
      }).unwrap();
      if (res.id) {
        toast.success("Permission added successfully");
        refetch();
        
        // If this is the current user, refresh their data to update permissions
        if (isCurrentUser) {
          await refreshUserData(currentUser?.employee_no ?? "");
          toast.success("Your permissions have been updated");
        }
      } else {
        toast.error("Failed to add permission");
      }
    } catch (error) {
      toast.error("Something went wrong");
      console.error("Error adding permission:", error);
    }
  };

  useEffect(() => {
    // Fetch permissions when the modal opens or when the add permission modal opens
    if (isOpen || showAddPermissionModal) {
      fetchUserPermsHandler();
    }
  }, [isOpen, showAddPermissionModal]);

  const fetchUserPermsHandler = async () => {
    try {
      await fetchUserPerms({
        page: 1,
        page_size: 1000 // Fetch up to 1000 permissions to ensure we get all
      }).unwrap();
    } catch (error) {
      toast.error("Failed to fetch user permissions");
      console.error("Error fetching user permissions:", error);
    }
  };

  //   permissionChecker

  const permissionChecker = (permissionId: number) => {
    return userMaps.some(
      (perm: PermType) => Number(perm.permission) === permissionId
    ) ? (
      <SquareCheckBigIcon className="text-green-500" />
    ) : (
      <Square className="text-gray-400" />
    );
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="User Permissions"
      description={`Preview of ${user.fullnames}'s permissions`}
    >
      <div className="space-y-6">
        {/* Profile Section */}
        <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <img
            src={getAvatarUrl(user.fullnames)}
            alt={user.fullnames}
            className="w-16 h-16 rounded-full border-2 border-white shadow-md"
          />
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              {user.fullnames}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">{user.email}</p>
            <div className="mt-2">
              {getStatusBadge(user?.status || "active")}
            </div>
          </div>
        </div>

        {loadingUserMaps ? (
          <div className="flex justify-center items-center h-[50px]">
            <SpinnerTemp type="spinner-double" size="sm" />
          </div>
        ) : userMaps.length > 0 ? (
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              Permissions
            </h4>
            
            <Tabs defaultValue="prospects" className="w-full">
              <TabsList className="grid grid-cols-5 mb-4">
                <TabsTrigger value="prospects" onClick={() => setActiveTab('prospects')}>Prospects</TabsTrigger>
                <TabsTrigger value="customers" onClick={() => setActiveTab('customers')}>Customers</TabsTrigger>
                <TabsTrigger value="sales" onClick={() => setActiveTab('sales')}>Sales </TabsTrigger>
                <TabsTrigger value="logistics" onClick={() => setActiveTab('logistics')}>Logistics</TabsTrigger>
                <TabsTrigger value="other" onClick={() => setActiveTab('other')}>Other</TabsTrigger>
              </TabsList>
              
              <TabsContent value="prospects" className="h-[300px] overflow-y-auto">
                {categorizedPermissions.prospects && categorizedPermissions.prospects.length > 0 ? (
                    <ul className="space-y-2">
                    {categorizedPermissions.prospects.map((permission: PermType) => (
                      <li
                      key={permission.id}
                      className="text-gray-700 dark:text-gray-300 bg-secondary p-2"
                      >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                        {permission.permission} - Can {permission.permission_name}
                        </span>{" "}
                        <X
                        onClick={() => setShowDeleteModal(permission)}
                        size="30px"
                        className="text-destructive bg-destructive/20 dark:text-yellow-300 dark:bg-yellow-300/20 p-1 rounded cursor-pointer"
                        />
                      </div>
                      </li>
                    ))}
                    </ul>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    No prospect permissions found
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="customers" className="h-[300px] overflow-y-auto">
                {categorizedPermissions.customers && categorizedPermissions.customers.length > 0 ? (
                    <ul className="space-y-2">
                    {categorizedPermissions.customers.map((permission: PermType) => (
                      <li
                      key={permission.id}
                      className="text-gray-700 dark:text-gray-300 bg-secondary p-2"
                      >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                        {permission.permission} - Can {permission.permission_name}
                        </span>{" "}
                        <X
                        onClick={() => setShowDeleteModal(permission)}
                        size="30px"
                        className="text-destructive bg-destructive/20 dark:text-yellow-300 dark:bg-yellow-300/20 p-1 rounded cursor-pointer"
                        />
                      </div>
                      </li>
                    ))}
                    </ul>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    No customer permissions found
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="sales" className="h-[300px] overflow-y-auto">
                {categorizedPermissions.sales && categorizedPermissions.sales.length > 0 ? (
                    <ul className="space-y-2">
                    {categorizedPermissions.sales.map((permission: PermType) => (
                      <li
                      key={permission.id}
                      className="text-gray-700 dark:text-gray-300 bg-secondary p-2"
                      >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                        {permission.permission} - Can {permission.permission_name}
                        </span>{" "}
                        <X
                        onClick={() => setShowDeleteModal(permission)}
                        size="30px"
                        className="text-destructive bg-destructive/20 dark:text-yellow-300 dark:bg-yellow-300/20 p-1 rounded cursor-pointer"
                        />
                      </div>
                      </li>
                    ))}
                    </ul>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    No sales permissions found
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="logistics" className="h-[300px] overflow-y-auto">
                {categorizedPermissions.logistics && categorizedPermissions.logistics.length > 0 ? (
                    <ul className="space-y-2">
                    {categorizedPermissions.logistics.map((permission: PermType) => (
                      <li
                      key={permission.id}
                      className="text-gray-700 dark:text-gray-300 bg-secondary p-2"
                      >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                        {permission.permission} - Can {permission.permission_name}
                        </span>{" "}
                        <X
                        onClick={() => setShowDeleteModal(permission)}
                        size="30px"
                        className="text-destructive bg-destructive/20 dark:text-yellow-300 dark:bg-yellow-300/20 p-1 rounded cursor-pointer"
                        />
                      </div>
                      </li>
                    ))}
                    </ul>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    No logistics permissions found
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="other" className="h-[300px] overflow-y-auto">
                {categorizedPermissions.other && categorizedPermissions.other.length > 0 ? (
                    <ul className="space-y-2">
                    {categorizedPermissions.other.map((permission: PermType) => (
                      <li
                      key={permission.id}
                      className="text-gray-700 dark:text-gray-300 bg-secondary p-2"
                      >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                        {permission.permission} - Can {permission.permission_name}
                        </span>{" "}
                        <X
                        onClick={() => setShowDeleteModal(permission)}
                        size="30px"
                        className="text-destructive bg-destructive/20 dark:text-yellow-300 dark:bg-yellow-300/20 p-1 rounded cursor-pointer"
                        />
                      </div>
                      </li>
                    ))}
                    </ul>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    No other permissions found
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="gap-4 flex justify-center items-center py-8 text-destructive">
            <TriangleAlert /> No Permissions found
          </div>
        )}
      </div>
      <div
        className="space-y-4 bg-primary text-center rounded p-2 text-secondary cursor-pointer"
        onClick={() => setShowAddPermissionModal(true)}
      >
        Assign Permission
      </div>

      {/* deleteModal  */}
      {showDeleteModal && (
        <ConfirmModal
          isOpen={!!showDeleteModal}
          onOpenChange={() => setShowDeleteModal(null)}
          title="Confirm Permission Removal"
          variant="danger"
          message={
            <div className="space-y-2">
              <p>
                Are you sure you want to remove the user permission <br />{" "}
                <strong>{showDeleteModal.permission_name}</strong>?
              </p>

              <p className="text-sm font-medium text-red-600 dark:text-red-400">
                This action cannot be undone.
              </p>
            </div>
          }
          confirmText="Remove Permission"
          confirmVariant="destructive"
          cancelText="Cancel"
          onConfirm={() => handleRemovePermission(showDeleteModal.id)}
        />
      )}

      <BaseModal
        size="lg"
        isOpen={showAddPermissionModal}
        onOpenChange={() => setShowAddPermissionModal(false)}
        title="Assign User Permissions"
        description={`Assign ${user.fullnames} permissions`}
      >
        <div className="space-y-4">
          {/* Search input */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search permissions..."
              className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-700 dark:text-white"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button 
                className="absolute right-2 top-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                onClick={() => setSearchTerm('')}
              >
                <X size={16} />
              </button>
            )}
          </div>
          
          {loadingUserPerms ? (
            <div className="flex justify-center items-center">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : creating ? (
            <div className="flex justify-center items-center h-[50px]">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : (
            <Tabs defaultValue={activeTab} className="w-full">
              <TabsList className="grid grid-cols-5 mb-4">
                <TabsTrigger value="prospects">Prospects </TabsTrigger>
                <TabsTrigger value="customers">Customers</TabsTrigger>
                <TabsTrigger value="sales">Sales </TabsTrigger>
                <TabsTrigger value="logistics">Logistics</TabsTrigger>
                <TabsTrigger value="other">Other</TabsTrigger>
              </TabsList>
              
              <TabsContent value="prospects" className="max-h-[500px] overflow-y-auto">
                {categorizedAvailablePermissions.prospects && categorizedAvailablePermissions.prospects.length > 0 ? (
                  <div className="space-y-2">
                    {categorizedAvailablePermissions.prospects
                      .filter((perm: any) => 
                        searchTerm === '' || 
                        perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        perm.permission_id.toString().includes(searchTerm)
                      )
                      .map((perm: any) => (
                      <div
                        key={perm.permission_id}
                        className="flex items-center justify-start p-2 bg-secondary rounded hover:bg-secondary/80 cursor-pointer space-x-2"
                        onClick={() => handleAddPermission(perm.permission_id)}
                      >
                        {permissionChecker(perm.permission_id)}
                        <Badge variant="outline" className="text-sm">
                          {perm.permission_id}
                        </Badge>
                        <span className="font-medium">{perm.permission_name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    {searchTerm ? `No prospect permissions match "${searchTerm}"` : "No prospect permissions available"}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="customers" className="max-h-[500px] overflow-y-auto">
                {categorizedAvailablePermissions.customers && categorizedAvailablePermissions.customers.length > 0 ? (
                  <div className="space-y-2">
                    {categorizedAvailablePermissions.customers
                      .filter((perm: any) => 
                        searchTerm === '' || 
                        perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        perm.permission_id.toString().includes(searchTerm)
                      )
                      .map((perm: any) => (
                      <div
                        key={perm.permission_id}
                        className="flex items-center justify-start p-2 bg-secondary rounded hover:bg-secondary/80 cursor-pointer space-x-2"
                        onClick={() => handleAddPermission(perm.permission_id)}
                      >
                        {permissionChecker(perm.permission_id)}
                        <Badge variant="outline" className="text-sm">
                          {perm.permission_id}
                        </Badge>
                        <span className="font-medium">{perm.permission_name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    {searchTerm ? `No customer permissions match "${searchTerm}"` : "No customer permissions available"}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="sales" className="max-h-[500px] overflow-y-auto">
                {categorizedAvailablePermissions.sales && categorizedAvailablePermissions.sales.length > 0 ? (
                  <div className="space-y-2">
                    {categorizedAvailablePermissions.sales
                      .filter((perm: any) => 
                        searchTerm === '' || 
                        perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        perm.permission_id.toString().includes(searchTerm)
                      )
                      .map((perm: any) => (
                      <div
                        key={perm.permission_id}
                        className="flex items-center justify-start p-2 bg-secondary rounded hover:bg-secondary/80 cursor-pointer space-x-2"
                        onClick={() => handleAddPermission(perm.permission_id)}
                      >
                        {permissionChecker(perm.permission_id)}
                        <Badge variant="outline" className="text-sm">
                          {perm.permission_id}
                        </Badge>
                        <span className="font-medium">{perm.permission_name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    {searchTerm ? `No sales permissions match "${searchTerm}"` : "No sales permissions available"}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="logistics" className="max-h-[500px] overflow-y-auto">
                {categorizedAvailablePermissions.logistics && categorizedAvailablePermissions.logistics.length > 0 ? (
                  <div className="space-y-2">
                    {categorizedAvailablePermissions.logistics
                      .filter((perm: any) => 
                        searchTerm === '' || 
                        perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        perm.permission_id.toString().includes(searchTerm)
                      )
                      .map((perm: any) => (
                      <div
                        key={perm.permission_id}
                        className="flex items-center justify-start p-2 bg-secondary rounded hover:bg-secondary/80 cursor-pointer space-x-2"
                        onClick={() => handleAddPermission(perm.permission_id)}
                      >
                        {permissionChecker(perm.permission_id)}
                        <Badge variant="outline" className="text-sm">
                          {perm.permission_id}
                        </Badge>
                        <span className="font-medium">{perm.permission_name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    {searchTerm ? `No logistics permissions match "${searchTerm}"` : "No logistics permissions available"}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="other" className="max-h-[500px] overflow-y-auto">
                {categorizedAvailablePermissions.other && categorizedAvailablePermissions.other.length > 0 ? (
                  <div className="space-y-2">
                    {categorizedAvailablePermissions.other
                      .filter((perm: any) => 
                        searchTerm === '' || 
                        perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        perm.permission_id.toString().includes(searchTerm)
                      )
                      .map((perm: any) => (
                      <div
                        key={perm.permission_id}
                        className="flex items-center justify-start p-2 bg-secondary rounded hover:bg-secondary/80 cursor-pointer space-x-2"
                        onClick={() => handleAddPermission(perm.permission_id)}
                      >
                        {permissionChecker(perm.permission_id)}
                        <Badge variant="outline" className="text-sm">
                          {perm.permission_id}
                        </Badge>
                        <span className="font-medium">{perm.permission_name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center py-8 text-gray-500">
                    {searchTerm ? `No other permissions match "${searchTerm}"` : "No other permissions available"}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}
        </div>
      </BaseModal>
    </BaseModal>
  );
};

export default UserPermissionsModal;
