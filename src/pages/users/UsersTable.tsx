import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Eye, Edit, Users, Search, KeyRoundIcon, UserCheck, UserX, MoreHorizontal, ChevronLeft, ChevronRight } from "lucide-react";
import { OutlinedButton } from "@/components/custom/buttons/buttons";
import { Badge } from "@/components/custom/badges/badges";
import { useGetUsersQuery } from "@/redux/slices/user";
import { <PERSON><PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import EditUserModal from "./EditUserModal";
import ViewUserModal from "./ViewUser";
import UserPermissionsModal from "./UserPermissionsModal";

export interface User {
  id: number;
  employee_no: string;
  email: string;
  fullnames: string;
  first_name: string;
  last_name: string;
  department: string | null;
  designation: string | null;
  status: string | null;
  team: string | null;
  region: string | null;
  manager: string | null;
  phone_number: string | null;
  gender: string | null;
  created_date: string;
  category: string | null;
}

interface TableUser {
  id: number;
  employee_no: string;
  email: string;
  fullnames: string;
  department: string | null;
  status: string | null;
  team: string | null;
  region: string | null;
  manager: string | null;
  phone: string | null;
  gender: string | null;
  category: string | null;
}

export default function UsersTable() {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  // Fetch all users for statistics (without search to get true totals)
  const {
    data: allUsersList,
  } = useGetUsersQuery({
    page: 1,
    page_size: 1000, // Fetch all users for statistics
    ordering: "-id",
  });

  // Fetch paginated users for display with proper pagination
  const statusMap: Record<string, string> = {
    active: "Active",
    inactive: "Inactive",
    converted: "Converted",
  };

  const apiParams = {
    search: (searchTerm || "").trim().toLowerCase(), // send lowercase to backend
    page: currentPage,
    page_size: itemsPerPage, // Use proper pagination
    ordering: "-id", // Order by newest first
    ...(statusMap[activeTab] ? { status: statusMap[activeTab] } : {}), // Map tab values to exact API values
  };

  console.log('API Params:', apiParams); // Debug log

  const {
    data: usersList,
    isLoading,
    isError,
    refetch,
  } = useGetUsersQuery(apiParams);



  // Transform filtered users data for display
  const usersData: TableUser[] = (usersList as any)?.data?.results && Array.isArray((usersList as any).data.results)
    ? (usersList as any).data.results.map((user: User) => ({
        id: user.id,
        employee_no: user.employee_no || "N/A",
        email: user.email || "N/A",
        fullnames: user.fullnames || `${user.first_name} ${user.last_name}`.trim() || "N/A",
        department: user.department || "N/A",
        status: user.status || "Active", // Default to Active if null
        team: user.team || "N/A",
        region: user.region || "N/A",
        manager: user.manager || "N/A",
        phone: user.phone_number || "N/A",
        gender: user.gender || "N/A",
        category: user.category || "N/A",
      }))
    : [];

  // Handle tab change and reset pagination
  const handleTabChange = (tabValue: string) => {
    setActiveTab(tabValue);
    setCurrentPage(1); // Reset to first page when changing tabs
    console.log('Tab changed to:', tabValue); // Debug log
  };

  // Pagination calculations
  const totalPages = Math.ceil(((usersList as any)?.data?.total_data || 0) / itemsPerPage);
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, (usersList as any)?.data?.total_data || 0);

  // Actions dropdown component
  const ActionsDropdown = ({ user }: { user: TableUser }) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="h-8 w-8 p-0 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-center"
          type="button"
        >
          <MoreHorizontal className="h-4 w-4 text-gray-600 dark:text-gray-400" />
          <span className="sr-only">Open menu</span>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={(e) => {
            e.preventDefault();
            setViewUser(user);
          }}
          className="cursor-pointer"
        >
          <Eye className="mr-2 h-4 w-4 text-blue-600" />
          View Details
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.preventDefault();
            setEditUser(user);
          }}
          className="cursor-pointer"
        >
          <Edit className="mr-2 h-4 w-4 text-green-600" />
          Edit User
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.preventDefault();
            setUserPermissionsData(user);
          }}
          className="cursor-pointer"
        >
          <KeyRoundIcon className="mr-2 h-4 w-4 text-purple-600" />
          Permissions
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // Modal states
  const [viewUser, setViewUser] = useState<TableUser | null>(null);
  const [editUser, setEditUser] = useState<TableUser | null>(null);
  const [userPermissionsData, setUserPermissionsData] =
    useState<TableUser | null>(null);





  return (
    <Screen>
      <div className="space-y-4">
        {/* Minimalistic Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Users Management
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Manage user accounts, permissions, and access control
              </p>
            </div>

            {/* Quick Action Indicators */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <Users className="w-4 h-4" />
                <span>Directory</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <Search className="w-4 h-4" />
                <span>Search</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <KeyRoundIcon className="w-4 h-4" />
                <span>Permissions</span>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Main Table Card with Tabs */}
        <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between gap-3">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white tracking-tight">
                User Directory
              </h2>
              <div className="relative w-full max-w-xs">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1); // Reset to first page when searching
                  }}
                  className="pl-10 pr-8 h-9 focus-visible:ring-1"
                />
                {isLoading && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2">
                    <div className="h-4 w-4 border-2 border-gray-300 dark:border-gray-600 border-t-blue-600 dark:border-t-blue-500 rounded-full animate-spin" />
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="p-0">
            <Tabs
              value={activeTab}
              onValueChange={handleTabChange}
              className="w-full"
            >
              <TabsList className="bg-gray-50 dark:bg-gray-700 p-1 rounded-none border-b border-gray-200 dark:border-gray-600 !flex !flex-nowrap justify-start h-auto p-0 w-full">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600 data-[state=active]:shadow-sm transition-all duration-200 text-sm px-4 py-2 !flex-none !rounded-sm !rounded-b-none cursor-pointer border border-transparent data-[state=active]:border-t-blue-500 data-[state=active]:border-x-blue-500 data-[state=active]:translate-y-0.5"
                >
                  <Users className="w-4 h-4 mr-1 hidden sm:block" />
                  All Employees
                </TabsTrigger>
                <TabsTrigger
                  value="active"
                  className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600 data-[state=active]:shadow-sm transition-all duration-200 text-sm px-4 py-2 !flex-none !rounded-sm !rounded-b-none cursor-pointer border border-transparent data-[state=active]:border-t-blue-500 data-[state=active]:border-x-blue-500 data-[state=active]:translate-y-0.5"
                >
                  <UserCheck className="w-4 h-4 mr-1 hidden sm:block" />
                  Active Users
                </TabsTrigger>
                <TabsTrigger
                  value="inactive"
                  className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600 data-[state=active]:shadow-sm transition-all duration-200 text-sm px-4 py-2 !flex-none !rounded-sm !rounded-b-none cursor-pointer border border-transparent data-[state=active]:border-t-blue-500 data-[state=active]:border-x-blue-500 data-[state=active]:translate-y-0.5"
                >
                  <UserX className="w-4 h-4 mr-1 hidden sm:block" />
                  Inactive Users
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="transform transition-all duration-800 origin-bottom animate-in fade-in slide-in-from-bottom-8">
                    <div className="p-4">
                      {isLoading ? (
                        <div className="flex items-center justify-center py-12">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-4 text-gray-600 dark:text-gray-400">
                              Loading users...
                            </p>
                          </div>
                        </div>
                      ) : isError ? (
                        <div className="text-center py-12">
                          <div className="text-red-500 text-lg font-medium">
                            Failed to load users
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Please try again later.
                          </p>
                          <OutlinedButton onClick={() => refetch()} className="mt-4">
                            Retry
                          </OutlinedButton>
                        </div>
                      ) : usersData.length === 0 ? (
                        <div className="text-center py-12">
                          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <div className="text-gray-500 text-lg font-medium">
                            No users found
                          </div>
                          <p className="text-gray-400 mt-2">
                            Users will appear here once they are added to the system
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-4">


                          <div className="rounded-md border">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Employee No</TableHead>
                                  <TableHead>Full Names</TableHead>
                                  <TableHead>Email</TableHead>
                                  <TableHead>Department</TableHead>
                                  <TableHead>Status</TableHead>
                                  <TableHead className="w-[100px]">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {usersData.map((user) => (
                                  <TableRow key={user.id}>
                                    <TableCell className="font-medium">
                                      {user.employee_no}
                                    </TableCell>
                                    <TableCell className="font-medium">
                                      {user.fullnames}
                                    </TableCell>
                                    <TableCell className="text-gray-600 dark:text-gray-400 text-sm">
                                      {user.email}
                                    </TableCell>
                                    <TableCell>
                                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-medium">
                                        {user.department}
                                      </span>
                                    </TableCell>
                                    <TableCell>
                                      <Badge
                                        variant={user.status?.toLowerCase() === "active" ? "default" : "destructive"}
                                        className={user.status?.toLowerCase() === "active"
                                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                                        }
                                      >
                                        {user.status || "Active"}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>
                                      <ActionsDropdown user={user} />
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Pagination */}
                          <div className="flex items-center justify-between px-2">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                Showing {startItem} to {endItem} of {(usersList as any)?.data?.total_data || 0} results
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Select
                                value={itemsPerPage.toString()}
                                onValueChange={(value) => {
                                  setItemsPerPage(Number(value));
                                  setCurrentPage(1);
                                }}
                              >
                                <SelectTrigger className="w-[70px]">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="10">10</SelectItem>
                                  <SelectItem value="20">20</SelectItem>
                                  <SelectItem value="50">50</SelectItem>
                                  <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                disabled={currentPage <= 1}
                              >
                                Previous
                              </Button>
                              <span className="text-sm text-gray-700 dark:text-gray-300">
                                Page {currentPage} of {totalPages}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                disabled={currentPage >= totalPages}
                              >
                                Next
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

              <TabsContent value="active" className="transform transition-all duration-800 origin-bottom animate-in fade-in slide-in-from-bottom-8">
                    <div className="p-4">
                      {isLoading ? (
                        <div className="flex items-center justify-center py-12">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
                            <p className="mt-4 text-gray-600 dark:text-gray-400">
                              Loading active users...
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {/* Search Input inside table */}
                          <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 border-b">
                            <div className="relative flex-1 max-w-md">
                              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                              <Input
                                placeholder="Search active users..."
                                value={searchTerm}
                                onChange={(e) => {
                                  setSearchTerm(e.target.value);
                                  setCurrentPage(1); // Reset to first page when searching
                                }}
                                className="pl-10"
                              />
                            </div>
                          </div>

                          <div className="rounded-md border">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Employee No</TableHead>
                                  <TableHead>Full Names</TableHead>
                                  <TableHead>Email</TableHead>
                                  <TableHead>Department</TableHead>
                                  <TableHead>Status</TableHead>
                                  <TableHead className="w-[100px]">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {usersData.map((user) => (
                                  <TableRow key={user.id}>
                                    <TableCell className="font-medium">
                                      {user.employee_no}
                                    </TableCell>
                                    <TableCell className="font-medium">
                                      {user.fullnames}
                                    </TableCell>
                                    <TableCell className="text-gray-600 dark:text-gray-400 text-sm">
                                      {user.email}
                                    </TableCell>
                                    <TableCell>
                                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-medium">
                                        {user.department}
                                      </span>
                                    </TableCell>
                                    <TableCell>
                                      <Badge
                                        variant={user.status?.toLowerCase() === "active" ? "default" : "destructive"}
                                        className={user.status?.toLowerCase() === "active"
                                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                                        }
                                      >
                                        {user.status || "Active"}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>
                                      <ActionsDropdown user={user} />
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Pagination */}
                          <div className="flex items-center justify-between px-2">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                Showing {startItem} to {endItem} of {(usersList as any)?.data?.total_data || 0} results
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Select
                                value={itemsPerPage.toString()}
                                onValueChange={(value) => {
                                  setItemsPerPage(Number(value));
                                  setCurrentPage(1);
                                }}
                              >
                                <SelectTrigger className="w-[70px]">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="10">10</SelectItem>
                                  <SelectItem value="20">20</SelectItem>
                                  <SelectItem value="50">50</SelectItem>
                                  <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                disabled={currentPage <= 1}
                              >
                                Previous
                              </Button>
                              <span className="text-sm text-gray-700 dark:text-gray-300">
                                Page {currentPage} of {totalPages}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                disabled={currentPage >= totalPages}
                              >
                                Next
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

              <TabsContent value="inactive" className="transform transition-all duration-800 origin-bottom animate-in fade-in slide-in-from-bottom-8">
                    <div className="p-4">
                      {isLoading ? (
                        <div className="flex items-center justify-center py-12">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
                            <p className="mt-4 text-gray-600 dark:text-gray-400">
                              Loading inactive users...
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {/* Search Input inside table */}
                          <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 border-b">
                            <div className="relative flex-1 max-w-md">
                              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                              <Input
                                placeholder="Search inactive users..."
                                value={searchTerm}
                                onChange={(e) => {
                                  setSearchTerm(e.target.value);
                                  setCurrentPage(1); // Reset to first page when searching
                                }}
                                className="pl-10"
                              />
                            </div>
                          </div>

                          <div className="rounded-md border">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Employee No</TableHead>
                                  <TableHead>Full Names</TableHead>
                                  <TableHead>Email</TableHead>
                                  <TableHead>Department</TableHead>
                                  <TableHead>Status</TableHead>
                                  <TableHead className="w-[100px]">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {usersData.map((user) => (
                                  <TableRow key={user.id}>
                                    <TableCell className="font-medium">
                                      {user.employee_no}
                                    </TableCell>
                                    <TableCell className="font-medium">
                                      {user.fullnames}
                                    </TableCell>
                                    <TableCell className="text-gray-600 dark:text-gray-400 text-sm">
                                      {user.email}
                                    </TableCell>
                                    <TableCell>
                                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-medium">
                                        {user.department}
                                      </span>
                                    </TableCell>
                                    <TableCell>
                                      <Badge
                                        variant={user.status?.toLowerCase() === "active" ? "default" : "destructive"}
                                        className={user.status?.toLowerCase() === "active"
                                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                                        }
                                      >
                                        {user.status || "Active"}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>
                                      <ActionsDropdown user={user} />
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Pagination */}
                          <div className="flex items-center justify-between px-2">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                Showing {startItem} to {endItem} of {(usersList as any)?.data?.total_data || 0} results
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Select
                                value={itemsPerPage.toString()}
                                onValueChange={(value) => {
                                  setItemsPerPage(Number(value));
                                  setCurrentPage(1);
                                }}
                              >
                                <SelectTrigger className="w-[70px]">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="10">10</SelectItem>
                                  <SelectItem value="20">20</SelectItem>
                                  <SelectItem value="50">50</SelectItem>
                                  <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                disabled={currentPage <= 1}
                              >
                                Previous
                              </Button>
                              <span className="text-sm text-gray-700 dark:text-gray-300">
                                Page {currentPage} of {totalPages}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                disabled={currentPage >= totalPages}
                              >
                                Next
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Modals */}
        <ViewUserModal
          isOpen={!!viewUser}
          onOpenChange={() => setViewUser(null)}
          user={viewUser}
        />
        <EditUserModal
          isOpen={!!editUser}
          onOpenChange={() => setEditUser(null)}
          user={editUser}
          onSave={() => {
            refetch(); // Refresh the users list
            setEditUser(null);
          }}
        />
        {userPermissionsData && (
          <UserPermissionsModal
            isOpen={!!userPermissionsData}
            onClose={() => setUserPermissionsData(null)}
            user={userPermissionsData}
          />
        )}
      </div>
    </Screen>
  );
}
