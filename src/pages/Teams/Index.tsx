import { useState } from "react";
import { motion } from "framer-motion";
import { KeyRound, Search, ChevronLeft, ChevronRight } from "lucide-react";

import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { useGetDepartmentsQuery } from "@/redux/slices/user";
import DepartmentPermissionsModal from "../Departments/DepartmentPermissionsModal";

interface DepartmentTypes {
  dp_id: number;
  dp_name: string;
  dep_head_code?: string;
  dep_head_name?: string;
  inactive?: boolean;
}

const Index = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [teamPermissionsData, setTeamPermissionsData] =
    useState<DepartmentTypes | null>(null);

  const apiParams = {
    search: (searchTerm || "").trim().toLowerCase(),
    page: currentPage,
    page_size: itemsPerPage,
  } as const;

  const { data: departmentsResp, isLoading, isError, refetch } = useGetDepartmentsQuery(apiParams);

  // Slice currently returns an array (transformResponse maps to data.results). If metadata is ever returned, prefer it.
  const departments: DepartmentTypes[] = Array.isArray(departmentsResp)
    ? (departmentsResp as DepartmentTypes[])
    : (departmentsResp as any)?.data?.results ?? [];

  const totalItems: number = (departmentsResp as any)?.data?.total_data ?? departments.length ?? 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <Screen>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between mb-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">Departments</h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">View department heads and manage permissions</p>
          </div>

          {/* Search */}
          <div className="relative w-full sm:w-80">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search departments..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
              className="pl-10 pr-8 h-9 focus-visible:ring-1"
            />
            {isLoading && (
              <div className="absolute right-2 top-1/2 -translate-y-1/2">
                <div className="h-4 w-4 border-2 border-gray-300 dark:border-gray-600 border-t-blue-600 rounded-full animate-spin" />
              </div>
            )}
          </div>
        </div>

        {/* Card */}
        <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4 sm:p-5">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <SpinnerTemp type="spinner-double" size="md" />
              </div>
            ) : isError ? (
              <div className="text-center py-12">
                <div className="text-red-500 text-lg font-medium">Failed to load departments</div>
                <p className="text-gray-600 dark:text-gray-400 mt-2">Please try again later.</p>
                <Button onClick={() => refetch()} className="mt-4" variant="outline">Retry</Button>
              </div>
            ) : departments.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg font-medium">No departments found</div>
                <p className="text-gray-400 mt-2">Try adjusting your search.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table */}
                <div className="rounded-md border border-gray-200 dark:border-gray-700">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Department Name</TableHead>
                        <TableHead>Department Head</TableHead>
                        <TableHead className="w-[90px] text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {departments.map((department) => (
                        <TableRow key={department.dp_id}>
                          <TableCell>{department.dp_name || "N/A"}</TableCell>
                          <TableCell>{department.dep_head_name || "N/A"}</TableCell>
                          <TableCell className="text-center">
                            <PrimaryButton
                              variant="outline"
                              size="sm"
                              onClick={() => setTeamPermissionsData(department)}
                              className="bg-white text-blue-600 border-blue-300 hover:bg-blue-50 inline-flex items-center"
                            >
                              <KeyRound className="h-4 w-4" />
                            </PrimaryButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Showing {startItem}-{endItem} of {totalItems}
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Rows per page</span>
                      <Select
                        value={String(itemsPerPage)}
                        onValueChange={(val) => {
                          const n = Number(val);
                          setItemsPerPage(n);
                          setCurrentPage(1);
                        }}
                      >
                        <SelectTrigger className="h-8 w-[84px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {[10, 20, 30, 50].map((n) => (
                            <SelectItem key={n} value={String(n)}>{n}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8"
                        disabled={currentPage <= 1}
                        onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <div className="text-xs w-16 text-center">
                        {currentPage} / {totalPages}
                      </div>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8"
                        disabled={currentPage >= totalPages}
                        onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {teamPermissionsData && (
          <DepartmentPermissionsModal
            isOpen={!!teamPermissionsData}
            onClose={() => setTeamPermissionsData(null)}
            department={teamPermissionsData}
          />
        )}
      </motion.div>
    </Screen>
  );
};

export default Index;
