import { Screen } from "@/app-components/layout/screen";
import {
  OutlinedButton,
  PrimaryButton,
} from "@/components/custom/buttons/buttons";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetReserveDiasporaPlotQuery } from "@/redux/slices/projects";
import { formatDate } from "@/utils/formatDate";
import { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import DiaporaReservationModal from "../Projects/modals/DiaporaReservationModal";
import { useAuthHook } from "@/utils/useAuthHook";

type Props = {};
interface ReservedTypes {
  id: number;
  trip: any;
  marketer: string;
  marketer_name: string;
  deadline: string;
  created_at: string;
}

const DiasporaReservations = ({}: Props) => {
  const { user_details } = useAuthHook();
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState("");
  const [selectedReserve, setSelectedReserve] = useState<ReservedTypes | null>(
    null
  );
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const { data: reserved, isLoading: reserved_loading } =
    useGetReserveDiasporaPlotQuery({
      marketer: user_details?.employee_no,
    });

  const handleEditReserve = (rsvd: ReservedTypes) => {
    setSelectedReserve(rsvd);
    setEditModalOpen(true);
  };

  const columns: ColumnDef<ReservedTypes>[] = [
    {
      // accessorKey: "id",
      header: "#",
      cell: ({ row }) => (
        <span className="font-medium">{parseInt(row.id) + 1}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "plots",
      header: "Plots",
      cell: (info) => {
        const plots = info.getValue() as string;
        return <span className="font-medium">{plots}</span>;
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "trip",
      header: "Trip",
      cell: ({ row }) => {
        return (
          <span className="font-medium">{row.original.trip.trip_name}</span>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "marketer_name",
      header: "Reserved By",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_at",
      header: "Reserved On",
      cell: (info) => formatDate(info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "deadline",
      header: "Expires On",
      cell: (info) => formatDate(info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },

    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        // const prospect = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            <PrimaryButton
              variant="outline"
              size="sm"
              onClick={() => handleEditReserve(row?.original)}
              className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
            >
              <span>Edit</span>
            </PrimaryButton>

            {/* <PrimaryButton
                variant="primary"
                size="sm"
                onClick={() => handleReallocateProspect(prospect.id)}
                className="bg-white !text-green-500 border border-green-300 hover:bg-green-300 hover:!text-white flex items-center space-x-1"
              >
                <span>Reallocate</span>
              </PrimaryButton> */}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  function SearchComponent() {
    return (
      <input
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        placeholder="Search prospects..."
      />
    );
  }

  return (
    <Screen>
      {reserved_loading ? (
        <SpinnerTemp type="spinner-double" />
      ) : (
        <div className="w-full h-full px-2 py-4">
          <div className="flex flex-col gap-4  w-full h-full ">
            <div className="flex flex-wrap justify-between items-center mb-4">
              <h1 className="text-3xl font-bold text-gray-800">
                Diaspora Trip Reserved plots
              </h1>
              <OutlinedButton
                variant="primary"
                className="bg-white border-blue-600 text-blue-600 hover:bg-blue-50 font-semibold py-2 px-4 rounded-md transition-colors"
                onClick={() => setIsAddModalOpen(true)}
              >
                Reserve Plot
              </OutlinedButton>
            </div>
            <DataTable
              containerClassName="bg-slate-50 dark:bg-inherit !overflow-x-auto"
              columns={columns}
              data={reserved?.data?.results || []}
              searchInput={<SearchComponent />}
              enableSelectColumn={true}
              enableToolbar={true}
              enableExportToExcel={true}
              enablePrintPdf={true}
              enablePagination={true}
              tBodyCellsClassName=""
              tBodyTrClassName="!items-start "
            />
          </div>

          {isAddModalOpen && (
            <DiaporaReservationModal
              openModal={isAddModalOpen}
              setOpenModal={setIsAddModalOpen}
              title="Diaspora Trip Plot Reservations"
            />
          )}
          {editModalOpen && selectedReserve && (
            <DiaporaReservationModal
              updateData={selectedReserve}
              openModal={editModalOpen}
              setOpenModal={setEditModalOpen}
              title="Update Diaspora Trip Plot Reservations"
            />
          )}
        </div>
      )}
    </Screen>
  );
};

export default DiasporaReservations;
