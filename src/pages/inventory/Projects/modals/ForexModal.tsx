import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";

import { useEffect, useState } from "react";
import { toast } from "sonner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import {
  useGetForexQuery,
  useUpdateForexMutation,
} from "@/redux/slices/projects";

type Props = {
  openModal: boolean;
  setOpenModal: (e: boolean) => void;
};

const ForexModal = ({ openModal, setOpenModal }: Props) => {
  const [updateForex, { isLoading: updating }] = useUpdateForexMutation();
  const { data: forexData, isLoading: loading } = useGetForexQuery({});

  useEffect(() => {
    if (!loading && forexData?.results.length > 0) {
      setusd(forexData?.results[0]?.USD || "");
      setgbp(forexData?.results[0]?.GBP || "");
      seteuro(forexData?.results[0]?.EURO || "");
    }
  }, [forexData, loading]);

  const [usd, setusd] = useState<string>("");
  const [gbp, setgbp] = useState<string>("");
  const [euro, seteuro] = useState<string>("");

  // submit handler.
  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!usd || !gbp || !euro) {
      toast.error("Fill all required fields");
      return;
    }

    const formData = {
      id: forexData?.results[0]?.id,
      USD: usd,
      GBP: gbp,
      EURO: euro,
    };

    try {
      const res = await updateForex(formData).unwrap();
      if (res) {
        toast.success("Updated created successfully");
        setOpenModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`${error?.error}`);
      }
      return;
    }
  }

  return (
    <BaseModal
      isOpen={openModal}
      onOpenChange={setOpenModal}
      size="lg"
      title="Forex Rates Management"
      description="Manage the forex rates for USD, GBP, and EURO."
      // footer={<Button onClick={() => setSbModal(false)}>Close</Button>}
    >
      <div className="py-4">
        {loading ? (
          <div className="flex justify-center items-center">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : (
          <form onSubmit={onSubmit} className="space-y-4">
            <div className="grid grid-cols-4 gap-4 items-center">
              <label className="text-xl"> 🇺🇸 USD</label>
              <div className="col-span-3 space-y-2">
                <input
                  className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  type="text"
                  name="usd"
                  value={usd}
                  onChange={(e) => setusd(e.target.value)}
                  placeholder="Enter USD rate"
                  required
                />
              </div>
              <label className="text-xl">🇬🇧 GBP</label>
              <div className="col-span-3 space-y-2">
                <input
                  className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  type="text"
                  name="gbp"
                  value={gbp}
                  onChange={(e) => setgbp(e.target.value)}
                  placeholder="Enter GBP rate"
                  required
                />
              </div>
              <label className="text-xl">🇩🇪 EURO</label>
              <div className="col-span-3 space-y-2">
                <input
                  className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  type="text"
                  name="euro"
                  value={euro}
                  onChange={(e) => seteuro(e.target.value)}
                  placeholder="Enter EURO rate"
                  required
                />
              </div>
            </div>
            <div className="w-full flex justify-end">
              {updating ? (
                <SpinnerTemp type="spinner-double" size="sm" />
              ) : (
                <Button type="submit" className="justify-end">
                  Submit
                </Button>
              )}
            </div>
          </form>
        )}
      </div>
    </BaseModal>
  );
};

export default ForexModal;
