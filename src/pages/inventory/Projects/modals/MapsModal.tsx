import BaseModal from "@/components/custom/modals/BaseModal";
import { toast } from "sonner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { projectTypes } from "@/types/project";
import { useUpdateProjectMutation } from "@/redux/slices/projects";
import { DataTable } from "@/components/custom/tables/Table1";
import { Link } from "react-router";
import { ColumnDef } from "@tanstack/react-table";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Edit } from "lucide-react";
import { useRef, useState } from "react";

type Props = {
  openModal: boolean;
  setOpenModal: (e: boolean) => void;
  searchValue: string;
  setSearchValue: (e: string) => void;
  currentPage: number;
  setCurrentPage: (e: number) => void;
  itemsPerPage: number;
  setItemsPerPage: (e: number) => void;
  data: projectTypes[] | null;
};

const MapsModal = ({
  openModal,
  setOpenModal,
  searchValue,
  setSearchValue,
  data,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  setItemsPerPage,
}: Props) => {
  const [updateProject, { isLoading: updating }] = useUpdateProjectMutation();
  const inputRefs = useRef<{ [key: number]: HTMLInputElement | null }>({});

  const handleInput = (projectInputValue: string, index: number) => {
    const [linkValue, setLinkValue] = useState(projectInputValue);

    return (
      <input
        type="text"
        ref={(el) => (inputRefs.current[index] = el)}
        value={linkValue}
        name="link"
        onChange={(e) => setLinkValue(e.target.value)}
        className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
      />
    );
  };

  // submit handler.
  async function handleUpdate(project: projectTypes, index: number) {
    const link = inputRefs.current[index]?.value;
    const formData = {
      projectId: project?.projectId,
      link,
    };

    try {
      const res = await updateProject(formData).unwrap();
      if (res) {
        toast.success("Updated successfully");
        setOpenModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`${error?.error}`);
      }
      return;
    }
  }

  const columns: ColumnDef<projectTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <Link
          to={`/projects/${info?.row?.original?.projectId}`}
          title="View Project"
        >
          <span className="font-medium underline capitalize text-blue-400">
            {info.getValue() as string}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "link",
      header: "Link",
      cell: ({ row }) => handleInput(row?.original?.link, row?.index),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const project = row?.original;
        return (
          <div className="flex space-x-2 justify-start">
            <PrimaryButton
              variant="outline"
              size="sm"
              onClick={() => handleUpdate(project, row.index)}
              className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
            >
              <span title="Edit">
                <Edit />
              </span>
            </PrimaryButton>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <BaseModal
      isOpen={openModal}
      onOpenChange={setOpenModal}
      size="2xl"
      title="Project Maps Management"
      description="Manage project maps by updating their links."
      // footer={<Button onClick={() => setSbModal(false)}>Close</Button>}
    >
      <div className="py-4">
        <DataTable<projectTypes>
          data={data || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          searchInput={
            <input
              value={searchValue}
              name="searchValue"
              type="search"
              onChange={(e) => setSearchValue(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search prospects..."
            />
          }
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={data?.length || 0}
        />
      </div>
    </BaseModal>
  );
};

export default MapsModal;
