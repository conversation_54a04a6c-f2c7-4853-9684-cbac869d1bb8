import { Screen } from "@/app-components/layout/screen";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import BookingApprovalCard from "./BookingApprovalCard";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { stat } from "fs";
import { useState } from "react";
import { useGetPlotBookingsQuery } from "@/redux/slices/projects";
import { useAuthHook } from "@/utils/useAuthHook";

type Props = {};

interface BookingType {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  lead: string;
  lead_name: string;
  marketer: string;
  marketer_name: string;
  customer: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
  office: string;
  deadline: string;
}

const index = ({}: Props) => {
  const [status, setStatus] = useState<string>("OPEN");
  const { user_details } = useAuthHook();
  const current_user_office = user_details?.office;
  console.log("current_user_office", user_details, current_user_office);

  const {
    data: bookingsData,
    isLoading: loading,
    isFetching,
  } = useGetPlotBookingsQuery({
    booking_type: "SPECIAL",
    office: current_user_office?.includes("HQ") ? "HQ" : current_user_office,
    status,
  });

  const handleSwithTab = (state: string) => {
    setStatus(state);
  };

  return (
    <Screen>
      <div className="min-h-screen space-y-5">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold ">Special Plot Bookings</h1>
        </div>
        <Tabs defaultValue="OPEN" className="w-full">
          <TabsList>
            {/* <TabsTrigger value="ALL" onClick={() => handleSwithTab("")}>
              All Bookings
            </TabsTrigger> */}
            <TabsTrigger value="OPEN" onClick={() => handleSwithTab("OPEN")}>
              Open Bookings
            </TabsTrigger>
            <TabsTrigger
              value="WAITING"
              onClick={() => handleSwithTab("WAITING")}
            >
              Waiting Bookings
            </TabsTrigger>
            <TabsTrigger value="TIMED" onClick={() => handleSwithTab("TIMED")}>
              Timed Out Bookings
            </TabsTrigger>
            <TabsTrigger value="DONE" onClick={() => handleSwithTab("DONE")}>
              Completed Bookings
            </TabsTrigger>
            {/* <TabsTrigger value="REJECTED">Rejected Cases</TabsTrigger> */}
          </TabsList>

          {/* <TabsContent value="ALL" className="my-10">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3">
              {loading || isFetching ? (
                <div className="flex justify-center items-center w-full h-full">
                  <SpinnerTemp type="spinner-double" size="md" />
                </div>
              ) : bookingsData?.data?.results?.length < 1 ? (
                <p>No Data Found</p>
              ) : (
                bookingsData?.data?.results.map((booking: BookingType) => (
                  <BookingApprovalCard
                    key={booking.booking_id}
                    rowData={booking}
                  />
                ))
              )}
            </div>
          </TabsContent> */}
          <TabsContent value="OPEN" className="my-10">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3">
              {loading || isFetching ? (
                <div className="flex justify-center items-center w-full h-full">
                  <SpinnerTemp type="spinner-double" size="md" />
                </div>
              ) : bookingsData?.data?.results?.length < 1 ? (
                <p>No Data Found</p>
              ) : (
                bookingsData?.data?.results.map((booking: BookingType) => (
                  <BookingApprovalCard
                    key={booking.booking_id}
                    rowData={booking}
                  />
                ))
              )}
            </div>
          </TabsContent>
          <TabsContent value="WAITING" className="my-10">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3">
              {loading || isFetching ? (
                <div className="flex justify-center items-center w-full h-full">
                  <SpinnerTemp type="spinner-double" size="md" />
                </div>
              ) : bookingsData?.data?.results?.length < 1 ? (
                <p>No Data Found</p>
              ) : (
                bookingsData?.data?.results.map((booking: BookingType) => (
                  <BookingApprovalCard
                    key={booking.booking_id}
                    rowData={booking}
                  />
                ))
              )}
            </div>
          </TabsContent>
          <TabsContent value="TIMED" className="my-10">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3">
              {loading || isFetching ? (
                <div className="flex justify-center items-center w-full h-full">
                  <SpinnerTemp type="spinner-double" size="md" />
                </div>
              ) : bookingsData?.data?.results?.length < 1 ? (
                <p>No Data Found</p>
              ) : (
                bookingsData?.data?.results.map((booking: BookingType) => (
                  <BookingApprovalCard
                    key={booking.booking_id}
                    rowData={booking}
                  />
                ))
              )}
            </div>
          </TabsContent>
          <TabsContent value="DONE" className="my-10">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3">
              {loading || isFetching ? (
                <div className="flex justify-center items-center w-full h-full">
                  <SpinnerTemp type="spinner-double" size="md" />
                </div>
              ) : bookingsData?.data?.results?.length < 1 ? (
                <p>No Data Found</p>
              ) : (
                bookingsData?.data?.results.map((booking: BookingType) => (
                  <BookingApprovalCard
                    key={booking.booking_id}
                    rowData={booking}
                  />
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};

export default index;
