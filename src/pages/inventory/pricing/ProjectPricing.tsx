import { Screen } from "@/app-components/layout/screen"
import BaseModal from "@/components/custom/modals/BaseModal";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

interface Booking {
    project: string;
    paymentModel: string;
    size: string;
    sizeCategory: string;
    plotType: string;
    view: string;
    deposit: string;
    monthlyInterest: string;
    '2': string;
    '3': string;
    '4': string;
    '5': string;
    '6': string;
    '7': string;
    '8': string;
    '9': string;
    '10': string;
    '11': string;
}

const data: Booking[] = [
    {
        project: 'Abundance Gardens',
        paymentModel: 'Standard',
        size: '1/8th Acre',
        sizeCategory: 'A',
        plotType: 'Residential',
        view: '',
        deposit: '500,000.00',
        monthlyInterest: '4,950.00',
        '2': '',
        '3': '0.00',
        '4': '',
        '5': '',
        '6': '0.00',
        '7': '',
        '8': '',
        '9': '',
        '10': '',
        '11': '',
    },
    {
        project: 'Achievers Paradise',
        paymentModel: 'Prorated',
        size: '1/8th Acre',
        sizeCategory: 'A',
        plotType: 'Residential',
        view: '',
        deposit: '700,000.00',
        monthlyInterest: '',
        '2': '',
        '3': '40,000.00',
        '4': '',
        '5': '',
        '6': '100,000.00',
        '7': '',
        '8': '',
        '9': '',
        '10': '',
        '11': '',
    }
]

const columns: ColumnDef<Booking>[] = [
    {
        accessorKey: 'actions',
        header: 'Actions',
        cell: info => {
            const rowData = info.row.original
            return (<A data={rowData} />);
        },
        enableColumnFilter: false,
        enableSorting: false,
    },
    {
        accessorKey: 'project',
        header: 'Project',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'paymentModel',
        header: 'Payment Model',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'size',
        header: 'Size',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'sizeCategory',
        header: 'Size Category',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'plotType',
        header: 'Plot Type',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'view',
        header: 'View',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'deposit',
        header: 'deposit',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'monthlyInterest',
        header: 'Monthly Interest',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
]

const ProjectPricing = () => {
    const [universalSearchValue, setuniversalSearchValue] = useState('')


    return (
        <Screen>
            <div className=" !m-0 min-h-screen w-full border rounded">
                <div className=' px-3 py-4   border-b'>
                    <h2 className=' font-bold text-lg'>Payment plan</h2>
                </div>
                <div className=''>
                    <DataTable<Booking>
                        data={data}
                        columns={columns}
                        title="Project Pricing"
                        enableSelectColumn={false}
                        enableColumnFilters={true}
                        enableSorting={true}
                        enableToolbar={true}
                        enableFullScreenToggle={true}
                        enableColumnControl={true}
                        tableClassName='border-none'
                        containerClassName=' py-2 '
                        tHeadClassName='border-t'
                        tHeadCellsClassName="border-r px-2"
                        tBodyCellsClassName="text-xs border-r px-2"
                        searchInput={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
                    />
                </div>
            </div>
        </Screen>
    )
}

export default ProjectPricing

interface SearchComponentProps {
    universalSearchValue: string,
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
    return <input
        value={universalSearchValue}
        onChange={e => setuniversalSearchValue(e.target.value)}
        className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
        placeholder="Search..."
    />
}

interface Props {
    data: Booking
}

function A({ data }: Props) {
    const [isModelXOpen, setIsModelXOpen] = React.useState(false);

    const formSchema = z.object({
        deposit: z.string().min(1, { message: 'Deposit is required' }).trim(),
        monthlyInterest: z.string().min(1, { message: 'Monthly interest is required' }).trim(),
        '2': z.string().trim().optional(),
        '3': z.string().trim().optional(),
        '4': z.string().trim().optional(),
        '5': z.string().trim().optional(),
        '6': z.string().trim().optional(),
        '7': z.string().trim().optional(),
        '8': z.string().trim().optional(),
        '9': z.string().trim().optional(),
        '10': z.string().trim().optional(),
        '11': z.string().trim().optional(),
    })

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            deposit: '',
            monthlyInterest: '',
            '2': '',
            '3': '',
            '4': '',
            '5': '',
            '6': '',
            '7': '',
            '8': '',
            '9': '',
            '10': '',
            '11': '',
        },
    })

    function onSubmit(values: z.infer<typeof formSchema>) {
        console.log(values)
    }

    return <>
        <Button className="bg-primary text-xs" size='sm' onClick={() => setIsModelXOpen(true)} >Edit</Button>

        <BaseModal className="py-6"
            size="2xl"
            isOpen={isModelXOpen}
            onOpenChange={setIsModelXOpen}
        >
            <div className="px-4  pb-10 dark:bg-black/40">
                <div className='border-l-[10px] border-primary bg-primary/10 px-5 py-5 text-md rounded'>
                    <p><strong>Project:</strong> {data.project}</p>
                    <p><strong>Size:</strong> {data.size}</p>
                </div>
                <div className='h-6'></div>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
                        <div className='flex gap-5 w-full'>
                            <FormField
                                control={form.control}
                                name="deposit"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>Deposit</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="monthlyInterest"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>Monthly Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className='flex gap-5 w-full'>
                            <FormField
                                control={form.control}
                                name="2"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>2 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="3"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>3 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className='flex gap-5 w-full'>
                            <FormField
                                control={form.control}
                                name="4"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>4 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="5"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>5 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className='flex gap-5 w-full'>
                            <FormField
                                control={form.control}
                                name="6"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>6 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="7"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>7 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className='flex gap-5 w-full'>
                            <FormField
                                control={form.control}
                                name="8"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>8 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="9"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>9 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className='flex gap-5 w-full'>
                            <FormField
                                control={form.control}
                                name="10"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>10 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="11"
                                render={({ field }) => (
                                    <FormItem className="flex-1">
                                        <FormLabel className='!text-foreground'>11 Months Interest</FormLabel>
                                        <FormControl>
                                            <Input placeholder="" {...field} className="border border-accent !rounded-sm bg-none" />
                                        </FormControl>
                                        <FormMessage className="text-[10px] px-3" />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <PrimaryButton type="submit" className='w-full !mt-6'>Submit</PrimaryButton>
                    </form>
                </Form>
            </div>
        </BaseModal>
    </>

}