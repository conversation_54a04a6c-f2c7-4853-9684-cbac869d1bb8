import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import MpesaTransactionsTable from "@/components/Inventory/MpesaTransactionsTable";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useGetMpesaTransactionsQuery } from "@/redux/slices/projects";
import { useState } from "react";

const MpesaTransactionsPage = () => {
  const [status, setStatus] = useState("OPEN");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const { data: transactions, isLoading } = useGetMpesaTransactionsQuery({
    status,
    search: searchValue,
    page: currentPage,
    page_size: itemsPerPage,
  });
  return (
    <Screen>
      <div className="min-h-screen space-y-4 p-4">
        <div className=" rounded-lg shadow-sm border">
          <div className="p-4 border-b">
            <h1 className="text-xl md:text-2xl font-semibold">
              MPESA TRANSACTIONS PAYBILL 921225
            </h1>
          </div>
          <div className="p-4">
            <Tabs defaultValue="OPEN" className="w-full">
              <TabsList className="w-full md:w-auto grid grid-cols-2 md:inline-flex mb-4 bg-primary text-white">
                <TabsTrigger
                  onClick={() => setStatus("OPEN")}
                  value="OPEN"
                  className="px-8"
                >
                  Open Transactions
                </TabsTrigger>
                <TabsTrigger
                  onClick={() => setStatus("CLOSED")}
                  value="CLOSED"
                  className="px-8"
                >
                  Closed Transactions
                </TabsTrigger>
              </TabsList>
              {isLoading ? (
                <div className="flex justify-center items-center h-12">
                  <SpinnerTemp type="spinner-double" size="md" />
                </div>
              ) : (
                <>
                  <TabsContent value="OPEN">
                    <MpesaTransactionsTable
                      currentPage={currentPage}
                      setCurrentPage={setCurrentPage}
                      itemsPerPage={itemsPerPage}
                      setItemsPerPage={setItemsPerPage}
                      totalItems={transactions.data?.total_data || 0}
                      data={transactions?.data?.results}
                      searchValue={searchValue}
                      setSearchValue={setSearchValue}
                    />
                  </TabsContent>
                  <TabsContent value="CLOSED">
                    <MpesaTransactionsTable
                      currentPage={currentPage}
                      setCurrentPage={setCurrentPage}
                      itemsPerPage={itemsPerPage}
                      setItemsPerPage={setItemsPerPage}
                      data={transactions?.data?.results}
                      totalItems={transactions.data?.total_data || 0}
                      searchValue={searchValue}
                      setSearchValue={setSearchValue}
                    />
                  </TabsContent>
                </>
              )}
            </Tabs>
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default MpesaTransactionsPage;
