import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import { useLazyGetInventoryAccountAllBookingsQuery } from "@/redux/slices/projects";
import { formatDate } from "@/utils/formatDate";
import { addComma } from "@/utils/helpers";
import React, { useEffect, useState } from "react";
import { utils, write } from "xlsx";
// import * as XLSX from "xlsx";

type Props = {
  allBookingModal: boolean;
  setAllBookingModal: (value: boolean) => void;
  title?: string;
};

interface ParamsType {
  BOOKING_TYPE: "MPESA" | "SPECIAL" | "OTHER" | "DIASPORA" | "ALL";
  STATUS:
    | "OPEN"
    | "OLD"
    | "TIMED"
    | "DONE"
    | "WAITING"
    | "SUSPENDED"
    | "REJECTED"
    | "REVERTED"
    | "ALL";
  MARKETER_NAME: string;
  MARKETER_EMPLOYEE_NO: string;
  PLOT_NUMBER: string;
  start_date: string;
  end_date: string;
  search: string;
  page: number;
  page_size: number;
}

const AllBookings = ({ allBookingModal, setAllBookingModal, title }: Props) => {
  const [downloadingExcel, setDownloadingExcel] = useState<boolean>(false);
  const [params, setParams] = useState<ParamsType>({
    BOOKING_TYPE: "ALL",
    STATUS: "ALL",
    MARKETER_EMPLOYEE_NO: "",
    MARKETER_NAME: "",
    PLOT_NUMBER: "",
    start_date: "",
    end_date: "",
    search: "",
    page: 1,
    page_size: 100,
  });

  const [getAllBookings, { data: allBookingsData, isLoading: loading }] =
    useLazyGetInventoryAccountAllBookingsQuery();

  useEffect(() => {
    handleFetchBoooking({ ...params });
  }, []);

  const handleFetchBoooking = async (params: any) => {
    try {
      const res = await getAllBookings(params).unwrap();
    } catch (error) {
      console.error("Error fetching data", error);
    }
  };

  const handleDownloadExcel = async () => {
    setDownloadingExcel(true);
    try {
      const data = allBookingsData?.results || [];

      const topHeaders = "Optiven All Bookings Report";
      const subHeaders = `Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`;

      const headerStyle = {
        font: { bold: true, sz: 14 },
        alignment: { horizontal: "center" },
      };
      const subHeaderStyle = {
        font: { bold: true, sz: 12 },
        alignment: { horizontal: "center" },
      };
      const topHeaderRow = [topHeaders];
      const subHeaderRow = [subHeaders];

      const headers = [
        "Plots",
        "Booking Type",
        "Status",
        "Amount",
        "Creation Date",
        "Marketer",
        "Lead",
        "Customer",
      ];

      const filteredData = data.map((item: any) => [
        item.plots,
        item.booking_type,
        item.status,
        item.amount,
        formatDate(item.creation_date),
        item.marketer_name,
        item.lead_name || "-",
        item.customer_name || "-",
      ]);

      const sheetData = [
        topHeaderRow,
        subHeaderRow,
        [],
        headers,
        ...filteredData,
      ];

      const worksheet = utils.aoa_to_sheet(sheetData);

      // Merge top header and subheader cells across all columns
      const colCount = headers.length;
      worksheet["!merges"] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: colCount - 1 } },
        { s: { r: 1, c: 0 }, e: { r: 1, c: colCount - 1 } },
      ];

      // Apply styles to top header and subheader
      worksheet["A1"].s = headerStyle;
      worksheet["A2"].s = subHeaderStyle;

      headers.forEach((_, index) => {
        const cellRef = utils.encode_cell({ r: 0, c: index });
        if (!worksheet[cellRef]) return;
        worksheet[cellRef].s = {
          font: { bold: true },
        };
      });

      worksheet["!cols"] = [
        { wch: 20 }, // Plots
        { wch: 30 }, // Booking types
        { wch: 30 }, // Status
        { wch: 40 }, // Amount
        { wch: 40 }, // Creation Date
        { wch: 50 }, // Marketer
        { wch: 50 }, // Lead
        { wch: 50 }, // customer
      ];

      // Step 2: Create a workbook and append the worksheet
      const workbook = utils.book_new();
      utils.book_append_sheet(workbook, worksheet, "All Bookings Report");

      // Step 3: Write the workbook to binary
      const excelBuffer = write(workbook, {
        bookType: "xlsx",
        type: "array",
        cellStyles: true,
      });

      // Step 4: Create a Blob and trigger download
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });

      // Extract filename from headers
      let filename = "inventory_all_booking.xlsx";

      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = blobUrl;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(blobUrl);
      setDownloadingExcel(false);
    } catch (error) {
      console.error("Error downloading Excel", error);
      setDownloadingExcel(false);
      return;
    }
  };

  return (
    <BaseModal
      isOpen={allBookingModal}
      onOpenChange={setAllBookingModal}
      size="full"
      title={title}
      description="All bookings report provides a comprehensive overview of all bookings made."
      footer={<Button onClick={() => setAllBookingModal(false)}>Close</Button>}
    >
      <div className="w-full ">
        <div className="flex flex-wrap justify-end items-center gap-3 py-2 ">
          <div className="flex flex-col">
            <label className="text-sm">Booking Types</label>
            <select
              className="border rounded px-2 py-1 text-sm"
              value={params.BOOKING_TYPE}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  BOOKING_TYPE: e.target.value as ParamsType["BOOKING_TYPE"],
                }))
              }
            >
              <option value="ALL">All Booking Types</option>
              <option value="MPESA">MPESA</option>
              <option value="SPECIAL">SPECIAL</option>
              <option value="OTHER">OTHER</option>
              <option value="DIASPORA">DIASPORA</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm">Status</label>
            <select
              className="border rounded px-2 py-1 text-sm"
              value={params.STATUS}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  STATUS: e.target.value as ParamsType["STATUS"],
                }))
              }
            >
              <option value="ALL">All Statuses</option>
              <option value="OPEN">OPEN</option>
              <option value="OLD">OLD</option>
              <option value="TIMED">TIMED</option>
              <option value="DONE">DONE</option>
              <option value="WAITING">WAITING</option>
              <option value="SUSPENDED">SUSPENDED</option>
              <option value="REJECTED">REJECTED</option>
              <option value="REVERTED">REVERTED</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm">Marketer Name</label>
            <input
              className="border rounded px-2 py-1 text-sm"
              type="text"
              placeholder="Marketer Name"
              value={params.MARKETER_NAME}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  MARKETER_NAME: e.target.value,
                }))
              }
            />
          </div>

          <div className="flex flex-col">
            <label className="text-sm">Plot Number</label>
            <input
              className="border rounded px-2 py-1 text-sm"
              type="text"
              placeholder="Plot Number"
              value={params.PLOT_NUMBER}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  PLOT_NUMBER: e.target.value,
                }))
              }
            />
          </div>

          <div className="flex flex-col">
            <label className="text-sm">Start Date</label>
            <input
              className="border rounded px-2 py-1 text-sm"
              type="date"
              value={params.start_date}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  start_date: e.target.value,
                }))
              }
            />
          </div>

          <div className="flex flex-col">
            <label className="text-sm">End Date</label>
            <input
              className="border rounded px-2 py-1 text-sm"
              type="date"
              value={params.end_date}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  end_date: e.target.value,
                }))
              }
            />
          </div>

          <div className="flex flex-col">
            <label className="text-sm">Page Size</label>
            <select
              className="border rounded px-2 py-1 text-sm"
              value={params.page_size}
              onChange={(e) =>
                setParams((prev) => ({
                  ...prev,
                  page_size: e.target.value as any,
                }))
              }
            >
              <option value={10}>10</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={500}>500</option>
              <option value={1000}>1000</option>
            </select>
          </div>

          <Button
            className="px-4 py-1"
            onClick={() => handleFetchBoooking(params)}
            disabled={loading}
          >
            {loading ? "Loading..." : "Filter"}
          </Button>

          <Button
            className="px-4 py-1"
            onClick={() => handleDownloadExcel()}
            disabled={downloadingExcel}
          >
            {downloadingExcel ? "Loading..." : "Download Excel"}
          </Button>
        </div>
        <table className="table-auto border w-full mb-4">
          <thead>
            <tr>
              <th className="border px-2 py-1 bg-secondary">Plot(s)</th>
              <th className="border px-2 py-1 bg-secondary">Booking Type</th>
              <th className="border px-2 py-1 bg-secondary">Status</th>
              <th className="border px-2 py-1 bg-secondary">Amount</th>
              <th className="border px-2 py-1 bg-secondary">Date</th>
              <th className="border px-2 py-1 bg-secondary">Marketer</th>
              <th className="border px-2 py-1 bg-secondary">Lead Name</th>
              <th className="border px-2 py-1 bg-secondary">Customer Name</th>
            </tr>
          </thead>
          <tbody>
            {(allBookingsData as any)?.results?.map(
              (booking: any, i: number) => (
                <tr key={i} className="hover:bg-gray-50 text-sm">
                  <td className="border px-2 py-1">{booking?.plots}</td>
                  <td className="border px-2 py-1">{booking?.booking_type}</td>
                  <td className="border px-2 py-1">{booking?.status}</td>
                  <td className="border px-2 py-1">
                    {addComma(booking?.amount) ?? "-"}
                  </td>
                  <td className="border px-2 py-1">
                    {booking?.creation_date
                      ? formatDate(booking?.creation_date)
                      : "-"}
                  </td>
                  <td className="border px-2 py-1">{booking?.marketer_name}</td>
                  <td className="border px-2 py-1">
                    {booking?.lead_name ? booking?.lead_name : "-"}
                  </td>
                  <td className="border px-2 py-1">
                    {booking?.customer_name ?? "-"}
                  </td>
                </tr>
              )
            )}
            {(!allBookingsData || allBookingsData.length === 0) && (
              <tr>
                <td
                  colSpan={8}
                  className="border px-2 py-4 text-center text-gray-400"
                >
                  No bookings found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </BaseModal>
  );
};

export default AllBookings;
