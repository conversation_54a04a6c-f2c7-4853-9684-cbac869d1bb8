import React, { useState, Component, ReactNode } from "react";
import { Screen } from "@/app-components/layout/screen";
import {
  Eye,
  User,
  Building2,
  Mail,
  Phone,
  MessageSquare,
  Users,
  UserCheck,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  BarChart3,
  Calendar,
  UserPlus,
  Search,
} from "lucide-react";
import ViewEngagementModal from "../../components/servicesdashboard/Engagements/ViewEngagementModal";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Pagination, PaginationContent, PaginationItem } from "@/components/ui/pagination";
import { useToast } from "@/hooks/use-toast";
import {
  useGetEngagementsQuery,
  usePartialUpdateEngagementMutation,
} from "@/redux/slices/engagementsApiSlice";

interface RelatedEntity {
  id: string | number;
  name: string;
  type: string;
}

interface Engagement {
  id: string;
  engagement_type: "Call" | "Email" | "Meeting" | "SMS" | "Chat" | "Visit" | "Event" | "Follow-up" | "Contact-add";
  title: string;
  description: string;
  subject: string;
  customer: string;
  prospect: string
  scheduled_at: string;
  assigned_to: string;
  created_date: string;
  entity_type: string;
  related_entity: RelatedEntity;
  unread: boolean;
  starred: boolean;
  participants: { id: string; avatar: string; name: string; role: string }[];
  comments: { id: string; text: string }[];
  attachment: string | null;
}

interface EngagementData {
  id: string;
  unread: boolean;
  engagement_type: string;
  title: string;
  prospect: number;
  sale:string;
  customer: string;
  marketingTeam: string;
  createdDate: string;
  starred: boolean;
  participants: { id: string; avatar: string; name: string; role: string }[];
  comments: { id: string; text: string }[];
  attachment: File | null;
  description: string;
}

class ErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean; error: Error | null }
> {
  state = { hasError: false, error: null as Error | null };
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  render() {
    if (this.state.hasError) {
      return (
        <Card className="m-8">
          <CardContent className="p-8 text-center text-red-600">
            Error: {this.state.error?.message || "Unknown error"}
          </CardContent>
        </Card>
      );
    }
    return this.props.children;
  }
}

const CustomerMarketerEngagement: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<
    "customer" | "sales" | "prospects"
  >("customer");
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedEngagement, setSelectedEngagement] =
    useState<Engagement | null>(null);
  const { toast } = useToast();

  // Pagination state per category
  const [customerPage, setCustomerPage] = useState(1);
  const [salesPage, setSalesPage] = useState(1);
  const [prospectsPage, setProspectsPage] = useState(1);

  const PAGE_SIZE = 50;

  // Search state (debounced and raw input) per category
  const [customerSearchInput, setCustomerSearchInput] = useState("");
  const [customerSearch, setCustomerSearch] = useState("");
  const [salesSearchInput, setSalesSearchInput] = useState("");
  const [salesSearch, setSalesSearch] = useState("");
  const [prospectsSearchInput, setProspectsSearchInput] = useState("");
  const [prospectsSearch, setProspectsSearch] = useState("");

  // Debounce search inputs
  React.useEffect(() => {
    const t = setTimeout(() => setCustomerSearch(customerSearchInput.trim()), 500);
    return () => clearTimeout(t);
  }, [customerSearchInput]);
  React.useEffect(() => {
    const t = setTimeout(() => setSalesSearch(salesSearchInput.trim()), 500);
    return () => clearTimeout(t);
  }, [salesSearchInput]);
  React.useEffect(() => {
    const t = setTimeout(() => setProspectsSearch(prospectsSearchInput.trim()), 500);
    return () => clearTimeout(t);
  }, [prospectsSearchInput]);

  // Reset page when search changes
  React.useEffect(() => { setCustomerPage(1); }, [customerSearch]);
  React.useEffect(() => { setSalesPage(1); }, [salesSearch]);
  React.useEffect(() => { setProspectsPage(1); }, [prospectsSearch]);

  const { data: customerEngagements, isLoading: customerLoading } =
    useGetEngagementsQuery({
      client_type: "Customer",
      page_size: PAGE_SIZE,
      page: customerPage,
      search: customerSearch || undefined,
    });
  const { data: salesEngagements, isLoading: salesLoading } =
    useGetEngagementsQuery({
      client_type: "Sale",
      page_size: PAGE_SIZE,
      page: salesPage,
      search: salesSearch || undefined,
    });
  const { data: prospectsEngagements, isLoading: prospectsLoading } =
    useGetEngagementsQuery({
      client_type: "Prospect",
      page_size: PAGE_SIZE,
      page: prospectsPage,
      search: prospectsSearch || undefined,
    });

  const [partialUpdateEngagement] = usePartialUpdateEngagementMutation();

  const transformEngagements = (data: any): Engagement[] => {
    // Handle both raw API response and transformed response
    const results = data?.data?.results || data?.results;
    return results
      ? results.map((eng: any) => ({
          id:
            eng.engagement_id ||
            `eng-${Math.random().toString(36).substring(2, 7)}`,
          engagement_type:
            eng.engagement_type &&
            [
              "Call",
              "Email",
              "Meeting",
              "SMS",
              "Chat",
              "Visit",
              "Event",
              "Follow-up",
              "Contact-add",
            ].includes(eng.engagement_type)
              ? eng.engagement_type
              : "Call",
          title: eng.subject || "Untitled",
          description: eng.description || "",
          status: eng.client_type || "Scheduled", // Use client_type instead of status
          prospect: eng.prospect || "Unknown",
          customer: eng.customer || "Unknown",
          scheduled_at: eng.scheduled_at || new Date().toISOString(),
          assigned_to: eng.created_by || "Unassigned",
          created_date: eng.created_at || new Date().toISOString(),
          entity_type: eng.entity_type || "",
          related_entity: eng.related_entity || { id: "", name: "", type: "" },
          unread: eng.unread || false,
          starred: eng.starred || false,
          participants: eng.participants || [],
          comments: eng.comments || [],
          attachment: eng.attachment || null,
        }))
      : [];
  };

  const customerEngagementsData = transformEngagements(customerEngagements);
  const salesEngagementsData = transformEngagements(salesEngagements);
  const prospectsEngagementsData = transformEngagements(prospectsEngagements);

  console.log("Customer Engagements Data:", customerEngagementsData);
  console.log("Sales Engagements Data:", salesEngagementsData);
  console.log("Prospects Engagements Data:", prospectsEngagementsData);

  const getStats = (engagements: Engagement[]) => ({
    total: engagements.length,
    call: engagements.filter((e) => e.engagement_type === "Call").length,
    email: engagements.filter((e) => e.engagement_type === "Email").length,
    meeting: engagements.filter((e) => e.engagement_type === "Meeting").length,
    sms: engagements.filter((e) => e.engagement_type === "SMS").length,
    chat: engagements.filter((e) => e.engagement_type === "Chat").length,
    visit: engagements.filter((e) => e.engagement_type === "Visit").length,
    event: engagements.filter((e) => e.engagement_type === "Event").length,
    followup: engagements.filter((e) => e.engagement_type === "Follow-up").length,
    contactAdd: engagements.filter((e) => e.engagement_type === "Contact-add")
      .length,
  });

  const customerStats = getStats(customerEngagementsData);
  const salesStats = getStats(salesEngagementsData);
  const prospectsStats = getStats(prospectsEngagementsData);

    const transformToEngagementData = (engagement: Engagement): EngagementData => ({
  id: engagement.engagement_id,
  unread: false, // Adjust based on API response
  engagement_type: engagement.engagement_type,
  title: engagement.description,
  customer: engagement.customer ?? null,
  prospect: engagement.prospect ?? null,
  sale: engagement.sale ?? null,
  marketingTeam: engagement.assigned_to ?? '',
  createdDate: engagement.created_date ?? '',
  starred: false, // Adjust based on API response
  participants: engagement.participants ?? [],
  comments: engagement.comments ?? [],
  attachment: null, // Adjust based on API response
  description: engagement.description ?? '',
});

  const handleEngagementUpdate = async (data: any) => {
    if (!selectedEngagement) return;
    try {
      await partialUpdateEngagement({
        engagementId: selectedEngagement.id,
        data: {
          subject: data.title,
          description: data.description || "",
          engagement_type: data.engagement_type,
          entity_name: data.customer,
          entity_type: data.entity_type,
          related_entity: data.related_entity,
          scheduled_at: data.scheduled_at,
          assigned_to_display: data.assigned_to,
          attachment: data.attachment,
        },
      }).unwrap();
      toast({
        title: "Success",
        description: "Engagement updated successfully",
      });
      setIsViewModalOpen(false);
    } catch (err: any) {
      toast({
        title: "Error",
        description: "Failed to update engagement",
        variant: "destructive",
      });
    }
  };

  const renderEngagementCard = (engagement: Engagement, index: number) => {
    const getEngagementTypeIcon = (type: string) => {
      switch (type?.toLowerCase()) {
        case "call":
          return Phone;
        case "email":
          return Mail;
        case "meeting":
          return Users;
        case "sms":
          return MessageSquare;
        case "chat":
          return MessageSquare;
        case "visit":
          return Building2;
        case "event":
          return Calendar;
        case "follow-up":
          return Clock;
        case "contact-add":
          return UserPlus;
        default:
          return Building2;
      }
    };

    const getEngagementTypeColor = (type: string) => {
      switch (type?.toLowerCase()) {
        case "call":
          return "from-blue-400 to-indigo-400";
        case "email":
          return "from-green-400 to-emerald-400";
        case "meeting":
          return "from-purple-400 to-violet-400";
        case "sms":
          return "from-teal-400 to-cyan-400";
        case "chat":
          return "from-cyan-400 to-sky-400";
        case "visit":
          return "from-orange-400 to-amber-400";
        case "event":
          return "from-pink-400 to-rose-400";
        case "follow-up":
          return "from-amber-400 to-yellow-400";
        case "contact-add":
          return "from-red-400 to-pink-400";
        default:
          return "from-gray-400 to-slate-400";
      }
    };

    const EngagementIcon = getEngagementTypeIcon(engagement.engagement_type);
    const colorGradient = getEngagementTypeColor(engagement.engagement_type);
    const rotations = [
      "-rotate-1",
      "rotate-1",
      "rotate-0",
      "-rotate-1",
      "rotate-1",
    ];
    const randomRotation = rotations[index % rotations.length];

    return (
      <div
        key={engagement.id}
        className={`group relative bg-white border border-gray-200 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:z-10`}
        style={{
          background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
          boxShadow: "0 10px 25px rgba(0,0,0,0.1), 0 4px 10px rgba(0,0,0,0.05)",
          transformOrigin: "center center",
        }}
      >
        <div
          className={`absolute top-0 left-0 right-0 h-2 rounded-t-2xl bg-gradient-to-r ${colorGradient}`}
        ></div>

        <div
          className={`absolute top-4 right-12 px-3 py-1 text-xs font-semibold rounded-full backdrop-blur-sm bg-opacity-80 border bg-gray-100 text-gray-700 border-gray-200`}
        >
          {engagement.engagement_type}
        </div>

        <div className="relative px-6 pt-8 pb-4">
          <div className="flex items-start gap-4">
            <div
              className={`relative p-3 rounded-xl shadow-md bg-gradient-to-br ${colorGradient.replace(
                "to-",
                "50 to-"
              )}50 border border-gray-200`}
            >
              <EngagementIcon className="h-6 w-6 text-gray-600" />
            </div>

            <div className="flex-1 min-w-0">
              <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight tracking-tight">
                {engagement.title}
              </h3>
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-600">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                {new Date(
                  engagement.created_date || engagement.scheduled_at
                ).toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                  year: "numeric",
                })}
              </div>
            </div>
          </div>
        </div>

        <div className="relative px-6 pb-6">
          <p className="mb-6 text-gray-600 text-sm leading-relaxed font-medium">
            {engagement.description || "No description available"}
          </p>

          <div className="space-y-3 mb-6">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl border border-gray-100">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                <Building2 className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">
                  Customer
                </p>
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {engagement.customer}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl border border-gray-100">
              <div className="w-8 h-8 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">
                  Assigned to
                </p>
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {engagement.assigned_to}
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <button
              onClick={() => {
                setSelectedEngagement(engagement);
                setIsViewModalOpen(true);
              }}
              className="group flex items-center gap-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-xl border border-blue-200 hover:border-blue-300 transition-all duration-200 font-medium text-sm"
            >
              <Eye className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
              <span>View</span>
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <ErrorBoundary>
      <Screen>
        <div className="relative overflow-hidden bg-gradient-to-br from-emerald-600 via-green-600 to-green-500 text-white">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-green-500/20"></div>
          <div className="absolute top-8 left-8 w-16 h-16 bg-white/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-20 right-16 w-12 h-12 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-16 left-1/4 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse delay-500"></div>

          <div className="relative container mx-auto px-6 py-12">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                  <div className="h-1 w-12 bg-gradient-to-r from-white/40 to-transparent rounded-full"></div>
                </div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-purple-100 to-indigo-100 bg-clip-text text-transparent">
                  Engagements Dashboard
                </h1>
                <p className="text-lg text-indigo-100 max-w-xl">
                  Track and manage all customer, sales, and prospect interactions in one unified view
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                {/* Global Search by active tab */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/80 h-4 w-4" />
                  <Input
                    placeholder={`Search ${activeCategory} engagements...`}
                    className="pl-10 w-full sm:w-80 bg-white/10 border-white/20 text-white placeholder:text-white/70 backdrop-blur-sm"
                    value={
                      activeCategory === 'customer' ? customerSearchInput :
                      activeCategory === 'sales' ? salesSearchInput :
                      prospectsSearchInput
                    }
                    onChange={(e) => {
                      const v = e.target.value;
                      if (activeCategory === 'customer') setCustomerSearchInput(v);
                      else if (activeCategory === 'sales') setSalesSearchInput(v);
                      else setProspectsSearchInput(v);
                    }}
                  />
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-white/20 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-white/90 text-sm font-medium">
                        Total Engagements
                      </p>
                      <p className="text-2xl font-bold text-white">
                        {(customerEngagements?.total_data ?? 0) +
                          (salesEngagements?.total_data ?? 0) +
                          (prospectsEngagements?.total_data ?? 0)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-6 -mt-6 relative z-10">
          <Tabs
            value={activeCategory}
            onValueChange={(value) => setActiveCategory(value as any)}
            className="w-full"
          >
           <TabsList className="grid w-full grid-cols-3 mb-8 bg-white rounded-2xl shadow-lg p-2">
  <TabsTrigger
    value="customer"
    className="flex items-center gap-2 border-2 border-green-600 data-[state=active]:bg-green-600 data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=inactive]:text-green-600 data-[state=inactive]:hover:border-green-700 rounded-xl py-3 px-6 font-semibold transition-all duration-200"
  >
    <UserCheck className="w-5 h-5" />
    Customer ({customerEngagements?.total_data ?? customerEngagements?.count ?? customerEngagementsData.length})
  </TabsTrigger>
  <TabsTrigger
    value="sales"
    className="flex items-center gap-2 border-2 border-green-600 data-[state=active]:bg-green-600 data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=inactive]:text-green-600 data-[state=inactive]:hover:border-green-700 rounded-xl py-3 px-6 font-semibold transition-all duration-200"
  >
    <TrendingUp className="w-5 h-5" />
    Sales ({salesEngagements?.total_data ?? salesEngagements?.count ?? salesEngagementsData.length})
  </TabsTrigger>
  <TabsTrigger
    value="prospects"
    className="flex items-center gap-2 border-2 border-green-600 data-[state=active]:bg-green-600 data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=inactive]:text-green-600 data-[state=inactive]:hover:border-green-700 rounded-xl py-3 px-6 font-semibold transition-all duration-200"
  >
    <Target className="w-5 h-5" />
    Prospects ({prospectsEngagements?.total_data ?? prospectsEngagements?.count ?? prospectsEngagementsData.length})
  </TabsTrigger>
</TabsList>

            <TabsContent value="customer" className="space-y-6">
              
                
                

              {customerLoading ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="animate-pulse h-64 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg rounded-2xl"
                    >
                      <div className="p-6">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="h-10 w-10 bg-gradient-to-r from-indigo-300 to-purple-300 rounded-xl"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2"></div>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : customerEngagementsData.length === 0 ? (
                <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-indigo-50 to-purple-50 rounded-2xl border border-gray-200 shadow-inner mb-8">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                    <div className="relative bg-gradient-to-r from-indigo-500 to-purple-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                      <UserCheck className="h-12 w-12 text-white" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                    No customer engagements yet
                  </h3>
                  <p className="text-gray-600 mb-8 max-w-md mx-auto">
                    Customer engagements will appear here once they are created in the system.
                  </p>
                </div>
              ) : (
                <>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {customerEngagementsData.map((engagement, index) =>
                      renderEngagementCard(engagement, index)
                    )}
                  </div>

                  {/* Pagination - Customer */}
                  {(customerEngagements?.num_pages || 1) > 1 && (
                    <div className="mt-8 flex justify-center">
                      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-2">
                        <Pagination>
                          <PaginationContent className="gap-1">
                            <PaginationItem>
                              <button
                                onClick={() => setCustomerPage(Math.max(1, customerPage - 1))}
                                className="h-10 px-4 rounded-xl font-medium bg-gray-100 hover:bg-gray-200 text-gray-700"
                                disabled={customerPage <= 1}
                              >
                                Previous
                              </button>
                            </PaginationItem>
                            {[...Array(Math.min(customerEngagements?.num_pages || 1, 5))].map((_, i) => {
                              const pageNum = i + Math.max(1, Math.min(customerPage - 2, (customerEngagements?.num_pages || 1) - 4));
                              return (
                                <PaginationItem key={i}>
                                  <button
                                    onClick={() => setCustomerPage(pageNum)}
                                    className={`h-10 w-10 rounded-xl flex items-center justify-center font-medium transition-all duration-200 ${
                                      pageNum === customerPage
                                        ? "bg-green-600 text-white"
                                        : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                                    }`}
                                  >
                                    {pageNum}
                                  </button>
                                </PaginationItem>
                              );
                            })}
                            <PaginationItem>
                              <button
                                onClick={() => setCustomerPage(Math.min(customerEngagements?.num_pages || 1, customerPage + 1))}
                                className="h-10 px-4 rounded-xl font-medium bg-gray-100 hover:bg-gray-200 text-gray-700"
                                disabled={customerPage >= (customerEngagements?.num_pages || 1)}
                              >
                                Next
                              </button>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="sales" className="space-y-6">
              
                

              {salesLoading ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="animate-pulse h-64 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg rounded-2xl"
                    >
                      <div className="p-6">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="h-10 w-10 bg-gradient-to-r from-purple-300 to-pink-300 rounded-xl"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2"></div>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : salesEngagementsData.length === 0 ? (
                <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-purple-50 to-pink-50 rounded-2xl border border-gray-200 shadow-inner mb-8">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                    <div className="relative bg-gradient-to-r from-purple-500 to-pink-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                      <TrendingUp className="h-12 w-12 text-white" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                    No sales engagements yet
                  </h3>
                  <p className="text-gray-600 mb-8 max-w-md mx-auto">
                    Sales engagements and lead file interactions will appear here once they are created.
                  </p>
                </div>
              ) : (
                <>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {salesEngagementsData.map((engagement, index) =>
                      renderEngagementCard(engagement, index)
                    )}
                  </div>

                  {/* Pagination - Sales */}
                  {(salesEngagements?.num_pages || 1) > 1 && (
                    <div className="mt-8 flex justify-center">
                      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-2">
                        <Pagination>
                          <PaginationContent className="gap-1">
                            <PaginationItem>
                              <button
                                onClick={() => setSalesPage(Math.max(1, salesPage - 1))}
                                className="h-10 px-4 rounded-xl font-medium bg-gray-100 hover:bg-gray-200 text-gray-700"
                                disabled={salesPage <= 1}
                              >
                                Previous
                              </button>
                            </PaginationItem>
                            {[...Array(Math.min(salesEngagements?.num_pages || 1, 5))].map((_, i) => {
                              const pageNum = i + Math.max(1, Math.min(salesPage - 2, (salesEngagements?.num_pages || 1) - 4));
                              return (
                                <PaginationItem key={i}>
                                  <button
                                    onClick={() => setSalesPage(pageNum)}
                                    className={`h-10 w-10 rounded-xl flex items-center justify-center font-medium transition-all duration-200 ${
                                      pageNum === salesPage
                                        ? "bg-green-600 text-white"
                                        : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                                    }`}
                                  >
                                    {pageNum}
                                  </button>
                                </PaginationItem>
                              );
                            })}
                            <PaginationItem>
                              <button
                                onClick={() => setSalesPage(Math.min(salesEngagements?.num_pages || 1, salesPage + 1))}
                                className="h-10 px-4 rounded-xl font-medium bg-gray-100 hover:bg-gray-200 text-gray-700"
                                disabled={salesPage >= (salesEngagements?.num_pages || 1)}
                              >
                                Next
                              </button>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="prospects" className="space-y-6">
             

              {prospectsLoading ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="animate-pulse h-64 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg rounded-2xl"
                    >
                      <div className="p-6">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="h-10 w-10 bg-gradient-to-r from-pink-300 to-red-300 rounded-xl"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2"></div>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : prospectsEngagementsData.length === 0 ? (
                <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-pink-50 to-red-50 rounded-2xl border border-gray-200 shadow-inner mb-8">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-pink-400 to-red-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                    <div className="relative bg-gradient-to-r from-pink-500 to-red-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                      <Target className="h-12 w-12 text-white" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                    No prospect engagements yet
                  </h3>
                  <p className="text-gray-600 mb-8 max-w-md mx-auto">
                    Prospect engagements and potential customer interactions will appear here.
                  </p>
                </div>
              ) : (
                <>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {prospectsEngagementsData.map((engagement, index) =>
                      renderEngagementCard(engagement, index)
                    )}
                  </div>

                  {/* Pagination - Prospects */}
                  {(prospectsEngagements?.num_pages || 1) > 1 && (
                    <div className="mt-8 flex justify-center">
                      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-2">
                        <Pagination>
                          <PaginationContent className="gap-1">
                            <PaginationItem>
                              <button
                                onClick={() => setProspectsPage(Math.max(1, prospectsPage - 1))}
                                className="h-10 px-4 rounded-xl font-medium bg-gray-100 hover:bg-gray-200 text-gray-700"
                                disabled={prospectsPage <= 1}
                              >
                                Previous
                              </button>
                            </PaginationItem>
                            {[...Array(Math.min(prospectsEngagements?.num_pages || 1, 5))].map((_, i) => {
                              const pageNum = i + Math.max(1, Math.min(prospectsPage - 2, (prospectsEngagements?.num_pages || 1) - 4));
                              return (
                                <PaginationItem key={i}>
                                  <button
                                    onClick={() => setProspectsPage(pageNum)}
                                    className={`h-10 w-10 rounded-xl flex items-center justify-center font-medium transition-all duration-200 ${
                                      pageNum === prospectsPage
                                        ? "bg-green-600 text-white"
                                        : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                                    }`}
                                  >
                                    {pageNum}
                                  </button>
                                </PaginationItem>
                              );
                            })}
                            <PaginationItem>
                              <button
                                onClick={() => setProspectsPage(Math.min(prospectsEngagements?.num_pages || 1, prospectsPage + 1))}
                                className="h-10 px-4 rounded-xl font-medium bg-gray-100 hover:bg-gray-200 text-gray-700"
                                disabled={prospectsPage >= (prospectsEngagements?.num_pages || 1)}
                              >
                                Next
                              </button>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>
        <ViewEngagementModal
  isOpen={isViewModalOpen}
  onOpenChange={setIsViewModalOpen}
  engagementData={selectedEngagement ? transformToEngagementData(selectedEngagement) : null}
  client_type={
    activeCategory === 'customer'
      ? 'Customer'
      : activeCategory === 'sales'
      ? 'Sale'
      : 'Prospect'
  }
  onSubmit={handleEngagementUpdate}
/>
              

        
      </Screen>
    </ErrorBoundary>
  );
};

export default CustomerMarketerEngagement;