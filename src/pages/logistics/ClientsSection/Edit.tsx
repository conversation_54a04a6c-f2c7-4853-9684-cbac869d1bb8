import { useState, useEffect } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface EditClientProps {
  isOpen: boolean;
  onClose: () => void;
  client: {
    id: string;
    clientName: string;
    clientPhoneNumber: string;
    clientEmail: string;
  };
  onUpdate: (updatedClient: { id: string; clientName: string; clientPhoneNumber: string; clientEmail: string }) => void;
}

export default function EditClient({ isOpen, onClose, client, onUpdate }: EditClientProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [clientData, setClientData] = useState({
    id: "",
    clientName: "",
    clientPhoneNumber: "",
    clientEmail: "",
  });

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
      setClientData(client);
    }
  }, [isOpen, client]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setClientData((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpdateClient = () => {
    onUpdate(clientData);
    onClose();
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Edit Client"
      description="Complete all steps to update the client"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleUpdateClient}
      steps={[
        {
          title: "Basic Information",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="clientName">Client Name</Label>
                <Input
                  id="clientName"
                  name="clientName"
                  value={clientData.clientName}
                  onChange={handleInputChange}
                  placeholder="Enter client name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="clientPhoneNumber">Phone Number</Label>
                <Input
                  id="clientPhoneNumber"
                  name="clientPhoneNumber"
                  value={clientData.clientPhoneNumber}
                  onChange={handleInputChange}
                  placeholder="Enter phone number (e.g., +2547XXXXXXXX)"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="clientEmail">Email</Label>
                <Input
                  id="clientEmail"
                  name="clientEmail"
                  type="email"
                  value={clientData.clientEmail}
                  onChange={handleInputChange}
                  placeholder="Enter client email"
                />
              </div>
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="space-y-4 py-2">
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Client Name:</span>
                  <span className="ml-2">{clientData.clientName}</span>
                </div>
                <div>
                  <span className="font-medium">Phone Number:</span>
                  <span className="ml-2">{clientData.clientPhoneNumber}</span>
                </div>
                <div>
                  <span className="font-medium">Email:</span>
                  <span className="ml-2">{clientData.clientEmail}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review the information above before updating this client.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}