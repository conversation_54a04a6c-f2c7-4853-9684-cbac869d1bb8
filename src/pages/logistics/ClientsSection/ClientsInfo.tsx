import {  useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Badge, DestructiveBadge, PrimaryBadge, SecondaryBadge } from "@/components/custom/badges/badges";
import { User, Mail, Phone, Calendar, FileText } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

interface ClientInfoProps {
  isOpen: boolean;
  onClose: () => void;
  client: {
    id: string;
    clientName: string;
    clientPhoneNumber: string;
    clientEmail: string;
    joinDate?: string;
    status?: string;
  };
  onUpdate: (updatedClient: {
    id: string;
    clientName: string;
    clientPhoneNumber: string;
    clientEmail: string;
  }) => void;
}

const ClientInfo: React.FC<ClientInfoProps> = ({ isOpen, onClose, client,  }) => {
  const [activeCustomerTab, setActiveCustomerTab] = useState("details");
    const [clientData] = useState({
        id: client.id,
        clientName: client.clientName,
        clientPhoneNumber: client.clientPhoneNumber,
        clientEmail: client.clientEmail,
    });
  

 

 

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title={`Customer: ${clientData.clientName}`}
      description="Complete customer profile and transaction history"
      size="lg"
      footer={
        <div className="flex justify-between w-full">
          <PrimaryButton variant="secondary" onClick={onClose}>
            Close
          </PrimaryButton>
          <div className="flex gap-2"></div>
        </div>
      }
    >
      <div className="py-2">
       
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 rounded-full p-3">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium">{clientData.id}</h3>
              <p className="text-sm text-gray-500">
                Joined {client.joinDate || "N/A"}
              </p>
            </div>
          </div>
          <Badge
            className={
              client.status === "Active"
                ? "bg-green-100 text-green-800 hover:bg-green-100"
                : "bg-gray-100 text-gray-800"
            }
          >
            {client.status || "N/A"}
          </Badge>
        </div>

       
        <Tabs
          value={activeCustomerTab}
          onValueChange={setActiveCustomerTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="properties">Properties</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
          </TabsList>

          
          <TabsContent value="details" className="space-y-4">
            { (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Email:</span>
                  <span className="text-sm">{clientData.clientEmail}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Phone:</span>
                  <span className="text-sm">{clientData.clientPhoneNumber}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Customer Since:</span>
                  <span className="text-sm">{client.joinDate || "N/A"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">ID Number:</span>
                  <span className="text-sm">{clientData.id}</span>
                </div>
              </div>
            )}
            <div className="border-t pt-4 mt-4">
              <h4 className="text-sm font-medium mb-2">Contact Preferences</h4>
              <div className="flex flex-wrap gap-2 dark:text-white">
                <PrimaryBadge  >
                  Email
                </PrimaryBadge>
                <SecondaryBadge >
                  Phone
                </SecondaryBadge>
                <DestructiveBadge >
                  WhatsApp
                </DestructiveBadge>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </BaseModal>
  );
};

export default ClientInfo;