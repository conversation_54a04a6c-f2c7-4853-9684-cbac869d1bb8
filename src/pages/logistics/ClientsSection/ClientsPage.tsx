import { Screen } from "@/app-components/layout/screen";
import { DataTable } from "@/components/custom/tables/Table1";
import { useState, useMemo, useEffect } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { logger, useDevLog } from "@/utils/logger";
import { redactCustomer, redactError, redactSiteVisit } from "@/utils/redactors";

import { Search, LucideEye, Users, AlertCircle, Loader2 } from "lucide-react";

import { PrimaryButton } from "@/components/custom/buttons/buttons";

import { toast } from "@/components/custom/Toast/MyToast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import AddClient from "./add";
import EditClient from "./Edit";
import DeleteClient from "./Delete";
import ClientInfo from "./ClientsInfo";
import { useGetSiteVisitsQuery, SiteVisit, Client } from "@/redux/slices/logistics";

// Interface for client data from site visits
interface ClientData {
  id: string;
  clientName: string;
  clientPhoneNumber: string;
  clientEmail: string;
  siteVisitCount: number;
  siteVisits: Array<{
    id: number;
    date: string;
    location: string;
    status: string;
    marketer: string;
    project: string;
  }>;
  // Keep these for backward compatibility and latest visit info
  siteVisitId?: number;
  siteVisitDate?: string;
  siteVisitLocation?: string;
  siteVisitStatus?: string;
  marketer?: string;
  project?: string;
}

const Clients = () => {
  const devLog = useDevLog();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isClientInfoModalOpen, setIsClientInfoModalOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<{
    id: string;
    clientName: string;
    clientPhoneNumber: string;
    clientEmail: string;
  } | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch all site visits to get clients
  const {
    data: siteVisitsResponse,
    isLoading: isLoadingSiteVisits,
    error: siteVisitsError,
    refetch: refetchSiteVisits,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 1000, // Get all site visits with their clients
  });



  // Refetch data when search term changes
  useEffect(() => {
    refetchSiteVisits();
  }, [searchTerm, refetchSiteVisits]);

  // Combined refetch function
  const refetchAllData = () => {
    refetchSiteVisits();
  };

  // Extract unique clients from site visits with aggregated data
  const clientsData = useMemo(() => {
    if (!siteVisitsResponse?.data?.results) return [];

    const clientsMap = new Map<string, ClientData>();

    // Process each site visit to extract client data
    siteVisitsResponse.data.results.forEach((siteVisit: SiteVisit, index: number) => {
      try {
        // Check if site visit has site_visit_client array (actual API response format)
        if (siteVisit.site_visit_client && Array.isArray(siteVisit.site_visit_client)) {
          siteVisit.site_visit_client.forEach((client: Client) => {
            if (client && (client.name || client.phone_number || client.email)) {
              const fullName = client.name || '';
              const nameParts = fullName.split(' ');
              const firstName = nameParts[0] || '';
              const lastName = nameParts.slice(1).join(' ') || '';
              const phone = client.phone_number || '';
              const email = client.email || '';
              
              // Create a unique key for each client (prefer email, then phone, then name+phone)
              const clientKey = email || phone || `${firstName}-${lastName}-${phone}`.replace(/\s+/g, '') || `sitevisit-${siteVisit.id}-client-${siteVisit.site_visit_client?.indexOf(client) || 0}`;
              const clientName = fullName || 'Unknown Client';
              
              if (clientsMap.has(clientKey)) {
                // Client already exists, add this site visit to their list
                const existingClient = clientsMap.get(clientKey)!;
                
                existingClient.siteVisits.push({
                  id: siteVisit.id || 0,
                  date: siteVisit.pickup_date || '',
                  location: siteVisit.pickup_location || '',
                  status: siteVisit.status || '',
                  marketer: siteVisit.marketer || '',
                  project: siteVisit.project || '',
                });
                existingClient.siteVisitCount = existingClient.siteVisits.length;
                
                // Update latest visit info (keep the most recent one)
                const latestVisit = existingClient.siteVisits[existingClient.siteVisits.length - 1];
                existingClient.siteVisitId = latestVisit.id;
                existingClient.siteVisitDate = latestVisit.date;
                existingClient.siteVisitLocation = latestVisit.location;
                existingClient.siteVisitStatus = latestVisit.status;
                existingClient.marketer = latestVisit.marketer;
                existingClient.project = latestVisit.project;
              } else {
                // New client, create entry
                const siteVisitData = {
                  id: siteVisit.id || 0,
                  date: siteVisit.pickup_date || '',
                  location: siteVisit.pickup_location || '',
                  status: siteVisit.status || '',
                  marketer: siteVisit.marketer || '',
                  project: siteVisit.project || '',
                };

                clientsMap.set(clientKey, {
                  id: clientKey,
                  clientName,
                  clientPhoneNumber: phone,
                  clientEmail: email,
                  siteVisitCount: 1,
                  siteVisits: [siteVisitData],
                  // Latest visit info for backward compatibility
                  siteVisitId: siteVisit.id,
                  siteVisitDate: siteVisit.pickup_date || '',
                  siteVisitLocation: siteVisit.pickup_location || '',
                  siteVisitStatus: siteVisit.status || '',
                  marketer: siteVisit.marketer || '',
                  project: siteVisit.project || '',
                });
              }
            }
          });
        } else if (siteVisit.clients && Array.isArray(siteVisit.clients)) {
          // Fallback: Check legacy clients array format
          siteVisit.clients.forEach((client: Client) => {
            if (client && (client.firstName || client.lastName || client.phone || client.email)) {
              const firstName = client.firstName || '';
              const lastName = client.lastName || '';
              const phone = client.phone || '';
              const email = client.email || '';
              
              // Create a unique key for each client (prefer email, then phone, then name+phone)
              const clientKey = email || phone || `${firstName}-${lastName}-${phone}`.replace(/\s+/g, '') || `sitevisit-${siteVisit.id}-client-${siteVisit.clients?.indexOf(client) || 0}`;
              const clientName = `${firstName} ${lastName}`.trim() || 'Unknown Client';
              
              if (clientsMap.has(clientKey)) {
                // Client already exists, add this site visit to their list
                const existingClient = clientsMap.get(clientKey)!;
                
                existingClient.siteVisits.push({
                  id: siteVisit.id || 0,
                  date: siteVisit.pickup_date || '',
                  location: siteVisit.pickup_location || '',
                  status: siteVisit.status || '',
                  marketer: siteVisit.marketer || '',
                  project: siteVisit.project || '',
                });
                existingClient.siteVisitCount = existingClient.siteVisits.length;
                
                // Update latest visit info (keep the most recent one)
                const latestVisit = existingClient.siteVisits[existingClient.siteVisits.length - 1];
                existingClient.siteVisitId = latestVisit.id;
                existingClient.siteVisitDate = latestVisit.date;
                existingClient.siteVisitLocation = latestVisit.location;
                existingClient.siteVisitStatus = latestVisit.status;
                existingClient.marketer = latestVisit.marketer;
                existingClient.project = latestVisit.project;
              } else {
                // New client, create entry
                const siteVisitData = {
                  id: siteVisit.id || 0,
                  date: siteVisit.pickup_date || '',
                  location: siteVisit.pickup_location || '',
                  status: siteVisit.status || '',
                  marketer: siteVisit.marketer || '',
                  project: siteVisit.project || '',
                };

                clientsMap.set(clientKey, {
                  id: clientKey,
                  clientName,
                  clientPhoneNumber: phone,
                  clientEmail: email,
                  siteVisitCount: 1,
                  siteVisits: [siteVisitData],
                  // Latest visit info for backward compatibility
                  siteVisitId: siteVisit.id,
                  siteVisitDate: siteVisit.pickup_date || '',
                  siteVisitLocation: siteVisit.pickup_location || '',
                  siteVisitStatus: siteVisit.status || '',
                  marketer: siteVisit.marketer || '',
                  project: siteVisit.project || '',
                });
              }
            }
          });
        } else if (siteVisit.site_visist_client__name || siteVisit.site_visist_client__phone_number || siteVisit.site_visist_client__email) {
          // Fallback: Check if site visit has client data in flattened fields
          const firstName = siteVisit.site_visist_client__name?.split(' ')[0] || '';
          const lastName = siteVisit.site_visist_client__name?.split(' ').slice(1).join(' ') || '';
          const phone = siteVisit.site_visist_client__phone_number || '';
          const email = siteVisit.site_visist_client__email || '';
          
          if (firstName || lastName || phone || email) {
            const clientKey = email || phone || `${firstName}-${lastName}-${phone}`.replace(/\s+/g, '') || `sitevisit-${siteVisit.id}-flattened`;
            const clientName = `${firstName} ${lastName}`.trim() || siteVisit.site_visist_client__name || 'Unknown Client';
            
            if (clientsMap.has(clientKey)) {
              // Client already exists, add this site visit to their list
              const existingClient = clientsMap.get(clientKey)!;
              
              existingClient.siteVisits.push({
                id: siteVisit.id || 0,
                date: siteVisit.pickup_date || '',
                location: siteVisit.pickup_location || '',
                status: siteVisit.status || '',
                marketer: siteVisit.marketer || '',
                project: siteVisit.project || '',
              });
              existingClient.siteVisitCount = existingClient.siteVisits.length;
              
              // Update latest visit info
              const latestVisit = existingClient.siteVisits[existingClient.siteVisits.length - 1];
              existingClient.siteVisitId = latestVisit.id;
              existingClient.siteVisitDate = latestVisit.date;
              existingClient.siteVisitLocation = latestVisit.location;
              existingClient.siteVisitStatus = latestVisit.status;
              existingClient.marketer = latestVisit.marketer;
              existingClient.project = latestVisit.project;
            } else {
              // New client, create entry
              const siteVisitData = {
                id: siteVisit.id || 0,
                date: siteVisit.pickup_date || '',
                location: siteVisit.pickup_location || '',
                status: siteVisit.status || '',
                marketer: siteVisit.marketer || '',
                project: siteVisit.project || '',
              };

              clientsMap.set(clientKey, {
                id: clientKey,
                clientName,
                clientPhoneNumber: phone,
                clientEmail: email,
                siteVisitCount: 1,
                siteVisits: [siteVisitData],
                siteVisitId: siteVisit.id,
                siteVisitDate: siteVisit.pickup_date || '',
                siteVisitLocation: siteVisit.pickup_location || '',
                siteVisitStatus: siteVisit.status || '',
                marketer: siteVisit.marketer || '',
                project: siteVisit.project || '',
              });
            }
          }
        }
      } catch (error) {
        logger.error(`Error processing site visit ${index}`, redactError(error));
      }
    });

    const clients = Array.from(clientsMap.values());
    logger.info('Client processing completed', {
      siteVisitsProcessed: siteVisitsResponse.data.results.length,
      uniqueClientsFound: clients.length
    });
    if (clients.length > 0) {
      devLog('Sample client with site visits', redactCustomer(clients[0]));
    }
    
    return clients;
  }, [siteVisitsResponse]);

  // Enhanced debug logging to help understand the data structure
  useEffect(() => {
    devLog('Client fetching debug');
    
    if (siteVisitsResponse?.data?.results) {
      devLog('Found site visits', { count: siteVisitsResponse.data.results.length });
      devLog('Sample site visit structure', redactSiteVisit(siteVisitsResponse.data.results[0]));
      
      // Check if site visits have clients
      const firstSiteVisit = siteVisitsResponse.data.results[0];
      if (firstSiteVisit) {
        devLog('Checking client data in first site visit', {
          hasSiteVisitClient: !!firstSiteVisit.site_visit_client,
          hasClients: !!firstSiteVisit.clients,
          siteVisitKeys: Object.keys(firstSiteVisit).length
        });
        
        if (firstSiteVisit.site_visit_client && Array.isArray(firstSiteVisit.site_visit_client)) {
          devLog('First site visit has clients in site_visit_client', { count: firstSiteVisit.site_visit_client.length });
          if (firstSiteVisit.site_visit_client.length > 0) {
            devLog('Sample client from site_visit_client', redactCustomer(firstSiteVisit.site_visit_client[0]));
          }
        } else if (firstSiteVisit.clients && Array.isArray(firstSiteVisit.clients)) {
          devLog('First site visit has clients in clients array', { count: firstSiteVisit.clients.length });
          if (firstSiteVisit.clients.length > 0) {
            devLog('Sample client from clients', redactCustomer(firstSiteVisit.clients[0]));
          }
        }
      }
    } else {
      devLog('No site visits data available');
    }
    
    devLog('Extracted unique clients', { count: clientsData.length });
    if (clientsData.length > 0) {
      devLog('Sample client data', redactCustomer(clientsData[0]));
    }
    devLog('End debug');
  }, [siteVisitsResponse, clientsData]);

  // Combined loading and error states
  const isLoading = isLoadingSiteVisits;
  const hasError = siteVisitsError;

  // Since we're filtering at the API level, we can use clientsData directly
  // But keep local filtering as a fallback for additional client-side filtering
  const filteredClients = useMemo(() => {
    if (!searchTerm) return clientsData;

    return clientsData.filter(
      (client) =>
        client.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.clientPhoneNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.clientEmail.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [clientsData, searchTerm]);



  // Define columns for DataTable
  const columns: ColumnDef<ClientData>[] = useMemo(() => [
    {
      accessorKey: "clientName",
      header: "Client Name",
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
            <Users className="w-5 h-5 text-primary" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{row.getValue("clientName")}</div>
            <div className="text-sm text-gray-500">
              {row.original.siteVisitCount} site visit{row.original.siteVisitCount !== 1 ? 's' : ''}
            </div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "clientPhoneNumber",
      header: "Phone Number",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">{row.getValue("clientPhoneNumber") || 'N/A'}</div>
          <Badge variant="outline" className="text-xs">Mobile</Badge>
        </div>
      ),
    },
    {
      accessorKey: "clientEmail",
      header: "Email Address",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">{row.getValue("clientEmail") || 'N/A'}</div>
          <div className="text-gray-500">Primary contact</div>
        </div>
      ),
    },
    {
      accessorKey: "siteVisitCount",
      header: "Site Visits",
      cell: ({ row }) => (
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full font-semibold text-sm">
            {row.original.siteVisitCount}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {row.original.siteVisitCount === 1 ? 'visit' : 'visits'}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "project",
      header: "Latest Project",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">{row.original.project || 'N/A'}</div>
          <div className="text-gray-500">Marketer: {row.original.marketer || 'N/A'}</div>
        </div>
      ),
    },
    {
      accessorKey: "siteVisitStatus",
      header: "Latest Status",
      cell: ({ row }) => {
        const status = row.original.siteVisitStatus;
        const statusColors = {
          'Pending': 'bg-yellow-100 text-yellow-800',
          'Approved': 'bg-blue-100 text-blue-800',
          'In Progress': 'bg-purple-100 text-purple-800',
          'Completed': 'bg-green-100 text-green-800',
          'Cancelled': 'bg-red-100 text-red-800',
          'Rejected': 'bg-red-100 text-red-800',
          'Reviewed': 'bg-gray-100 text-gray-800',
        };
        return (
          <div className="text-sm">
            <Badge className={`${statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}>
              {status || 'N/A'}
            </Badge>
            <div className="text-xs text-gray-500 mt-1">
              {row.original.siteVisitDate ? new Date(row.original.siteVisitDate).toLocaleDateString() : 'No date'}
            </div>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedClient(row.original);
              setIsClientInfoModalOpen(true);
            }}
            className="h-8 w-8 p-0 hover:bg-blue-50"
          >
            <LucideEye className="h-4 w-4 text-blue-600" />
          </Button>
        </div>
      ),
    },
  ], []);

  // Search component for the DataTable
  const SearchComponent = () => (
    <div className="relative max-w-md">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gray-400" />
      </div>
      <Input
        type="text"
        placeholder="Search clients..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="pl-10 pr-4 py-2 border-gray-200 focus:ring-2 focus:ring-primary focus:border-primary"
      />
    </div>
  );

  return (
    <Screen>
      <div className="min-h-screen space-y-8 py-8">
        {/* Modern Header Section */}
        <Card className="border-0 shadow-lg bg-gradient-to-r from-primary/5 to-primary/10">
          <CardHeader className="pb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Site Visit Clients</CardTitle>
                  <CardDescription className="text-gray-600 mt-1">
                    View clients from site visits and their details
                  </CardDescription>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>Total Site Visits: {siteVisitsResponse?.data?.results?.length || 0}</span>
                    <span>•</span>
                    <span>Unique Clients: {clientsData.length}</span>
                    <span>•</span>
                    <span>Filtered: {filteredClients.length}</span>
                    {clientsData.length > 0 && (
                      <>
                        <span>•</span>
                        <span>Avg Visits/Client: {(clientsData.reduce((sum, client) => sum + client.siteVisitCount, 0) / clientsData.length).toFixed(1)}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <PrimaryButton
                variant="primary"
                className="flex items-center space-x-2 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all"
                onClick={() => refetchAllData()}
              >
                <Search className="w-4 h-4" />
                <span>Refresh Data</span>
              </PrimaryButton>
            </div>
          </CardHeader>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card className="border-0 shadow-lg">
            <CardContent className="text-center py-12">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8 text-primary animate-spin" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Loading clients...</h3>
              <p className="text-gray-500">Fetching client data from site visits</p>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {hasError && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Failed to load client data. Please try again later.
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchAllData()}
                className="ml-2"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Enhanced DataTable Section */}
        {!isLoading && !hasError && (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-6">
              <DataTable
                data={filteredClients}
                columns={columns}
                title="Site Visit Clients Directory"
                enableToolbar={true}
                enableExportToExcel={true}
                enablePrintPdf={true}
                enablePagination={true}
                enableColumnFilters={true}
                enableSorting={true}
                enableSelectColumn={false}
                searchInput={<SearchComponent />}
                containerClassName="border-0"
                tableClassName="border-0"
                tHeadClassName="bg-gray-50/50"
                tHeadCellsClassName="border-b border-gray-200 px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider"
                tBodyCellsClassName="px-6 py-4 whitespace-nowrap border-b border-gray-100"
                tBodyTrClassName="hover:bg-gray-50/50 transition-colors"
              />
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {!isLoading && !hasError && filteredClients.length === 0 && (
          <Card className="border-0 shadow-lg">
            <CardContent className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm
                  ? "No clients match your search criteria."
                  : siteVisitsResponse?.data?.results?.length === 0
                    ? "No site visits found. Clients are extracted from site visits."
                    : "No client information found in the site visits. This could mean that site visits don't have clients assigned yet."
                }
              </p>
              <div className="text-sm text-gray-400 mb-6">
                Total site visits: {siteVisitsResponse?.data?.results?.length || 0}
              </div>
              {!searchTerm && (
                <PrimaryButton
                  variant="primary"
                  onClick={() => refetchAllData()}
                  className="flex items-center space-x-2 mx-auto"
                >
                  <Search className="w-4 h-4" />
                  <span>Refresh Data</span>
                </PrimaryButton>
              )}
            </CardContent>
          </Card>
        )}


        {isAddModalOpen && (
          <AddClient
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onAdd={(_newClient) => {
              // For now, show a message that this feature is not available for API-sourced data
              toast("info", "Client data is sourced from site visits. Please create a site visit to add clients.");
              setIsAddModalOpen(false);
            }}
          />
        )}

        {isEditModalOpen && selectedClient && (
          <EditClient
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            client={selectedClient}
            onUpdate={(_updatedClient: { id?: any; clientName?: string; clientPhoneNumber?: string; clientEmail?: string; }) => {
              // For now, show a message that this feature is not available for API-sourced data
              toast("info", "Client data is sourced from site visits. Please update the site visit to modify client information.");
              setIsEditModalOpen(false);
            }}
          />
        )}

        {isDeleteModalOpen && selectedClient && (
          <DeleteClient
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            client={selectedClient}
            onDelete={() => {
              // For now, show a message that this feature is not available for API-sourced data
              toast("info", "Client data is sourced from site visits. Please manage clients through the site visits section.");
              setIsDeleteModalOpen(false);
              setSelectedClient(null);
            }}
          />
        )}

        {isClientInfoModalOpen && selectedClient && (
          <ClientInfo
            isOpen={isClientInfoModalOpen}
            onClose={() => setIsClientInfoModalOpen(false)}
            client={selectedClient}
            onUpdate={() => {
              // This is a view-only modal, no update functionality needed
              setIsClientInfoModalOpen(false);
            }}
          />
        )}
      </div>
    </Screen>
  );
};

export default Clients;