import { Dialog, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface DeleteClientProps {
  isOpen: boolean;
  onClose: () => void;
  client: {
    id: string;
    clientName: string;
    clientPhoneNumber: string;
    clientEmail: string;
  };
  onDelete: () => void;
}

export default function DeleteClient({ isOpen, onClose, client, onDelete }: DeleteClientProps) {
  const handleDelete = () => {
    onDelete();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Client</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the client <strong>{client.clientName}</strong>? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}