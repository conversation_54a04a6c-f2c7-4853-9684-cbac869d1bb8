
import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
//import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import React from "react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import { CalendarIcon, Car, MapPin, Clock, User, AlertCircle, Building } from "lucide-react";
import { useCreateSpecialBookingMutation, useGetVehiclesQuery, useGetDriversQuery } from "@/redux/slices/logistics";
import { useGetDepartmentsQuery } from "@/redux/slices/user";
import { toast } from "@/components/custom/Toast/MyToast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { logger, useDevLog, logSummary } from "@/utils/logger";
import { redactEmployee, redactApiResponse, redactError } from "@/utils/redactors";

export default function CreateASpecialAssignment() {
    const devLog = useDevLog();
    const [isModelXOpen, setIsModelXOpen] = React.useState(false);
    const [createSpecialBooking, { isLoading: isCreating }] = useCreateSpecialBookingMutation();
    const [assignedToOpen, setAssignedToOpen] = React.useState(false);

    // Fetch vehicles for selection
    const { data: vehiclesResp, isLoading: vehiclesLoading } = useGetVehiclesQuery({
        page: 1,
        page_size: 50,
    });

    // Fetch drivers for selection
    const { data: driversResp, isLoading: driversLoading } = useGetDriversQuery({
        page: 1,
        page_size: 50,
    });

    // Fetch departments for "Assigned To" selection
    const { data: departmentsResp, isLoading: departmentsLoading } = useGetDepartmentsQuery({
        page: 1,
        page_size: 100,
    });

    const vehicles = vehiclesResp?.data?.results || [];
    // Filter out Samuel Njuguna Njogu from drivers list
    const drivers = (driversResp?.data?.results || []).filter((driver: any) => {
        const fullName = driver.fullnames || `${driver.first_name || ''} ${driver.last_name || ''}`.trim();
        return !fullName.toLowerCase().includes('samuel') || 
               !fullName.toLowerCase().includes('njuguna') || 
               !fullName.toLowerCase().includes('njogu');
    });
    const departments = Array.isArray(departmentsResp) ? departmentsResp : [];

    // Debug log available vehicles
    React.useEffect(() => {
        if (vehicles.length > 0) {
            devLog('Available vehicles for special assignment', vehicles.slice(0, 3).map(v => ({
                id: v.id,
                make: v.make,
                model: v.model,
                registration: v.vehicle_registration,
                status: v.status
            })));
        }
    }, [vehicles]);

    const formSchema = z.object({
        assigned_to: z.string().min(1, { message: 'Assigned to is required' }).trim(),
        pickup_location: z.string().min(1, { message: 'Pickup location is required' }).trim(),
        destination: z.string().min(1, { message: 'Destination is required' }).trim(),
        reason: z.string().optional(),
        remarks: z.string().optional(),
        reservation_date: z.date(),
        reservation_time: z.string().min(1, { message: 'Time is required' }).trim(),
        driver: z.string().min(1, { message: 'Driver is required' }).trim(),
        vehicle: z.string().min(1, { message: 'Vehicle is required' }).trim(),
    })

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            assigned_to: "",
            pickup_location: "",
            destination: "",
            reason: "",
            remarks: "",
            reservation_date: new Date(),
            reservation_time: "",
            driver: "",
            vehicle: "",
        },
    })

    async function onSubmit(values: z.infer<typeof formSchema>) {
        try {
            devLog('Form submission values', { fieldCount: Object.keys(values).length });

            const formattedDate = format(values.reservation_date, "yyyy-MM-dd");

            // Validate and convert vehicle ID
            const vehicleId = parseInt(values.vehicle);
            devLog('Vehicle validation', {
                originalValue: values.vehicle,
                parsedValue: vehicleId,
                isNaN: isNaN(vehicleId),
                isValid: !isNaN(vehicleId) && vehicleId > 0
            });

            if (isNaN(vehicleId) || vehicleId <= 0) {
                toast.error("❌ Please select a valid vehicle.");
                logger.error('Invalid vehicle ID', { vehicleId: values.vehicle });
                return;
            }

            const payload = {
                ...values,
                reservation_date: formattedDate,
                vehicle_id: vehicleId, // API expects vehicle_id, not vehicle
                driver_id: values.driver, // API expects driver_id, not driver
                assigned_to: values.assigned_to, // Department ID as string
                status: 'Approved', // Default status - special assignments are auto-approved
            };

            // Remove the old field names to avoid confusion
            delete payload.vehicle;
            delete payload.driver;

            devLog("Full payload being sent to API", { fieldCount: Object.keys(payload).length });
            devLog("Field mapping", {
                vehicle_id: payload.vehicle_id,
                driver_id: payload.driver_id,
                types: {
                    vehicle_id: typeof payload.vehicle_id,
                    driver_id: typeof payload.driver_id
                },
                validation: {
                    originalVehicle: values.vehicle,
                    parsedVehicleId: vehicleId,
                    isValid: !isNaN(vehicleId) && vehicleId > 0
                }
            });

            const result = await createSpecialBooking(payload).unwrap();
            logger.info("Special booking created successfully", { bookingId: result.id });

            // Check if vehicle was saved properly
            if (result.vehicle === null && payload.vehicle_id) {
                logger.warn("Vehicle was not saved properly in the backend", {
                  sentVehicleId: payload.vehicle_id,
                  receivedVehicle: result.vehicle
                });
                toast.success("✅ Special assignment booked successfully! (Note: Vehicle assignment may need backend fix)");
            } else {
                toast.success("✅ Special assignment booked successfully!");
            }

            setIsModelXOpen(false);
            form.reset();
        } catch (error: any) {
            logger.error("Failed to create special booking", redactError(error));

            if (error?.data?.vehicle) {
                toast.error("❌ Invalid vehicle selected. Please try again.");
            } else if (error?.data?.reservation_date) {
                toast.error("❌ Invalid reservation date. Please select a valid date.");
            } else if (error?.data?.reservation_time) {
                toast.error("❌ Invalid reservation time. Please select a valid time.");
            } else {
                toast.error("❌ Failed to book special assignment. Please check your input and try again.");
            }
        }
    }

    return <>
        <Button
            onClick={() => setIsModelXOpen(true)}
            className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
        >
            <Car className="w-4 h-4 mr-2" />
            Book Assignment
        </Button>

        <BaseModal
            className="!p-0"
            size="xl"
            isOpen={isModelXOpen}
            onOpenChange={setIsModelXOpen}
            title="Create Special Assignment"
            description="Book a special transportation assignment"
        >
            <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
                <Card className="border-0 shadow-lg">
                    <CardHeader className="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-t-lg">
                        <CardTitle className="flex items-center text-xl">
                            <Car className="w-6 h-6 mr-3" />
                            Special Assignment Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6">
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="assigned_to"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-col">
                                                <FormLabel className="flex items-center text-sm font-medium">
                                                    <Building className="w-4 h-4 mr-2 text-orange-500" />
                                                    Assigned To Department *
                                                </FormLabel>
                                                <Popover open={assignedToOpen} onOpenChange={setAssignedToOpen}>
                                                    <PopoverTrigger asChild>
                                                        <FormControl>
                                                            <Button
                                                                variant="outline"
                                                                role="combobox"
                                                                aria-expanded={assignedToOpen}
                                                                className={cn(
                                                                    "justify-between border-2 border-gray-200 focus:border-orange-500 rounded-lg",
                                                                    !field.value && "text-muted-foreground"
                                                                )}
                                                            >
                                                                {field.value
                                                                    ? String(departments.find((dept: any) => dept?.dp_id === parseInt(field.value))?.dp_name || field.value)
                                                                    : departmentsLoading
                                                                    ? "Loading departments..."
                                                                    : "Select department..."}
                                                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                            </Button>
                                                        </FormControl>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-[300px] p-0">
                                                        <Command>
                                                            <CommandInput placeholder="Search departments..." />
                                                            <CommandEmpty>
                                                                {departmentsLoading ? (
                                                                    <div className="flex items-center justify-center py-4">
                                                                        <Building className="w-4 h-4 animate-spin mr-2" />
                                                                        <span className="text-sm text-gray-500">Loading departments...</span>
                                                                    </div>
                                                                ) : (
                                                                    "No department found."
                                                                )}
                                                            </CommandEmpty>
                                                            <CommandGroup>
                                                                {!departmentsLoading && departments.map((department: any) => {
                                                                    if (!department || !department.dp_id) return null;
                                                                    return (
                                                                        <CommandItem
                                                                            key={department.dp_id}
                                                                            value={`${String(department.dp_name || '')} ${String(department.dep_head_name || '')}`}
                                                                            onSelect={() => {
                                                                                form.setValue("assigned_to", String(department.dp_id || ''));
                                                                                setAssignedToOpen(false);
                                                                            }}
                                                                        >
                                                                            <Check
                                                                                className={cn(
                                                                                    "mr-2 h-4 w-4",
                                                                                    field.value === String(department.dp_id) ? "opacity-100" : "opacity-0"
                                                                                )}
                                                                            />
                                                                            <div className="flex items-center space-x-2">
                                                                                <Building className="w-4 h-4 text-orange-600" />
                                                                                <div>
                                                                                    <div className="font-medium">{String(department.dp_name || 'Unknown Department')}</div>
                                                                                    <div className="text-xs text-gray-500">
                                                                                        Head: {String(department.dep_head_name || 'No head assigned')}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </CommandItem>
                                                                    );
                                                                })}
                                                            </CommandGroup>
                                                        </Command>
                                                    </PopoverContent>
                                                </Popover>
                                                <FormMessage className="text-xs" />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="driver"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-sm font-medium">
                                                    <User className="w-4 h-4 mr-2 text-orange-500" />
                                                    Driver *
                                                </FormLabel>
                                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                    <FormControl>
                                                        <SelectTrigger className="border-2 border-gray-200 focus:border-orange-500 rounded-lg">
                                                            <SelectValue placeholder={
                                                                driversLoading
                                                                    ? "Loading drivers..."
                                                                    : "Select a driver"
                                                            } />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {driversLoading && (
                                                            <div className="flex items-center justify-center py-4">
                                                                <User className="w-4 h-4 animate-spin mr-2" />
                                                                <span className="text-sm text-gray-500">Loading drivers...</span>
                                                            </div>
                                                        )}
                                                        {!driversLoading && drivers.length === 0 && (
                                                            <div className="flex items-center justify-center py-4">
                                                                <AlertCircle className="w-4 h-4 text-gray-400 mr-2" />
                                                                <span className="text-sm text-gray-500">No drivers available</span>
                                                            </div>
                                                        )}
                                                        {!driversLoading && drivers.map((driver: any) => (
                                                            <SelectItem key={driver.id} value={driver.employee_no}>
                                                                <div className="flex items-center space-x-2">
                                                                    <User className="w-4 h-4 text-orange-600" />
                                                                    <div>
                                                                        <div className="font-medium">{driver.fullnames}</div>
                                                                        <div className="text-xs text-gray-500">
                                                                            {driver.employee_no} • {driver.phone_number || 'No phone'}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage className="text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="pickup_location"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-sm font-medium">
                                                    <MapPin className="w-4 h-4 mr-2 text-green-500" />
                                                    Pickup Location *
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="e.g., ABSA Towers, Westlands"
                                                        {...field}
                                                        className="border-2 border-gray-200 focus:border-green-500 rounded-lg"
                                                    />
                                                </FormControl>
                                                <FormMessage className="text-xs" />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="destination"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-sm font-medium">
                                                    <MapPin className="w-4 h-4 mr-2 text-red-500" />
                                                    Destination *
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="e.g., Nakuru, Eldoret"
                                                        {...field}
                                                        className="border-2 border-gray-200 focus:border-red-500 rounded-lg"
                                                    />
                                                </FormControl>
                                                <FormMessage className="text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="reason"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center text-sm font-medium">
                                                <AlertCircle className="w-4 h-4 mr-2 text-blue-500" />
                                                Reason for Assignment
                                            </FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="Describe the purpose of this special assignment..."
                                                    className="resize-none border-2 border-gray-200 focus:border-blue-500 rounded-lg min-h-[80px]"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage className="text-xs" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="remarks"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center text-sm font-medium">
                                                <AlertCircle className="w-4 h-4 mr-2 text-purple-500" />
                                                Additional Remarks
                                            </FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="Any additional notes or special instructions..."
                                                    className="resize-none border-2 border-gray-200 focus:border-purple-500 rounded-lg min-h-[60px]"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage className="text-xs" />
                                        </FormItem>
                                    )}
                                />

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <FormField
                                        control={form.control}
                                        name="reservation_date"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-col">
                                                <FormLabel className="flex items-center text-sm font-medium">
                                                    <CalendarIcon className="w-4 h-4 mr-2 text-blue-500" />
                                                    Reservation Date *
                                                </FormLabel>
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <FormControl>
                                                            <Button
                                                                variant={"outline"}
                                                                className={cn(
                                                                    "pl-3 text-left font-normal border-2 border-gray-200 focus:border-blue-500 rounded-lg",
                                                                    !field.value && "text-muted-foreground"
                                                                )}
                                                            >
                                                                {field.value ? (
                                                                    format(field.value, "PPP")
                                                                ) : (
                                                                    <span>Pick a date</span>
                                                                )}
                                                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                                            </Button>
                                                        </FormControl>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-auto p-0" align="start">
                                                        <Calendar
                                                            mode="single"
                                                            selected={field.value}
                                                            onSelect={field.onChange}
                                                            disabled={(date) =>
                                                                date < new Date()
                                                            }
                                                            initialFocus
                                                        />
                                                    </PopoverContent>
                                                </Popover>
                                                <FormMessage className="text-xs" />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="reservation_time"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-sm font-medium">
                                                    <Clock className="w-4 h-4 mr-2 text-green-500" />
                                                    Reservation Time *
                                                </FormLabel>
                                                <FormControl>
                                                    <input
                                                        {...field}
                                                        className="border-2 border-gray-200 focus:border-green-500 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-200"
                                                        type="time"
                                                    />
                                                </FormControl>
                                                <FormMessage className="text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                </div>




                                <FormField
                                    control={form.control}
                                    name="vehicle"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center text-sm font-medium">
                                                <Car className="w-4 h-4 mr-2 text-blue-500" />
                                                Assign Vehicle *
                                            </FormLabel>
                                            <Select
                                                onValueChange={(value) => {
                                                    devLog('Vehicle selected', { vehicleId: value });
                                                    field.onChange(value);
                                                }}
                                                defaultValue={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="border-2 border-gray-200 focus:border-blue-500 rounded-lg">
                                                        <SelectValue placeholder={
                                                            vehiclesLoading
                                                                ? "Loading vehicles..."
                                                                : "Select a vehicle"
                                                        } />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {vehiclesLoading && (
                                                        <div className="flex items-center justify-center py-4">
                                                            <Car className="w-4 h-4 animate-spin mr-2" />
                                                            <span className="text-sm text-gray-500">Loading vehicles...</span>
                                                        </div>
                                                    )}
                                                    {!vehiclesLoading && vehicles.length === 0 && (
                                                        <div className="flex items-center justify-center py-4">
                                                            <AlertCircle className="w-4 h-4 text-gray-400 mr-2" />
                                                            <span className="text-sm text-gray-500">No vehicles available</span>
                                                        </div>
                                                    )}
                                                    {!vehiclesLoading && vehicles.map((vehicle: any) => (
                                                        <SelectItem key={vehicle.id} value={vehicle.id.toString()}>
                                                            <div className="flex items-center space-x-2">
                                                                <Car className="w-4 h-4 text-blue-600" />
                                                                <div>
                                                                    <div className="font-medium">{vehicle.vehicle_registration}</div>
                                                                    <div className="text-xs text-gray-500">
                                                                        {vehicle.make} {vehicle.model} • {vehicle.status}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-xs" />
                                        </FormItem>
                                    )}
                                />


                                <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsModelXOpen(false)}
                                        className="px-6"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isCreating}
                                        className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8"
                                    >
                                        {isCreating ? (
                                            <>
                                                <Car className="w-4 h-4 mr-2 animate-spin" />
                                                Creating...
                                            </>
                                        ) : (
                                            <>
                                                <Car className="w-4 h-4 mr-2" />
                                                Create Assignment
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </BaseModal>
    </>
}



