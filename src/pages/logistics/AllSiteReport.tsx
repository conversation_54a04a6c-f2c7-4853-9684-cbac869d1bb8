import React, { useMemo } from "react";
import { Card6 } from "@/components/custom/cards/Card6";
import { Screen } from "@/app-components/layout/screen";
import { DataTable } from "@/components/custom/tables/Table1";
import Tab1 from "@/components/custom/tabs/Tab1";
import { PersonStanding } from "lucide-react";
import { useGetSiteVisitsQuery } from "@/redux/slices/logistics";

function AllSiteReport() {
  // fetch from API
  const { data, error, isLoading } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 10,
    ordering: '-created_at,-pickup_date,-id' // Descending order (most recent first)
  });

  // normalize response to an array
  const visitsData = useMemo(() => {
    if (!data) return [];
    if (Array.isArray(data)) return data;
    if ("results" in data && Array.isArray((data as any).results)) {
      return (data as any).results;
    }
    return [];
  }, [data]);

  // derive counts for cards
  const totalVisits = visitsData.length;
  interface SiteVisit {
    status: 'Pending' | 'Approved' | 'Enroute' | 'Complete' | 'Rejected' | 'Canceled';
    marketer: string;
    project: string;
    pickup_location: string;
    driver: string;
    pickup_date: string;
  }

  interface ApiResponse {
    results: SiteVisit[];
  }
  const pendingVisits: number = visitsData.filter((v: SiteVisit) => v.status === "Pending").length;
  const approvedVisits: number = visitsData.filter((v: SiteVisit) => v.status === "Approved").length;
  const enrouteVisits: number = visitsData.filter((v: SiteVisit) => v.status === "Enroute").length;
  const completedVisits: number = visitsData.filter((v: SiteVisit) => v.status === "Complete").length;
  const rejectedVisits: number = visitsData.filter((v: SiteVisit) => v.status === "Rejected").length;

  // table column definitions
  const visitsColumns = useMemo(
    () => [
      { header: "Clients", accessorKey: "clients" },
      { header: "Marketer", accessorKey: "marketer" },
      { header: "Site Name", accessorKey: "project" },
      { header: "Pickup Location", accessorKey: "pickup_location" },
      { header: "Chauffeur", accessorKey: "driver" },
      {
        header: "Date",
        accessorKey: "pickup_date",
        cell: ({ row }: any) => new Date(row.original.pickup_date).toLocaleDateString("en-CA"),
      },
      {
        id: "status",
        header: "Status",
        cell: ({ row }: any) => {
          const s = row.original.status as string;
          let badgeClass = "px-2 py-1 rounded-full text-xs font-medium";
          if (s === "Complete") {
            badgeClass += " bg-green-500 text-white animate-pulse";
          } else if (s === "Pending") {
            badgeClass += " border border-gray-300 text-gray-600";
          } else if (s === "Rejected") {
            badgeClass += " bg-red-500 text-white";
          } else if (s === "Canceled") {
            badgeClass += " bg-gray-200 text-gray-800";
          }
          return <span className={badgeClass}>{s}</span>;
        },
      },
    ],
    []
  );

  // summary cards config
  const stats = [
    {
      title: "All Site Visits",
      value: totalVisits,
      cardBg: "bg-gradient-to-br from-yellow-100 to-orange-100",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-500",
      change: "+5%",
      changeLabel: "this week",
      positive: true,
    },
    {
      title: "Pending Site Visits",
      value: pendingVisits,
      cardBg: "bg-gradient-to-br from-beige-50 to-gray-100",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-500",
      change: "+2%",
      changeLabel: "this week",
      positive: false,
    },
    {
      title: "Approved Site Visits",
      value: approvedVisits,
      cardBg: "bg-gradient-to-br from-blue-100 to-yellow-100",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-500",
      change: "+8%",
      changeLabel: "this week",
      positive: true,
    },
    {
      title: "Enroute Site Visits",
      value: enrouteVisits,
      cardBg: "bg-gradient-to-br from-green-100 to-yellow-100",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-500",
      change: "+3%",
      changeLabel: "this week",
      positive: true,
    },
    {
      title: "Completed Site Visits",
      value: completedVisits,
      cardBg: "bg-gradient-to-br from-blue-100 to-cyan-100",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-500",
      change: "+12%",
      changeLabel: "this week",
      positive: true,
    },
    {
      title: "Rejected Site Visits",
      value: rejectedVisits,
      cardBg: "bg-gradient-to-br from-brown-100 to-indigo-100",
      iconBg: "bg-blue-100",
      iconColor: "text-red-500",
      change: "+1%",
      changeLabel: "this week",
      positive: false,
    },
  ];

  // tabbed tables
  interface TabItem {
    value: string;
    title: string;
    content: React.ReactNode;
  }

  interface CommonTableProps {
    tableClassName: string;
    tHeadClassName: string;
    tBodyTrClassName: string;
    tBodyCellsClassName: string;
  }

  const commonTableProps: CommonTableProps = {
    tableClassName: "border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-md w-full text-sm sm:text-base",
    tHeadClassName: "bg-gray-50 dark:bg-gray-700 text-xs uppercase text-gray-600 dark:text-gray-300 font-semibold sticky top-0 z-10",
    tBodyTrClassName: "hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150",
    tBodyCellsClassName: "border-t border-gray-200 dark:border-gray-600 py-2 sm:py-4 px-2 sm:px-4 max-sm:px-1"
  };

  const tabs: TabItem[] = useMemo(
    () => [
      {
        value: "AllSiteVisits",
        title: "All Site Visits",
        content: (
          <DataTable
            data={visitsData}
            columns={visitsColumns}
            enableToolbar
            enablePagination
            {...commonTableProps}
          />
        ),
      },
      {
        value: "PendingSiteVisits",
        title: "Pending Site Visits",
        content: (
          <DataTable
            data={visitsData.filter((v: SiteVisit) => v.status === "Pending")}
            columns={visitsColumns}
            enableToolbar
            enablePagination
            {...commonTableProps}
          />
        ),
      },
      {
        value: "ApprovedSiteVisits",
        title: "Approved Site Visits",
        content: (
          <DataTable
            data={visitsData.filter((v: SiteVisit) => v.status === "Approved")}
            columns={visitsColumns}
            enableToolbar
            enablePagination
            {...commonTableProps}
          />
        ),
      },
      {
        value: "EnrouteSiteVisits", 
        title: "Enroute Site Visits",
        content: (
          <DataTable
            data={visitsData.filter((v: SiteVisit) => v.status === "Enroute")}
            columns={visitsColumns}
            enableToolbar
            enablePagination
            {...commonTableProps}
          />
        ),
      },
      {
        value: "CompletedSiteVisits",
        title: "Completed Site Visits", 
        content: (
          <DataTable
            data={visitsData.filter((v: SiteVisit) => v.status === "Complete")}
            columns={visitsColumns}
            enableToolbar
            enablePagination
            {...commonTableProps}
          />
        ),
      },
      {
        value: "RejectedSiteVisits",
        title: "Rejected Site Visits",
        content: (
          <DataTable
            data={visitsData.filter((v: SiteVisit) => v.status === "Rejected")}
            columns={visitsColumns}
            enableToolbar
            enablePagination
            {...commonTableProps}
          />
        ),
      },
    ],
    [visitsData, visitsColumns]
  );

  return (
    <div>
      <Screen>
        <div className="flex justify-between items-center my-6">
          <h1 className="text-3xl font-bold ml-4">Site Visits Report</h1>
        </div>

        {isLoading ? (
          <p>Loading site visits…</p>
        ) : error ? (
          <p className="text-red-600">Error loading site visits.</p>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-6 my-6">
              {stats.map((card, idx) => (
                <Card6
                  key={idx}
                  title={card.title}
                  value={String(card.value)}
                  icon={PersonStanding}
                  iconBg={card.iconBg}
                  iconColor={card.iconColor}
                  change={card.change}
                  changeLabel={card.changeLabel}
                  positive={card.positive}
                  cardBg={card.cardBg}
                />
              ))}
            </div>

            <Tab1
              tabs={tabs}
              defaultValue="AllSiteVisits"
              className="rounded-lg shadow-md border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800"
              TabsListClassName="flex bg-gray-50 dark:bg-gray-700 rounded-t-lg border-b border-gray-200 dark:border-gray-600"
              TabsTriggerClassName="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"
              TabsContentClassName="p-4"
            />
          </>
        )}
      </Screen>
    </div>
  );
}

export default AllSiteReport;
