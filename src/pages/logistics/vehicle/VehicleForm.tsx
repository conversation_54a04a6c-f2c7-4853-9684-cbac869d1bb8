import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, Edit, PenTool, Loader, Pen } from "lucide-react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import BaseModal from "@/components/custom/modals/BaseModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  useCreateVehicleMutation,
  useGetVehicleQuery,
  useUpdateVehicleMutation,
  useGetDriversQuery,
} from "@/redux/slices/logistics";
import { logger, useDevLog, logSummary } from "@/utils/logger";
import { redactVehicle, redactApiResponse, redactError } from "@/utils/redactors";
import { toast } from "@/components/custom/Toast/MyToast";
import { useAuthHook } from "@/utils/useAuthHook";
import { VehicleData } from "./VehicleDetails";

// Component props
interface VehicleFormProps {
  mode: "create" | "edit";
  vehicleData?: VehicleData;
  onSuccess?: (data: VehicleData) => void;
  onCancel?: () => void;
  trigger?: React.ReactNode; // Custom trigger element
}

function VehicleForm({
  mode = "create",
  vehicleData,
  onSuccess,
  onCancel,
  trigger,
}: VehicleFormProps) {
  const devLog = useDevLog();
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const formSchema = z.object({
    make: z.string().min(1, { message: "Make is required" }).trim(),
    model: z.string().min(1, { message: "Model is required" }).trim(),
    body_type: z.string().min(1, { message: "Body type is required" }).trim(),
    number_of_seats: z
      .string()
      .min(1, { message: "Number of seats is required" })
      .refine((val) => !isNaN(parseInt(val, 10)) && parseInt(val, 10) > 0, {
        message: "Number of seats must be a positive number",
      }),
    engine_capacity: z
      .string()
      .min(1, { message: "Engine capacity is required" })
      .trim(),
    vehicle_registration: z
      .string()
      .min(1, { message: "Vehicle registration is required" })
      .max(20, { message: "Vehicle registration must be 20 characters or less" })
      .trim(),
    passengers_assigned: z.boolean().default(false),
    status: z.string().min(1, { message: "Status is required" }).trim(),
    driver: z.string().optional(),
  });

  // Initialize the form with default values based on mode
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues:
      mode === "edit" && vehicleData
        ? {
            make: vehicleData.make,
            model: vehicleData.model,
            body_type: vehicleData.body_type,
            number_of_seats: vehicleData.number_of_seats.toString(),
            engine_capacity: vehicleData.engine_capacity,
            vehicle_registration: vehicleData.vehicle_registration,
            passengers_assigned: vehicleData.passengers_assigned || false,
            status: vehicleData.status,
            driver: vehicleData.driver || "",
          }
        : {
            make: "",
            model: "",
            body_type: "",
            number_of_seats: "",
            engine_capacity: "",
            vehicle_registration: "",
            passengers_assigned: false,
            status: "Available",
            driver: "",
          },
  });

  // // Reset form when mode or vehicleData changes
  // React.useEffect(() => {
  //     if (mode === 'edit' && vehicleData) {
  //         form.reset({
  //             make: vehicleData.make,
  //             model: vehicleData.model,
  //             body_type: vehicleData.body_type,
  //             number_of_seats: vehicleData.number_of_seats,
  //             engine_capacity: vehicleData.engine_capacity,
  //             vehicle_registration: vehicleData.vehicle_registration,
  //              passengers_assigned: vehicleData.passengers_assigned,
  //             status: vehicleData.status,
  //             driver: vehicleData.driver,
  //         });
  //     } else {
  //         form.reset({
  //             make: '',
  //             model: '',
  //             body_type: '',
  //             number_of_seats: '',
  //             engine_capacity: '',
  //             vehicle_registration: '',
  //             status: '',
  //             driver: '',
  //         });
  //     }
  // }, [mode, vehicleData, form]);

  const [createVehicle, { isLoading: createVehicleIsLoading }] =
    useCreateVehicleMutation();
  const [patchVehicle, { isLoading: patchVehicleIsLoading }] =
    useUpdateVehicleMutation();
  const { data: selectedVehicleData, isLoading: selectedVehicleDataIsLoading } =
    useGetVehicleQuery(Number(vehicleData?.id), {
      skip: !vehicleData?.id || mode === "create"
    });
  const { data: driversData } = useGetDriversQuery({});

  // Get current user details for fallback
  const { user_details } = useAuthHook();
  React.useEffect(() => {
    if (selectedVehicleData && mode === "edit") {
      form.reset({
        make: selectedVehicleData.make,
        model: selectedVehicleData.model,
        body_type: selectedVehicleData.body_type,
        number_of_seats: selectedVehicleData.number_of_seats.toString(),
        engine_capacity: selectedVehicleData.engine_capacity,
        vehicle_registration: selectedVehicleData.vehicle_registration,
        passengers_assigned: selectedVehicleData.passengers_assigned || false,
        status: selectedVehicleData.status,
        driver: selectedVehicleData.driver || "",
      });
      setSelectedValue(selectedVehicleData.passengers_assigned || false);
    }
  }, [selectedVehicleData, mode, form]);

  const [selectedValue, setSelectedValue] = useState<boolean>(
    vehicleData?.passengers_assigned || false
  );

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      // Prepare the data according to API specification
      const submitData = {
        ...values,
        number_of_seats: parseInt(values.number_of_seats, 10), // Convert to integer
        passengers_assigned: selectedValue, // Use the state value for passengers_assigned
        // Only include driver if it's not "none" and not empty
        ...(values.driver && values.driver !== "none" && { driver: values.driver }),
      };

      devLog("Submitting vehicle data", { fieldCount: Object.keys(submitData).length });

      if (mode === "create") {
        try {
          const response = await createVehicle(submitData).unwrap();
          toast.success("Vehicle created successfully");
          setIsModalOpen(false);
          form.reset();
          setSelectedValue(false); // Reset passengers_assigned state
        } catch (error: any) {
          logger.error("Create vehicle error", redactError(error));
          toast.error(
            error?.data?.message ||
            "Error creating vehicle. Please try again later."
          );
        }
      } else {
        try {
          if (!vehicleData?.id) {
            toast.error("Vehicle ID is required for updating.");
            return;
          }
          const response = await patchVehicle({
            id: vehicleData.id,
            ...submitData,
          }).unwrap();
          toast.success("Vehicle updated successfully");
          setIsModalOpen(false);
        } catch (error: any) {
          logger.error("Update vehicle error", redactError(error));
          toast.error(
            error?.data?.message ||
            "Error updating vehicle. Please try again later."
          );
        }
      }
    } catch (error) {
      logger.error("Error submitting vehicle form", redactError(error));
      toast.error("An unexpected error occurred. Please try again.");
    }
  }

  // Handle modal close
  const handleCloseModal = () => {
    setIsModalOpen(false);
    form.reset();
    setSelectedValue(false); // Reset passengers_assigned state
    if (onCancel) {
      onCancel();
    }
  };

  // Default trigger button if none provided
  const defaultTrigger =
    mode === "create" ? (
      <button
        onClick={() => setIsModalOpen(true)}
        className="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-md shadow-sm text-sm hover:bg-primary/95"
      >
        <Plus size={16} />
        <span>Create Vehicle</span>
      </button>
    ) : (
      <div onClick={() => setIsModalOpen(true)} className="flex justify-center">
        <Pen size={16} />
      </div>
    );

  return (
    <>
      {/* Render custom trigger or default button */}
      {trigger ? (
        <div onClick={() => setIsModalOpen(true)}>{trigger}</div>
      ) : (
        defaultTrigger
      )}

      <BaseModal
        className="py-6 dark:bg-background"
        size="2xl"
        title={mode === "create" ? "Create Vehicle" : "Update Vehicle Details"}
        isOpen={isModalOpen}
        onOpenChange={(open) => {
          if (!open) handleCloseModal();
          else setIsModalOpen(open);
        }}
      >
        <div className="pb-10">
          {selectedVehicleDataIsLoading && mode == "edit" ? (
            <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-70">
              <div className="w-12 h-12 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin"></div>
            </div>
          ) : (
            ""
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 gap-y-3 w-full">
                <FormField
                  control={form.control}
                  name="make"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="!text-foreground">Make</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Nissan"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="!text-foreground">Model</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Hiace"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="body_type"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="!text-foreground">
                        Body type
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Wagon"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="number_of_seats"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="!text-foreground">
                        Number of seats
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="10"
                          type="number"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="engine_capacity"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="!text-foreground">
                        Engine capacity
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="2000cc"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="vehicle_registration"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="!text-foreground">
                        Vehicle registration
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="KDE 111X"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />
                <div className="space-y-2">
                  <label className="text-sm">Passengers assigned</label>
                  <Select
                    value={selectedValue ? "true" : "false"}
                    onValueChange={(val) => {
                      setSelectedValue(val === "true");
                    }}
                  >
                    <FormControl>
                      <SelectTrigger className="border border-accent !rounded-sm bg-none focus:!ring-0">
                        <SelectValue placeholder="Select passengers assigned" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">True</SelectItem>
                      <SelectItem value="false">False</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="!text-foreground">Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="border border-accent !rounded-sm bg-none focus:!ring-0">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Available">Available</SelectItem>
                          <SelectItem value="In Use">In Use</SelectItem>
                          <SelectItem value="Under Maintenance">
                            Under Maintenance
                          </SelectItem>
                          <SelectItem value="Out of Service">
                            Out of Service
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="driver"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel className="!text-foreground">Driver (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="border border-accent !rounded-sm bg-none focus:!ring-0">
                            <SelectValue placeholder="Select driver (optional)" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No driver assigned</SelectItem>
                          {driversData?.data?.results?.length ? (
                            driversData.data.results.map((driver: any) => (
                              driver.employee_no ? (
                                <SelectItem
                                  key={driver.id}
                                  value={driver.employee_no}
                                >
                                  {driver.fullnames} ({driver.employee_no})
                                </SelectItem>
                              ) : null
                            )).filter(Boolean)
                          ) : (
                            user_details?.employee_no ? (
                              <SelectItem value={user_details.employee_no}>
                                {user_details?.fullnames || user_details?.employee_no} (Current User)
                              </SelectItem>
                            ) : null
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75 " />
                    </FormItem>
                  )}
                />

                <PrimaryButton type="submit" className="w-full md:col-span-2 ">
                  {createVehicleIsLoading || patchVehicleIsLoading ? (
                    <span className="flex gap-2 items-center justify-center">
                      <Loader
                        className="fas fa-spinner animate-spin"
                        size={20}
                      />
                      Loading...
                    </span>
                  ) : mode === "create" ? (
                    "Create Vehicle"
                  ) : (
                    "Update Vehicle"
                  )}
                  {/* {mode === 'create' ? 'Create Vehicle' : 'Update Vehicle'} */}
                </PrimaryButton>
              </div>
            </form>
          </Form>
        </div>
      </BaseModal>
    </>
  );
}

export default VehicleForm;
