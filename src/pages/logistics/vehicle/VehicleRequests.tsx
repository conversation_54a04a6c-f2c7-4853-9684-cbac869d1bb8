import React, { useState } from "react";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import {
  useDeleteVehicleRequestMutation,
  useGetVehicleRequestsQuery,
  useUpdateVehicleRequestMutation,
  useGetDriversQuery,
} from "@/redux/slices/logistics";
import { logger, useDevLog, logSummary } from "@/utils/logger";
import { redactVehicle, redactApiResponse, redactError } from "@/utils/redactors";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import VehicleRequestForm from "./VehicleRequestForm";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import {
  Trash,
  Eye,
  CheckCircle,
  XCircle,
  Play,
  Square,
  User,
  Calendar,
  Clock,
  MapPin,
  Users,
  Car
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import BaseModal from "@/components/custom/modals/BaseModal";
import { format } from "date-fns";

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "Pending":
        return { color: "bg-yellow-100 text-yellow-800 border-yellow-200", icon: Clock };
      case "Approved":
        return { color: "bg-blue-100 text-blue-800 border-blue-200", icon: CheckCircle };
      case "In Progress":
        return { color: "bg-purple-100 text-purple-800 border-purple-200", icon: Play };
      case "Trip Completed":
        return { color: "bg-green-100 text-green-800 border-green-200", icon: CheckCircle };
      case "Rejected":
        return { color: "bg-red-100 text-red-800 border-red-200", icon: XCircle };
      default:
        return { color: "bg-gray-100 text-gray-800 border-gray-200", icon: Clock };
    }
  };

  const config = getStatusConfig(status);
  const IconComponent = config.icon;

  return (
    <Badge className={`${config.color} border font-medium`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {status}
    </Badge>
  );
};

// Vehicle Request View Modal Component
const VehicleRequestViewModal = ({
  isOpen,
  onClose,
  request,
  onStatusUpdate,
  onDelete
}: {
  isOpen: boolean;
  onClose: () => void;
  request: any;
  onStatusUpdate: (id: number, status: string, driverId?: string) => void;
  onDelete: (id: number) => void;
}) => {
  const [selectedDriver, setSelectedDriver] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { data: driversData } = useGetDriversQuery({});

  const handleStatusUpdate = async (status: string, driverId?: string) => {
    if (status === "Approved" && !selectedDriver) {
      toast.error("Please select a driver before approving the request");
      return;
    }

    setIsUpdating(true);
    try {
      await onStatusUpdate(request.id, status, driverId || selectedDriver);
      if (status === "Approved") {
        setSelectedDriver("");
      }
    } finally {
      setIsUpdating(false);
    }
  };

  const canApprove = request?.status === "Pending";
  const canStart = request?.status === "Approved";
  const canComplete = request?.status === "In Progress";
  const canReject = request?.status === "Pending";

  if (!request) return null;

  return (
    <>
      <BaseModal
        isOpen={isOpen}
        onOpenChange={onClose}
        size="2xl"
        title="Vehicle Request Details"
      >
        <div className="p-6 max-h-[80vh] overflow-y-auto">
          <div className="flex justify-between items-start mb-6">
            <div>
              <p className="text-gray-600">Request ID: {request.id}</p>
            </div>
            <StatusBadge status={request.status} />
          </div>

          {/* Request Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Car className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Vehicle</p>
                  <p className="font-medium">{request.vehicle?.vehicle_registration || "No Vehicle"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <MapPin className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">From</p>
                  <p className="font-medium">{request.pickup_location}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <MapPin className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">To</p>
                  <p className="font-medium">{request.destination_location}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Passengers</p>
                  <p className="font-medium">{request.number_of_passengers}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Date</p>
                  <p className="font-medium">
                    {format(new Date(request.pickup_date), "MMM dd, yyyy")}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Time</p>
                  <p className="font-medium">{request.pickup_time}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="w-5 h-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Requester</p>
                  <p className="font-medium">
                    {typeof request.requester === 'string'
                      ? request.requester
                      : (request.requester?.first_name && request.requester?.last_name)
                        ? `${request.requester.first_name} ${request.requester.last_name}`
                        : request.requester?.fullnames || 'Unknown'}
                  </p>
                </div>
              </div>

              {request.driver && (
                <div className="flex items-center gap-3">
                  <User className="w-5 h-5 text-primary" />
                  <div>
                    <p className="text-sm text-gray-600">Driver</p>
                    <p className="font-medium">
                      {typeof request.driver === 'string'
                        ? request.driver
                        : request.driver?.fullnames ||
                          (request.driver?.first_name && request.driver?.last_name)
                            ? `${request.driver.first_name} ${request.driver.last_name}`
                            : 'Unknown Driver'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Purpose and Remarks */}
          {request.purpose && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-1">Purpose</p>
              <p className="font-medium bg-gray-50 p-3 rounded">{request.purpose}</p>
            </div>
          )}

          {request.remarks && (
            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-1">Remarks</p>
              <p className="bg-gray-50 p-3 rounded">{request.remarks}</p>
            </div>
          )}

          {/* Driver Assignment Section */}
          {canApprove && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-3">Assign Driver to Approve Request</h4>
              <Select value={selectedDriver} onValueChange={setSelectedDriver}>
                <SelectTrigger className="mb-3">
                  <SelectValue placeholder="Select a driver to approve request" />
                </SelectTrigger>
                <SelectContent>
                  {driversData?.data?.results?.map((driver: any) => (
                    <SelectItem key={driver.id} value={driver.employee_no}>
                      {driver.fullnames} ({driver.employee_no})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-blue-700">
                A driver must be assigned before the request can be approved.
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 pt-4 border-t">
            {canApprove && (
              <Button
                onClick={() => handleStatusUpdate("Approved")}
                disabled={isUpdating || !selectedDriver}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                {isUpdating ? "Approving..." : "Approve Request"}
              </Button>
            )}

            {canStart && (
              <Button
                onClick={() => handleStatusUpdate("In Progress")}
                disabled={isUpdating}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Play className="w-4 h-4 mr-2" />
                {isUpdating ? "Starting..." : "Start Trip"}
              </Button>
            )}

            {canComplete && (
              <Button
                onClick={() => handleStatusUpdate("Trip Completed")}
                disabled={isUpdating}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Square className="w-4 h-4 mr-2" />
                {isUpdating ? "Completing..." : "Complete Trip"}
              </Button>
            )}

            {canReject && (
              <Button
                onClick={() => handleStatusUpdate("Rejected")}
                disabled={isUpdating}
                variant="destructive"
              >
                <XCircle className="w-4 h-4 mr-2" />
                {isUpdating ? "Rejecting..." : "Reject Request"}
              </Button>
            )}

            <VehicleRequestForm mode="edit" vehicleRequestData={request} />

            <Button
              onClick={() => setShowDeleteModal(true)}
              variant="outline"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <Trash className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </BaseModal>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        title="Confirm Deletion"
        variant="danger"
        message="Are you sure you want to delete this vehicle request? This action cannot be undone."
        confirmText="Delete"
        confirmVariant="destructive"
        cancelText="Cancel"
        onConfirm={() => {
          onDelete(request.id);
          setShowDeleteModal(false);
          onClose();
        }}
      />
    </>
  );
};

// Delete Vehicle Request Component
const DeleteVehicleRequest = ({ id, onDelete }: { id: number; onDelete: (id: number) => void }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleConfirm = () => {
    onDelete(id);
    setIsOpen(false);
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="text-red-600 hover:text-red-700 hover:bg-red-50"
      >
        <Trash className="w-4 h-4" />
      </Button>

      <ConfirmModal
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        title="Confirm Deletion"
        variant="danger"
        message="Are you sure you want to delete this vehicle request? This action cannot be undone."
        confirmText="Delete"
        confirmVariant="destructive"
        cancelText="Cancel"
        onConfirm={handleConfirm}
      />
    </>
  );
};

// Main VehicleRequests Component
const VehicleRequests = () => {
  const devLog = useDevLog();
  const [universalSearchValue, setUniversalSearchValue] = useState("");
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [showViewModal, setShowViewModal] = useState(false);

  const { data: vehicleRequestsData, isLoading } = useGetVehicleRequestsQuery({});
  const [updateVehicleRequest] = useUpdateVehicleRequestMutation();
  const [deleteVehicleRequest] = useDeleteVehicleRequestMutation();

  const handleStatusUpdate = async (id: number, status: string, driverId?: string) => {
    try {
      if (!id || isNaN(id)) {
        toast.error("Invalid request ID");
        return;
      }

      const updateData: any = { status };
      if (driverId) {
        updateData.driver = driverId;
      }

      await updateVehicleRequest({ id, ...updateData }).unwrap();
      toast.success(`Vehicle request ${status.toLowerCase()} successfully`);
    } catch (error) {
      logger.error("Update error", redactError(error));
      toast.error("Failed to update vehicle request. Please try again.");
    }
  };

  const handleDelete = async (id: number) => {
    try {
      if (!id || isNaN(id)) {
        toast.error("Invalid request ID");
        return;
      }

      await deleteVehicleRequest(id).unwrap();
      toast.success("Vehicle request deleted successfully");
    } catch (error) {
      logger.error("Delete error", redactError(error));
      toast.error("Failed to delete vehicle request. Please try again.");
    }
  };

  const handleViewRequest = (request: any) => {
    setSelectedRequest(request);
    setShowViewModal(true);
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "pickup_date",
      header: "Date",
      cell: (info) => {
        const date = info.getValue() as string;
        return date ? format(new Date(date), "MMM dd, yyyy") : "N/A";
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "pickup_time",
      header: "Time",
      cell: (info) => info.getValue() || "N/A",
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "pickup_location",
      header: "From",
      cell: (info) => info.getValue() || "N/A",
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "destination_location",
      header: "To",
      cell: (info) => info.getValue() || "N/A",
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "vehicle",
      header: "Vehicle",
      cell: (info) => {
        const vehicle = info.getValue() as any;
        return vehicle?.vehicle_registration || "N/A";
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "requester",
      header: "Requester",
      cell: (info) => {
        const requester = info.getValue() as any;
        if (typeof requester === 'string') {
          return requester;
        }
        return (requester?.first_name && requester?.last_name)
          ? `${requester.first_name} ${requester.last_name}`
          : requester?.fullnames || 'Unknown';
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => {
        const status = info.getValue() as string;
        return <StatusBadge status={status} />;
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex gap-2 justify-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleViewRequest(rowData)}
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            >
              <Eye className="w-4 h-4" />
            </Button>
            <VehicleRequestForm mode="edit" vehicleRequestData={rowData} />
            <DeleteVehicleRequest id={rowData?.id} onDelete={handleDelete} />
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-70">
          <SpinnerTemp type="tail-spinner" size="md" />
        </div>
      )}

      <DataTable
        data={vehicleRequestsData?.data?.results || []}
        columns={columns}
        title="Vehicle Requests"
        enableSelectColumn={false}
        enableColumnFilters={true}
        enableSorting={true}
        enableToolbar={true}
        enableFullScreenToggle={true}
        tableClassName="border-none"
        containerClassName="py-2"
        tHeadClassName="border-t"
        tHeadCellsClassName="border-r px-2"
        tBodyCellsClassName="text-xs border-r px-2"
        searchInput={
          <SearchComponent
            universalSearchValue={universalSearchValue}
            setUniversalSearchValue={setUniversalSearchValue}
          />
        }
        customButtons={<VehicleRequestForm mode="create" />}
      />

      {/* View Modal */}
      <VehicleRequestViewModal
        isOpen={showViewModal}
        onClose={() => {
          setShowViewModal(false);
          setSelectedRequest(null);
        }}
        request={selectedRequest}
        onStatusUpdate={handleStatusUpdate}
        onDelete={handleDelete}
      />
    </div>
  );
};

// Search Component
interface SearchComponentProps {
  universalSearchValue: string;
  setUniversalSearchValue: React.Dispatch<React.SetStateAction<string>>;
}

function SearchComponent({
  universalSearchValue,
  setUniversalSearchValue,
}: SearchComponentProps) {
  return (
    <input
      value={universalSearchValue}
      onChange={(e) => setUniversalSearchValue(e.target.value)}
      className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
      placeholder="Search vehicle requests..."
    />
  );
}

export default VehicleRequests;
