import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import React, { useState } from "react";
import VehicleForm from "./VehicleForm";
import {
  useDeleteVehicleMutation,
  useGetVehiclesQuery,
} from "@/redux/slices/logistics";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Trash } from "lucide-react";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/custom/Toast/MyToast";

export interface VehicleData {
  id: number;
  make: string;
  model: string;
  body_type: string;
  number_of_seats: string;
  engine_capacity: string;
  vehicle_registration: string;
  passengers_assigned: boolean;
  status: "Available" | "In Use" | "Under Maintenance" | "Out of Service";
  created_at: string;
  driver: string;
}

const data: VehicleData[] = [
  {
    id: 1,
    make: "Lexus",
    model: "570",
    body_type: "S.Wagon",
    number_of_seats: "5",
    engine_capacity: "3L",
    vehicle_registration: "KDE 123Q",
    passengers_assigned: true,
    status: "Available",
    created_at: "",
    driver: "<PERSON> Arson",
  },
];

export default function VehicleDetails() {
  const columns: ColumnDef<VehicleData>[] = [
    {
      accessorKey: "make",
      header: "Make",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "model",
      header: "Model",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "body_type",
      header: "Body Type",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "number_of_seats",
      header: "Number of Seats",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "engine_capacity",
      header: "Engine Capacity",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "vehicle_registration",
      header: "Registration",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "passengers_assigned",
      header: "Passengers",
      cell: (info) => ((info.getValue() as boolean) ? "Yes" : "No"),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "driver",
      header: "Driver",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex gap-2 justify-center">
            <VehicleForm mode="edit" vehicleData={rowData} />
            <DeleteVehicle id={rowData?.id} />
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  const [universalSearchValue, setuniversalSearchValue] = useState("");

  const { data: VehiclesData, isLoading } = useGetVehiclesQuery({});

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center  bg-white bg-opacity-70">
          <div className="w-12 h-12 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin"></div>
        </div>
      )}
      <DataTable<VehicleData>
        data={VehiclesData?.data?.results || []}
        columns={columns}
        title="Vehicles"
        enableSelectColumn={false}
        enableColumnFilters={true}
        enableSorting={true}
        enableToolbar={true}
        enableFullScreenToggle={true}
        tableClassName="border-none"
        containerClassName=" py-2 "
        tHeadClassName="border-t"
        tHeadCellsClassName="border-r px-2"
        tBodyCellsClassName="text-xs border-r px-2"
        searchInput={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
        customButtons={
          <div className="flex gap-2">
            <VehicleForm mode="create" />
          </div>
        }
      />
    </div>
  );
}

interface SearchComponentProps {
  universalSearchValue: string;
  setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>;
}

function SearchComponent({
  universalSearchValue,
  setuniversalSearchValue,
}: SearchComponentProps) {
  return (
    <input
      value={universalSearchValue}
      onChange={(e) => setuniversalSearchValue(e.target.value)}
      className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
      placeholder="Search vehicle details..."
    />
  );
}

interface deleteProps {
  id: number;
}

function DeleteVehicle({ id }: deleteProps) {
  const [isOpen, setIsOpen] = useState(false);

  const [deleteVehicle, { isLoading: deleteVehicleIsLoading }] =
    useDeleteVehicleMutation();
  const handleConfirm = async () => {
    try {
      await deleteVehicle(id).unwrap();
      toast.success("Vehicle deletion successful");
      setIsOpen(false);
    } catch (error) {
      toast.error("Failed to delete vehicle. Please try again later.");
    }
  };

  return (
    <>
      <Trash onClick={() => setIsOpen(true)} size={17} />

      <div>
        <ConfirmModal
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          title="Confirm Deletion"
          variant="danger"
          message="Are you sure you want to delete this item? This action cannot be undone."
          confirmText="Delete"
          confirmVariant="destructive"
          cancelText="Cancel"
          onConfirm={handleConfirm}
        />
      </div>
    </>
  );
}
