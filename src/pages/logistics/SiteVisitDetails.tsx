import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Clock, 
  Calendar, 
  Car, 
  User, 
  Edit, 
  Save, 
  X, 
  ArrowLeft,
  Truck,
  Key,
  Building
} from 'lucide-react';
import { 
  useGetSiteVisitQuery, 
  useUpdateSiteVisitMutation,
  SiteVisit 
} from "@/redux/slices/logistics";
import { toast } from "@/components/custom/Toast/MyToast";
import { logger, useDevLog, logSummary } from "@/utils/logger";
import { redactSiteVisit, redactApiResponse, redactError } from "@/utils/redactors";
import { format } from 'date-fns';

const SiteVisitDetails = () => {
  const devLog = useDevLog();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<SiteVisit>>({});

  const { 
    data: siteVisit, 
    isLoading, 
    isError,
    refetch 
  } = useGetSiteVisitQuery(Number(id), {
    skip: !id
  });

  const [updateSiteVisit, { isLoading: isUpdating }] = useUpdateSiteVisitMutation();

  useEffect(() => {
    if (siteVisit) {
      setEditData({
        pickup_time: siteVisit.pickup_time,
        pickup_location: siteVisit.pickup_location,
        transport_type: siteVisit.transport_type || 'company_vehicle',
        remarks: siteVisit.remarks || '',
      });
    }
  }, [siteVisit]);

  const handleSave = async () => {
    if (!id || !siteVisit) return;

    try {
      // Prepare the payload with proper pickup location based on transport type
      const payload = {
        id: Number(id),
        ...editData,
        pickup_location: editData.transport_type === 'self_drive'
          ? 'Self Drive'
          : editData.transport_type === 'own_means'
          ? 'Own Means'
          : editData.pickup_location
      };

      await updateSiteVisit(payload).unwrap();

      toast.success('✅ Site visit updated successfully!');
      setIsEditing(false);
      refetch();
    } catch (error: any) {
      logger.error('Failed to update site visit', redactError(error));
      toast.error('❌ Failed to update site visit. Please try again.');
    }
  };

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'completed':
      case 'reviewed':
        return 'secondary';
      case 'pending':
      case 'in progress':
        return 'outline';
      case 'rejected':
      case 'cancelled':
        return 'destructive';
      default:
        return 'default';
    }
  };

  const getTransportIcon = (transportType: string) => {
    switch (transportType) {
      case 'self_drive':
        return <Key className="w-4 h-4" />;
      case 'own_means':
        return <User className="w-4 h-4" />;
      case 'outsourced':
        return <Truck className="w-4 h-4" />;
      default:
        return <Car className="w-4 h-4" />;
    }
  };

  const getTransportLabel = (transportType: string) => {
    switch (transportType) {
      case 'self_drive':
        return 'Self Drive (Staff Vehicle)';
      case 'own_means':
        return 'Own Means (Client Vehicle)';
      case 'outsourced':
        return 'Outsourced';
      default:
        return 'Company Vehicle';
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Car className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading site visit details...</p>
          </div>
        </div>
      </Screen>
    );
  }

  if (isError || !siteVisit) {
    return (
      <Screen>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <X className="w-12 h-12 mx-auto mb-4 text-red-500" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Site Visit Not Found</h3>
            <p className="text-gray-600 mb-6">The requested site visit could not be found.</p>
            <Button onClick={() => navigate('/logistics')} className="flex items-center space-x-2">
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Logistics</span>
            </Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6 p-6">
        {/* Header */}
        <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/logistics')}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back</span>
                </Button>
                <div>
                  <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                    <MapPin className="w-6 h-6 mr-3 text-blue-600" />
                    Site Visit Details
                  </CardTitle>
                  <CardDescription className="text-gray-600 mt-1">
                    View and edit site visit information
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge variant={getStatusVariant(siteVisit.status)} className="text-sm">
                  {siteVisit.status}
                </Badge>
                {!isEditing ? (
                  <Button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-2"
                  >
                    <Edit className="w-4 h-4" />
                    <span>Edit</span>
                  </Button>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsEditing(false);
                        setEditData({
                          pickup_time: siteVisit.pickup_time,
                          pickup_location: siteVisit.pickup_location,
                          transport_type: siteVisit.transport_type || 'company_vehicle',
                          remarks: siteVisit.remarks || '',
                        });
                      }}
                      className="flex items-center space-x-2"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel</span>
                    </Button>
                    <Button
                      onClick={handleSave}
                      disabled={isUpdating}
                      className="flex items-center space-x-2"
                    >
                      <Save className="w-4 h-4" />
                      <span>{isUpdating ? 'Saving...' : 'Save'}</span>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Site Visit Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                <Building className="w-5 h-5 mr-2 text-green-600" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Visit ID</Label>
                  <p className="text-lg font-semibold text-gray-900">#{siteVisit.id}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Project</Label>
                  <p className="text-lg font-semibold text-gray-900">
                    {typeof siteVisit.project === 'object'
                      ? siteVisit.project?.name || 'Unknown Project'
                      : String(siteVisit.project || 'Unknown Project')
                    }
                  </p>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-600">Marketer</Label>
                <div className="flex items-center space-x-2 mt-1">
                  <User className="w-4 h-4 text-purple-600" />
                  <span className="text-gray-900">
                    {typeof siteVisit.marketer === 'object'
                      ? siteVisit.marketer?.fullnames || siteVisit.marketer?.username || 'Unknown Marketer'
                      : String(siteVisit.marketer || 'Unknown Marketer')
                    }
                  </span>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Driver</Label>
                <div className="flex items-center space-x-2 mt-1">
                  <Car className="w-4 h-4 text-orange-600" />
                  <span className="text-gray-900">
                    {typeof siteVisit.driver === 'object'
                      ? siteVisit.driver?.fullnames || siteVisit.driver?.username || 'Unknown Driver'
                      : String(siteVisit.driver || 'Not assigned')
                    }
                  </span>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Schedule</Label>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-blue-600" />
                    <span className="text-gray-900">
                      {format(new Date(siteVisit.pickup_date), 'MMM dd, yyyy')}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-green-600" />
                    <span className="text-gray-900">{siteVisit.pickup_time}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Editable Information */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                <Edit className="w-5 h-5 mr-2 text-blue-600" />
                Editable Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Pickup Time */}
              <div>
                <Label className="text-sm font-medium text-gray-600">Pickup Time</Label>
                {isEditing ? (
                  <Input
                    type="time"
                    value={editData.pickup_time || ''}
                    onChange={(e) => setEditData({ ...editData, pickup_time: e.target.value })}
                    className="mt-1"
                  />
                ) : (
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock className="w-4 h-4 text-green-600" />
                    <span className="text-gray-900">{siteVisit.pickup_time}</span>
                  </div>
                )}
              </div>

              {/* Pickup Location */}
              <div>
                <Label className="text-sm font-medium text-gray-600">Pickup Location</Label>
                {isEditing ? (
                  <div>
                    <Input
                      value={editData.pickup_location || ''}
                      onChange={(e) => setEditData({ ...editData, pickup_location: e.target.value })}
                      placeholder={
                        editData.transport_type === 'self_drive'
                          ? "Self Drive - No pickup required"
                          : editData.transport_type === 'own_means'
                          ? "Own Means - Client uses own transport"
                          : "Enter pickup location"
                      }
                      className="mt-1"
                      disabled={editData.transport_type === 'self_drive' || editData.transport_type === 'own_means'}
                    />
                    {(editData.transport_type === 'self_drive' || editData.transport_type === 'own_means') && (
                      <p className="text-xs text-gray-500 mt-1">
                        Pickup location is automatically set for {editData.transport_type === 'self_drive' ? 'self-drive' : 'own means'} transport
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 mt-1">
                    <MapPin className="w-4 h-4 text-red-600" />
                    <span className="text-gray-900">{siteVisit.pickup_location}</span>
                  </div>
                )}
              </div>

              {/* Transport Type */}
              <div>
                <Label className="text-sm font-medium text-gray-600">Transport Type</Label>
                {isEditing ? (
                  <Select
                    value={editData.transport_type || 'company_vehicle'}
                    onValueChange={(value) => {
                      const newEditData = {
                        ...editData,
                        transport_type: value as any
                      };

                      // Auto-set pickup location based on transport type
                      if (value === 'self_drive') {
                        newEditData.pickup_location = 'Self Drive';
                      } else if (value === 'own_means') {
                        newEditData.pickup_location = 'Own Means';
                      } else if (editData.pickup_location === 'Self Drive' || editData.pickup_location === 'Own Means') {
                        // Reset to original pickup location if changing from self_drive/own_means
                        newEditData.pickup_location = siteVisit.pickup_location;
                      }

                      setEditData(newEditData);
                    }}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="company_vehicle">
                        <div className="flex items-center space-x-2">
                          <Car className="w-4 h-4" />
                          <span>Company Vehicle</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="self_drive">
                        <div className="flex items-center space-x-2">
                          <Key className="w-4 h-4" />
                          <span>Self Drive (Staff Vehicle)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="own_means">
                        <div className="flex items-center space-x-2">
                          <User className="w-4 h-4" />
                          <span>Own Means (Client Vehicle)</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center space-x-2 mt-1">
                    {getTransportIcon(siteVisit.transport_type || 'company_vehicle')}
                    <span className="text-gray-900">
                      {getTransportLabel(siteVisit.transport_type || 'company_vehicle')}
                    </span>
                  </div>
                )}
              </div>

              {/* Remarks */}
              <div>
                <Label className="text-sm font-medium text-gray-600">Remarks</Label>
                {isEditing ? (
                  <Textarea
                    value={editData.remarks || ''}
                    onChange={(e) => setEditData({ ...editData, remarks: e.target.value })}
                    placeholder="Enter any additional remarks"
                    className="mt-1 min-h-[80px]"
                  />
                ) : (
                  <p className="text-gray-900 mt-1 min-h-[80px] p-3 bg-gray-50 rounded-md">
                    {siteVisit.remarks || 'No remarks provided'}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default SiteVisitDetails;
