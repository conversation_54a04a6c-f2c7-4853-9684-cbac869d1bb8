import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";

import MultiStepForm from "@/components/custom/forms/MultiStepForm";
import FormStep from "@/components/custom/forms/FormStep";
import { Screen } from "@/app-components/layout/screen";
import SiteVisitDetails from "@/components/SiteVisitBooking/SiteVisitDetails";
import ClientDetails from "@/components/SiteVisitBooking/ClientDetails";
import ConfirmDetails from "@/components/SiteVisitBooking/ConfirmDetails";

import { useCreateSiteVisitMutation, useGetSiteVisitsQuery, useGetSiteVisitSurveysQuery, SiteVisit } from "@/redux/slices/logistics";
import { toast } from "@/components/custom/Toast/MyToast";
import { useAuthHook } from "@/utils/useAuthHook";
import { useLogisticsPermissions } from "@/hooks/useLogisticsPermissions";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, CheckCircle } from "lucide-react";
import UnauthorizedPage from "@/pages/UnauthorizedPage";
import { logger, useDevLog, logSummary } from "@/utils/logger";
import { redactSiteVisit, redactApiResponse, redactError } from "@/utils/redactors";

const SiteVisitForm: React.FC = () => {
  const devLog = useDevLog();
  /** Check permissions */
  const { canBookVisit } = useLogisticsPermissions();

  /** API mutation */
  const [createSiteVisit, { isLoading }] = useCreateSiteVisitMutation();

  /** App navigation */
  const navigate = useNavigate();

  /** Get current user */
  const { user_details } = useAuthHook();

  // If user doesn't have permission to book visits, show unauthorized page
  if (!canBookVisit) {
    return <UnauthorizedPage />;
  }

  /** Check for pending surveys */
  const { data: completedVisitsResp } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    status: "Completed",
    marketer: user_details?.employee_no || undefined,
  });

  // Fetch existing surveys to check which visits already have surveys
  const { data: existingSurveysResp } = useGetSiteVisitSurveysQuery({
    page: 1,
    page_size: 1000,
  });

  // Calculate pending surveys count (completed visits without surveys)
  const pendingSurveysCount = useMemo(() => {
    if (!completedVisitsResp?.data?.results || !existingSurveysResp?.data?.results) return 0;

    // Get list of site visit IDs that already have surveys
    const visitIdsWithSurveys = new Set(
      existingSurveysResp.data.results.map((survey: any) => survey.site_visit)
    );

    // Count completed visits that don't have surveys yet
    return completedVisitsResp.data.results
      .filter((visit: SiteVisit) => {
        return visit.status === 'Completed' && !visitIdsWithSurveys.has(visit.id);
      }).length;
  }, [completedVisitsResp, existingSurveysResp]);

  /** ============== Form submit handler ============== */
  const handleFormComplete = async (formData: any) => {
    devLog("Raw formData", { fieldCount: Object.keys(formData).length });

    const {
      projectId,
      projectName,
      pickupLocation,
      pickupDate,
      pickupTime,
      marketerName,
      clients,
      selfDrive,
      transportType,
      remarks,
    } = formData;

    /* ---------- Validation ---------- */
    // Check for pending surveys limit
    if (pendingSurveysCount > 2) {
      toast.error(`❌ You have ${pendingSurveysCount} pending surveys. Please complete your existing surveys before booking a new site visit. Maximum allowed: 2 pending surveys.`);
      return;
    }

    if (!projectId)    { toast.error("Please select a project."); return; }
    // Only require pickup location for company vehicle transport
    if (!pickupLocation && transportType !== 'self_drive' && transportType !== 'own_means') {
      toast.error("Please enter a pickup location.");
      return;
    }
    if (!pickupDate)   { toast.error("Please select a pickup date."); return; }
    if (!pickupTime)   { toast.error("Please select a pickup time."); return; }

    // Disallow past-date bookings
    const today = new Date();          today.setHours(0, 0, 0, 0);
    const selected = pickupDate instanceof Date ? pickupDate : new Date(pickupDate);
    selected.setHours(0, 0, 0, 0);
    if (selected < today) {
      toast.error("Cannot book a site visit for a past date.");
      return;
    }

    // Validate clients
    if (!clients?.length) {
      toast.error("Please add at least one person (client or representative).");
      return;
    }
    for (let i = 0; i < clients.length; i++) {
      const c = clients[i];
      if (!c.firstName || !c.lastName || !c.phone) {
        const personType = c.client_type === 'representative' ? 'Representative' : 'Client';
        toast.error(`Please fill in all required fields for ${personType} ${i + 1}.`);
        return;
      }
    }

    /* ---------- Build payload ---------- */
    const formattedDate =
      pickupDate instanceof Date
        ? format(pickupDate, "yyyy-MM-dd")
        : pickupDate;

    // Determine pickup location based on transport type
    let finalPickupLocation = pickupLocation;
    if (transportType === 'self_drive') {
      finalPickupLocation = 'Self Drive';
    } else if (transportType === 'own_means') {
      finalPickupLocation = 'Own Means';
    }

    const payload: Partial<SiteVisit> = {
      project: projectId,
      pickup_location: finalPickupLocation || '',
      pickup_date: formattedDate,
      pickup_time: pickupTime,
      marketer: marketerName || "OL/HR/001",
      status: "Pending",
      remarks: remarks || `Site visit for ${projectName}`,
      clients,
      is_self_drive: selfDrive || false,
      transport_type: transportType || 'company_vehicle',
    };

    devLog("Submitting payload", { fieldCount: Object.keys(payload).length });

    /* ---------- Submit ---------- */
    try {
      await createSiteVisit(payload).unwrap();
      toast.success(`✅ Site visit for ${projectName} booked successfully!`);

      // Graceful redirect after 3 seconds
      setTimeout(() => navigate("/logistics-dash"), 3000);
    } catch (err: any) {
      logger.error("Booking failed", redactError(err));
      const { data } = err || {};
      if (data?.project) toast.error("❌ Invalid project selected.");
      else if (data?.pickup_date) toast.error("❌ Invalid pickup date.");
      else if (data?.pickup_time) toast.error("❌ Invalid pickup time.");
      else toast.error("❌ Failed to book site visit. Please try again.");
    }
  };

  /** ============== UI ============== */
  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 py-5">
        <div className="container px-4 mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="p-6 bg-background rounded-lg border">
              <div className="prose dark:prose-invert max-w-none mb-6">
                <h2 className="text-2xl font-bold">Book A Site Visit</h2>
              </div>

              {/* Survey Status Alert */}
              {pendingSurveysCount > 0 && (
                <Alert className={`mb-6 ${
                  pendingSurveysCount > 2
                    ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                    : pendingSurveysCount === 2
                    ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
                    : 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
                }`}>
                  <div className="flex items-center">
                    {pendingSurveysCount > 2 ? (
                      <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
                    ) : pendingSurveysCount === 2 ? (
                      <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    )}
                    <AlertDescription className="ml-2">
                      {pendingSurveysCount > 2 ? (
                        <span className="text-red-800 dark:text-red-200 font-medium">
                          ⚠️ You have {pendingSurveysCount} pending surveys. Please complete your existing surveys before booking a new site visit. Maximum allowed: 2 pending surveys.
                        </span>
                      ) : pendingSurveysCount === 2 ? (
                        <span className="text-yellow-800 dark:text-yellow-200 font-medium">
                          ⚠️ You have {pendingSurveysCount} pending surveys. You're at the limit! Complete some surveys before booking more visits.
                        </span>
                      ) : (
                        <span className="text-blue-800 dark:text-blue-200">
                          📋 You have {pendingSurveysCount} pending survey{pendingSurveysCount !== 1 ? 's' : ''}. You can book up to {2 - pendingSurveysCount} more site visit{2 - pendingSurveysCount !== 1 ? 's' : ''}.
                        </span>
                      )}
                    </AlertDescription>
                  </div>
                </Alert>
              )}

              {pendingSurveysCount === 0 && (
                <Alert className="mb-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <AlertDescription className="ml-2">
                    <span className="text-green-800 dark:text-green-200">
                      ✅ Great! You have no pending surveys. You can book up to 2 site visits.
                    </span>
                  </AlertDescription>
                </Alert>
              )}

              <MultiStepForm
                onComplete={handleFormComplete}
                variant="horizontal"
                className="mb-8"
                submitLabel={
                  pendingSurveysCount > 2
                    ? "Complete Surveys First"
                    : isLoading
                    ? "Booking..."
                    : "Submit"
                }

                showStepTitles
              >
                <FormStep
                  title="Site Visit Details"
                  description="Provide site visit information"
                >
                  <SiteVisitDetails />
                </FormStep>

                <FormStep
                  title="Client Details"
                  description="Add client contact info"
                >
                  <ClientDetails />
                </FormStep>

                <FormStep
                  title="Confirm Details"
                  description="Review and confirm your details"
                >
                  <ConfirmDetails />
                </FormStep>
              </MultiStepForm>
            </div>
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default SiteVisitForm;
