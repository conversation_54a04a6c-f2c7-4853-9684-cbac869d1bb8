import { useState, useMemo } from "react";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Card7 } from "@/components/custom/cards/Card7";
import { Eye } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import DriverProfileModal from "./Driversinfo";
import { Input } from "@/components/ui/input";
import OnlyIconButton from "@/components/custom/buttons/onlyiconbutton";
import { useGetDriversQuery } from "@/redux/slices/logistics";

const DriversPage = () => {
  const [activeTab, setActiveTab] = useState("Cards");
  const [isDriverModalOpen, setIsDriverModalOpen] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState<any | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [page] = useState(1);
  const pageSize = 10;

  // Fetch drivers from API
  const { data: driversResponse, isLoading, error } = useGetDriversQuery({
    page,
    page_size: pageSize,
  });

  const drivers = useMemo(() => {
    if (!driversResponse?.data?.results) return [];
    return driversResponse.data.results.map((driver: any) => ({
      id: driver.id.toString(),
      name: driver.fullnames || `${driver.first_name} ${driver.last_name}`,
      email: driver.email,
      phoneNumber: driver.phone_number || "N/A",
      imageUrl: "public/kirill-balobanov-2rIs8OH5ng0-unsplash.jpg", // Default image
      license: driver.category || "N/A",
      licenseNumber: driver.employee_no || "N/A",
      status: driver.is_available ? "Active" : "Inactive",
      joinDate: driver.created_date?.split("T")[0] || "N/A",
      employee_no: driver.employee_no || "N/A", // Add employee_no field
      trips: [], // These would need to be fetched separately
      plannedTrips: [], // These would need to be fetched separately
    }));
  }, [driversResponse]);

  const handleViewProfile = (driver: any) => {
    setSelectedDriver(driver);
    setIsDriverModalOpen(true);
  };

  const filteredDrivers = useMemo(() => {
    if (!searchQuery) return drivers;
    const query = searchQuery.toLowerCase();
    return drivers.filter(
      (driver) =>
        driver.name.toLowerCase().includes(query) ||
        driver.email.toLowerCase().includes(query) ||
        driver.licenseNumber.toLowerCase().includes(query)
    );
  }, [drivers, searchQuery]);

  const columns = [
    { accessorKey: "name", header: "Name", enableSorting: true },
    { accessorKey: "email", header: "Email", enableSorting: true },
    { accessorKey: "phoneNumber", header: "Phone Number" },
    { accessorKey: "license", header: "License Type" },
    { accessorKey: "licenseNumber", header: "License Number" },
    { accessorKey: "status", header: "Status", enableSorting: true },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }: any) => (
        <div className="flex gap-3">
          <OnlyIconButton
            icon={Eye}
            className="text-green-600 hover:text-green-800 transition-colors"
            onClick={() => handleViewProfile(row.original)}
            children={undefined}
          />
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Screen>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Error Loading Drivers</h2>
            <p className="text-gray-600">Please try again later</p>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="min-h-screen space-y-8 py-10 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-3xl font-bold text-gray-900">Drivers</h1>
          <div className="flex gap-4 w-full sm:w-auto">
            <Input
              placeholder="Search drivers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:w-64 rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="flex space-x-6 border-b border-gray-200">
          {["Cards", "Table"].map((tab) => (
            <button
              key={tab}
              className={`px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === tab
                  ? "border-b-2 border-blue-600 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab(tab)}
            >
              {tab} View
            </button>
          ))}
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {activeTab === "Cards" && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredDrivers.length > 0 ? (
                  filteredDrivers.map((driver) => (
                    <div
                      key={driver.id}
                      className="transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
                    >
                      <Card7
                        headerBgClass="bg-gradient-to-r from-gray-50 to-gray-100"
                        className="rounded-xl shadow-md border border-gray-100 overflow-hidden"
                        avatarSlot={
                          <img
                            src={driver.imageUrl}
                            alt={`${driver.name}`}
                            className="w-20 h-20 rounded-full ring-2 ring-white object-cover"
                          />
                        }
                        titleSlot={
                          <h3 className="text-lg font-semibold text-gray-900">
                            {driver.name}
                          </h3>
                        }
                        subtitleSlot={
                          <p className="text-sm text-gray-600">{driver.email}</p>
                        }
                        pillSlots={
                          <div className="flex gap-4">
                            <div className="flex flex-col items-center">
                              <span className="text-xs px-3 py-1 rounded-full bg-green-100 text-green-800 font-medium">
                                {driver.license}
                              </span>
                              <span className="text-xs text-gray-500 mt-1">
                                License Type
                              </span>
                            </div>
                            <div className="flex flex-col items-center">
                              <span
                                className={`text-xs px-3 py-1 rounded-full font-medium ${
                                  driver.status === "Active"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-yellow-100 text-yellow-800"
                                }`}
                              >
                                {driver.status}
                              </span>
                              <span className="text-xs text-gray-500 mt-1">
                                Status
                              </span>
                            </div>
                          </div>
                        }
                        actionSlot={
                          <PrimaryButton
                            variant="primary"
                            onClick={() => handleViewProfile(driver)}
                            className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white"
                          >
                            View Statistics
                          </PrimaryButton>
                        }
                      />
                    </div>
                  ))
                ) : (
                  <p className="col-span-full text-center text-gray-500">
                    No drivers found.
                  </p>
                )}
              </div>
            )}

            {activeTab === "Table" && (
              <DataTable
                data={filteredDrivers}
                columns={columns}
                title="Drivers Table"
              />
            )}
          </>
        )}
      </div>

      {selectedDriver && (
        <DriverProfileModal
          isOpen={isDriverModalOpen}
          onOpenChange={setIsDriverModalOpen}
          driverData={selectedDriver}
        />
      )}
    </Screen>
  );
};

export default DriversPage;