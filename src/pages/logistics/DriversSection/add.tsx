import { useState, useEffect } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddDriverProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (newDriver: {
    driverName: string;
    driverPhoneNumber: string;
    driverEmail: string;
    licenseType: string;
    licenseNumber: string;
  }) => void;
}

export default function AddDriver({ isOpen, onClose, onAdd }: AddDriverProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [driverData, setDriverData] = useState<{
    driverName: string;
    driverPhoneNumber: string;
    driverEmail: string;
    licenseType: string;
    licenseNumber: string;
  }>({
    driverName: "",
    driverPhoneNumber: "",
    driverEmail: "",
    licenseType: "",
    licenseNumber: "",
  });
  const [errors, setErrors] = useState<{
    driverName?: string;
    driverPhoneNumber?: string;
    driverEmail?: string;
    licenseType?: string;
    licenseNumber?: string;
  }>({});

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
      setDriverData({
        driverName: "",
        driverPhoneNumber: "",
        driverEmail: "",
        licenseType: "",
        licenseNumber: "",
      });
      setErrors({});
    }
  }, [isOpen]);

  const validateStep = (step: number): boolean => {
    const newErrors: typeof errors = {};

    if (step === 0) {
      
      if (!driverData.driverName) {
        newErrors.driverName = "Driver name is required";
      } else if (driverData.driverName.length < 2) {
        newErrors.driverName = "Driver name must be at least 2 characters";
      }

      if (!driverData.driverEmail) {
        newErrors.driverEmail = "Email is required";
      } else if (
        !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(
          driverData.driverEmail
        )
      ) {
        newErrors.driverEmail = "Invalid email format";
      }

      if (!driverData.driverPhoneNumber) {
        newErrors.driverPhoneNumber = "Phone number is required";
      } else if (!/^\+254\d{9}$/.test(driverData.driverPhoneNumber)) {
        newErrors.driverPhoneNumber =
          "Phone number must be in the format +254XXXXXXXXX";
      }
    } else if (step === 1) {
      
      if (!driverData.licenseType) {
        newErrors.licenseType = "License type is required";
      } else if (
        !["Commercial", "CDL-A", "CDL-B", "Non-CDL"].includes(
          driverData.licenseType
        )
      ) {
        newErrors.licenseType = "Invalid license type";
      }

      if (!driverData.licenseNumber) {
        newErrors.licenseNumber = "License number is required";
      } else if (!/^[A-Z0-9]{6,10}$/.test(driverData.licenseNumber)) {
        newErrors.licenseNumber =
          "License number must be 6-10 alphanumeric characters";
      }
    }

    setErrors(newErrors);

  
    if (Object.keys(newErrors).length > 0) {
      toast("error", "Please fix the errors before proceeding.");
    }

    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setDriverData((prev) => ({ ...prev, [name]: value }));
    
    setErrors((prev) => ({ ...prev, [name]: undefined }));
  };

  const handleStepChange = (newStep: number) => {
    if (newStep > currentStep) {
     
      if (!validateStep(currentStep)) return;
    }
    setCurrentStep(newStep);
  };

  const handleAddDriver = () => {
    
    if (validateStep(0) && validateStep(1)) {
      onAdd(driverData);
      toast("success", "Driver added successfully!");
      onClose();
    }
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Add New Driver"
      description="Complete all steps to add a new driver"
      currentStep={currentStep}
      onStepChange={handleStepChange}
      onComplete={handleAddDriver}
      steps={[
        {
          title: "Personal Information",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="driverName">Driver Name</Label>
                <Input
                  id="driverName"
                  name="driverName"
                  value={driverData.driverName}
                  onChange={handleInputChange}
                  placeholder="Enter driver's full name"
                  className={errors.driverName ? "border-red-500" : ""}
                />
                {errors.driverName && (
                  <p className="text-red-500 text-sm">{errors.driverName}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="driverPhoneNumber">Phone Number</Label>
                <Input
                  id="driverPhoneNumber"
                  name="driverPhoneNumber"
                  value={driverData.driverPhoneNumber}
                  onChange={handleInputChange}
                  placeholder="Enter phone number (e.g., +2547XXXXXXXX)"
                  className={errors.driverPhoneNumber ? "border-red-500" : ""}
                />
                {errors.driverPhoneNumber && (
                  <p className="text-red-500 text-sm">
                    {errors.driverPhoneNumber}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="driverEmail">Email</Label>
                <Input
                  id="driverEmail"
                  name="driverEmail"
                  type="email"
                  value={driverData.driverEmail}
                  onChange={handleInputChange}
                  placeholder="Enter driver's email"
                  className={errors.driverEmail ? "border-red-500" : ""}
                />
                {errors.driverEmail && (
                  <p className="text-red-500 text-sm">{errors.driverEmail}</p>
                )}
              </div>
            </div>
          ),
        },
        {
          title: "License Information",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="licenseType">License Type</Label>
                <select
                  id="licenseType"
                  name="licenseType"
                  value={driverData.licenseType}
                  onChange={handleInputChange}
                  className={`w-full rounded-md border px-3 py-2 text-sm ${
                    errors.licenseType
                      ? "border-red-500"
                      : "border-gray-300"
                  }`}
                >
                  <option value="">Select license type</option>
                  <option value="Commercial">Commercial</option>
                  <option value="CDL-A">CDL-A</option>
                  <option value="CDL-B">CDL-B</option>
                  <option value="Non-CDL">Non-CDL</option>
                </select>
                {errors.licenseType && (
                  <p className="text-red-500 text-sm">{errors.licenseType}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="licenseNumber">License Number</Label>
                <Input
                  id="licenseNumber"
                  name="licenseNumber"
                  value={driverData.licenseNumber}
                  onChange={handleInputChange}
                  placeholder="Enter license number"
                  className={errors.licenseNumber ? "border-red-500" : ""}
                />
                {errors.licenseNumber && (
                  <p className="text-red-500 text-sm">{errors.licenseNumber}</p>
                )}
              </div>
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="space-y-4 py-2">
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Driver Name:</span>
                  <span className="ml-2">{driverData.driverName}</span>
                </div>
                <div>
                  <span className="font-medium">Phone Number:</span>
                  <span className="ml-2">{driverData.driverPhoneNumber}</span>
                </div>
                <div>
                  <span className="font-medium">Email:</span>
                  <span className="ml-2">{driverData.driverEmail}</span>
                </div>
                <div>
                  <span className="font-medium">License Type:</span>
                  <span className="ml-2">{driverData.licenseType}</span>
                </div>
                <div>
                  <span className="font-medium">License Number:</span>
                  <span className="ml-2">{driverData.licenseNumber}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review the information above before adding this driver.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}