import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import CustomerInfoHeader, {
  Customer,
} from "@/components/customer-section/CustomerInfoHeader";
import CustomerTabNavigation from "@/components/customer-section/CustomerTabNavigation";
import CustomerSidebar from "@/components/customer-section/CustomerSidebar";
import EditCustomerDrawer from "@/components/drawers/EditCustomerDrawer";
import { Screen } from "@/app-components/layout/screen";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useGetCustomerDetailsQuery } from "@/redux/slices/customersApiSlice";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuthHook } from "@/utils/useAuthHook";

// Define API response interface to map to the Customer interface
interface CustomerApiResponse {
  customer_no: string;
  customer_name: string;
  national_id?: string;
  passport_no?: string;
  kra_pin?: string;
  dob?: string;
  gender?: "Male" | "Female";
  marital_status?: "Single" | "Married" | "Divorced" | "Widowed";
  phone?: string;
  alternative_phone?: string;
  primary_email?: string;
  alternative_email?: string;
  address?: string;
  customer_type: "Individual" | "Group";
  country_of_residence?: string;
  date_of_registration: string;
  otp?: string;
  otp_generated_at?: string;
  lead_source?: number;
  marketer?: string;
}

const CustomerDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [activeDrawer, setActiveDrawer] = useState<string | null>(null);
  const [isSidebarDrawerOpen, setIsSidebarDrawerOpen] = useState(false);

  // Get current user from auth
  const { user_details } = useAuthHook();
  const currentUser = user_details ? {
    id: user_details.employee_no || "",
    name: user_details.fullnames || ""
  } : undefined;

  // Fetch customer details using the API
  const {
    data: customerData,
    isLoading,
    isError,
    error,
  } = useGetCustomerDetailsQuery(id || "");

  useEffect(() => {
    if (customerData) {
      const customerName = customerData.customer_name || "Customer Details";
      document.title = `${customerName} | Optiven CRM`;
    }
    return () => {
      document.title = "Optiven CRM";
    };
  }, [customerData]);

  const handleOpenDrawer = (drawer: string) => {
    setActiveDrawer(drawer);
    if (drawer === "sidebar") {
      setIsSidebarDrawerOpen(true);
    }
  };

  const handleCloseDrawer = () => {
    setActiveDrawer(null);
    setIsSidebarDrawerOpen(false);
  };

  const handleCustomerUpdate = (data: any) => {
    // This would typically trigger a refetch or update the local state
    console.log("Customer updated:", data);
  };

  // Format customer data from API response to match the Customer interface from CustomerInfoHeader
  const formatCustomerForHeader = (
    customer: CustomerApiResponse | undefined
  ): Customer => {
    if (!customer)
      return {
        id: "",
        name: "",
        email: "",
        phone: "",
        customer_no: "",
      };

    return {
      id: customer.customer_no,
      name: customer.customer_name,
      email: customer.primary_email || "",
      phone: customer.phone || "",
      position: "", // Not available in the API response
      company: "", // Not available in the API response
      customer_no: customer.customer_no,
      nationalId: customer.national_id,
      passportNo: customer.passport_no,
      kraPin: customer.kra_pin,
      dob: customer.dob,
      gender: customer.gender,
      maritalStatus: customer.marital_status,
      alternativePhone: customer.alternative_phone,
      alternativeEmail: customer.alternative_email,
      address: customer.address,
      customerType: customer.customer_type,
      countryOfResidence: customer.country_of_residence,
      dateOfRegistration: customer.date_of_registration,
      otp: customer.otp,
      otpGeneratedAt: customer.otp_generated_at,
      leadSource: customer.lead_source,
      marketer: customer.marketer,
    };
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-[calc(100vh-100px)]">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      </Screen>
    );
  }

  if (isError) {
    return (
      <Screen>
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>
              Error loading customer details. Please try again later.
            </AlertDescription>
          </Alert>
        </div>
      </Screen>
    );
  }

  const formattedCustomer = formatCustomerForHeader(customerData);

  return (
    <Screen>
      <div className="min-h-screen flex flex-col">
        <div className="flex flex-1">
          <div className="w-full md:w-72 xl:w-72 border-r shrink-0 md:h-[calc(100vh-44px)] overflow-auto">
            <CustomerInfoHeader
              customer={formattedCustomer}
              onEdit={() => handleOpenDrawer("edit")}
            />
          </div>

          <div className="hidden md:flex flex-1 flex-col bg-gray-50 md:h-[calc(100vh-44px)] overflow-hidden">
            <div className="flex-1 overflow-auto dark:bg-gray-900">
              <div className="p-4 md:p-6 mx-auto">
                <CustomerTabNavigation
                  customerId={formattedCustomer.id}
                  customerPhone={formattedCustomer.phone}
                  customerName={formattedCustomer.name}
                  customerEmail={formattedCustomer.email}
                />
              </div>
            </div>
          </div>

          <div className="hidden md:block md:w-80 xl:w-96 border-l shrink-0 md:h-[calc(100vh-44px)] overflow-auto">
            <CustomerSidebar
              className="h-full"
              customerNo={formattedCustomer.customer_no}
              entityType="customer"
              entityId={formattedCustomer.id}
              currentUser={currentUser}
            />
          </div>
        </div>

        {/* Mobile Tabs */}
        <div className="md:hidden bg-white dark:bg-gray-900 p-4">
          <CustomerTabNavigation
            customerId={formattedCustomer.id}
            customerPhone={formattedCustomer.phone}
            customerName={formattedCustomer.name}
            customerEmail={formattedCustomer.email}
          />
        </div>
      </div>

      {/* Drawers */}

      <EditCustomerDrawer
        isOpen={activeDrawer === "edit"}
        onClose={handleCloseDrawer}
        customer={formattedCustomer}
        onUpdate={handleCustomerUpdate}
      />

      {/* Mobile Side Navigation */}
      <div className="fixed right-2 top-1/4 md:hidden z-30">
        <button
          onClick={() => handleOpenDrawer("sidebar")}
          className="group relative flex items-center justify-center focus:outline-none"
          aria-label="Open sidebar menu"
        >
          <div className="absolute -left-6 bg-primary rounded-l-full w-12 h-24 shadow-lg opacity-90 transition-all duration-300 group-hover:w-14 group-hover:opacity-100"></div>
          <span className="absolute -left-14 text- font-medium rotate-90 uppercase tracking-wide text-xs">
            Menu
          </span>
          <div className="absolute -left-4 flex flex-col items-center justify-center h-24 z-10 gap-1.5">
            <div className="w-4 h-0.5 rounded-full bg-white group-hover:w-5 transition-all"></div>
            <div className="w-4 h-0.5 rounded-full bg-white group-hover:w-5 transition-all delay-75"></div>
            <div className="w-4 h-0.5 rounded-full bg-white group-hover:w-5 transition-all delay-150"></div>
          </div>
        </button>
      </div>

      {/* Sidebar Drawer for Mobile */}
      <Sheet
        open={activeDrawer === "sidebar"}
        onOpenChange={() => handleCloseDrawer()}
      >
        <SheetContent side="right" className="w-[85%] sm:w-[385px] p-0">
          <SheetHeader className="p-4 border-b">
            <SheetTitle>Sidebar Details</SheetTitle>
          </SheetHeader>
          <CustomerSidebar
            className="h-full"
            customerNo={formattedCustomer.customer_no}
            entityType="customer"
            entityId={formattedCustomer.id}
            currentUser={currentUser}
          />
        </SheetContent>
      </Sheet>
    </Screen>
  );
};

export default CustomerDetails;