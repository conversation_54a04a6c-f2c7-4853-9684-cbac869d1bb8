import { useState, useEffect, useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Search } from "lucide-react";
import { 
  useGetAllCustomersQuery, 
  useSearchAllCustomersPermissionLessQuery 
} from "@/redux/slices/customersApiSlice";
import { useCustomerPermissions } from "@/hooks/useCustomerPermissions";
import CustomerPermissionIndicator from "@/components/customer/CustomerPermissionIndicator";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";

// Import the shared Customer interface
import { Customer } from "@/components/customer-section/CustomerInfoHeader";
import { searchDebouncer } from "@/utils/debouncers";

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  
  // Get customer permissions
  const {
    hasAnyCustomerAccess,
    apiParams,
    userDetails
  } = useCustomerPermissions();

  // Create query parameters for permission-based query
  const permissionQueryParams = {
    page: currentPage,
    page_size: itemsPerPage,
    ordering: "customer_name",
    // TEMPORARY FIX: Remove apiParams as the API doesn't support OFFICE parameter
    // ...apiParams
  };

  // Create query parameters for permission-less search
  const searchQueryParams = {
    page: currentPage,
    page_size: itemsPerPage,
    ordering: "customer_name",
    search: searchValue,
  };
  
  console.log('All Customers API params:', permissionQueryParams);
  console.log('Search Customers Permission-less API params:', searchQueryParams);
  console.log('All Customers Permission debugging:', {
    hasAnyCustomerAccess,
    apiParams,
    userPermissions: hasAnyCustomerAccess ? 'User has access' : 'User has NO access',
    userDetails
  });
  
  // Use permission-based query when user has access
  const {
    data: permissionCustomersData,
    isLoading: permissionIsLoading,
    isError: permissionIsError,
    error: permissionError,
    refetch: permissionRefetch,
    isFetching: permissionIsFetching
  } = useGetAllCustomersQuery(permissionQueryParams, {
    skip: !hasAnyCustomerAccess, // Skip if user has no access
  });

  // Use permission-less search query for search functionality
  const {
    data: searchCustomersData,
    isLoading: searchIsLoading,
    isError: searchIsError,
    error: searchError,
    refetch: searchRefetch,
    isFetching: searchIsFetching
  } = useSearchAllCustomersPermissionLessQuery(searchQueryParams, {
    // skip: !searchValue || searchValue.length < 2, // Only search when there's a meaningful search term
  });

  // Determine which data to use based on search state and permissions
  const shouldUseSearchResults = searchValue && searchValue.length >= 2;
  const customersData = shouldUseSearchResults ? searchCustomersData : permissionCustomersData;
  const isLoading = shouldUseSearchResults ? searchIsLoading : permissionIsLoading;
  const isError = shouldUseSearchResults ? searchIsError : permissionIsError;
  const error = shouldUseSearchResults ? searchError : permissionError;
  const refetch = shouldUseSearchResults ? searchRefetch : permissionRefetch;
  const isFetching = shouldUseSearchResults ? searchIsFetching : permissionIsFetching;

  useEffect(() => {
    if (isError) {
      console.error("Error fetching customers:", error);
    }
  }, [isError, error]);

  // Log the API response
  useEffect(() => {
    console.log('Active Customers API Response:', {
      customersData,
      isLoading,
      isError,
      error,
      customersCount: customersData?.results?.length || 0,
      totalData: customersData?.total_data || 0,
      usingSearchResults: shouldUseSearchResults
    });
  }, [customersData, isLoading, isError, error, shouldUseSearchResults]);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchValue]);

  const handleViewCustomer = (customerNo: string) => {
    console.log(`View customer ${customerNo} details`);
  };

  // Map API response fields to Customer interface fields
  const mapApiResponseToCustomer = (apiCustomer: any): Customer => {
    // Helper function to clean and validate string values
    const cleanString = (value: any): string => {
      if (!value) return "";
      const cleaned = String(value).trim();
      return cleaned === "" ? "" : cleaned;
    };

    return {
      id: apiCustomer.id || "",
      customer_no: apiCustomer.customer_no || "",
      name: cleanString(apiCustomer.customer_name),
      email: cleanString(apiCustomer.primary_email),
      phone: cleanString(apiCustomer.phone),
      position: cleanString(apiCustomer.position),
      company: cleanString(apiCustomer.company),
      nationalId: cleanString(apiCustomer.national_id),
      passportNo: cleanString(apiCustomer.passport_no),
      kraPin: cleanString(apiCustomer.kra_pin),
      dob: cleanString(apiCustomer.dob),
      gender: cleanString(apiCustomer.gender),
      maritalStatus: cleanString(apiCustomer.marital_status),
      alternativePhone: cleanString(apiCustomer.alternative_phone),
      alternativeEmail: cleanString(apiCustomer.alternative_email),
      address: cleanString(apiCustomer.address),
      customerType: cleanString(apiCustomer.customer_type) as "Individual" | "Group" | undefined,
      countryOfResidence: cleanString(apiCustomer.country_of_residence),
      dateOfRegistration: cleanString(apiCustomer.date_of_registration),
      otp: cleanString(apiCustomer.otp),
      otpGeneratedAt: cleanString(apiCustomer.otp_generated_at),
      leadSource: apiCustomer.lead_source || 0,
      marketer: cleanString(apiCustomer.marketer),
      plotNumbers: cleanString(apiCustomer.plot_numbers),
    };
  };

  const columns: ColumnDef<Customer>[] = [
    {
      accessorKey: "customer_no",
      header: "Customer No",
      cell: (info) => {
        const rowData = info.row.original;
        const customerNo = (info.getValue() as string) || "N/A";
        return (
          <Link
            to={`/customer/${rowData.customer_no}`}
            className='block font-bold text-blue-700 underline'
          >
            {customerNo}
          </Link>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "name",
      header: "Customer Name",
      cell: (info) => {
        const rowData = info.row.original;
        const name = (info.getValue() as string) || "N/A";
        return (
          <Link
            to={`/customer/${rowData.customer_no}`}
            className='block font-medium hover:text-blue-700 hover:underline'
          >
            {name}
          </Link>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "phone",
      header: "Phone Number",
      cell: (info) => (
        <span className="font-medium">
          {(info.getValue() as string) || "N/A"}
        </span>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "email",
      header: "Primary Email",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: true,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: true,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const customer = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            <Link to={`/customer/${customer.customer_no}`}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewCustomer(customer.customer_no)}
                className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
              >
                <span>View</span>
              </Button>
            </Link>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  // Get customers data array
  const customersArray = customersData?.results || [];

  // Transform API response data to match our Customer interface
  const transformedCustomers = customersArray.map(mapApiResponseToCustomer);

  // Debug: Log the first few transformed customers
  useEffect(() => {
    if (transformedCustomers.length > 0) {
      console.log('Transformed customers sample:', transformedCustomers.slice(0, 3));
      console.log('Raw API customers sample:', customersArray.slice(0, 3));
    }
  }, [transformedCustomers, customersArray]);

  return (
    <div className="space-y-6">
      {/* Permission Indicator */}
      {/* <CustomerPermissionIndicator /> */}
      
      {/* Main Table Card */}
      {(hasAnyCustomerAccess || shouldUseSearchResults) ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 relative">
          <input
                                        value={searchInput}
                                        name="searchInput"
                                        type="search"
                                        onChange={(e) =>
                                          searchDebouncer(
                                            e.target.value,
                                            setSearchInput,
                                            setSearchValue
                                          )
                                        }
                                        className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                                        placeholder="Search Customers..."
                                      />
            {(isLoading || isFetching) ? (
              <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                <SpinnerTemp type="spinner-double" size="md" />
              </div>
            ) : null}
            
            {isError ? (
              <div className="text-center py-12">
                <div className="text-red-500 text-lg font-medium">
                  Failed to load customers
                </div>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  Please try again later.
                </p>
                <Button onClick={() => refetch()} className="mt-4">
                  Retry
                </Button>
              </div>
            ) : transformedCustomers.length === 0 && !isLoading ? (
              <div className="text-center py-12">
                                <div className="text-gray-500 text-lg font-medium">
                                  {searchInput
                                    ? "No customers found matching your search"
                                    : "No customers found"}
                                </div>
                                <p className="text-gray-400 mt-2">
                                  {searchInput
                                    ? "Try adjusting your search terms or search with at least 3 characters"
                                    : "customers will appear here once they are added to the system"}
                                </p>
                                <a  href='/customers/overview#all-customers'>
                                Reload Customers
                                </a> 
                               
                              </div>
            ) : (
              <DataTable<Customer>
                data={transformedCustomers}
                columns={columns}
                enableToolbar
                enableExportToExcel
                enablePrintPdf
                enablePagination
                enableColumnFilters
                enableSorting
                enableSelectColumn
                title={shouldUseSearchResults ? "Customer Search Results" : "All Customers List"}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                totalItems={customersData?.total_data || 0}
                tBodyCellsClassName="border-t !p-2"
            />
            )}
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
          <div className="text-red-500 text-lg font-medium mb-2">
            Access Restricted
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            You don't have permission to view customer data. However, you can search for specific customers.
          </p>
          <div className="max-w-md mx-auto">
            <SearchComponent 
              universalSearchValue={searchValue} 
              setuniversalSearchValue={setSearchValue}
              placeholder="Search for customers (min 2 characters)..."
            />
          </div>
          {searchValue && searchValue.length >= 2 && (
            <p className="text-sm text-gray-500 mt-2">
              Searching all customers for: "{searchValue}"
            </p>
          )}
        </div>
      )}
    </div>
  );
}

export default Customers

interface SearchComponentProps {
  universalSearchValue: string,
  setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>,
  placeholder?: string
}

function SearchComponent({ 
  universalSearchValue, 
  setuniversalSearchValue, 
  placeholder = "Search customer details..." 
}: SearchComponentProps) {
  return (
    <input
      value={universalSearchValue}
      onChange={e => setuniversalSearchValue(e.target.value)}
      className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
      placeholder={placeholder}
    />
  );
}