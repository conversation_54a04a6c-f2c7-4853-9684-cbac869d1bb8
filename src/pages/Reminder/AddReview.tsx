import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import BaseModal from "@/components/custom/modals/BaseModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Button } from "@/components/ui/button";
import {
  Bell,
  Calendar,
  AlertTriangle,
  AlertCircle,
  Circle,
  CheckCircle2,
  User,
} from "lucide-react";
import { format } from "date-fns";

interface AddEventModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  date: Date;
  newEvent: {
    title: string;
    description: string;
    reminder_time: string;
    reminder_date: string;
    reminder_type: 'Follow-up Call' | 'Payment Reminder' | 'Document Collection' | 'Site Visit' | 'Meeting' | 'Email' | 'SMS' | 'General';
    priority: 'Low' | 'Normal' | 'High' | 'Urgent';
    status: 'Active' | 'Snoozed' | 'Completed' | 'Cancelled';
    reminder_notes: string;
    action_url: string;
    client_type?: 'Prospect' | 'Customer' | 'Sale' | null; // Made optional and nullable
    created_by: string;
    customer?: string | null; // Made optional
    prospect?: number | null; // Made optional
    sale?: string | null; // Made optional
  };
  setNewEvent: (event: AddEventModalProps["newEvent"]) => void;
  handleAddEvent: () => void;
}

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'Urgent': return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'High': return <AlertCircle className="h-4 w-4 text-orange-500" />;
    case 'Normal': return <Circle className="h-4 w-4 text-blue-500" />;
    case 'Low': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    default: return <Circle className="h-4 w-4 text-gray-500" />;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'Urgent': return 'border-red-200 bg-red-50';
    case 'High': return 'border-orange-200 bg-orange-50';
    case 'Normal': return 'border-blue-200 bg-blue-50';
    case 'Low': return 'border-green-200 bg-green-50';
    default: return 'border-gray-200 bg-gray-50';
  }
};

export default function AddEventModal({
  isOpen,
  onOpenChange,
  date,
  newEvent,
  setNewEvent,
  handleAddEvent,
}: AddEventModalProps) {
  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="✨ Create New Reminder"
      description={`Schedule a reminder for ${format(new Date(newEvent.reminder_date), 'MMMM d, yyyy')}`}
      footer={
        <>
          <PrimaryButton variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </PrimaryButton>
          <PrimaryButton
            onClick={handleAddEvent}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
          >
            <Bell className="h-4 w-4 mr-2" />
            Create Reminder
          </PrimaryButton>
        </>
      }
    >
      <div className="space-y-6 py-4">
        {/* Basic Information Card */}
        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center gap-2 mb-3">
              <Bell className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-green-800">Basic Information</h3>
            </div>

            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                Title *
              </Label>
              <Input
                id="title"
                value={newEvent.title}
                onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                className="border-green-200 focus:border-green-400 focus:ring-green-200"
                placeholder="What would you like to be reminded about?"
                maxLength={255}
              />
            </div>

            {/* Date */}
            <div className="space-y-2">
              <Label htmlFor="reminder_date" className="text-sm font-medium text-gray-700">
                Date *
              </Label>
              <Input
                id="reminder_date"
                type="date"
                value={newEvent.reminder_date}
                onChange={(e) => setNewEvent({ ...newEvent, reminder_date: e.target.value })}
                className="border-green-200 focus:border-green-400 focus:ring-green-200"
              />
            </div>

            {/* Time */}
            <div className="space-y-2">
              <Label htmlFor="reminder_time" className="text-sm font-medium text-gray-700">
                Time *
              </Label>
              <Input
                id="reminder_time"
                type="time"
                value={newEvent.reminder_time}
                onChange={(e) => setNewEvent({ ...newEvent, reminder_time: e.target.value })}
                className="border-green-200 focus:border-green-400 focus:ring-green-200"
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Description
              </Label>
              <Textarea
                id="description"
                value={newEvent.description}
                onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                className="border-green-200 focus:border-green-400 focus:ring-green-200 min-h-[80px]"
                placeholder="Add more details about this reminder..."
              />
            </div>
          </CardContent>
        </Card>

        {/* Settings Card */}
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center gap-2 mb-3">
              <Bell className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-blue-800">Reminder Settings</h3>
            </div>

            <div className="grid grid-cols-2 gap-4">
              {/* Reminder Type */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Type</Label>
                <Select
                  value={newEvent.reminder_type}
                  onValueChange={(value) => setNewEvent({ 
                    ...newEvent, 
                    reminder_type: value as 'Follow-up Call' | 'Payment Reminder' | 'Document Collection' | 'Site Visit' | 'Meeting' | 'Email' | 'SMS' | 'General'
                  })}
                >
                  <SelectTrigger className="border-blue-200 focus:border-blue-400">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {['Follow-up Call', 'Payment Reminder', 'Document Collection', 'Site Visit', 'Meeting', 'Email', 'SMS', 'General'].map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Priority</Label>
                <Select
                  value={newEvent.priority}
                  onValueChange={(value) => setNewEvent({ 
                    ...newEvent, 
                    priority: value as 'Low' | 'Normal' | 'High' | 'Urgent'
                  })}
                >
                  <SelectTrigger className={`border-blue-200 focus:border-blue-400 ${getPriorityColor(newEvent.priority)}`}>
                    <div className="flex items-center gap-2">
                      {getPriorityIcon(newEvent.priority)}
                      <SelectValue placeholder="Select priority" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {['Low', 'Normal', 'High', 'Urgent'].map((priority) => (
                      <SelectItem key={priority} value={priority}>
                        <div className="flex items-center gap-2">
                          {getPriorityIcon(priority)}
                          {priority}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Status</Label>
              <Select
                value={newEvent.status}
                onValueChange={(value) => setNewEvent({ 
                  ...newEvent, 
                  status: value as 'Active' | 'Snoozed' | 'Completed' | 'Cancelled'
                })}
              >
                <SelectTrigger className="border-blue-200 focus:border-blue-400">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {['Active', 'Completed', 'Snoozed', 'Cancelled'].map((status) => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Client Type - Made optional */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Client Type (Optional)
              </Label>
              <Select
                value={newEvent.client_type || ''}
                onValueChange={(value) => setNewEvent({ 
                  ...newEvent, 
                  client_type: value ? value as 'Prospect' | 'Customer' | 'Sale' : null
                })}
              >
                <SelectTrigger className="border-blue-200 focus:border-blue-400">
                  <SelectValue placeholder="Not specified" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Not specified</SelectItem>
                  {['Prospect', 'Customer', 'Sale'].map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information Card - All fields optional */}
        <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-yellow-50">
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center gap-2 mb-3">
              <User className="h-5 w-5 text-amber-600" />
              <h3 className="font-semibold text-amber-800">Additional Information (Optional)</h3>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="reminder_notes" className="text-sm font-medium text-gray-700">
                Notes
              </Label>
              <Textarea
                id="reminder_notes"
                value={newEvent.reminder_notes}
                onChange={(e) => setNewEvent({ ...newEvent, reminder_notes: e.target.value })}
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-200 min-h-[80px]"
                placeholder="Additional notes for the reminder..."
              />
            </div>

            {/* Action URL */}
            <div className="space-y-2">
              <Label htmlFor="action_url" className="text-sm font-medium text-gray-700">
                Action URL
              </Label>
              <Input
                id="action_url"
                value={newEvent.action_url || ''}
                onChange={(e) => setNewEvent({ ...newEvent, action_url: e.target.value })}
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-200"
                placeholder="https://example.com"
                maxLength={200}
              />
            </div>

            {/* Customer */}
            <div className="space-y-2">
              <Label htmlFor="customer" className="text-sm font-medium text-gray-700">
                Customer
              </Label>
              <Input
                id="customer"
                value={newEvent.customer || ''}
                onChange={(e) => setNewEvent({ ...newEvent, customer: e.target.value })}
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-200"
                placeholder="Customer name"
              />
            </div>

            {/* Prospect ID */}
            <div className="space-y-2">
              <Label htmlFor="prospect" className="text-sm font-medium text-gray-700">
                Prospect ID
              </Label>
              <Input
                id="prospect"
                type="number"
                value={newEvent.prospect || ''}
                onChange={(e) => setNewEvent({ 
                  ...newEvent, 
                  prospect: e.target.value ? parseInt(e.target.value) : null 
                })}
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-200"
                placeholder="Prospect ID"
              />
            </div>

            {/* Sale */}
            <div className="space-y-2">
              <Label htmlFor="sale" className="text-sm font-medium text-gray-700">
                Sale
              </Label>
              <Input
                id="sale"
                value={newEvent.sale || ''}
                onChange={(e) => setNewEvent({ ...newEvent, sale: e.target.value })}
                className="border-amber-200 focus:border-amber-400 focus:ring-amber-200"
                placeholder="Sale reference"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </BaseModal>
  );
}