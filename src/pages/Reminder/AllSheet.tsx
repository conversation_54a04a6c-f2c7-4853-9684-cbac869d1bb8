import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  ListFilter,
  Pencil,
  Trash2,
  Calendar,
  Clock,
  ChevronRight,
  Edit,
  AlertTriangle,
  AlertCircle,
  Circle,
  CheckCircle2,
  Tag,
  Repeat,
  Bell
} from "lucide-react";
import OnlyIconButton from "@/components/custom/buttons/onlyiconbutton";

interface AllEventsSheetProps {
  events: Array<{
    id: string;
    date: Date;
    title: string;
    description: string;
    time: string;
    status: string;
    priority?: string;
    reminder_type?: string;
    tags?: string | null;
    repeat_pattern?: string;
    advance_notice_minutes?: number;
    snoozed?: boolean;
  }>;
  handleRemoveEvent: (eventId: string) => void;
  handleEditEvent: (event: any) => void;
}

export default function AllEventsSheet({
  events,
  handleRemoveEvent,
  handleEditEvent,
}: AllEventsSheetProps) {
  const [filter, setFilter] = useState("all");

  const getPriorityIcon = (priority?: string) => {
    switch (priority) {
      case 'Critical': return <AlertTriangle className="h-3 w-3 text-red-500" />;
      case 'High': return <AlertCircle className="h-3 w-3 text-orange-500" />;
      case 'Normal': return <Circle className="h-3 w-3 text-blue-500" />;
      case 'Low': return <CheckCircle2 className="h-3 w-3 text-green-500" />;
      default: return <Circle className="h-3 w-3 text-gray-500" />;
    }
  };

  const getTypeIcon = (type?: string) => {
    switch (type) {
      case 'Task': return '✅';
      case 'Appointment': return '📅';
      case 'Meeting': return '🤝';
      case 'Birthday': return '🎂';
      case 'Anniversary': return '💝';
      case 'Payment': return '💳';
      case 'Health': return '🏥';
      default: return '📋';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "active":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "snoozed":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };
  
  const filteredEvents = events.filter(event => {
    if (filter === "all") return true;
    return event.status.toLowerCase() === filter.toLowerCase();
  });
  
  const sortedEvents = filteredEvents.sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  return (
    <Sheet>
      <SheetTrigger asChild>
        <PrimaryButton variant="outline" size="sm" className="flex items-center gap-2 border-green-200 text-green-700 hover:bg-green-50">
          <ListFilter className="h-4 w-4" />
          <span>View All ({events.length})</span>
        </PrimaryButton>
      </SheetTrigger>
      <SheetContent className="sm:max-w-lg bg-gradient-to-b from-green-50 to-white">
        <SheetHeader className="pb-6">
          <SheetTitle className="text-2xl text-green-800 flex items-center gap-2">
            📋 All Reminders
          </SheetTitle>
          <SheetDescription className="text-green-600">
            View and manage your reminders • {events.length} total
          </SheetDescription>
        </SheetHeader>

        <div className="flex gap-2 my-4 overflow-x-auto pb-2">
          {["all", "active", "completed", "snoozed", "cancelled"].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`px-3 py-2 rounded-lg text-xs font-medium capitalize whitespace-nowrap transition-all ${
                filter === status
                  ? "bg-green-600 text-white shadow-md"
                  : "bg-white text-green-700 border border-green-200 hover:bg-green-50"
              }`}
            >
              {status === 'all' ? `All (${events.length})` : status}
            </button>
          ))}
        </div>
        
        <Separator className="my-2" />
        
        <div className="mt-4">
          <ScrollArea className="h-96 pr-4">
            {sortedEvents.length > 0 ? (
              sortedEvents.map((event) => (
                <Card
                  key={event.id}
                  className="mb-4 border-2 hover:shadow-md transition-all duration-200 bg-gradient-to-br from-white to-gray-50"
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getTypeIcon(event.reminder_type)}</span>
                        <div>
                          <h4 className="font-semibold text-gray-800 line-clamp-1">{event.title}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={`text-xs ${getStatusColor(event.status)}`}>
                              {event.status}
                            </Badge>
                            {event.priority && (
                              <Badge variant="outline" className="text-xs">
                                <div className="flex items-center gap-1">
                                  {getPriorityIcon(event.priority)}
                                  {event.priority}
                                </div>
                              </Badge>
                            )}
                            {event.snoozed && (
                              <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                                <Bell className="h-3 w-3 mr-1" />
                                Snoozed
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-1">
                        <OnlyIconButton
                          onClick={() => handleEditEvent(event)}
                          icon={Edit}
                          className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                        >
                          <Pencil className="h-4 w-4" />
                        </OnlyIconButton>
                        <OnlyIconButton
                          onClick={() => handleRemoveEvent(event.id)}
                          icon={Trash2}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </OnlyIconButton>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3.5 w-3.5" />
                          <span>{event.date.toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3.5 w-3.5" />
                          <span>{event.time}</span>
                        </div>
                        {event.advance_notice_minutes && (
                          <Badge variant="secondary" className="text-xs">
                            {event.advance_notice_minutes}min notice
                          </Badge>
                        )}
                      </div>

                      {event.description && (
                        <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded line-clamp-2">
                          {event.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {event.tags && event.tags.trim() && (
                            <div className="flex items-center gap-1">
                              <Tag className="h-3 w-3 text-gray-500" />
                              {event.tags.split(',').slice(0, 2).map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag.trim()}
                                </Badge>
                              ))}
                              {event.tags.split(',').length > 2 && (
                                <span className="text-xs text-gray-500">+{event.tags.split(',').length - 2}</span>
                              )}
                            </div>
                          )}

                          {event.repeat_pattern && event.repeat_pattern !== 'None' && (
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Repeat className="h-3 w-3" />
                              <span>{event.repeat_pattern}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card className="border-2 border-dashed border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
                <CardContent className="flex flex-col items-center justify-center h-40 text-center p-6">
                  <div className="mb-3 p-3 rounded-full bg-green-100">
                    <Calendar className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-green-800 mb-1">No reminders found</h3>
                  <p className="text-xs text-green-600">
                    {filter !== "all" ? "Try changing your filter" : "Add your first reminder to get started"}
                  </p>
                </CardContent>
              </Card>
            )}
          </ScrollArea>
        </div>
      </SheetContent>
    </Sheet>
  );
}