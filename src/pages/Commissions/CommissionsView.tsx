import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { motion } from "framer-motion";
import { BarChart3, Building2, Users, TrendingUp, Activity, ArrowUpRight, Sparkles } from "lucide-react";
import { useState } from "react";
import ReportsModal from "../CustomDshboard/ReportsModal";
import ReportsModalHeader from "./ReportPeriod";
import CommissionModal from "./CommissionPeriod";

function CommissionsView() {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isHeaderModalOpen, setIsHeaderModalOpen] = useState(false);
    const [isCommissionLineOpen, setCommissionLineOpen] = useState(false);
    
    const containerVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const cardHoverVariants = {
        hover: { 
            scale: 1.02,
            y: -5,
            transition: { 
                type: "spring", 
                stiffness: 400, 
                damping: 25 
            }
        }
    };

    return (
        <Screen>
            <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="space-y-8"
            >
                {/* Header Section */}
                <motion.div 
                    variants={itemVariants}
                    className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between"
                >
                    <div className="flex items-center gap-4">
                        <motion.div 
                            whileHover={{ rotate: 360 }}
                            transition={{ duration: 0.6 }}
                            className="relative p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg"
                        >
                            <BarChart3 className="w-10 h-10 text-white" />
                            <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center"
                            >
                                <Sparkles className="w-2 h-2 text-yellow-800" />
                            </motion.div>
                        </motion.div>
                        <div>
                            <motion.h1 
                                variants={itemVariants}
                                className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent"
                            >
                                Commission Dashboard
                            </motion.h1>
                            <motion.p 
                                variants={itemVariants}
                                className="text-gray-600 dark:text-gray-400 text-lg mt-1"
                            >
                                Monitor performance and track earnings
                            </motion.p>
                        </div>
                    </div>

                    {/* Stats Cards */}
                    <motion.div 
                        variants={itemVariants}
                        className="grid grid-cols-1 sm:grid-cols-3 gap-4 w-full lg:w-auto lg:min-w-[600px]"
                    >
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 rounded-2xl p-4 border border-blue-200/50 dark:border-blue-700/50 backdrop-blur-sm"
                        >
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-blue-500 rounded-xl shadow-lg">
                                    <Building2 className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                    <p className="text-blue-600 dark:text-blue-400 text-sm font-semibold">Total Offices</p>
                                    <p className="text-blue-900 dark:text-blue-100 text-2xl font-bold">4</p>
                                </div>
                            </div>
                            <motion.div
                                animate={{ x: [0, 100, 0] }}
                                transition={{ duration: 3, repeat: Infinity }}
                                className="absolute top-0 right-0 w-20 h-20 bg-blue-200/30 rounded-full -mr-10 -mt-10"
                            />
                        </motion.div>

                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 rounded-2xl p-4 border border-green-200/50 dark:border-green-700/50 backdrop-blur-sm"
                        >
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-500 rounded-xl shadow-lg">
                                    <Users className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                    <p className="text-green-600 dark:text-green-400 text-sm font-semibold">Total Marketers</p>
                                    <p className="text-green-900 dark:text-green-100 text-2xl font-bold"></p>
                                </div>
                            </div>
                            <motion.div
                                animate={{ x: [0, -100, 0] }}
                                transition={{ duration: 4, repeat: Infinity }}
                                className="absolute top-0 right-0 w-20 h-20 bg-green-200/30 rounded-full -mr-10 -mt-10"
                            />
                        </motion.div>

                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/20 rounded-2xl p-4 border border-purple-200/50 dark:border-purple-700/50 backdrop-blur-sm"
                        >
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-purple-500 rounded-xl shadow-lg">
                                    <TrendingUp className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                    <p className="text-purple-600 dark:text-purple-400 text-sm font-semibold">Avg Performance</p>
                                    <p className="text-purple-900 dark:text-purple-100 text-2xl font-bold">87%</p>
                                </div>
                            </div>
                            <motion.div
                                animate={{ scale: [1, 1.1, 1] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="absolute top-0 right-0 w-20 h-20 bg-purple-200/30 rounded-full -mr-10 -mt-10"
                            />
                        </motion.div>
                    </motion.div>
                </motion.div>

                {/* Main Content Cards */}
                <motion.div 
                    variants={itemVariants}
                    className="grid grid-cols-1 md:grid-cols-2 gap-8"
                >
                    {/* Commission Headers Card */}
                    <motion.div
                        variants={cardHoverVariants}
                        whileHover="hover"
                        className="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-3xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm"
                    >
                        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        
                        <div className="relative p-8">
                            <div className="flex items-start justify-between mb-6">
                                <div className="flex items-center gap-4">
                                    <motion.div 
                                        whileHover={{ scale: 1.1, rotate: 5 }}
                                        className="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl shadow-lg"
                                    >
                                        <BarChart3 className="w-7 h-7 text-white" />
                                    </motion.div>
                                    <div>
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                                            Commission Headers
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                                            View commission summary reports
                                        </p>
                                    </div>
                                </div>
                                <motion.div
                                    whileHover={{ scale: 1.1 }}
                                    className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                    <ArrowUpRight className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                                </motion.div>
                            </div>
                            
                            <motion.div
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <PrimaryButton
                                    onClick={() => setCommissionLineOpen(true)}
                                    className="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                                    size="sm"
                                >
                                    View Headers
                                </PrimaryButton>
                            </motion.div>
                        </div>
                        
                        {/* Decorative elements */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-emerald-200/20 rounded-full blur-2xl" />
                        <div className="absolute -bottom-5 -left-5 w-20 h-20 bg-emerald-300/20 rounded-full blur-xl" />
                    </motion.div>

                    {/* Commission Lines Card */}
                    <motion.div
                        variants={cardHoverVariants}
                        whileHover="hover"
                        className="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-3xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm"
                    >
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        
                        <div className="relative p-8">
                            <div className="flex items-start justify-between mb-6">
                                <div className="flex items-center gap-4">
                                    <motion.div 
                                        whileHover={{ scale: 1.1, rotate: -5 }}
                                        className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg"
                                    >
                                        <Activity className="w-7 h-7 text-white" />
                                    </motion.div>
                                    <div>
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                                            Commission Lines
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                                            View detailed commission line items
                                        </p>
                                    </div>
                                </div>
                                <motion.div
                                    whileHover={{ scale: 1.1 }}
                                    className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                    <ArrowUpRight className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </motion.div>
                            </div>
                            
                            <motion.div
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <PrimaryButton
                                    onClick={() => setIsHeaderModalOpen(true)}
                                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                                    size="sm"
                                >
                                    View Lines
                                </PrimaryButton>
                            </motion.div>
                        </div>
                        
                        {/* Decorative elements */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-blue-200/20 rounded-full blur-2xl" />
                        <div className="absolute -bottom-5 -left-5 w-20 h-20 bg-blue-300/20 rounded-full blur-xl" />
                    </motion.div>
                </motion.div>

                {/* Background decorative elements */}
                <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
                    <motion.div
                        animate={{ 
                            x: [0, 100, 0],
                            y: [0, -50, 0],
                            rotate: [0, 180, 360]
                        }}
                        transition={{ 
                            duration: 20, 
                            repeat: Infinity, 
                            ease: "linear" 
                        }}
                        className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
                    />
                    <motion.div
                        animate={{ 
                            x: [0, -150, 0],
                            y: [0, 100, 0],
                            rotate: [360, 180, 0]
                        }}
                        transition={{ 
                            duration: 25, 
                            repeat: Infinity, 
                            ease: "linear" 
                        }}
                        className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-emerald-400/10 to-blue-400/10 rounded-full blur-3xl"
                    />
                </div>
            </motion.div>

            {/* Modals */}
            <ReportsModal open={isModalOpen} onOpenChange={setIsModalOpen} />
            <ReportsModalHeader open={isHeaderModalOpen} onOpenChange={setIsHeaderModalOpen} />
            <CommissionModal open={isCommissionLineOpen} onOpenChange={setCommissionLineOpen} />
        </Screen>
    );
}

export default CommissionsView;