import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { useGetCommissionLinesQuery } from '@/redux/slices/commissionApiSlice';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';

export interface CommissionReport {
  id: number;
  period_start_date: string;
  period_end_date: string;
  role: string;
  Deposit_amount: number;
  deposit_perc: number;
  deposit_commission: number;
  installment_amount: number;
  installment_perc: number;
  installment_commission: number;
  Total_commission: number;
  Tl_gained_comm_from_members: number;
  rm_achieved_MIB: number;
  rm_commission_rate: number;
  rm_commission_amount: number;
  commisison_payable_TL: number;
  emp_no_id: string;
}

export interface CommissionLine {
  id: number;
  period_start_date: string;
  period_end_date: string;
  transaction_date: string;
  plot_number: string;
  new_deposits_collected: number;
  installments_collected: number;
  marketer_no_id: string;
}

interface CommissionLineProps {
  isModalOpen: boolean;
  setIsModalOpen: (state: boolean) => void; // Fixed: should accept boolean parameter
  params?: {
    period_name: string;
    start_date: string;
    end_date: string;
    // add more fields if needed
  };
}

const CommissionHeaders = ({ isModalOpen, setIsModalOpen, params }: CommissionLineProps) => {
    const shouldFetch = !!params?.start_date;

    useGetCommissionLinesQuery(
      {
        MARKETING_PERIOD: params?.start_date || "ALL",
        MARKETER_EMPLOYEE_NO: "ALL",
        page: 1,
        page_size: 20,
      },
      {
        skip: !shouldFetch,
      }
    );

    const handleCloseModal = () => setIsModalOpen(false);

    // Define table columns with proper typing
    const columns: TableColumn<CommissionLine>[] = [
        {
            key: 'marketer_no_id',
            title: 'Marketer No',
        },
        {
            key: 'period_start_date',
            title: 'Period Start',
        },
        {
            key: 'period_end_date',
            title: 'Period End',
        },
        {
            key: 'transaction_date',
            title: 'Transaction Date',
        },
        {
            key: 'plot_number',
            title: 'Plot Number',
        },
        {
            key: 'new_deposits_collected',
            title: 'New Deposits',
            render: (value: number) => <span>{formatNumberWithCommas(value)}</span>,
        },
        {
            key: 'installments_collected',
            title: 'Installments',
            render: (value: number) => <span>{formatNumberWithCommas(value)}</span>,
        },
    ];

    if (!params?.start_date) {
      return <div className="p-4 text-center text-gray-500">Select a period to view commission lines.</div>;
    }

    return (
        <LazyModal<CommissionLine>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Commission Lines Report"
            url="/commission-lines-report"
            params={{
                MARKETING_PERIOD: params?.start_date || "ALL",
                MARKETER_EMPLOYEE_NO: "ALL",
                page: 1,
                page_size: 20,
            }}
            columns={columns}
            size="lg"
        />
    );
}

export default CommissionHeaders;