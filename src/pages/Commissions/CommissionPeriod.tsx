import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Card, CardContent } from "@/components/ui/card";
import { X, Calendar, CheckCircle } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

import { useGetPeriodsQuery } from "@/redux/slices/hrDashboardApiSlice";
import { MarketingPeriod } from "@/types/marketer";
import CommmissionHeaders from "./CommissionLines";
 

interface DateRange {
  from?: Date;
  to?: Date;
}

interface ReportsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onViewReport?: (range: DateRange) => void;
}

export default function CommissionModal({
  open,
  onOpenChange,
  onViewReport,
}: ReportsModalProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<MarketingPeriod | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showReportsTable, setShowReportsTable] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRole, setSelectedRole] = useState<string>("ALL");
  const roles = ["ALL", "HQ", "KAREN", "TEAM-LEADERS", "MARKETER"];

  // Fetch marketing periods from API
  const { data: periodsData, isLoading: isLoadingPeriods, error: periodsError } = useGetPeriodsQuery({
    page: currentPage,
    page_size: 20
  });

  const handlePeriodSelect = (period: MarketingPeriod) => {
    setSelectedPeriod(period);
    // Automatically open the report when a period is selected
    handleViewReport(period);
  };

  const handleViewReport = (period?: MarketingPeriod) => {
    const periodToUse = period || selectedPeriod;
    if (!periodToUse) return;

    setIsLoading(true);
    try {
      const dateRange: DateRange = {
        from: new Date(periodToUse.period_start_date),
        to: new Date(periodToUse.period_end_date),
      };
      onViewReport?.(dateRange);
      setShowReportsTable(true);
      onOpenChange(false); // <-- Add this line to close the modal!
    } finally {
      setTimeout(() => setIsLoading(false), 500);
    }
  };

  const handleReset = () => {
    setSelectedPeriod(null);
    setShowReportsTable(false);
  };

  const handleCloseCommissionModal = () => {
    setShowReportsTable(false);
  };

  const formatPeriodName = (period: MarketingPeriod) => {
    const startDate = new Date(period.period_start_date);
    const endDate = new Date(period.period_end_date);
    return `${format(startDate, "MMM yyyy")} - ${format(endDate, "MMM yyyy")}`;
  };

  return (
    <>
      <BaseModal
        open={open}
        onOpenChange={onOpenChange}
        title="Marketers Performance Reports"
        description="Select a marketing period to view marketer performance reports"
        className="max-w-4xl"
        size="full"
      >
        {/* Office/Role Filter Dropdown */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
            Filter by Office/Role
          </label>
          <select
            value={selectedRole}
            onChange={e => setSelectedRole(e.target.value)}
            className="border rounded px-2 py-1"
          >
            {roles.map(role => (
              <option key={role} value={role}>{role}</option>
            ))}
          </select>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col gap-6 p-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-md"
        >
          <div>
            <div className="flex justify-between items-center mb-4">
              <span className="font-medium text-gray-700 dark:text-gray-200">
                Select Marketing Period
              </span>
              {selectedPeriod && (
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  className="flex items-center gap-2"
                >
                  <X className="w-4 h-4" />
                  Reset
                </PrimaryButton>
              )}
            </div>

            {/* Period Selection Cards */}
            <div className="space-y-4">
              {isLoadingPeriods ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 6 }).map((_, idx) => (
                    <Card key={idx} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-20 bg-gray-200 rounded"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : periodsError ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Failed to load periods. Please try again.</p>
                </div>
              ) : periodsData?.results && periodsData.results.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {periodsData.results.map((period: MarketingPeriod, index: number) => (
                      <Card
                        key={`${period.period_start_date}-${period.period_end_date}-${index}`}
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
                          selectedPeriod?.period_start_date === period.period_start_date && selectedPeriod?.period_end_date === period.period_end_date
                            ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                            : "border-gray-200 hover:border-green-300"
                        )}
                        onClick={() => handlePeriodSelect(period)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                <Calendar className="w-5 h-5 text-green-600 dark:text-green-400" />
                              </div>
                              <div>
                                <h4 className="font-semibold text-gray-900 dark:text-white">
                                  {formatPeriodName(period)}
                                </h4>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {format(new Date(period.period_start_date), "MMM dd")} - {format(new Date(period.period_end_date), "MMM dd, yyyy")}
                                </p>
                              </div>
                            </div>
                            {selectedPeriod?.period_start_date === period.period_start_date && selectedPeriod?.period_end_date === period.period_end_date && (
                              <CheckCircle className="w-5 h-5 text-green-600" />
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Pagination */}
                  {periodsData && periodsData.num_pages > 1 && (
                    <div className="flex items-center justify-between pt-4">
                      <PrimaryButton
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        size="sm"
                      >
                        Previous
                      </PrimaryButton>

                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Page {currentPage} of {periodsData.num_pages}
                      </span>

                      <PrimaryButton
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.min(periodsData.num_pages, prev + 1))}
                        disabled={currentPage === periodsData.num_pages}
                        size="sm"
                      >
                        Next
                      </PrimaryButton>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">No periods available.</p>
                </div>
              )}
            </div>
          </div>

          {/* Selected Summary */}
          {selectedPeriod && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Selected Period Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-green-700 dark:text-green-300">Period:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">{formatPeriodName(selectedPeriod)}</p>
                </div>
                <div>
                  <span className="text-green-700 dark:text-green-300">Date Range:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">
                    {format(new Date(selectedPeriod.period_start_date), "MMM dd")} - {format(new Date(selectedPeriod.period_end_date), "MMM dd, yyyy")}
                  </p>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </BaseModal>

      {/* Commission Headers Modal */}
      <CommmissionHeaders
        isModalOpen={showReportsTable}
        setIsModalOpen={handleCloseCommissionModal}
        params={{
          period_name: selectedPeriod ? formatPeriodName(selectedPeriod) : "",
          start_date: selectedPeriod ? selectedPeriod.period_start_date : "",
          end_date: selectedPeriod ? selectedPeriod.period_end_date : "",
          role: selectedRole, // <-- Only these four roles will be selectable
        }}
      />
    </>
  );
}