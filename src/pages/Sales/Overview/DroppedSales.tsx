import React, { useState } from 'react'
import SalesTable from './SalesTable';
import { useGetDroppedSalesQuery } from '@/redux/slices/sales';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { CircleAlert } from 'lucide-react';
import { searchDebouncer } from '@/utils/debouncers';

const DroppedSales = () => {
    // dropped sales
    const [droppedSalesItemsPerPage, setDroppedSalesItemsPerPage] = useState(20)
    const [droppedSalesCurrentPage, setDroppedSalesCurrentPage] = useState(1)
    const [universalSearchValue, setuniversalSearchValue] = useState('') // debounced value used for API
    const [searchInput, setSearchInput] = useState('') // immediate input value
    const [purchaseType, setPurchaseType] = useState<'' | 'Cash' | 'Installment' | 'Financing'>('')

    // Build params for backend pagination + filtering
    const params = {
        page_size: droppedSalesItemsPerPage,
        page: droppedSalesCurrentPage,
        search: universalSearchValue || undefined,
        lead_file_status_dropped: true,
        purchase_type: purchaseType !== '' ? purchaseType : undefined,
    } as const

    const { data: droppedSalesData, isLoading, isFetching } = useGetDroppedSalesQuery(params);

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    const totalItems = droppedSalesData?.data?.total_data || 0;

    // Reset page to 1 when search or filter changes
    const handleSearchChange = (value: string) => {
        searchDebouncer(value, setSearchInput, (v: string) => {
            setuniversalSearchValue(v);
            setDroppedSalesCurrentPage(1);
        })
    };
    const handlePurchaseTypeChange = (value: '' | 'Cash' | 'Installment' | 'Financing') => {
        setPurchaseType(value);
        setDroppedSalesCurrentPage(1);
    };

    return (
        <>
            <div className='relative'>
                {showLoader ? (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                ) : (!totalItems || totalItems === 0) ? (
                    <div className='flex flex-col gap-2 justify-center items-center h-64'>
                        <CircleAlert size={45} className='text-muted-foreground' />
                        <p className="text-center text-xs">No data available.</p>
                        <p className="text-center text-xs">You may not have permission to view this section.</p>
                    </div>
                ) : (
                    <SalesTable
                        data={droppedSalesData}
                        itemsPerPage={droppedSalesItemsPerPage}
                        setItemsPerPage={setDroppedSalesItemsPerPage}
                        currentPage={droppedSalesCurrentPage}
                        setCurrentPage={setDroppedSalesCurrentPage}
                        SearchComponent={
                            <SearchAndFilter
                                universalSearchValue={searchInput}
                                onSearchChange={handleSearchChange}
                                purchaseType={purchaseType}
                                onPurchaseTypeChange={handlePurchaseTypeChange}
                            />
                        }
                    />

                )}
            </div>
        </>
    )
}

export default DroppedSales

interface SearchAndFilterProps {
    universalSearchValue: string;
    onSearchChange: (value: string) => void;
    purchaseType: '' | 'Cash' | 'Installment' | 'Financing';
    onPurchaseTypeChange: (value: '' | 'Cash' | 'Installment' | 'Financing') => void;
}

function SearchAndFilter({ universalSearchValue, onSearchChange, purchaseType, onPurchaseTypeChange }: SearchAndFilterProps) {
    return (
        <div className="flex gap-2 items-center w-full">
            {/* Search input with debouncer */}
            <input
                value={universalSearchValue}
                onChange={e => onSearchChange(e.target.value)}
                className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
                placeholder="Search sales details..."
            />
            {/* Purchase type filter */}
            <select
                value={purchaseType}
                onChange={(e) => onPurchaseTypeChange(e.target.value as '' | 'Cash' | 'Installment' | 'Financing')}
                className="px-2 py-2 border rounded text-sm bg-white"
                title="Filter by purchase type"
            >
                <option value="">All</option>
                <option value="Cash">Cash</option>
                <option value="Installment">Installment</option>
                <option value="Financing">Financing</option>
            </select>
        </div>
    );
}




// import React, { useState } from 'react'
// import SalesTable from './SalesTable';
// import { useGetAllSalesQuery } from '@/redux/slices/sales';
// import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
// import { useSalesPermissions } from '@/hooks/useSalesPermissions';

// const DroppedSales = () => {
//     // Get sales permissions
//     const { 
//         hasAnySalesAccess,
//         apiParams,
//         userDetails
//     } = useSalesPermissions();

//     // dropped sales
//     const [droppedSalesItemsPerPage, setDroppedSalesItemsPerPage] = useState(20)
//     const [droppedSalesCurrentPage, setDroppedSalesCurrentPage] = useState(1)
//     const [universalSearchValue, setuniversalSearchValue] = useState('')

//     // Get API parameters based on permissions
//     const getApiParams = () => {
//         // Use the simplified permission-based API parameters
//         return {
//             ...apiParams,
//             lead_file_status_dropped: true,
//             page_size: droppedSalesItemsPerPage,
//             page: droppedSalesCurrentPage,
//             search: universalSearchValue || undefined
//         };
//     };

//     const { data: droppedSalesData, isLoading, isFetching } = useGetAllSalesQuery(
//         getApiParams(),
//         {
//             skip: !hasAnySalesAccess // Skip the query if user has no access
//         }
//     );

//     // Show loader when initially loading or when tab is changing
//     const showLoader = isLoading || isFetching;

//     return (
//         <>
//             {hasAnySalesAccess ? (
//                 <div className='relative'>
//                     {showLoader && (
//                         <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
//                             <SpinnerTemp type="spinner-double" size="md" />
//                         </div>
//                     )}
//                     <SalesTable
//                         data={droppedSalesData}
//                         itemsPerPage={droppedSalesItemsPerPage}
//                         setItemsPerPage={setDroppedSalesItemsPerPage}
//                         currentPage={droppedSalesCurrentPage}
//                         setCurrentPage={setDroppedSalesCurrentPage}
//                         SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
//                     />
//                 </div>
//             ) : (
//                 <div className="text-center py-8 text-gray-500">
//                     <p>Contact your administrator to request sales viewing permissions.</p>
//                 </div>
//             )}
//         </>
//     )
// }

// export default DroppedSales

// interface SearchComponentProps {
//     universalSearchValue: string,
//     setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
// }

// function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
//     return <input
//         value={universalSearchValue}
//         onChange={e => setuniversalSearchValue(e.target.value)}
//         className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
//         placeholder="Search sales details..."
//     />
// }