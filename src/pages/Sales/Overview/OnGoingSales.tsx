import React, { useState } from 'react'
import SalesTable from './SalesTable';
import { useGetOngoingSalesQuery } from '@/redux/slices/sales';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { CircleAlert } from 'lucide-react';
import { searchDebouncer } from '@/utils/debouncers';

const OnGoingSales = () => {
    const [universalSearchValue, setuniversalSearchValue] = useState('') // debounced value used for API
    const [searchInput, setSearchInput] = useState('') // immediate input value

    // ongoing sales
    const [onGoingSalesItemsPerPage, setOnGoingSalesItemsPerPage] = useState(20)
    const [onGoingSalesCurrentPage, setOnGoingSalesCurrentPage] = useState(1)
    const [purchaseType, setPurchaseType] = useState<'' | 'Cash' | 'Installment' | 'Financing'>('')

    // Build params for backend pagination + filtering
    const params = {
        page_size: onGoingSalesItemsPerPage,
        page: onGoingSalesCurrentPage,
        search: universalSearchValue || undefined,
        purchase_type: purchaseType !== '' ? purchaseType : undefined,
    } as const

    const { data: ongoingSalesData, isLoading, isFetching } = useGetOngoingSalesQuery(params);

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    const totalItems = ongoingSalesData?.data?.total_data || 0;

    // Reset page to 1 when search or filter changes
    const handleSearchChange = (value: string) => {
        searchDebouncer(value, setSearchInput, (v: string) => {
            setuniversalSearchValue(v);
            setOnGoingSalesCurrentPage(1);
        })
    };
    const handlePurchaseTypeChange = (value: '' | 'Cash' | 'Installment' | 'Financing') => {
        setPurchaseType(value);
        setOnGoingSalesCurrentPage(1);
    };

    return (
        <>
            <div className='relative'>
                {showLoader ? (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                ) : (!totalItems || totalItems === 0) ? (
                <div className='flex flex-col gap-2 justify-center items-center h-64'>
                    <CircleAlert size={45} className='text-muted-foreground' />
                    <p className="text-center text-xs">No data available.</p>
                    <p className="text-center text-xs">You may not have permission to view this section.</p>
                </div>
                ) : (
                <SalesTable
                    data={ongoingSalesData}
                    itemsPerPage={onGoingSalesItemsPerPage}
                    setItemsPerPage={setOnGoingSalesItemsPerPage}
                    currentPage={onGoingSalesCurrentPage}
                    setCurrentPage={setOnGoingSalesCurrentPage}
                    SearchComponent={
                        <SearchAndFilter
                            universalSearchValue={searchInput}
                            onSearchChange={handleSearchChange}
                            purchaseType={purchaseType}
                            onPurchaseTypeChange={handlePurchaseTypeChange}
                        />
                    }
                />
               )}
            </div>
        </>
    )
}

export default OnGoingSales

interface SearchAndFilterProps {
    universalSearchValue: string;
    onSearchChange: (value: string) => void;
    purchaseType: '' | 'Cash' | 'Installment' | 'Financing';
    onPurchaseTypeChange: (value: '' | 'Cash' | 'Installment' | 'Financing') => void;
}

function SearchAndFilter({ universalSearchValue, onSearchChange, purchaseType, onPurchaseTypeChange }: SearchAndFilterProps) {
    return (
        <div className="flex gap-2 items-center w-full">
            {/* Search input with debouncer */}
            <input
                value={universalSearchValue}
                onChange={e => onSearchChange(e.target.value)}
                className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
                placeholder="Search sales details..."
            />
            {/* Purchase type filter */}
            <select
                value={purchaseType}
                onChange={(e) => onPurchaseTypeChange(e.target.value as '' | 'Cash' | 'Installment' | 'Financing')}
                className="px-2 py-2 border rounded text-sm bg-white"
                title="Filter by purchase type"
            >
                <option value="">All</option>
                <option value="Cash">Cash</option>
                <option value="Installment">Installment</option>
                <option value="Financing">Financing</option>
            </select>
        </div>
    );
}