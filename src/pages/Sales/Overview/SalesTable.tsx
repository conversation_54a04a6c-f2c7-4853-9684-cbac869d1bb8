import { DataTable } from '@/components/custom/tables/Table1';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { ColumnDef } from '@tanstack/react-table';
import React, { ReactNode } from 'react'
import { useNavigate } from 'react-router-dom';

interface SalesComponentProps {
    data: any;
    itemsPerPage: number;
    setItemsPerPage: (items: number) => void;
    currentPage?: number;
    setCurrentPage?: (page: number) => void;
    SearchComponent?: ReactNode;
}

interface Sale {
    lead_file_no: string
    customer_name: string
    plot_id: string
    plot_no?: string
    selling_price: number
    total_paid: number
    balance_lcy: number
    marketer_id: string
    fullnames: string
}

export const SalesColumnsDefs: ColumnDef<Sale>[] = [
    {
        accessorKey: 'customer_name',
        header: 'Customer',
        cell: info => {
            return <span className='font-semibold text-blue-700 underline'>{(info.getValue() as string).slice(0, 20)}</span>
        },
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'lead_file_no',
        header: 'Lead No',
        cell: info => {
            return <span className='font-semibold text-blue-700 underline'>{info.getValue() as string}</span>
        },
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'selling_price',
        header: 'Selling Price',
        cell: info => formatNumberWithCommas(info.getValue() as number),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'total_paid',
        header: 'Total Paid',
        cell: info => formatNumberWithCommas(info.getValue() as number),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'balance_lcy',
        header: 'Balance',
        cell: info => formatNumberWithCommas(info.getValue() as number),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'plot',
        header: 'Plot Number',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'marketer',
        header: 'Marketer',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
]

const SalesTable: React.FC<SalesComponentProps> = ({ data: AllSalesData, itemsPerPage, setItemsPerPage, currentPage, setCurrentPage, SearchComponent }) => {
    const navigate = useNavigate();

    // Extract results from the API response
    const salesResults = AllSalesData?.data?.results || [];
    const totalItems = AllSalesData?.data?.total_data || 0;

    return (
        <>
            <DataTable<Sale>
                data={salesResults}
                columns={SalesColumnsDefs}
                title="All Sales"
                enableSelectColumn={false}
                enableColumnFilters={true}
                enableSorting={true}
                enableToolbar={true}
                enableFullScreenToggle={true}
                enableExportToExcel={true}
                enablePrintPdf={true}
                tableClassName='border-none'
                containerClassName='py-2'
                tHeadClassName='border-t'
                tHeadCellsClassName="px-2"
                tBodyCellsClassName="text-xs px-2 !text-[11px]"
                onRowClick={(row: any) => { navigate(`/sales/sales-card/${row.original.lead_file_no}`, { replace: false }); }}
                searchInput={SearchComponent}
                enablePagination={true}
                paginationClassName='px-3 !py-5'
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                totalItems={totalItems}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
            />
        </>
    )
}

export default SalesTable