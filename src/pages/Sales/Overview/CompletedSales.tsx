import React, { useState } from 'react'
import SalesTable from './SalesTable';
import { useGetCompletedSalesQuery } from '@/redux/slices/sales';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { CircleAlert } from 'lucide-react';
import { searchDebouncer } from '@/utils/debouncers';

const CompletedSales = () => {
    // completed sales
    const [completedSalesItemsPerPage, setCompletedSalesItemsPerPage] = useState(20)
    const [completedSalesCurrentPage, setCompletedSalesCurrentPage] = useState(1)
    const [universalSearchValue, setuniversalSearchValue] = useState('') // debounced value used for API
    const [searchInput, setSearchInput] = useState('') // immediate input value
    const [purchaseType, setPurchaseType] = useState<'' | 'Cash' | 'Installment' | 'Financing'>('')

    // Build params for backend pagination + filtering
    const params = {
        page_size: completedSalesItemsPerPage,
        page: completedSalesCurrentPage,
        search: universalSearchValue || undefined,
        // Only include purchase_type when a specific filter is selected
        purchase_type: purchaseType !== '' ? purchaseType : undefined,
    } as const

    const { data: completedSalesData, isLoading, isFetching } = useGetCompletedSalesQuery(params);

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    const totalItems = completedSalesData?.data?.total_data || 0;

    // Reset page to 1 whenever search or filter changes (backend pagination)
    const handleSearchChange = (value: string) => {
        searchDebouncer(value, setSearchInput, (v: string) => {
            setuniversalSearchValue(v);
            setCompletedSalesCurrentPage(1);
        })
    };
    const handlePurchaseTypeChange = (value: '' | 'Cash' | 'Installment' | 'Financing') => {
        setPurchaseType(value);
        setCompletedSalesCurrentPage(1);
    };

    return (
        <>
            <div className='relative'>
                {showLoader ? (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                ) : (!totalItems || totalItems === 0) ? (
                    <div className='flex flex-col gap-2 justify-center items-center h-64'>
                        <CircleAlert size={45} className='text-muted-foreground' />
                        <p className="text-center text-xs">No data available.</p>
                        <p className="text-center text-xs">You may not have permission to view this section.</p>
                    </div>
                ) : (
                    <SalesTable
                        data={completedSalesData}
                        itemsPerPage={completedSalesItemsPerPage}
                        setItemsPerPage={setCompletedSalesItemsPerPage}
                        currentPage={completedSalesCurrentPage}
                        setCurrentPage={setCompletedSalesCurrentPage}
                        SearchComponent={
                            <SearchAndFilter
                                universalSearchValue={searchInput}
                                onSearchChange={handleSearchChange}
                                purchaseType={purchaseType}
                                onPurchaseTypeChange={handlePurchaseTypeChange}
                            />
                        }
                    />
                )}
            </div>
        </>
    )
}

export default CompletedSales

interface SearchAndFilterProps {
    universalSearchValue: string;
    onSearchChange: (value: string) => void;
    purchaseType: '' | 'Cash' | 'Installment' | 'Financing';
    onPurchaseTypeChange: (value: '' | 'Cash' | 'Installment' | 'Financing') => void;
}

function SearchAndFilter({ universalSearchValue, onSearchChange, purchaseType, onPurchaseTypeChange }: SearchAndFilterProps) {
    return (
        <div className="flex gap-2 items-center w-full">
            {/* Search input with debouncer */}
            <input
                value={universalSearchValue}
                onChange={e => onSearchChange(e.target.value)}
                className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
                placeholder="Search sales details..."
            />
            {/* Purchase type filter */}
            <select
                value={purchaseType}
                onChange={(e) => onPurchaseTypeChange(e.target.value as '' | 'Cash' | 'Installment' | 'Financing')}
                className="px-2 py-2 border rounded text-sm bg-white"
                title="Filter by purchase type"
            >
                <option value="">All</option>
                <option value="Cash">Cash</option>
                <option value="Installment">Installment</option>
                <option value="Financing">Financing</option>
            </select>
        </div>
    );
}





// import React, { useState } from 'react'
// import SalesTable from './SalesTable';
// import { useGetCompletedSalesQuery } from '@/redux/slices/sales';
// import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
// import { useSalesPermissions } from '@/hooks/useSalesPermissions';


// const CompletedSales = () => {
//     // Get sales permissions
//     const { 
//         hasAnySalesAccess,
//         apiParams,
//         userDetails
//     } = useSalesPermissions();

//     // compeleted sales
//     const [completedSalesItemsPerPage, setCompletedSalesItemsPerPage] = useState(20)
//     const [completedSalesCurrentPage, setCompletedSalesCurrentPage] = useState(1)
//     const [universalSearchValue, setuniversalSearchValue] = useState('')

//     // Get API parameters based on permissions
//     const getApiParams = () => {
//         // Use the simplified permission-based API parameters
//         return {
//             ...apiParams,
//             page_size: completedSalesItemsPerPage,
//             page: completedSalesCurrentPage,
//             search: universalSearchValue || undefined
//         };
//     };

//     const { data: completedSalesData, isLoading, isFetching } = useGetCompletedSalesQuery(
//         getApiParams(),
//         {
//             skip: !hasAnySalesAccess // Skip the query if user has no access
//         }
//     );

//     // Show loader when initially loading or when tab is changing
//     const showLoader = isLoading || isFetching;

//     return (
//         <>
//             {hasAnySalesAccess ? (
//                 <div className='relative'>
//                     {showLoader && (
//                         <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
//                             <SpinnerTemp type="spinner-double" size="md" />
//                         </div>
//                     )}
//                     <SalesTable
//                         data={completedSalesData}
//                         itemsPerPage={completedSalesItemsPerPage}
//                         setItemsPerPage={setCompletedSalesItemsPerPage}
//                         currentPage={completedSalesCurrentPage}
//                         setCurrentPage={setCompletedSalesCurrentPage}
//                         SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
//                     />
//                 </div>
//             ) : (
//                 <div className="text-center py-8 text-gray-500">
//                     <p>Contact your administrator to request sales viewing permissions.</p>
//                 </div>
//             )}
//         </>
//     )
// }

// export default CompletedSales

// interface SearchComponentProps {
//     universalSearchValue: string,
//     setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
// }

// function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
//     return <input
//         value={universalSearchValue}
//         onChange={e => setuniversalSearchValue(e.target.value)}
//         className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
//         placeholder="Search sales details..."
//     />
// }