import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { Badge } from "@/components/ui/badge";
import { useGetPaymentInstallmentsQuery } from "@/redux/slices/sales";
import { formatNumberWithCommas, formatShortDate } from "@/utils/salesDataFormatter";
import { ColumnDef } from "@tanstack/react-table";
import { TriangleAlert } from "lucide-react";
import { useState } from "react";

interface PaymentInstallmentProps {
    plot_no: string;
}

interface Installment {
    id: number
    member_no: string
    leadfile_no: string
    line_no: number
    installment_no: number
    installment_amount: string
    remaining_Amount: string
    due_date: string
    paid: string
    plot_No: string
    plot_Name: string
    amount_Paid: string
    penaties_Accrued: number

}

const data = [
    {
        "id": 17140,
        "member_no": "CL000022",
        "leadfile_no": "LF000002",
        "line_no": 153925,
        "installment_no": 6,
        "installment_amount": "30,417",
        "remaining_Amount": "0",
        "due_date": "2024-01-06",
        "paid": "Yes",
        "plot_No": "PLT0008027",
        "plot_Name": "OG1680",
        "amount_Paid": "30,417",
        "penaties_Accrued": 0,
        "created_at": null,
        "updated_at": null
    },
    {
        "id": 17139,
        "member_no": "CL000022",
        "leadfile_no": "LF000002",
        "line_no": 153924,
        "installment_no": 5,
        "installment_amount": "30,417",
        "remaining_Amount": "0",
        "due_date": "2023-12-06",
        "paid": "Yes",
        "plot_No": "PLT0008027",
        "plot_Name": "OG1680",
        "amount_Paid": "30,417",
        "penaties_Accrued": 0,
        "created_at": null,
        "updated_at": null
    },
    {
        "id": 17138,
        "member_no": "CL000022",
        "leadfile_no": "LF000002",
        "line_no": 153923,
        "installment_no": 4,
        "installment_amount": "30,417",
        "remaining_Amount": "0",
        "due_date": "2023-11-06",
        "paid": "Yes",
        "plot_No": "PLT0008027",
        "plot_Name": "OG1680",
        "amount_Paid": "30,417",
        "penaties_Accrued": 0,
        "created_at": null,
        "updated_at": null
    },
]

const SalesColumnsDefs: ColumnDef<Installment>[] = [
    {
        accessorKey: 'plot_No',
        header: 'Plot No',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',

    },
    {
        accessorKey: 'penaties_Accrued',
        header: 'Penalty',
        cell: info => formatNumberWithCommas(info.getValue() as string),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'paid',
        header: 'Status',
        cell: info => {
            const paid = info.getValue();
            const isPaid = paid === true || paid === "true" || paid === 1 || paid === "1" || paid === "Yes";
            return (
                <Badge variant={isPaid ? "default" : "destructive"} className="px-4">
                    {isPaid ? "Paid" : "Unpaid"}
                </Badge>
            );
        },
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
]


const Penalties: React.FC<PaymentInstallmentProps> = ({ plot_no }) => {
    const { data, isLoading, isFetching } = useGetPaymentInstallmentsQuery({ plot_No: plot_no, page_size: 1000, });
    
    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    return (
        <div className=" w-[90vw] md:w-[39vw]  ">
            {
                showLoader
                    ? <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                    : data?.data?.results.filter((item:any) => item.penaties_Accrued > 0).length > 0
                        ? <DataTable<Installment>
                            data={data?.data?.results.filter((item:any) => item.penaties_Accrued > 0)}
                            columns={SalesColumnsDefs}
                            enableSelectColumn={false}
                            enableColumnFilters={false}
                            enableSorting={false}
                            tableClassName='border-none  '
                            containerClassName=' pt-2 '
                            tHeadClassName='border-t'
                            tHeadCellsClassName=" px-2  !py-4 !text-gray-900 dark:!text-gray-100 !font-bold"
                            tBodyCellsClassName="text-xs  px-2 !text-gray-900 dark:!text-gray-100"
                        />
                        : <div className='h-[30vh] flex flex-col gap-2 items-center justify-center'>
                            <TriangleAlert size={45} className='text-red-600' />
                            <p className='text-xs'>Penalties data is not available.</p>
                        </div>
            }


        </div>
    );
}

export default Penalties