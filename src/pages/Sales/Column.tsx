// src/components/columns.ts
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

import { PrimaryBadge } from "@/components/custom/badges/badges";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Sales } from "./type";

export const getColumns = (
  handleCloseTransaction: (txnId: string) => void
): ColumnDef<Sales>[] => [
  {
    accessorKey: "id",
    header: "#",
    cell: (info) => <span className="font-medium">{info.getValue() as string}</span>,
    enableColumnFilter: false,
  },
  {
    accessorKey: "LeadFileNo",
    header: "Lead File No",
    cell: (info) => <span className="font-medium">{info.getValue() as string}</span>,
    enableColumnFilter: true,
  },
  {
    accessorKey: "Customername",
    header: "Customer Name",
    cell: (info) => <span className="font-medium">{info.getValue() as string}</span>,
    enableColumnFilter: true,
  },
  {
    accessorKey: "PlotNo",
    header: "Plot No",
    cell: (info) => info.getValue() as string,
    enableColumnFilter: true,
  },
  {
    accessorKey: "PurchasePrice",
    header: "Purchase Price",
    cell: (info) => {
      const amount = info.getValue() as number;
      return (
        <span className="font-medium">
          {amount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </span>
      );
    },
    enableColumnFilter: true,
  },
  {
    accessorKey: "SellingPrice",
    header: "Selling Price",
    cell: (info) => {
      const amount = info.getValue() as number;
      return (
        <span className="font-medium">
          {amount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </span>
      );
    },
    enableColumnFilter: true,
  },
  {
    accessorKey: "TotalPaid",
    header: "Total Paid",
    cell: (info) => {
      const amount = info.getValue() as number;
      return (
        <span className="font-medium">
          {amount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </span>
      );
    },
    enableColumnFilter: true,
  },
  {
    accessorKey: "Balance",
    header: "Balance",
    cell: (info) => {
      const amount = info.getValue() as number;
      return (
        <span className="font-medium">
          {amount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </span>
      );
    },
    enableColumnFilter: true,
  },
  {
    accessorKey: "Marketer",
    header: "Marketer",
    cell: (info) => info.getValue() as string,
    enableColumnFilter: true,
  },
  {
    accessorKey: "PurchaseType",
    header: "Purchase Type",
    cell: (info) => info.getValue() as string,
    enableColumnFilter: true,
  },
  {
    accessorKey: "CommissionThreshhold",
    header: "Commission Threshold",
    cell: (info) => info.getValue() as string,
    enableColumnFilter: true,
  },
  {
    accessorKey: "BookingDate",
    header: "Booking Date",
    cell: (info) => {
      const date = info.getValue() as Date;
      return (
        <span className="font-medium whitespace-nowrap">
          {format(date, "yyyy-MM-dd HH:mm:ss")}
        </span>
      );
    },
    enableColumnFilter: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: (info) => {
      const status = info.getValue() as "ACTIVE" | "DROPPED";
      return (
        <PrimaryBadge
          variant="outline"
          className={`font-medium px-4 py-0.5 transition-colors duration-200 ${
            status === "ACTIVE"
              ? "bg-gradient-to-r from-green-400 to-green-600 text-white"
              : "bg-gradient-to-r from-red-400 to-red-600 text-white"
          }`}
        >
          {status}
        </PrimaryBadge>
      );
    },
    enableColumnFilter: true,
  },
  {
    id: "actions",
    header: "Action",
    cell: ({ row }) => {
      const transaction = row.original;
      return (
        <div className="flex justify-center">
          {transaction.status === "ACTIVE" && (
            <PrimaryButton
              variant="destructive"
              size="sm"
              onClick={() => handleCloseTransaction(transaction.id)}
              className="transition-colors duration-200 bg-red-600 hover:bg-red-700"
            >
              Close
            </PrimaryButton>
          )}
        </div>
      );
    },
    enableColumnFilter: false,
  },
];