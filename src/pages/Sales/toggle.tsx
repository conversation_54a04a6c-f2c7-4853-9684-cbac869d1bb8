
import { motion } from "framer-motion";
import { PrimaryButton, OutlinedButton } from "@/components/custom/buttons/buttons";

interface StatusToggleButtonsProps {
  activeStatus: "ALL" | "ACTIVE" | "DROPPED";
  setActiveStatus: (status: "ALL" | "ACTIVE" | "DROPPED") => void;
}

export function StatusToggleButtons({ activeStatus, setActiveStatus }: StatusToggleButtonsProps) {
  return (
    <motion.div
      className="flex space-x-2 mb-4"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <PrimaryButton
        variant={activeStatus === "ALL" ? "default" : "outline"}
        size="sm"
        onClick={() => setActiveStatus("ALL")}
        className={`transition-colors duration-200 ${
          activeStatus === "ALL"
            ? "bg-blue-600 hover:bg-blue-700"
            : "hover:bg-blue-100 dark:hover:bg-blue-900"
        }`}
      >
        All
      </PrimaryButton>
      <OutlinedButton
        variant={activeStatus === "ACTIVE" ? "default" : "outline"}
        size="sm"
        onClick={() => setActiveStatus("ACTIVE")}
        className={`transition-colors duration-200 ${
          activeStatus === "ACTIVE"
            ? "bg-green-600 hover:bg-green-700"
            : "hover:bg-green-100 dark:hover:bg-blue-900"
        }`}
      >
        Active
      </OutlinedButton>
      <OutlinedButton
        variant={activeStatus === "DROPPED" ? "default" : "outline"}
        size="sm"
        onClick={() => setActiveStatus("DROPPED")}
        className={`transition-colors duration-200 ${
          activeStatus === "DROPPED"
            ? "bg-red-600 hover:bg-red-700"
            : "hover:bg-red-100 dark:hover:bg-blue-900"
        }`}
      >
        Dropped
      </OutlinedButton>
    </motion.div>
  );
}