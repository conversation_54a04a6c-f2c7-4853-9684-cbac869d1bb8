import { PersonStanding } from "lucide-react";
import Tab2 from "@/components/custom/tabs/Tab2";
import { DataTable } from "@/components/custom/tables/Table1";
import { getColumns } from "./Column"; // Import your column definitions
import { TableSkeleton } from "./tableskeleton";

// Define interfaces for type safety
interface Sales {
  id: string;
  status: "ALL" | "ACTIVE" | "DROPPED";
  [key: string]: any; // Add other fields as needed
}

interface TabConfig {
  title: string;
  icon: React.ReactNode;
  value: "ALL" | "ACTIVE" | "DROPPED";
  transactions: Sales[];
}

interface TabSaleProps {
  transactions: Sales[];
  activeStatus: "ALL" | "ACTIVE" | "DROPPED";
  setActiveStatus: (status: "ALL" | "ACTIVE" | "DROPPED") => void;
}

const TabSale = ({ transactions, activeStatus, setActiveStatus }: TabSaleProps) => {
  // Filter transactions based on status
  const filteredTransactions = {
    ALL: transactions,
    ACTIVE: transactions.filter((txn) => txn.status === "ACTIVE"),
    DROPPED: transactions.filter((txn) => txn.status === "DROPPED"),
  };

  // Define tabs configuration
  const tabs: TabConfig[] = [
    {
      title: "All",
      icon: <PersonStanding className="w-5 h-5" />,
      value: "ALL",
      transactions: filteredTransactions.ALL,
    },
    {
      title: "Active",
      icon: <PersonStanding className="w-5 h-5" />,
      value: "ACTIVE",
      transactions: filteredTransactions.ACTIVE,
    },
    {
      title: "Dropped",
      icon: <PersonStanding className="w-5 h-5" />,
      value: "DROPPED",
      transactions: filteredTransactions.DROPPED,
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      <Tab2
        tabs={tabs.map(({ title, icon, value, transactions }) => ({
          title,
          icon,
          value,
          content: (
            <div className="p-4">
            {false ? (
              <TableSkeleton />
            ) : (
              <DataTable<Sales>
                data={transactions}
                columns={getColumns()} // Pass your column definitions here
                enableToolbar={true}
                enableExportToExcel={true}
                enablePagination={true}
                enableColumnFilters={true}
                enableSorting={true}
                tableClassName="border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-md w-full text-sm sm:text-base"
                tHeadClassName="bg-gray-50 dark:bg-gray-700 text-xs uppercase text-gray-600 dark:text-gray-300 font-semibold"
                tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
                tBodyCellsClassName="border-t border-gray-200 dark:border-gray-600 py-2 sm:py-4 px-2 sm:px-4"
              />
            )}

            </div>
          ),
        }))}
        activeTab={activeStatus}
        onTabChange={(tabValue: string) => {
          // Validate tabValue before casting
          if (["ALL", "ACTIVE", "DROPPED"].includes(tabValue)) {
            setActiveStatus(tabValue as "ALL" | "ACTIVE" | "DROPPED");
          }
        }}
      />
    </div>
  );
};

export default TabSale;