import { useState } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { Check } from "lucide-react";

export default function AddDigitalLeadModal({ isOpen, onOpenChange }: { isOpen: boolean; onOpenChange: (open: boolean) => void }) {
  const [leadDetails, setLeadDetails] = useState({
    name: "",
    phoneNumber: "",
    email: "",
    region: "",
    allocatedMarketer: "",
    telemarketer: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLeadDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    console.log("New Lead Details:", leadDetails);
    onOpenChange(false); // Close the modal after submission
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Add New Lead"
      description="Complete the following steps to add a new lead"
      size="lg"
      steps={[
        {
          title: "Lead Information",
          content: (
            <div className="space-y-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">Enter Lead Details</h3>
              <div className="space-y-2">
                <Label htmlFor="lead-name">Name</Label>
                <Input
                  id="lead-name"
                  name="name"
                  value={leadDetails.name}
                  onChange={handleInputChange}
                  placeholder="Enter lead name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lead-phone">Phone Number</Label>
                <Input
                  id="lead-phone"
                  name="phoneNumber"
                  value={leadDetails.phoneNumber}
                  onChange={handleInputChange}
                  placeholder="Enter phone number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lead-email">Email Address</Label>
                <Input
                  id="lead-email"
                  name="email"
                  value={leadDetails.email}
                  onChange={handleInputChange}
                  placeholder="Enter email address"
                />
              </div>
            </div>
          ),
        },
        {
          title: "Region and Allocation",
          content: (
            <div className="space-y-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">Assign Region and Marketers</h3>
              <div className="space-y-2">
                <Label htmlFor="lead-region">Region</Label>
                <Input
                  id="lead-region"
                  name="region"
                  value={leadDetails.region}
                  onChange={handleInputChange}
                  placeholder="Enter region"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="allocated-marketer">Allocated Marketer</Label>
                <Input
                  id="allocated-marketer"
                  name="allocatedMarketer"
                  value={leadDetails.allocatedMarketer}
                  onChange={handleInputChange}
                  placeholder="Enter marketer name"
                />
              </div>
              
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800">Lead Added Successfully!</h3>
              <p className="text-gray-500 mt-2 mb-6">
                The new lead has been successfully added to the system.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg border text-left max-w-md mx-auto">
                <h4 className="text-sm font-medium mb-2">Lead Details:</h4>
                <ul className="list-disc ml-5 text-sm text-gray-600">
                  <li>Name: {leadDetails.name}</li>
                  <li>Phone Number: {leadDetails.phoneNumber}</li>
                  <li>Email: {leadDetails.email}</li>
                  <li>Region: {leadDetails.region}</li>
                  <li>Allocated Marketer: {leadDetails.allocatedMarketer}</li>
                  
                </ul>
              </div>
            </div>
          ),
        },
      ]}
      
    />
  );
}