import { SetStateAction, useState, useMemo } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, Activity, ArrowLeftRight, RefreshCw } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  useGetPropectsQuery,
  useLazyGetLeadSourceQuery,
} from "@/redux/slices/propects";
import { ProspectTypes } from "@/types/prospects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Link } from "react-router-dom";
import ReallocateProspects from "@/pages/Prospects/reallocate";
import CustomSelectField from "@/components/CustomSelectField";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";

interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DigitalLeadsTableModal({}: ProspectsTableModalProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isReallocateModalOpen, setIsReallocateModalOpen] = useState(false);
  const [selectedProspect, setSelectedProspect] = useState<any | null>(null);
  const [actionTitle, setActionTitle] = useState<string>("");
  const [lead_source, setLeadSource] = useState("");
  const [lead_source_label, setLeadSourceLabel] = useState("");
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API

  const [activeTab, setActiveTab] = useState<"allocated" | "unallocated">(
    "allocated"
  );

  // Fetch prospects data
  const {
    data: prospectsData,
    isLoading: prospectsLoading,
    isFetching,
    error: prospectsError,
    refetch,
  } = useGetPropectsQuery({
    marketer__isnull: activeTab === "unallocated" ? true : false,
    department__dp_name: "DIGITAL MARKETING",
    page: currentPage,
    page_size: itemsPerPage, // Get all prospects
    lead_source__id: lead_source,
  });

  const [
    fetchLeadSource,
    { data: leadSourcesData, isLoading: loadingLeadSources },
  ] = useLazyGetLeadSourceQuery();

  const refetchData = async () => {
    setSearchInput("");
    setSearchValue("");
    setLeadSource("");
    setLeadSourceLabel("");
    refetch();
  };

  const handleReallocateProspect = (prospectId: number, actionName: string) => {
    const prospect = prospectsData?.data?.results.find(
      (p: ProspectTypes) => p.id === prospectId
    );
    if (prospect) {
      setSelectedProspect(prospect);
      setActionTitle(actionName);
      setIsReallocateModalOpen(true);
    }
  };

  const columns: ColumnDef<ProspectTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <Link
          to={`/prospects/${info?.row?.original?.id}`}
          title="View Prospect"
        >
          <span className="font-medium underline capitalize text-blue-400">
            {info.getValue() as string}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "phone",
      header: "Phone",
      cell: (info) =>
        (info.getValue() as string) ? (
          <Link
            to={`/prospects/${info?.row?.original?.id}`}
            title="View Prospect"
          >
            <span className="font-medium underline capitalize text-blue-400">
              {info.getValue() as string}
            </span>
          </Link>
        ) : (
          "N/A"
        ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => {
        const pin = info.getValue() as string;
        return <span className="font-medium">{pin ? pin : "--"}</span>;
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "department_member_name",
      header: "Allocated By",
      cell: (info) => (
        <span className="text-green-700 bg-green-300 px-2 py-1 rounded-full">
          {(info.getValue() as string) || "Self"}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "lead_source_name",
      header: "Lead Source",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "project_name",
      header: "Project",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "Hot"
              ? "bg-destructive text-white px-3"
              : info?.getValue() === "Warm"
              ? "bg-yellow-400 text-black px-3"
              : "bg-blue-400 text-white"
          } text-center px-2 py-1 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const prospect = row.original;
        return (
          <div className="flex space-x-2 justify-start">
            {!prospect.marketer ? (
              <PrimaryButton
                variant="primary"
                size="sm"
                onClick={() =>
                  handleReallocateProspect(prospect.id, "Allocate")
                }
                className="text-white bg-green-500 border-green-300 hover:bg-green-50 flex items-center space-x-1"
              >
                <span>Allocate</span>
              </PrimaryButton>
            ) : (
              <PrimaryButton
                variant="primary"
                size="sm"
                onClick={() =>
                  handleReallocateProspect(prospect.id, "Reallocate")
                }
                className="bg-white !text-green-500 border border-green-300 hover:bg-green-300 hover:!text-white flex items-center space-x-1"
              >
                <span title="Reallocate">
                  <ArrowLeftRight />
                </span>
              </PrimaryButton>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <div
    // open={open}
    // onOpenChange={onOpenChange}
    // title="Leads Management"
    // description="View and manage allocated and unallocated leads"
    // className="max-w-4xl"
    // size="full"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
        <div className="flex items-start  justify-start gap-2 mb-4 flex-wrap">
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "allocated"
                ? "bg-blue-500 text-white"
                : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("allocated")}
          >
            Allocated Leads
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "unallocated"
                ? "bg-blue-500 text-white"
                : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("unallocated")}
          >
            Unallocated Leads
          </button>
          <div>
            <CustomSelectField
              valueField="id"
              labelField="name"
              data={leadSourcesData?.data?.results}
              queryFunc={fetchLeadSource}
              setValue={setLeadSource}
              loader={loadingLeadSources}
              useSearchField={true}
              placeholder={
                lead_source_label != ""
                  ? lead_source_label
                  : "Filter lead source by name"
              }
              labelSetter={setLeadSourceLabel}
            />
          </div>
          <div className="">
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search prospects..."
            />
          </div>

          <Button
            variant="secondary"
            onClick={refetchData}
            disabled={prospectsLoading || isFetching}
          >
            {prospectsLoading || isFetching ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <div className="flex gap-2 items-center   ">
                <RefreshCw className="w-4 h-4" /> Refresh
              </div>
            )}
          </Button>
        </div>

        {prospectsLoading || isFetching ? (
          <div className="flex items-center justify-center py-12">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : prospectsError ? (
          <div className="text-center py-12">
            <Activity className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Failed to Load Prospects
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Unable to fetch prospects data. Please try again later.
            </p>
          </div>
        ) : prospectsData?.data?.results.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-4">
            No {activeTab === "allocated" ? "allocated" : "unallocated"} leads
            found.
          </div>
        ) : (
          <div className="w-full overflow-x-auto">
            <DataTable<ProspectTypes>
              data={prospectsData?.data?.results || []}
              columns={columns}
              title={
                activeTab === "allocated"
                  ? "Allocated Leads"
                  : "Unallocated Leads"
              }
              // enableExportToExcel={true}
              // enablePrintPdf={true}
              enableColumnFilters={true}
              enablePagination={true}
              enableSorting={true}
              enableToolbar={true}
              containerClassName="min-w-[700px] bg-white dark:bg-gray-900 rounded-lg shadow-md"
              tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300"
              tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
              tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
              tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
              tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              tBodyCellsClassName="px-4 py-2"
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              setItemsPerPage={setItemsPerPage}
              totalItems={prospectsData?.data?.total_data || 0}
            />
          </div>
        )}

        {isReallocateModalOpen && selectedProspect && (
          <ReallocateProspects
            isOpen={isReallocateModalOpen}
            onClose={() => {
              setIsReallocateModalOpen(false);
              setSelectedProspect(null);
            }}
            prospect={selectedProspect}
            title={actionTitle}
          />
        )}
      </div>
    </div>
  );
}
