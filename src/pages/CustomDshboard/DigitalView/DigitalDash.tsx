import { Screen } from "@/app-components/layout/screen";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import {
  Users,
  UserX,
  CheckCircle2,
  Activity,
  TrendingUp,
  Target,
  UserPlus,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Sparkles,
  Award,
  Zap,
  GitBranchPlus
} from "lucide-react";
import { Link } from "react-router-dom";
import { useState, useMemo } from "react";
import { CustomActiveShapePieChart } from "@/components/custom/charts/PieChartVariants";
import DigitalLeadsTableModal from "./DigitalLeads";
import AllocatedMarketerTableModal from "./AllocatedLeads";
import UnallocatedMarketerTableModal from "./UnAllocatedLeads";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import AddDigitalLeadModal from "./NewLead";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import "./dashboard-animations.css";
import { useGetDigitalDashboardQuery } from "@/redux/slices/digitalApiSlice";
import DigitalCustomersTableModal from "./DigitalCustomers";
import DigitalSalesTableModal from "./DigitalSales";
import UnallocatedLeadsTableModal from "./UnAllocatedLeads";
import DigitalUnallocatedLeadsReport from "@/pages/Reports/DigitalReports/DigitalUnallocatedLeadsReport";
import DigitalTelemarketingSalesReport from "@/pages/Reports/DigitalReports/DigitalSalesReport";
import DigitalCustomersReport from "@/pages/Reports/DigitalReports/DigitalCustomersReport";

interface Metric {
  action?: any;
  title: string;
  value: number | string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  iconBg: string;
  link?: string;
  modal?: "allLeads" | "allocated" | "unallocated" | "all" | "customers" | "leads" | "sales" | "digitalunallocated";
  change?: string;
  changeLabel?: string;
  trend?: "up" | "down" | "neutral";
}

interface Task {
  id: string;
  title: string;
  status: "pending" | "completed";
}

function DigitalDash() {

  const {
    data: dashboardData,
    isLoading: dashboardLoading,
    error: dashboardError
  } = useGetDigitalDashboardQuery({});

  const [isLeadModalOpen, setIsLeadModalOpen] = useState(false);
  const [isAllocatedModalOpen, setIsAllocatedModalOpen] = useState(false);
  const [isAllSalesModalOpen, setIsAllSalesModalOpen] = useState(false);
  const [isAllCustomersModalOpen, setIsAllCustomersModalOpen] = useState(false);
  const [isUnallocatedModalOpen, setIsUnallocatedModalOpen] = useState(false);
  const [isAddLeadModalOpen, setIsAddLeadModalOpen] = useState(false);
  const [isAllModalOpen, setIsAllModalOpen] = useState(false);
  const [isUnallocatedLeadsModalOpen, setIsUnallocatedLeadsModalOpen] = useState(false);

  const [isDigitalUnallocatedLeadsModalOpen, setIsDigitalUnallocatedLeadsModalOpen] = useState(false);
  const [isDigitalUnallocatedSalesModalOpen, setIsDigitalUnallocatedSalesModalOpen] = useState(false);
  const [isDigitalCustomersModalOpen, setIsDigitalCustomersModalOpen] = useState(false);


  // Calculate metrics from dashboard data
  const metrics: Metric[] = useMemo(() => {
    const prospects = dashboardData?.prospects || {};
    const leads = dashboardData?.leads || {};
    const sales = dashboardData?.sales || {};
    const customers = dashboardData?.customers || {};

    const totalLeads = leads.total_leads || 0;
    const allocatedLeads = prospects.allocated_prospects || 0;
    const unallocatedLeads = prospects.unallocated_prospects || 0;
    const convertedLeads = leads.converted_leads || 0;
    const conversionRate = totalLeads > 0 ? ((convertedLeads / totalLeads) * 100).toFixed(1) : 0;

    return [
      {
        title: "Total Customers",
        value: customers.total_optiven_customers || 0,
        icon: Users,
        color: "text-blue-600 dark:text-blue-400",
        bgColor: "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20",
        iconBg: "bg-blue-700",
        change: `+${Math.floor((customers.total_optiven_customers || 0) * 0.05)}`,
        changeLabel: "this month",
        trend: "up",
        action: () => window.location.href = "/customers/overview#all-customers",
      },
      {
        title: "Digital Customers",
        value: customers.digital_customers || 0,
        icon: UserPlus,
        color: "text-cyan-600 dark:text-cyan-400",
        bgColor: "bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20",
        iconBg: "bg-cyan-700",
        change: 'View all',
        changeLabel: ' ',
        // change: `+${Math.floor((customers.digital_customers || 0) * 0.05)}`,
        // changeLabel: "this month",
        trend: "up",
        modal: "customers",
      },
      // Leads Card
      {
        title: "Total Leads",
        value: totalLeads,
        icon: BarChart3,
        color: "text-indigo-600 dark:text-indigo-400",
        bgColor: "bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20",
        iconBg: "bg-indigo-700",
        change: `+${Math.floor(totalLeads * 0.10)}`,
        changeLabel: "this month",
        trend: "up",
        modal: "leads",
      },
      {
        title: "Digital Unallocated Leads",
        value: leads.unallocated_leads,
        icon: GitBranchPlus,
        color: "text-indigo-600 dark:text-indigo-400",
        bgColor: "bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20",
        iconBg: "bg-indigo-700",
        change: 'View all',
        changeLabel: ' ',
        // change: `+${Math.floor(leads.unallocated_leads * 0.10)}`,
        // changeLabel: "this month",
        trend: "up",
        modal: "digitalunallocated",
      },
      // Sales Card
      {
        title: "Total Sales",
        value: sales.total_sales || 0,
        icon: CheckCircle2,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
        iconBg: "bg-green-700",
        change: `+${Math.floor((sales.total_sales || 0) * 0.07)}`,
        changeLabel: "this month",
        trend: "up",
        modal: "sales",
        action: () => window.location.href = "/sales/overview#all-sales",
      },
      {
        title: "Digital Sales",
        value: sales.count || 0,
        icon: CheckCircle2,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
        iconBg: "bg-green-700",
        change: 'View all',
        changeLabel: ' ',
        // change: `+${Math.floor((sales.total_sales || 0) * 0.07)}`,
        // changeLabel: "this month",
        trend: "up",
        modal: "sales",

      },

      {
        title: "Total Prospects",
        value: prospects.total_prospects || 0,
        icon: Users,
        color: "text-green-600 dark:text-blue-400",
        bgColor: "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-blue-800/20",
        iconBg: "bg-green-700",
        modal: "allLeads",
        change: `+${Math.floor((prospects.total_prospects || 0) * 0.12)}`,
        changeLabel: "this month",
        trend: "up",
      },
      {
        title: "Allocated Leads",
        value: allocatedLeads,
        icon: Target,
        color: "text-emerald-600 dark:text-emerald-400",
        bgColor: "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20",
        iconBg: "bg-emerald-500",
        modal: "allocated",
        change: `${allocatedLeads > 0 ? '+' : ''}${Math.floor(allocatedLeads * 0.08)}`,
        changeLabel: "assigned this week",
        trend: "up",
      },
      {
        title: "Awaiting Assignment",
        value: unallocatedLeads,
        icon: UserX,
        color: "text-amber-600 dark:text-amber-400",
        bgColor: "bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20",
        iconBg: "bg-amber-500",
        modal: "unallocated",
        change: unallocatedLeads > 0 ? `${unallocatedLeads}` : "0",
        changeLabel: "pending assignment",
        trend: unallocatedLeads > 0 ? "neutral" : "up",
      },
      {
        title: "Conversion Rate",
        value: `${conversionRate}%`,
        icon: TrendingUp,
        color: "text-purple-600 dark:text-purple-400",
        bgColor: "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",
        iconBg: "bg-purple-500",
        link: "/sales/all",
        change: `${convertedLeads} converted`,
        changeLabel: "successful sales",
        trend: "up",
      },
    ];
  }, [dashboardData]);

  const tasks: Task[] = [
    { id: "1", title: "Follow up with new leads", status: "pending" },
    { id: "2", title: "Review sales report", status: "completed" },
    { id: "3", title: "Assign unallocated leads", status: "pending" },
  ];

  const handleCardClick = (metric: Metric) => {
    if (metric.action) {
      metric.action();
    } else {
      switch (metric.modal) {
        case "customers":
          setIsDigitalCustomersModalOpen(true);
          // setIsAllCustomersModalOpen(true);
          break;
        case "leads":
          setIsLeadModalOpen(true);
          break;
        case "sales":
          setIsDigitalUnallocatedSalesModalOpen(true);
          // setIsAllSalesModalOpen(true);
          break;
        case "allocated":
          setIsAllocatedModalOpen(true);
          break;

        case "unallocated":
          setIsUnallocatedModalOpen(true);
          break;
        case "digitalunallocated":
          setIsDigitalUnallocatedLeadsModalOpen(true);
          // setIsUnallocatedLeadsModalOpen(true);
          break;
        default:
          setIsAllModalOpen(true); // fallback
      }
    }
  };

  if (dashboardLoading) {
    return (
      <Screen>
        <div className="space-y-8">
          {/* Enhanced Loading Header */}
          <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-green-600 to-emerald-600 rounded-2xl shadow-2xl animate-pulse">
            <div className="px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <div className="w-8 h-8 bg-white/30 rounded"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-8 bg-white/30 rounded w-64"></div>
                  <div className="h-4 bg-white/20 rounded w-48"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Card
                key={i}
                className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-0 shadow-lg animate-pulse"
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="p-3 bg-gray-200 dark:bg-gray-600 rounded-xl w-12 h-12"></div>
                    <div className="w-16 h-6 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                  </div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24 mt-3"></div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-16 mb-1"></div>
                </CardContent>
                <CardFooter className="pt-0">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-32"></div>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Loading Charts and Tasks */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card className="bg-white dark:bg-gray-800 border-0 shadow-xl animate-pulse">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-200 dark:bg-gray-600 rounded-lg w-10 h-10"></div>
                    <div className="space-y-2">
                      <div className="h-5 bg-gray-200 dark:bg-gray-600 rounded w-48"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-32"></div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="w-80 h-80 bg-gray-200 dark:bg-gray-600 rounded-full mx-auto"></div>
                </CardContent>
              </Card>
            </div>
            <div>
              <Card className="bg-white dark:bg-gray-800 border-0 shadow-xl animate-pulse">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-200 dark:bg-gray-600 rounded-lg w-10 h-10"></div>
                    <div className="space-y-2">
                      <div className="h-5 bg-gray-200 dark:bg-gray-600 rounded w-32"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="h-16 bg-gray-200 dark:bg-gray-600 rounded-xl"></div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Loading Spinner */}
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-3">
              <SpinnerTemp type="spinner-double" size="lg" />
              <span className="text-lg font-medium text-gray-600 dark:text-gray-400">
                Loading dashboard data...
              </span>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (dashboardError) {
    return (
      <Screen>
        <div className="space-y-8">
          {/* Error Header */}
          <div className="bg-green-800">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Activity className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold text-white">Digital Dashboard</h1>
                  <p className="text-red-100 text-lg">Unable to load dashboard data</p>
                </div>
              </div>
            </div>
          </div>

          {/* Error Content */}
          <div className="flex items-center justify-center py-16">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-2xl max-w-md w-full">
              <CardContent className="p-8 text-center">
                <div className="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Activity className="w-10 h-10 text-red-500 dark:text-red-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  Connection Failed
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  We're having trouble connecting to our servers. Please check your internet connection and try again.
                </p>
                <div className="space-y-3">
                  <PrimaryButton
                    onClick={() => window.location.reload()}
                    className="w-full bg-red-500 hover:bg-red-600 text-white"
                  >
                    <RefreshCw className="w-5 h-5 mr-2" />
                    Retry Connection
                  </PrimaryButton>
                  <button
                    onClick={() => window.history.back()}
                    className="w-full px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Go Back
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-8 animate-fade-in-up">
        {/* Enhanced Header Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-brown-600 to-emerald-900 dark:from-blue-800 dark:via-purple-800 dark:to-indigo-800 rounded-2xl shadow-2xl hover-lift animate-glow">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative px-8 py-12">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-3 animate-slide-in-right stagger-1">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl glass-effect animate-float">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold text-white">Digital Dashboard</h1>
                    <p className="text-blue-100 text-lg">Real-time prospects and sales analytics</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-white/90 animate-slide-in-right stagger-2">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span className="text-sm">{new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Sparkles className="w-5 h-5 animate-bounce-gentle" />
                    <span className="text-sm">Live Data</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4 animate-slide-in-right stagger-3">

                <button className="p-3 bg-white/20 backdrop-blur-sm rounded-xl hover:bg-white/30 transition-all duration-300 hover-lift glass-effect">
                  <RefreshCw className="w-6 h-6 text-white" />
                </button>
              </div>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32 animate-float"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24 animate-float" style={{ animationDelay: '1s' }}></div>
        </div>
        {/* Enhanced Metrics Cards */}
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {metrics.map((metric, index) => {
            // Choose outline color based on metric or index
            let outline =
              index % 3 === 0
                ? "border-purple-400 group-hover:border-purple-500 dark:border-purple-500 dark:group-hover:border-purple-400"
                : index % 3 === 1
                  ? "border-green-400 group-hover:border-green-500 dark:border-green-500 dark:group-hover:border-green-400"
                  : "border-blue-400 group-hover:border-blue-500 dark:border-blue-500 dark:group-hover:border-blue-400";

            return (
              <Card
                key={metric.title}
                className={`
                  group cursor-pointer transform transition-all duration-500 hover:scale-105 hover-lift animate-fade-in-up
                  border-2 shadow-lg hover:shadow-2xl transition-all duration-300 group-hover:shadow-xl relative overflow-hidden
                  ${metric.bgColor}
                  ${outline}
                  group-focus-within:ring-2 group-focus-within:ring-emerald-400
                  ring-1 ring-inset ring-white/30 dark:ring-black/20
                `}
                onClick={() => handleCardClick(metric)}
                onKeyDown={(e) => e.key === "Enter" && handleCardClick(metric)}
                role="button"
                tabIndex={0}
                aria-label={`Open ${metric.title} modal`}
                style={{ animationDelay: `${(index + 1) * 150}ms` }}
              >
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-5 pointer-events-none">
                  <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white transform translate-x-16 -translate-y-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white transform -translate-x-12 translate-y-12"></div>
                </div>
                <CardHeader className="pb-2 relative">
                  <div className="flex items-center justify-between">
                    <div className={`p-3 rounded-xl ${metric.iconBg} shadow-lg`}>
                      <metric.icon className="h-6 w-6 text-white" />
                    </div>
                    {metric.trend && (
                      <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${metric.trend === 'up'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : metric.trend === 'down'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                        }`}>
                        {metric.trend === 'up' && <TrendingUp className="w-3 h-3" />}
                        {metric.trend === 'down' && <TrendingUp className="w-3 h-3 rotate-180" />}
                        {metric.trend === 'neutral' && <Activity className="w-3 h-3" />}
                      </div>
                    )}
                  </div>
                  <CardTitle className={`text-sm font-medium ${metric.color} mt-3`}>
                    {metric.title}
                  </CardTitle>
                </CardHeader>

                <CardContent className="pb-2 relative">
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                    {metric.value}
                  </div>
                </CardContent>

                <CardFooter className="pt-0 relative">
                  {metric.change && metric.changeLabel && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                      <span className={`font-medium ${metric.trend === 'up' ? 'text-green-600 dark:text-green-400' :
                        metric.trend === 'down' ? 'text-red-600 dark:text-red-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`}>
                        {metric.change}
                      </span>
                      <span>{metric.changeLabel}</span>
                    </div>
                  )}
                </CardFooter>
              </Card>
            );
          })}
        </div>
        {/* Enhanced Analytics and Tasks Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 animate-fade-in-up" style={{ animationDelay: '800ms' }}>
          {/* Charts Section */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-xl hover-lift animate-slide-in-right" style={{ animationDelay: '900ms' }}>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg animate-float">
                      <PieChart className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
                        Prospects Distribution
                      </CardTitle>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Visual breakdown of lead categories
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-300 hover-lift">
                      <Download className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-300 hover-lift">
                      <Filter className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="flex justify-center items-center">
                <div className="w-[320px] h-[320px] animate-fade-in-up" style={{ animationDelay: '1200ms' }}>
                  <CustomActiveShapePieChart />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Tasks Section */}
          <div className="space-y-6">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-xl hover-lift animate-slide-in-right" style={{ animationDelay: '1000ms' }}>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                      <CheckCircle2 className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-bold text-gray-900 dark:text-white">
                        Today's Tasks
                      </CardTitle>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {tasks.filter(t => t.status === 'pending').length} pending
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
                    <Award className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                    <span className="text-sm font-medium text-emerald-600 dark:text-emerald-400">
                      {tasks.filter(t => t.status === 'completed').length}/{tasks.length}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {tasks.length === 0 ? (
                  <div className="text-center py-8">
                    <Zap className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">No tasks available</p>
                  </div>
                ) : (
                  <ul className="space-y-3">
                    {tasks.map((task, index) => (
                      <li
                        key={task.id}
                        className="group p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-xl hover:shadow-md transition-all duration-300"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-lg ${task.status === "completed"
                              ? "bg-green-100 dark:bg-green-900/30"
                              : "bg-yellow-100 dark:bg-yellow-900/30"
                              }`}>
                              <CheckCircle2
                                className={`h-5 w-5 ${task.status === "completed"
                                  ? "text-green-600 dark:text-green-400"
                                  : "text-yellow-600 dark:text-yellow-400"
                                  }`}
                              />
                            </div>
                            <span
                              className={`font-medium ${task.status === "completed"
                                ? "line-through text-gray-500 dark:text-gray-400"
                                : "text-gray-900 dark:text-white"
                                }`}
                            >
                              {task.title}
                            </span>
                          </div>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${task.status === "completed"
                              ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                              : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                              }`}
                          >
                            {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <DigitalLeadsTableModal open={isLeadModalOpen} onOpenChange={setIsLeadModalOpen} />
      <UnallocatedLeadsTableModal open={isUnallocatedLeadsModalOpen} onOpenChange={setIsUnallocatedLeadsModalOpen} />
      <DigitalSalesTableModal open={isAllSalesModalOpen} onOpenChange={setIsAllSalesModalOpen} />
      <DigitalCustomersTableModal open={isAllCustomersModalOpen} onOpenChange={setIsAllCustomersModalOpen} />
      <AllocatedMarketerTableModal open={isAllocatedModalOpen} onOpenChange={setIsAllocatedModalOpen} />
      <UnallocatedMarketerTableModal open={isUnallocatedModalOpen} onOpenChange={setIsUnallocatedModalOpen} />
      <AddDigitalLeadModal isOpen={isAddLeadModalOpen} onOpenChange={setIsAddLeadModalOpen} />
      <DigitalLeadsTableModal open={isAllModalOpen} onOpenChange={setIsAllModalOpen} />

      <DigitalUnallocatedLeadsReport isModalOpen={isDigitalUnallocatedLeadsModalOpen} setIsModalOpen={setIsDigitalUnallocatedLeadsModalOpen} />
      <DigitalTelemarketingSalesReport isModalOpen={isDigitalUnallocatedSalesModalOpen} setIsModalOpen={setIsDigitalUnallocatedSalesModalOpen} />
      <DigitalCustomersReport isModalOpen={isDigitalCustomersModalOpen} setIsModalOpen={setIsDigitalCustomersModalOpen} />


    </Screen>
  );
}

export default DigitalDash;