import React, { useState } from "react";
import {
  Users,
  DollarSign,
  BarChart3,
  CheckCircle,
  TrendingUp,
  Target,
  ArrowUpRight,
  RefreshCw,
  UserPlus,
  GitBranch,
  Thermometer,
  Flame,
  Snowflake,
  FileText,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

// Mock data based on the provided structure
const mockDashboardData = {
  customers: {
    total_optiven_customers: 11206,
    digital_customers: 0,
  },
  sales: {
    total_sales: 13368,
    digital_team_sales: 0,
    active_sales: 10191,
    completed_sales: 3177,
  },
  leads: {
    total_leads: 7137,
    unallocated_leads: 369,
    converted_leads: 38,
    allocated_prospects: 6768,
    hot_prospects: 604,
    warm_prospects: 1160,
    cold_prospects: 5373,
  },
};

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  iconBg: string;
  iconColor: string;
  cardBg: string;
  borderColor: string;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  modal?: boolean;
  action?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon: Icon,
  iconBg,
  iconColor,
  cardBg,
  borderColor,
  change,
  changeType,
  modal = false,
  action,
}) => {
  const handleClick = () => {
    if (action) {
      action();
    }
  };

  return (
    <Card
      className={`${borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${cardBg}`}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div
            className={`w-8 h-8 rounded-lg flex items-center justify-center ${iconBg}`}
          >
            <Icon className={`w-4 h-4 ${iconColor}`} />
          </div>
          {modal && <ArrowUpRight className="w-3 h-3 text-gray-400" />}
        </div>
        <div className="space-y-1">
          <p className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
            {title}
          </p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {typeof value === "number" ? value.toLocaleString() : value}
          </p>
        </div>
      </CardContent>
      <CardFooter className="px-4 py-3 bg-gray-50/50 dark:bg-gray-800/50">
        <div className="flex items-center text-xs">
          <span
            className={`font-medium ${
              changeType === "positive"
                ? "text-green-600 dark:text-green-400"
                : changeType === "negative"
                ? "text-red-600 dark:text-red-400"
                : "text-gray-600 dark:text-gray-400"
            }`}
          >
            {change}
          </span>
        </div>
      </CardFooter>
    </Card>
  );
};

function Dashboard() {
  const [isLoading, setIsLoading] = useState(false);

  // Create metrics array using the dashboard data
  const metrics = [
    {
      title: "Total Customers",
      value: mockDashboardData.customers.total_optiven_customers,
      icon: Users,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: "All Customers",
      changeType: "positive" as const,
      action: () =>
        (window.location.href = "/customers/overview#all-customers"),
      modal: true,
    },
    {
      title: "Digital Customers",
      value: mockDashboardData.customers.digital_customers,
      icon: UserPlus,
      iconBg: "bg-indigo-100",
      iconColor: "text-indigo-600",
      cardBg: "bg-gradient-to-br from-indigo-50 to-indigo-100",
      borderColor: "border-indigo-200",
      change: "Digital Team",
      changeType: "positive" as const,
      modal: true,
    },
    {
      title: "Total Sales",
      value: mockDashboardData.sales.total_sales,
      icon: BarChart3,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      change: "All Sales",
      changeType: "positive" as const,
      action: () => (window.location.href = "/sales/overview#all-sales"),
      modal: true,
    },
    {
      title: "Digital Team Sales",
      value: mockDashboardData.sales.digital_team_sales,
      icon: DollarSign,
      iconBg: "bg-emerald-100",
      iconColor: "text-emerald-600",
      cardBg: "bg-gradient-to-br from-emerald-50 to-emerald-100",
      borderColor: "border-emerald-200",
      change: "Digital Sales",
      changeType: "positive" as const,
      modal: true,
    },
    {
      title: "Active Sales",
      value: mockDashboardData.sales.active_sales,
      icon: TrendingUp,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: "Active",
      changeType: "positive" as const,
      action: () => (window.location.href = "/sales/overview#on-going-sales"),
      modal: true,
    },
    {
      title: "Completed Sales",
      value: mockDashboardData.sales.completed_sales,
      icon: CheckCircle,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      change: "Completed",
      changeType: "positive" as const,
      action: () => (window.location.href = "/sales/overview#completed-sales"),
      modal: true,
    },
  ];

  const leadMetrics = [
    {
      title: "Total Leads",
      value: mockDashboardData.leads.total_leads,
      icon: Target,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      change: "All Leads",
      changeType: "neutral" as const,
      action: () => (window.location.href = "/prospects"),
      modal: false,
    },
    {
      title: "Unallocated Leads",
      value: mockDashboardData.leads.unallocated_leads,
      icon: GitBranch,
      iconBg: "bg-yellow-100",
      iconColor: "text-yellow-600",
      cardBg: "bg-gradient-to-br from-yellow-50 to-yellow-100",
      borderColor: "border-yellow-200",
      change: "Pending Assignment",
      changeType: "neutral" as const,
      modal: true,
    },
    {
      title: "Converted Leads",
      value: mockDashboardData.leads.converted_leads,
      icon: CheckCircle,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      change: "Successful Conversions",
      changeType: "positive" as const,
      modal: true,
    },
    {
      title: "Allocated Prospects",
      value: mockDashboardData.leads.allocated_prospects,
      icon: Users,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      change: "Assigned Prospects",
      changeType: "positive" as const,
      modal: true,
    },
    {
      title: "Hot Prospects",
      value: mockDashboardData.leads.hot_prospects,
      icon: Flame,
      iconBg: "bg-red-100",
      iconColor: "text-red-600",
      cardBg: "bg-gradient-to-br from-red-50 to-red-100",
      borderColor: "border-red-200",
      change: "High Priority",
      changeType: "positive" as const,
      modal: true,
    },
    {
      title: "Warm Prospects",
      value: mockDashboardData.leads.warm_prospects,
      icon: Thermometer,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      change: "Medium Priority",
      changeType: "neutral" as const,
      modal: true,
    },
    {
      title: "Cold Prospects",
      value: mockDashboardData.leads.cold_prospects,
      icon: Snowflake,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: "Low Priority",
      changeType: "neutral" as const,
      modal: true,
    },
  ];

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Digital Dashboard
            </h1>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-600">
                Digital team overview and insights
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                {isLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Customer and Sales Metrics */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {metrics.map((metric) => (
              <MetricCard key={metric.title} {...metric} />
            ))}
          </div>

          {/* Leads Metrics */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Lead Management
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
              {leadMetrics.map((metric) => (
                <MetricCard key={metric.title} {...metric} />
              ))}
            </div>
          </div>

          {/* Key Performance Indicators Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">
                      Digital Customer Percentage
                    </p>
                    <p className="text-2xl font-bold">
                      {mockDashboardData.customers.digital_customers &&
                      mockDashboardData.customers.total_optiven_customers
                        ? Math.round(
                            (mockDashboardData.customers.digital_customers /
                              mockDashboardData.customers
                                .total_optiven_customers) *
                              100
                          )
                        : 0}
                      %
                    </p>
                  </div>
                  <UserPlus className="w-8 h-8 text-blue-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">
                      Digital Sales Percentage
                    </p>
                    <p className="text-2xl font-bold">
                      {mockDashboardData.sales.digital_team_sales &&
                      mockDashboardData.sales.total_sales
                        ? Math.round(
                            (mockDashboardData.sales.digital_team_sales /
                              mockDashboardData.sales.total_sales) *
                              100
                          )
                        : 0}
                      %
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm">
                      Lead Conversion Rate
                    </p>
                    <p className="text-2xl font-bold">
                      {mockDashboardData.leads.converted_leads &&
                      mockDashboardData.leads.total_leads
                        ? Math.round(
                            (mockDashboardData.leads.converted_leads /
                              mockDashboardData.leads.total_leads) *
                              100
                          )
                        : 0}
                      %
                    </p>
                  </div>
                  <Target className="w-8 h-8 text-orange-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">
                      Hot Prospects Ratio
                    </p>
                    <p className="text-2xl font-bold">
                      {mockDashboardData.leads.hot_prospects &&
                      mockDashboardData.leads.allocated_prospects
                        ? Math.round(
                            (mockDashboardData.leads.hot_prospects /
                              mockDashboardData.leads.allocated_prospects) *
                              100
                          )
                        : 0}
                      %
                    </p>
                  </div>
                  <Flame className="w-8 h-8 text-purple-200" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Report Generation Section */}
          <Card className="border-0 shadow-lg bg-white">
            <div className="bg-primary p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-secondary">
                    Digital Team Report Generator
                  </h2>
                  <p className="text-secondary mt-1">
                    Create comprehensive digital team performance reports
                  </p>
                </div>
              </div>
            </div>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-blue-50"
                >
                  <Users className="w-5 h-5" />
                  <span>Customer Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-green-50"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>Sales Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-orange-50"
                >
                  <Target className="w-5 h-5" />
                  <span>Leads Report</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
}

export default Dashboard;
