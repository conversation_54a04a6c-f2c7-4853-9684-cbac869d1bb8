import React, { useState } from "react";
import {
  Users,
  DollarSign,
  BarChart3,
  Target,
  ArrowUpRight,
  RefreshCw,
  UserPlus,
  Flame,
  FileText,
  Loader2,
  Activity,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useGetDigitalDashboardQuery } from "@/redux/slices/digitalApiSlice";
import { useNavigate } from "react-router-dom";
import AddProspects from "@/pages/Prospects/addlead";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import DigitalLeadsTableModal from "./DigitalLeads";

interface GroupedCardProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  iconBg: string;
  iconColor: string;
  cardBg: string;
  borderColor: string;
  metrics: Array<{
    label: string;
    value: string | number;
    subtext?: string;
  }>;
  action?: () => void;
}

const GroupedCard: React.FC<GroupedCardProps> = ({
  title,
  icon: Icon,
  iconBg,
  iconColor,
  cardBg,
  borderColor,
  metrics,
  action,
}) => {
  const handleClick = () => {
    if (action) {
      action();
    }
  };

  return (
    <Card
      className={`${borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${cardBg}`}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div
            className={`w-8 h-8 rounded-lg flex items-center justify-center ${iconBg}`}
          >
            <Icon className={`w-4 h-4 ${iconColor}`} />
          </div>
          <ArrowUpRight className="w-3 h-3 text-gray-400" />
        </div>
        <div className="space-y-3">
          <p className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide">
            {title}
          </p>
          <div className="space-y-2">
            {metrics.map((metric, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {metric.label}
                </span>
                <div className="text-right">
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {typeof metric.value === "number"
                      ? metric.value.toLocaleString()
                      : metric.value}
                  </span>
                  {metric.subtext && (
                    <p className="text-xs text-gray-500">{metric.subtext}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

function Dashboard() {
  const navigate = useNavigate();
  const [openAddLeadModal, setOpenAddLeadModal] = useState(false);
  const [isLeadModalOpen, setIsLeadModalOpen] = useState(false);

  const {
    data: dashboardData,
    isLoading: dashboardLoading,
    isFetching,
    error: dashboardError,
    refetch,
  } = useGetDigitalDashboardQuery({});

  // Create grouped cards for better organization
  const groupedCards = [
    {
      title: "Lead Management",
      icon: Target,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      metrics: [
        {
          label: "Total Leads",
          value: dashboardData?.leads.total_leads,
        },
        {
          label: "Allocated Prospects",
          value: dashboardData?.leads.allocated_prospects,
          //   subtext: "Assigned",
        },
        {
          label: "Unallocated",
          value: dashboardData?.leads.unallocated_leads,
          //   subtext: "Pending assignment",
        },
        {
          label: "Converted",
          value: dashboardData?.leads.converted_leads,
          //   subtext: "Successful",
        },
      ],
      action: () => navigate("/prospects"),
    },
    {
      title: "Leads Priority",
      icon: Flame,
      iconBg: "bg-red-100",
      iconColor: "text-red-600",
      cardBg: "bg-gradient-to-br from-red-50 to-red-100",
      borderColor: "border-red-200",
      metrics: [
        {
          label: "Hot Prospects",
          value: dashboardData?.leads.hot_prospects,
          //   subtext: "High priority",
        },
        {
          label: "Warm Prospects",
          value: dashboardData?.leads.warm_prospects,
          //   subtext: "Medium priority",
        },
        {
          label: "Cold Prospects",
          value: dashboardData?.leads.cold_prospects,
          //   subtext: "Low priority",
        },
      ],
      action: () => navigate("/prospects"),
    },
    {
      title: "Customer Overview",
      icon: Users,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      metrics: [
        {
          label: "Total Customers",
          value: dashboardData?.customers.total_optiven_customers,
        },
        {
          label: "Digital Customers",
          value: dashboardData?.customers.digital_customers,
          //   subtext: "Digital team",
        },
      ],
      action: () => navigate("/customers/overview#all-customers"),
    },
    {
      title: "Sales Performance",
      icon: BarChart3,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      metrics: [
        {
          label: "Total Sales",
          value: dashboardData?.sales.total_sales,
        },
        {
          label: "Digital Sales",
          value: dashboardData?.sales.digital_team_sales,
          //   subtext: "Digital team",
        },
        {
          label: "Active Sales",
          value: dashboardData?.sales.active_sales,
          //   subtext: "Ongoing",
        },
        {
          label: "Completed Sales",
          value: dashboardData?.sales.completed_sales,
          //   subtext: "Finished",
        },
      ],
      action: () => navigate("/sales/overview#all-sales"),
    },
  ];

  if (dashboardError) {
    return (
      <Screen>
        <div className="space-y-8">
          {/* Error Header */}
          <div className="bg-green-800">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Activity className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold text-white">
                    Digital Dashboard
                  </h1>
                  <p className="text-red-100 text-lg">
                    Unable to load dashboard data
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Error Content */}
          <div className="flex items-center justify-center py-16">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-2xl max-w-md w-full">
              <CardContent className="p-8 text-center">
                <div className="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Activity className="w-10 h-10 text-red-500 dark:text-red-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  Connection Failed
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  We're having trouble connecting to our servers. Please check
                  your internet connection and try again.
                </p>
                <div className="space-y-3">
                  <PrimaryButton
                    onClick={() => window.location.reload()}
                    className="w-full bg-red-500 hover:bg-red-600 text-white"
                  >
                    <RefreshCw className="w-5 h-5 mr-2" />
                    Retry Connection
                  </PrimaryButton>
                  <button
                    onClick={() => window.history.back()}
                    className="w-full px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Go Back
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Digital Dashboard
            </h1>
            <div className="flex items-center space-x-2">
              <Button
                variant="default"
                size="sm"
                onClick={() => setOpenAddLeadModal(true)}
                disabled={dashboardLoading || isFetching}
              >
                {dashboardLoading || isFetching ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <span>Add Lead</span>
                )}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={refetch}
                disabled={dashboardLoading || isFetching}
              >
                {dashboardLoading || isFetching ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {dashboardLoading ? (
            <div className="w-full text-center py-20">
              <Loader2 className="w-8 h-8 mr-2 animate-spin inline-block text-gray-500" />
            </div>
          ) : (
            <>
              {/* Grouped Dashboard Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {groupedCards.map((card) => (
                  <GroupedCard key={card.title} {...card} />
                ))}
              </div>

              {/* Key Performance Indicators Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-100 text-sm">
                          Digital Leads Percentage
                        </p>
                        <p className="text-2xl font-bold">
                          {dashboardData?.leads.total_leads &&
                          dashboardData?.leads.total_optiven_leads
                            ? Math.round(
                                (dashboardData?.leads.total_leads /
                                  dashboardData?.leads.total_optiven_leads) *
                                  100
                              )
                            : 0}
                          %
                        </p>
                      </div>
                      <UserPlus className="w-8 h-8 text-blue-200" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-100 text-sm">
                          Digital Sales Percentage
                        </p>
                        <p className="text-2xl font-bold">
                          {dashboardData?.sales.digital_team_sales &&
                          dashboardData?.sales.total_sales
                            ? Math.round(
                                (dashboardData?.sales.digital_team_sales /
                                  dashboardData?.sales.total_sales) *
                                  100
                              )
                            : 0}
                          %
                        </p>
                      </div>
                      <DollarSign className="w-8 h-8 text-green-200" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-orange-100 text-sm">
                          Lead Conversion Rate
                        </p>
                        <p className="text-2xl font-bold">
                          {dashboardData?.leads.converted_leads &&
                          dashboardData?.leads.total_leads
                            ? (
                                (dashboardData?.leads.converted_leads /
                                  dashboardData?.leads.total_leads) *
                                100
                              ).toFixed(2)
                            : 0}
                          %
                        </p>
                      </div>
                      <Target className="w-8 h-8 text-orange-200" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-purple-100 text-sm">
                          Hot Prospects Ratio
                        </p>
                        <p className="text-2xl font-bold">
                          {dashboardData?.leads.hot_prospects &&
                          dashboardData?.leads.allocated_prospects
                            ? Math.round(
                                (dashboardData?.leads.hot_prospects /
                                  dashboardData?.leads.allocated_prospects) *
                                  100
                              )
                            : 0}
                          %
                        </p>
                      </div>
                      <Flame className="w-8 h-8 text-purple-200" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Report Generation Section */}
              <Card className="border-0 shadow-lg bg-white">
                <div className="bg-primary p-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                      <FileText className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-secondary">
                        Digital Team Leads
                      </h2>
                      <p className="text-secondary mt-1">
                        Digital Team Leads Prospects
                      </p>
                    </div>
                  </div>
                </div>
                <CardContent className="p-6">
                  <DigitalLeadsTableModal
                    open={isLeadModalOpen}
                    onOpenChange={setIsLeadModalOpen}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* <Button
                      variant="secondary"
                      className="h-12 flex items-center justify-center space-x-2 hover:bg-blue-50"
                    >
                      <Users className="w-5 h-5" />
                      <span>Customer Report</span>
                    </Button>
                    <Button
                      variant="secondary"
                      className="h-12 flex items-center justify-center space-x-2 hover:bg-green-50"
                    >
                      <BarChart3 className="w-5 h-5" />
                      <span>Sales Report</span>
                    </Button> */}
                    {/* <Button
                      variant="secondary"
                      className="h-12 flex items-center justify-center space-x-2 hover:bg-orange-50"
                      onClick={() => setIsLeadModalOpen(true)}
                    >
                      <Target className="w-5 h-5" />
                      <span>Leads Report</span>
                    </Button> */}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>

      {openAddLeadModal && (
        <AddProspects
          isOpen={openAddLeadModal}
          onClose={() => setOpenAddLeadModal(false)}
        />
      )}
    </Screen>
  );
}

export default Dashboard;
