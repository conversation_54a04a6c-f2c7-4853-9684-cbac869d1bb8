import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, Activity, TrendingUp, Users, DollarSign, FileText } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { ProspectTypes } from "@/types/prospects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetDigitalSalesQuery } from "@/redux/slices/digitalApiSlice";

interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DigitalSalesTableModal({ open, onOpenChange }: ProspectsTableModalProps) {
  // Fetch sales data
  const {
    data: salesData,
    isLoading,
    error
  } = useGetDigitalSalesQuery({
    page: 1,
    page_size: 10,
  });

  const allSales = salesData?.data?.results || [];

  // Calculate summary statistics
  const totalSales = allSales.length;
  const totalRevenue = allSales.reduce((sum: number, sale: ProspectTypes) => sum + (sale.selling_price || 0), 0);
  const totalPaid = allSales.reduce((sum: number, sale: ProspectTypes) => sum + (sale.total_paid || 0), 0);
  const totalBalance = allSales.reduce((sum: number, sale: ProspectTypes) => sum + (sale.balance || 0), 0);

  const columns: ColumnDef<ProspectTypes>[] = [
    { 
      accessorKey: "lead_file_no", 
      header: "Lead File No",
      cell: ({ row }) => (
        <span className="font-medium text-blue-600 dark:text-blue-400">
          {row.getValue("lead_file_no")}
        </span>
      )
    },
    { accessorKey: "customer_no", header: "Customer No" },
    { 
      accessorKey: "customer_name", 
      header: "Customer Name",
      cell: ({ row }) => (
        <span className="font-semibold text-gray-900 dark:text-white">
          {row.getValue("customer_name")}
        </span>
      )
    },
    { 
      accessorKey: "selling_price", 
      header: "Selling Price",
      cell: ({ row }) => (
        <span className="font-bold text-green-600 dark:text-green-400">
          ${row.getValue("selling_price")?.toLocaleString()}
        </span>
      )
    },
    { 
      accessorKey: "total_paid", 
      header: "Total Paid",
      cell: ({ row }) => (
        <span className="font-semibold text-emerald-600 dark:text-emerald-400">
          ${row.getValue("total_paid")?.toLocaleString()}
        </span>
      )
    },
    { 
      accessorKey: "balance", 
      header: "Balance",
      cell: ({ row }) => (
        <span className="font-semibold text-orange-600 dark:text-orange-400">
          ${row.getValue("balance")?.toLocaleString()}
        </span>
      )
    },
    { 
      accessorKey: "booking_date", 
      header: "Booking Date",
      cell: ({ row }) => (
        <span className="text-gray-600 dark:text-gray-400">
          {new Date(row.getValue("booking_date")).toLocaleDateString()}
        </span>
      )
    },
    { accessorKey: "marketer_name", header: "Marketer Name" },
    { accessorKey: "marketer_employee_no", header: "Marketer No" },
    { 
      accessorKey: "plot_number", 
      header: "Plot Number",
      cell: ({ row }) => (
        <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
          {row.getValue("plot_number")}
        </span>
      )
    },
    { accessorKey: "project_name", header: "Project Name" },
    { 
      accessorKey: "status", 
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusColors = {
          active: "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
          pending: "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",
          completed: "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",
          cancelled: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
        };
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[status as keyof typeof statusColors] || "bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200"}`}>
            {status}
          </span>
        );
      }
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors">
            <Settings size={18} className="text-gray-600 dark:text-gray-400" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel className="font-semibold">Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit sale ${row.original.customer_name}`);
              }}
              className="cursor-pointer"
            >
              <FileText className="w-4 h-4 mr-2" />
              Edit Sale
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`View sale ${row.original.customer_name}`);
              }}
              className="cursor-pointer"
            >
              <Users className="w-4 h-4 mr-2" />
              View Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title=""
      description=""
      className="max-w-7xl"
      size="full"
    >
      <div className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-lg">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
              <TrendingUp className="w-8 h-8" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Digital Sales Dashboard</h2>
              <p className="text-blue-100">Comprehensive sales management and analytics</p>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Users className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-blue-100 text-sm">Total Sales</p>
                  <p className="text-xl font-bold">{totalSales.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <DollarSign className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-blue-100 text-sm">Total Revenue</p>
                  <p className="text-xl font-bold">${totalRevenue.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <TrendingUp className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-blue-100 text-sm">Total Paid</p>
                  <p className="text-xl font-bold">${totalPaid.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <FileText className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-blue-100 text-sm">Outstanding Balance</p>
                  <p className="text-xl font-bold">${totalBalance.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <SpinnerTemp type="spinner-double" size="lg" />
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading sales data...</p>
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-full w-fit mx-auto mb-4">
                <Activity className="w-12 h-12 text-red-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Failed to Load Sales Data
              </h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                We encountered an error while fetching the sales information. Please check your connection and try again.
              </p>
              <button 
                onClick={() => window.location.reload()} 
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : allSales.length === 0 ? (
            <div className="text-center py-16">
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-full w-fit mx-auto mb-4">
                <FileText className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No Sales Found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                There are currently no sales records to display. New sales will appear here once they are added to the system.
              </p>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  All Digital Sales ({allSales.length})
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Complete overview of all digital sales transactions
                </p>
              </div>
              <div className="w-full overflow-x-auto">
                <DataTable<ProspectTypes>
                  data={allSales}
                  columns={columns}
                  title="Digital Sales Records"
                  enableExportToExcel={true}
                  enablePrintPdf={true}
                  enableColumnFilters={true}
                  enablePagination={true}
                  enableSorting={true}
                  enableToolbar={true}
                  containerClassName="min-w-[700px] bg-white dark:bg-gray-800 rounded-lg shadow-lg"
                  tableClassName="w-full border-collapse bg-white dark:bg-gray-800 text-sm text-left text-gray-700 dark:text-gray-300"
                  tHeadClassName="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-700 dark:text-gray-300 font-semibold"
                  tHeadCellsClassName="px-6 py-4 border-b border-gray-200 dark:border-gray-700"
                  tBodyClassName="divide-y divide-gray-100 dark:divide-gray-700"
                  tBodyTrClassName="hover:bg-blue-50 dark:hover:bg-gray-700/50 transition-all duration-200"
                  tBodyCellsClassName="px-6 py-4"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </BaseModal>
  );
}

export interface SaleType {
  name: any;
  lead_file_no: string;
  customer_no: string;
  customer_name: string;
  selling_price: number;
  total_paid: number;
  balance: number;
  booking_date: string;
  marketer_name: string;
  marketer_employee_no: string;
  plot_number: string;
  project_name: string;
  status: string;
}