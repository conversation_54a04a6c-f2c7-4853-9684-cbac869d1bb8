import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, Activity } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";

import { ProspectTypes } from "@/types/prospects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetDigitalUnallocatedLeadsQuery } from "@/redux/slices/digitalApiSlice";


interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function UnallocatedLeadsTableModal({ open, onOpenChange }: ProspectsTableModalProps) {
  // Fetch prospects data
  const {
    data: prospectsData,
    isLoading: prospectsLoading,
    error: prospectsError
  } = useGetDigitalUnallocatedLeadsQuery({
    page: 1,
    page_size: 1000, // Get all prospects
  });

  // Filter only unallocated leads
  const unallocatedLeads = prospectsData?.data?.results?.filter(
    (prospect: ProspectTypes) => !prospect.marketer || prospect.marketer.trim() === ''
  ) || [];

  const columns: ColumnDef<ProspectTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <span className="font-medium capitalize">
          {info.getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "phone",
      header: "Phone Number",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "city",
      header: "City",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "project_name",
      header: "Project",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: (info) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          info.getValue() === "Hot"
            ? "bg-red-100 text-red-800"
            : info.getValue() === "Warm"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-blue-100 text-blue-800"
        }`}>
          {info.getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "lead_source_name",
      header: "Lead Source",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Assign marketer to ${row.original.name}`);
              }}
            >
              Assign Marketer
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`View prospect ${row.original.name}`);
              }}
            >
              View Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Unallocated Leads"
      description="View and manage unallocated prospects awaiting marketer assignment"
      className="max-w-6xl"
      size="full"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
        {prospectsLoading ? (
          <div className="flex items-center justify-center py-12">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : prospectsError ? (
          <div className="text-center py-12">
            <Activity className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Failed to Load Prospects
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Unable to fetch prospects data. Please try again later.
            </p>
          </div>
        ) : unallocatedLeads.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-8">
            No unallocated leads found. All prospects have been assigned to marketers.
          </div>
        ) : (
          <DataTable<ProspectTypes>
            data={unallocatedLeads}
            columns={columns}
            title="Unallocated Leads"
            enableExportToExcel={true}
            enablePrintPdf={true}
            enableColumnFilters={true}
            enablePagination={true}
            enableSorting={true}
            enableToolbar={true}
            containerClassName="max-w-full"
            tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300 shadow-md rounded-lg overflow-hidden"
            tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
            tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
            tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
            tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            tBodyCellsClassName="px-4 py-2"
          />
        )}
      </div>
    </BaseModal>
  );
}