import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/custom/badges/badges";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  Users,
  TrendingUp,
  Target,
  Award,
  AlertCircle,
  RefreshCw,
  Search,
  Settings,
  Grid3X3,
  PieChart
} from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useGetTeamsPerformanceQuery } from "@/redux/slices/hrDashboardApiSlice";
import { format } from "date-fns";

import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";

interface TeamsManagementModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface TeamData {
  line_no: number;
  team: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: number;
  daily_target: number;
  MIB_achieved: number;
  MIB_Perfomance: number;
}

// Define columns for the data table
const columns: ColumnDef<TeamData>[] = [
  {
    accessorKey: "team",
    header: "Team",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("team")}</div>
    ),
  },
  {
    accessorKey: "period_start_date",
    header: "Period Start",
    cell: ({ row }) => (
      <div>{format(new Date(row.getValue("period_start_date")), "MMM dd, yyyy")}</div>
    ),
  },
  {
    accessorKey: "period_end_date",
    header: "Period End",
    cell: ({ row }) => (
      <div>{format(new Date(row.getValue("period_end_date")), "MMM dd, yyyy")}</div>
    ),
  },
  {
    accessorKey: "daily_target",
    header: "Daily Target",
    cell: ({ row }) => (
      <div>{formatCurrency(row.getValue("daily_target"))}</div>
    ),
  },
  {
    accessorKey: "monthly_target",
    header: "Monthly Target",
    cell: ({ row }) => (
      <div>{formatCurrency(row.getValue("monthly_target"))}</div>
    ),
  },
  {
    accessorKey: "MIB_achieved",
    header: "Achieved",
    cell: ({ row }) => (
      <div>{formatCurrency(row.getValue("MIB_achieved"))}</div>
    ),
  },
  {
    accessorKey: "MIB_Perfomance",
    header: "Performance",
    cell: ({ row }) => {
      const performance = row.getValue("MIB_Perfomance") as number;
      const status = getStatusFromPerformance(performance);
      return (
        <div className="flex items-center gap-2">
          <span>{performance.toFixed(1)}%</span>
          <Badge className={getStatusColor(status)} size="sm">
            {status}
          </Badge>
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <PrimaryButton variant="ghost" size="sm" className="p-2">
        <Settings className="w-4 h-4" />
      </PrimaryButton>
    ),
  },
];

// Helper functions
const getStatusFromPerformance = (performance: number): string => {
  if (performance >= 90) return "Excellent";
  if (performance >= 70) return "On Track";
  if (performance >= 50) return "Needs Attention";
  return "Behind";
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "Excellent":
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200";
    case "On Track":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200";
    case "Needs Attention":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400 border-orange-200";
    case "Behind":
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400 border-gray-200";
  }
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export default function TeamsManagementModal({
  open,
  onOpenChange,
}: TeamsManagementModalProps) {
  const [viewMode, setViewMode] = useState<"overview" | "list">("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const { data: teamsData, isLoading: isLoadingTeams, error: teamsError } = useGetTeamsPerformanceQuery({
    page: currentPage,
    page_size: 20
  });

  const filteredTeams = teamsData?.results?.filter(team => 
    team.team.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const uniqueTeams = filteredTeams.reduce((acc, current) => {
    const existingTeam = acc.find(team => team.team === current.team);
    if (!existingTeam) {
      acc.push(current);
    } else {
      const currentEndDate = new Date(current.period_end_date);
      const existingEndDate = new Date(existingTeam.period_end_date);

      if (currentEndDate > existingEndDate) {
        const index = acc.findIndex(team => team.team === current.team);
        acc[index] = current;
      }
    }
    return acc;
  }, [] as TeamData[]);

  const totalTeams = uniqueTeams.length;
  const totalTarget = uniqueTeams.reduce((sum: number, team) => sum + team.monthly_target, 0);
  const totalAchieved = uniqueTeams.reduce((sum, team) => sum + team.MIB_achieved, 0);
  const avgProgress = uniqueTeams.length > 0
    ? uniqueTeams.reduce((sum, team) => sum + team.MIB_Perfomance, 0) / uniqueTeams.length
    : 0;

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Teams Management"
      description="Comprehensive teams overview and management dashboard"
      className="max-w-7xl"
      size="full"
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6 p-4"
      >
        {/* Header Controls */}
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          {/* View Mode Toggle */}
          <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <PrimaryButton
              variant={viewMode === "overview" ? "primary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("overview")}
              className={cn(
                "flex items-center gap-2",
                viewMode === "overview" 
                  ? "bg-green-600 text-white" 
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              <PieChart className="w-4 h-4" />
              Overview
            </PrimaryButton>
            <PrimaryButton
              variant={viewMode === "list" ? "primary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className={cn(
                "flex items-center gap-2",
                viewMode === "list" 
                  ? "bg-green-600 text-white" 
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              <Grid3X3 className="w-4 h-4" />
              Teams List
            </PrimaryButton>
          </div>

          {/* Search */}
          <div className="flex gap-4 flex-1 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search teams..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <PrimaryButton
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </PrimaryButton>
          </div>
        </div>

        {/* Overview Mode */}
        {viewMode === "overview" && (
          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Teams</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalTeams}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Target</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{formatCurrency(totalTarget)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Award className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Achieved</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{formatCurrency(totalAchieved)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                      <TrendingUp className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Performance</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">{avgProgress.toFixed(1)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Performance Overview Cards */}
            {isLoadingTeams ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, idx) => (
                  <Card key={idx} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="h-32 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : teamsError ? (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600">Failed to load teams data. Please try again.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {uniqueTeams.map((team, index) => (
                  <motion.div
                    key={`${team.team}-${team.line_no}`}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="hover:shadow-lg transition-all duration-200 border-2 hover:border-green-300">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-lg font-bold text-gray-900 dark:text-white">
                              {team.team}
                            </CardTitle>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {format(new Date(team.period_start_date), "MMM dd")} - {format(new Date(team.period_end_date), "MMM dd, yyyy")}
                            </p>
                          </div>
                          <Badge className={getStatusColor(getStatusFromPerformance(team.MIB_Perfomance))}>
                            {getStatusFromPerformance(team.MIB_Perfomance)}
                          </Badge>
                        </div>
                      </CardHeader>

                      <CardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Daily Target</span>
                            <p className="font-semibold text-gray-900 dark:text-white">{formatCurrency(team.daily_target)}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Performance</span>
                            <p className="font-semibold text-gray-900 dark:text-white">{team.MIB_Perfomance.toFixed(1)}%</p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Progress</span>
                            <span className="font-semibold text-gray-900 dark:text-white">{team.MIB_Perfomance.toFixed(1)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min(team.MIB_Perfomance, 100)}%` }}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Monthly Target</span>
                            <p className="font-semibold text-gray-900 dark:text-white">{formatCurrency(team.monthly_target)}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">MIB Achieved</span>
                            <p className="font-semibold text-gray-900 dark:text-white">{formatCurrency(team.MIB_achieved)}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* List Mode (DataTable) */}
        {viewMode === "list" && (
          <div className="space-y-4">
            {isLoadingTeams ? (
              <div className="space-y-3">
                {Array.from({ length: 5 }).map((_, idx) => (
                  <Card key={idx} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="h-16 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : teamsError ? (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600">Failed to load teams data. Please try again.</p>
              </div>
            ) : (
              <DataTable
                columns={columns}
                data={uniqueTeams}
                enableExportToExcel={true}
                enablePrintPdf={true}
                enablePagination={true}
                
               
                
               
               
                
              />
            )}
          </div>
        )}

        {/* No Results */}
        {!isLoadingTeams && !teamsError && uniqueTeams.length === 0 && (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Teams Found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              No teams match your current search criteria.
            </p>
            <PrimaryButton
              variant="outline"
              className="mt-4"
              onClick={() => setSearchTerm("")}
            >
              Clear Search
            </PrimaryButton>
          </div>
        )}
      </motion.div>
    </BaseModal>
  );
}