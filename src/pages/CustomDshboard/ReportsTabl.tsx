import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import { Settings, AlertCircle, Search, X, TrendingUp, Award, Target } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetMarketerPerformanceByPeriodQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { MarketerReport } from "@/types/marketer";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Pie<PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface EmployeeTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedPeriod?: {
    period: {
      period_name: string;
      start_date: string;
      end_date: string;
      target: number;
      achieved: number;
      progress: number;
    };
    office: string;
  } | null;
}

export default function EmployeeTableModal({
  open,
  onOpenChange,
  selectedPeriod,
}: EmployeeTableModalProps) {
  const [marketerFilter, setMarketerFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20); // or your preferred default

  // Fetch all marketer reports data (no pagination params)
  const {
    data: reportsData,
    isLoading,
    error,
  } = useGetMarketerPerformanceByPeriodQuery(
    {
      marketing_period: selectedPeriod?.period?.start_date || "ALL",
      marketer_employee_no: marketerFilter.trim() || "ALL",
      page: currentPage,
      page_size: pageSize,
    },
    {
      skip: !selectedPeriod?.period,
    }
  );

  if (error) {
    console.error("API Error Details:", error);
  }

  const data: MarketerReport[] = reportsData?.results || [];
  const totalRecords = reportsData?.count || 0;
  const totalPages = reportsData?.num_pages || 1;

  const handleFilterChange = (value: string) => {
    setMarketerFilter(value);
  };

  const columns: ColumnDef<MarketerReport>[] = [
    {
      accessorKey: "fullnames", // Changed from marketer_no_id
      header: "Marketer Name", // Updated header
      cell: (info) => info.getValue() || "Unknown", // Added fallback
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    // {
    //   accessorKey: "id",
    //   header: "ID",
    //   cell: (info) => info.getValue(),
    //   enableColumnFilter: false,
    //   filterFn: "includesString",
    // },
    {
      accessorKey: "period_start_date",
      header: "Period Start",
      cell: (info) => {
        const value = info.getValue() as string;
        try {
          return value ? new Date(value).toLocaleDateString("en-KE") : "N/A";
        } catch {
          return "N/A";
        }
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "period_end_date",
      header: "Period End",
      cell: (info) => {
        const value = info.getValue() as string;
        try {
          return value ? new Date(value).toLocaleDateString("en-KE") : "N/A";
        } catch {
          return "N/A";
        }
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "monthly_target",
      header: "Monthly Target",
      cell: (info) => {
        const value = info.getValue() as number;
        return value ? new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 0,
        }).format(value) : "0";
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "daily_target",
      header: "Daily Target",
      cell: (info) => {
        const value = info.getValue() as number;
        return value ? new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 2,
        }).format(value) : "0";
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "MIB_achieved",
      header: "MIB Achieved",
      cell: (info) => {
        const value = info.getValue() as number;
        return value ? new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 0,
        }).format(value) : "0";
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "MIB_Perfomance",
      header: "Performance %",
      cell: (info) => {
        const value = info.getValue() as number;
        const safeValue = value || 0;
        return (
          <Badge
            variant={safeValue >= 100 ? "default" : "secondary"}
            className={cn(
              safeValue >= 100
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : safeValue >= 70
                  ? "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
            )}
          >
            {safeValue.toFixed(2)}%
          </Badge>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
  ];

  console.log("DataTable data length:", data.length);

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Marketer Performance Report"
      description={
        selectedPeriod
          ? `${selectedPeriod.period.period_name} - ${selectedPeriod.office} Office`
          : "View marketer performance records"
      }
      className="max-w-[95vw] w-full"
      size="full"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md max-w-full overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Loading marketer reports...
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400">
                Failed to load marketer reports
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Error:{" "}
                {error && "status" in error
                  ? `${error.status}`
                  : "Unknown error"}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Check console for detailed error information
              </p>
            </div>
          </div>
        ) : !selectedPeriod ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
              <p className="text-orange-600 dark:text-orange-400">
                No period selected
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Please select a period to view reports
              </p>
            </div>
          </div>
        ) : (
          <div className="w-full max-w-full overflow-hidden space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-blue-600 dark:text-blue-400 text-xs font-medium">Total Marketers</p>
                    <p className="text-blue-900 dark:text-blue-100 text-xl font-bold">{totalRecords}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <Award className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-green-600 dark:text-green-400 text-xs font-medium">Avg Performance</p>
                    <p className="text-green-900 dark:text-green-100 text-xl font-bold">
                      {data.length > 0 ? (data.reduce((sum, item) => sum + (item.MIB_Perfomance || 0), 0) / data.length).toFixed(1) : 0}%
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-purple-600 dark:text-purple-400 text-xs font-medium">Top Performer</p>
                    <p className="text-purple-900 dark:text-purple-100 text-xl font-bold">
                      {data.length > 0 ? Math.max(...data.map(item => item.MIB_Perfomance || 0)).toFixed(1) : 0}%
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Distribution Pie Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Distribution</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Excellent (≥100%)', value: data.filter(item => (item.MIB_Perfomance || 0) >= 100).length, color: '#10B981' },
                        { name: 'Good (70-99%)', value: data.filter(item => (item.MIB_Perfomance || 0) >= 70 && (item.MIB_Perfomance || 0) < 100).length, color: '#F59E0B' },
                        { name: 'Needs Improvement (<70%)', value: data.filter(item => (item.MIB_Perfomance || 0) < 70).length, color: '#EF4444' }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[
                        { name: 'Excellent (≥100%)', value: data.filter(item => (item.MIB_Perfomance || 0) >= 100).length, color: '#10B981' },
                        { name: 'Good (70-99%)', value: data.filter(item => (item.MIB_Perfomance || 0) >= 70 && (item.MIB_Perfomance || 0) < 100).length, color: '#F59E0B' },
                        { name: 'Needs Improvement (<70%)', value: data.filter(item => (item.MIB_Perfomance || 0) < 70).length, color: '#EF4444' }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Top Performers Bar Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top 10 Performers</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={[...data].sort((a, b) => (b.MIB_achieved || 0) - (a.MIB_achieved || 0)).slice(0, 10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="fullnames" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip formatter={(value) => [new Intl.NumberFormat("en-KE", { style: "currency", currency: "KES", minimumFractionDigits: 0 }).format(value as number), 'MIB Achieved']} />
                    <Bar dataKey="MIB_achieved" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            <div className="table-mobile-scroll">
              <DataTable<MarketerReport>
                data={data}
                columns={columns}
                title={`Marketer Performance - ${selectedPeriod.period.period_name}`}
                enableExportToExcel={true}
                enablePrintPdf={true}
                enableColumnFilters={true}
                enableSorting={true}
                enableToolbar={true}
                containerClassName="w-full bg-white dark:bg-gray-900 rounded-lg shadow-md"
                tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300"
                tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
                tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
                tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
                tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                tBodyCellsClassName="px-4 py-2"
              />
            </div>

            {/* Pagination Controls */}
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 my-6 px-2">
              <div className="flex items-center gap-2">
                <PrimaryButton
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === 1
                    ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                    : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                    }`}
                  aria-label="First page"
                >
                  First
                </PrimaryButton>
                <PrimaryButton
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === 1
                    ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                    : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                    }`}
                  aria-label="Previous page"
                >
                  Previous
                </PrimaryButton>
                <span className="mx-2 text-sm text-gray-700 dark:text-gray-300">
                  Page <span className="font-semibold">{currentPage}</span> of{" "}
                  <span className="font-semibold">{totalPages}</span>
                </span>
                <PrimaryButton
                  onClick={() =>
                    setCurrentPage((p) => Math.min(totalPages, p + 1))
                  }
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === totalPages
                    ? "bg-green-600 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                    : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                    }`}
                  aria-label="Next page"
                >
                  Next
                </PrimaryButton>
                <PrimaryButton
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${currentPage === totalPages
                    ? "bg-green-600 dark:bg-green-600 text-green-600 cursor-not-allowed"
                    : "bg-white dark:bg-green-700 text-gray-600 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                    }`}
                  aria-label="Last page"
                >
                  Last
                </PrimaryButton>
              </div>
              <div className="flex items-center gap-2">
                <label
                  htmlFor="pageSize"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  Records per page:
                </label>
                <select
                  id="pageSize"
                  value={pageSize}
                  onChange={(e) => {
                    setPageSize(Number(e.target.value));
                    setCurrentPage(1);
                  }}
                  className="px-3 py-1 rounded-lg border border-green-200 dark:border-green-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-sm focus:ring-2 focus:ring-green-500"
                >
                  {[10, 20, 50, 100].map((size) => (
                    <option key={size} value={size}>
                      {size} / page
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </BaseModal>
  );
}
