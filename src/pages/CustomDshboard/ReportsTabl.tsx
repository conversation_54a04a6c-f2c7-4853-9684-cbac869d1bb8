import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import { Settings, AlertCircle, Search, X } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetMarketerPerformanceByPeriodQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { MarketerReport } from "@/types/marketer";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

interface EmployeeTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedPeriod?: {
    period: {
      period_name: string;
      start_date: string;
      end_date: string;
      target: number;
      achieved: number;
      progress: number;
    };
    office: string;
  } | null;
}

export default function EmployeeTableModal({
  open,
  onOpenChange,
  selectedPeriod,
}: EmployeeTableModalProps) {
  const [marketerFilter, setMarketerFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20); // or your preferred default

  // Fetch all marketer reports data (no pagination params)
  const {
    data: reportsData,
    isLoading,
    error,
  } = useGetMarketerPerformanceByPeriodQuery(
    {
      marketing_period: selectedPeriod?.period?.start_date || "ALL",
      marketer_employee_no: marketerFilter.trim() || "ALL",
      page: currentPage,
      page_size: pageSize,
    },
    {
      skip: !selectedPeriod?.period,
    }
  );

  if (error) {
    console.error("API Error Details:", error);
  }

  const data: MarketerReport[] = reportsData?.results || [];
  const totalRecords = reportsData?.count || 0;
  const totalPages = reportsData?.num_pages || 1;

  const handleFilterChange = (value: string) => {
    setMarketerFilter(value);
  };

  const columns: ColumnDef<MarketerReport>[] = [
    {
      accessorKey: "fullnames", // Changed from marketer_no_id
      header: "Marketer Name", // Updated header
      cell: (info) => info.getValue() || "Unknown", // Added fallback
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    // {
    //   accessorKey: "id",
    //   header: "ID",
    //   cell: (info) => info.getValue(),
    //   enableColumnFilter: false,
    //   filterFn: "includesString",
    // },
    {
      accessorKey: "period_start_date",
      header: "Period Start",
      cell: (info) => {
        const value = info.getValue() as string;
        return new Date(value).toLocaleDateString("en-KE");
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "period_end_date",
      header: "Period End",
      cell: (info) => {
        const value = info.getValue() as string;
        return new Date(value).toLocaleDateString("en-KE");
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "monthly_target",
      header: "Monthly Target",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 0,
        }).format(value);
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "daily_target",
      header: "Daily Target",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 2,
        }).format(value);
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "MIB_achieved",
      header: "MIB Achieved",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 0,
        }).format(value);
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "MIB_Perfomance",
      header: "Performance %",
      cell: (info) => {
        const value = info.getValue() as number;
        return (
          <Badge
            variant={value >= 100 ? "default" : "secondary"}
            className={cn(
              value >= 100
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : value >= 70
                ? "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
            )}
          >
            {value.toFixed(2)}%
          </Badge>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
  ];

  console.log("DataTable data length:", data.length);

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Marketer Performance Report"
      description={
        selectedPeriod
          ? `${selectedPeriod.period.period_name} - ${selectedPeriod.office} Office`
          : "View marketer performance records"
      }
      className="max-w-[95vw] w-full"
      size="full"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md max-w-full overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Loading marketer reports...
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400">
                Failed to load marketer reports
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Error:{" "}
                {error && "status" in error
                  ? `${error.status}`
                  : "Unknown error"}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Check console for detailed error information
              </p>
            </div>
          </div>
        ) : !selectedPeriod ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
              <p className="text-orange-600 dark:text-orange-400">
                No period selected
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Please select a period to view reports
              </p>
            </div>
          </div>
        ) : (
          <div className="w-full max-w-full overflow-hidden">
            <div className="table-mobile-scroll">
              <DataTable<MarketerReport>
                data={data}
                columns={columns}
                title={`Marketer Performance - ${selectedPeriod.period.period_name}`}
                enableExportToExcel={true}
                enablePrintPdf={true}
                enableColumnFilters={true}
                enableSorting={true}
                enableToolbar={true}
                containerClassName="w-full bg-white dark:bg-gray-900 rounded-lg shadow-md"
                tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300"
                tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
                tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
                tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
                tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                tBodyCellsClassName="px-4 py-2"
              />
            </div>

            {/* Pagination Controls */}
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 my-6 px-2">
              <div className="flex items-center gap-2">
                <PrimaryButton
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${
                    currentPage === 1
                      ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                      : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                  }`}
                  aria-label="First page"
                >
                  First
                </PrimaryButton>
                <PrimaryButton
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${
                    currentPage === 1
                      ? "bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                      : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                  }`}
                  aria-label="Previous page"
                >
                  Previous
                </PrimaryButton>
                <span className="mx-2 text-sm text-gray-700 dark:text-gray-300">
                  Page <span className="font-semibold">{currentPage}</span> of{" "}
                  <span className="font-semibold">{totalPages}</span>
                </span>
                <PrimaryButton
                  onClick={() =>
                    setCurrentPage((p) => Math.min(totalPages, p + 1))
                  }
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${
                    currentPage === totalPages
                      ? "bg-green-600 dark:bg-gray-700 text-gray-400 cursor-not-allowed"
                      : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                  }`}
                  aria-label="Next page"
                >
                  Next
                </PrimaryButton>
                <PrimaryButton
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-lg border text-sm font-medium transition-colors ${
                    currentPage === totalPages
                      ? "bg-green-600 dark:bg-green-600 text-green-600 cursor-not-allowed"
                      : "bg-white dark:bg-green-700 text-gray-600 dark:text-gray-200 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700"
                  }`}
                  aria-label="Last page"
                >
                  Last
                </PrimaryButton>
              </div>
              <div className="flex items-center gap-2">
                <label
                  htmlFor="pageSize"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  Records per page:
                </label>
                <select
                  id="pageSize"
                  value={pageSize}
                  onChange={(e) => {
                    setPageSize(Number(e.target.value));
                    setCurrentPage(1);
                  }}
                  className="px-3 py-1 rounded-lg border border-green-200 dark:border-green-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-sm focus:ring-2 focus:ring-green-500"
                >
                  {[10, 20, 50, 100].map((size) => (
                    <option key={size} value={size}>
                      {size} / page
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </BaseModal>
  );
}
