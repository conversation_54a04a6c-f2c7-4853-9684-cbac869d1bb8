import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  User,
  DollarSign,
  Calendar,
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  Banknote,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  MapPin,
  CreditCard,
  Target,
  PieChart,
  BarChart3,
  ArrowLeft
} from 'lucide-react';
import { useGetCreditsTeamDetailsQuery } from '@/redux/slices/teams';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { CreditOfficerDetailsType } from '@/types/creditsTeam';

const CreditOfficerDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { credit_officer_id } = useParams();

  const { data: detailsData, isLoading, error } = useGetCreditsTeamDetailsQuery(
    credit_officer_id as string,
    { skip: !credit_officer_id }
  ) as {
    data: CreditOfficerDetailsType | undefined;
    isLoading: boolean;
    error: any;
  };

  const getCreditOfficerIcon = () => {
    return <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
  };

  const getPerformanceBadge = (collected: number, due: number) => {
    const percentage = due > 0 ? (collected / due) * 100 : 0;

    if (percentage >= 80) {
      return (
        <Badge variant="default" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Excellent
        </Badge>
      );
    } else if (percentage >= 60) {
      return (
        <Badge variant="secondary" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800">
          <Clock className="w-3 h-3 mr-1" />
          Good
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">
          <AlertCircle className="w-3 h-3 mr-1" />
          Needs Improvement
        </Badge>
      );
    }
  };

  return (
    <Screen>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                {getCreditOfficerIcon()}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Credit Officer Details</h1>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Officer ID • {credit_officer_id}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(-1)}
                className="hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <SpinnerTemp type="spinner-double" size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Error Loading Details
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Unable to load credit officer details. Please try again.
            </p>
            <div className="flex justify-center space-x-3">
              <Button onClick={() => window.location.reload()} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
              <Button onClick={() => navigate(-1)}>
                Close
              </Button>
            </div>
          </div>
        ) : detailsData ? (
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="border-l-4 border-l-blue-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Officer ID</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{credit_officer_id}</p>
                    </div>
                    <CreditCard className="w-8 h-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Customers</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{detailsData.Portfolio.all_customers}</p>
                    </div>
                    <Users className="w-8 h-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Sales</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{detailsData.Portfolio.all_sales}</p>
                    </div>
                    <BarChart3 className="w-8 h-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Collections Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Banknote className="w-5 h-5 text-green-600" />
                  <span>Collections Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">All Overdue Collections</p>
                    <p className="text-lg font-bold text-green-600">KES {formatNumberWithCommas(detailsData.all_overdue_collections)}</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Collections Collected</p>
                    <p className="text-lg font-bold text-blue-600">KES {formatNumberWithCommas(detailsData.overdue_collections_collected)}</p>
                  </div>
                  <div className="text-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Monthly Due</p>
                    <p className="text-lg font-bold text-amber-600">KES {formatNumberWithCommas(detailsData.monthly_installments_due)}</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Monthly Collected</p>
                    <p className="text-lg font-bold text-purple-600">KES {formatNumberWithCommas(detailsData.monthly_installments_due_collected)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Daily Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5 text-blue-600" />
                  <span>Today's Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Installments Due Today</p>
                    <p className="text-2xl font-bold text-indigo-600">{detailsData.installments_due_today}</p>
                    <p className="text-xs text-gray-500">KES {formatNumberWithCommas(detailsData.installments_due_today_total)}</p>
                  </div>
                  <div className="text-center p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Collected Today</p>
                    <p className="text-2xl font-bold text-emerald-600">{detailsData.installments_collected_today}</p>
                  </div>
                  <div className="text-center p-4 bg-rose-50 dark:bg-rose-900/20 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Additional Deposits</p>
                    <p className="text-2xl font-bold text-rose-600">KES {formatNumberWithCommas(detailsData.additionaldeposits_installments_collected)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Portfolio Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <PieChart className="w-5 h-5 text-purple-600" />
                  <span>Portfolio Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Total Portfolio Value</span>
                      <span className="font-bold text-gray-900 dark:text-white">KES {formatNumberWithCommas(detailsData.Portfolio.portfolio_total_paid)}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Sales Below Threshold</span>
                      <span className="font-bold text-red-600">{detailsData.sales_below_threshold_count}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Overdue Below Threshold</span>
                      <span className="font-bold text-amber-600">{detailsData.overdue_below_threshold_count}</span>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Final Payments (MIB)</span>
                      <span className="font-bold text-green-600">KES {formatNumberWithCommas(detailsData.finalpaymentscollected_mib)}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Final Payment Count</span>
                      <span className="font-bold text-blue-600">{detailsData.finalpaymentscollected_no_of_payments}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Expected Installments</span>
                      <span className="font-bold text-purple-600">KES {formatNumberWithCommas(detailsData.total_expexted_installments)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Current Period */}
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Current Period</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {detailsData.Current_Month.Period_Start_Date} to {detailsData.Current_Month.Period_End_Date}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Details Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              We couldn't find details for the provided credit officer ID.
            </p>
          </div>
        )}
      </div>
    </Screen>
  );
};

export default CreditOfficerDetailsPage;