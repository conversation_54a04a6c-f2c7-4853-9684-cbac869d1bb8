import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";

import {
  Activity,
  TrendingUp,
  Users,
  BarChart3,
  Building2,
  Calendar,
  Target,
  Award,
  Pie<PERSON>hart as PieChartIcon,
  LineChart as LineChartIcon,
  <PERSON><PERSON>hart as BarChartIcon
} from "lucide-react";
import ReportsModal from "./ReportsModal";
import OfficeReportsModal from "./OfficesselectModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import RegionReportsModal from "./RegionSelect";
import { useGethrDashboardQuery, useGetAllMarketingPeriodsQuery, useGetAllMarketersQuery, useGetMarketerPerformanceByPeriodQuery, useGetTeamsPerformanceQuery } from "@/redux/slices/hrDashboardApiSlice";
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Line, AreaChart, Area } from 'recharts';
import { motion } from "framer-motion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";


// TypeScript interfaces for API response
interface CurrentPeriod {
  period_name: string;
  start_date: string;
  end_date: string;
  target: number;
  achieved: number;
  progress: number;
}

interface OfficeData {
  office: string;
  total_marketers: number;
  current_period: CurrentPeriod;
}










function HrDashboard() {
  const { data: officeData, isLoading: officeLoading, error: officeError } = useGethrDashboardQuery({});
  console.log("Office data:", officeData);
  const { data: periodsData, isLoading: periodsLoading } = useGetAllMarketingPeriodsQuery({});
  console.log("Period Data:", periodsData);
  const { data: allMarketersData, isLoading: marketersLoading } = useGetAllMarketersQuery({});
  console.log("All Marketers Data:", allMarketersData);
  const { data: marketerPerformanceData, isLoading: perfLoading } = useGetMarketerPerformanceByPeriodQuery({});
  console.log("Marketer Performance Data:", marketerPerformanceData);
  const { data: teamsData, isLoading: teamsLoading } = useGetTeamsPerformanceQuery({});
  console.log("Teams Data:", teamsData);

  const [showReports, setShowReports] = useState(false);
  const [showOfficeReports, setShowOfficeReports] = useState(false);
  const [showEmployeeByRegion, setShowEmployeeByRegion] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  const isLoading = officeLoading || periodsLoading || marketersLoading || perfLoading || teamsLoading;
  const error = officeError;






  return (
    <Screen>
      <div className="min-h-screen bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 py-4 space-y-4">
          {/* Header with Office Cards */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4"
          >
            {/* Header Section */}
            <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
                  <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                    HR Analytics Dashboard
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 text-base sm:text-lg">
                    Comprehensive Performance Management & Insights
                  </p>
                </div>
              </div>

              {!isLoading && !error && officeData?.offices && (
                <div className="flex flex-col sm:flex-row flex-wrap gap-4 w-full lg:w-auto">
                  <div className="flex-1 min-w-[180px] bg-white rounded-xl px-4 py-3 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-3">
                      <Building2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      <div>
                        <p className="text-blue-600 dark:text-blue-400 text-xs sm:text-sm font-medium">Total Offices</p>
                        <p className="text-blue-900 dark:text-blue-100 text-lg sm:text-xl font-bold">{officeData.offices.length}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 min-w-[180px] bg-white rounded-xl px-4 py-3 border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
                      <div>
                        <p className="text-green-600 dark:text-green-400 text-xs sm:text-sm font-medium">Total Marketers</p>
                        <p className="text-green-900 dark:text-green-100 text-lg sm:text-xl font-bold">
                          {officeData.offices.reduce((sum: number, office: OfficeData) => sum + office.total_marketers, 0)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 min-w-[180px] bg-white rounded-xl px-4 py-3 border border-purple-200 dark:border-purple-800">
                    <div className="flex items-center gap-3">
                      <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                      <div>
                        <p className="text-purple-600 dark:text-purple-400 text-xs sm:text-sm font-medium">Avg Performance</p>
                        <p className="text-purple-900 dark:text-purple-100 text-lg sm:text-xl font-bold">
                          {(officeData.offices.reduce((sum: number, office: OfficeData) => sum + office.current_period.progress, 0) / officeData.offices.length).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white">
              <TabsTrigger value="overview" className="bg-secondary data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">Overview</TabsTrigger>
              <TabsTrigger value="marketers" className="bg-secondary data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">Marketers</TabsTrigger>
              <TabsTrigger value="offices" className="bg-secondary data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">Offices</TabsTrigger>
              <TabsTrigger value="teams" className="bg-secondary data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">Teams</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Key Performance Indicators */}
              {!isLoading && !error && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
                >
                  {/* Total Offices */}
                  <Card className="bg-white border-blue-200 dark:border-blue-800">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Offices</p>
                          <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                            {officeData?.offices?.length || 0}
                          </p>
                        </div>
                        <Building2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Total Marketers */}
                  <Card className="bg-white border-green-200 dark:border-green-800">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-green-600 dark:text-green-400">Total Marketers</p>
                          <p className="text-3xl font-bold text-green-900 dark:text-green-100">
                            {officeData?.offices?.reduce((sum: number, office: OfficeData) => sum + office.total_marketers, 0) || 0}
                          </p>
                        </div>
                        <Users className="w-8 h-8 text-green-600 dark:text-green-400" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Average Performance */}
                  <Card className="bg-white border-purple-200 dark:border-purple-800">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Avg Performance</p>
                          <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
                            {officeData?.offices?.length ?
                              (officeData.offices.reduce((sum: number, office: OfficeData) => sum + office.current_period.progress, 0) / officeData.offices.length).toFixed(1) : 0}%
                          </p>
                        </div>
                        <TrendingUp className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Active Periods */}
                  <Card className="bg-white border-orange-200 dark:border-orange-800">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Active Periods</p>
                          <p className="text-3xl font-bold text-orange-900 dark:text-orange-100">
                            {periodsData?.results?.filter(p => new Date(p.period_end_date) >= new Date()).length || 0}
                          </p>
                        </div>
                        <Calendar className="w-8 h-8 text-orange-600 dark:text-orange-400" />
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Charts Section */}
              {!isLoading && !error && officeData?.offices && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                >
                  {/* Pie Chart - Total Marketers by Office */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <PieChartIcon className="w-5 h-5" />
                        Marketers Distribution
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={officeData.offices.filter(office => office.total_marketers > 0).map(office => ({
                              name: office.office,
                              value: office.total_marketers,
                            }))}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {officeData.offices.filter(office => office.total_marketers > 0).map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'][index % 5]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                      <div className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800/50 p-3 rounded-lg">
                        <p className="font-medium text-gray-900 dark:text-gray-100 mb-1">How this chart is calculated:</p>
                        <p>This pie chart shows the distribution of marketers across different offices.</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Bar Chart - Progress by Office */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChartIcon className="w-5 h-5" />
                        Progress by Office (%)
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={officeData.offices.filter((office: any) => office.current_period.progress > 0)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="office" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="current_period.progress" fill="#8884d8" name="Progress %" />
                        </BarChart>
                      </ResponsiveContainer>
                      <div className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800/50 p-3 rounded-lg">
                        <p className="font-medium text-gray-900 dark:text-gray-100 mb-1">How this chart is calculated:</p>
                        <p>This bar chart displays the progress percentage for each office towards their current period targets.</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Action Cards Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                {/* Marketers Performance Card */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
                >
                  <div className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
                        <Activity className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">Marketers Performance</h3>
                        <p className="text-xs text-gray-600 dark:text-gray-400">View MIB and achievements</p>
                        <div className="mt-2">
                          <div className="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Avg MIB Achievement</span>
                            <span>{marketerPerformanceData?.results ? (marketerPerformanceData.results.reduce((sum, m) => sum + m.MIB_Perfomance, 0) / marketerPerformanceData.results.length).toFixed(1) : 0}%</span>
                          </div>
                          <div className="w-full bg-white dark:bg-gray-700 rounded-full h-2">
                            <div className="bg-emerald-600 h-2 rounded-full" style={{ width: `${marketerPerformanceData?.results ? Math.min(marketerPerformanceData.results.reduce((sum, m) => sum + m.MIB_Perfomance, 0) / marketerPerformanceData.results.length, 100) : 0}%` }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <PrimaryButton
                      onClick={() => setShowReports(true)}
                      className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                      size="sm"
                    >
                      View Reports
                    </PrimaryButton>
                  </div>
                </motion.div>

                {/* Offices Performance Card */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
                >
                  <div className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <Building2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">Offices Performance</h3>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Monitor office metrics</p>
                        <div className="mt-2">
                          <div className="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Avg Office Progress</span>
                            <span>{officeData?.offices ? (officeData.offices.reduce((sum: any, o: any) => sum + o.current_period.progress, 0) / officeData.offices.length).toFixed(1) : 0}%</span>
                          </div>
                          <div className="w-full bg-white dark:bg-gray-700 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${officeData?.offices ? Math.min(officeData.offices.reduce((sum: any, o: any) => sum + o.current_period.progress, 0) / officeData.offices.length, 100) : 0}%` }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <PrimaryButton
                      onClick={() => setShowOfficeReports(true)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                      size="sm"
                    >
                      Check Offices
                    </PrimaryButton>
                  </div>
                </motion.div>
              </motion.div>
            </TabsContent>

            {/* Marketers Tab */}
            <TabsContent value="marketers" className="space-y-6">
              {!isLoading && !error && (allMarketersData || marketerPerformanceData) && (
                <>
                  {/* KPIs */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          Avg MIB Achievement
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {marketerPerformanceData?.results ? (marketerPerformanceData.results.reduce((sum, m) => sum + m.MIB_Perfomance, 0) / marketerPerformanceData.results.length).toFixed(1) : 0}%
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Award className="w-4 h-4" />
                          Top Performer
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {marketerPerformanceData?.results ? [...marketerPerformanceData.results].sort((a, b) => b.MIB_Perfomance - a.MIB_Perfomance)[0]?.fullnames : 'N/A'}
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <TrendingUp className="w-4 h-4" />
                          Above 100%
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {marketerPerformanceData?.results ? marketerPerformanceData.results.filter(m => m.MIB_Perfomance >= 100).length : 0}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <PieChartIcon className="w-5 h-5" />
                          Performance Distribution
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={[
                                { name: 'Excellent (≥100%)', value: marketerPerformanceData?.results ? marketerPerformanceData.results.filter(m => m.MIB_Perfomance >= 100).length : 0, fill: '#10B981' },
                                { name: 'Good (70-99%)', value: marketerPerformanceData?.results ? marketerPerformanceData.results.filter(m => m.MIB_Perfomance >= 70 && m.MIB_Perfomance < 100).length : 0, fill: '#F59E0B' },
                                { name: 'Needs Improvement (<70%)', value: marketerPerformanceData?.results ? marketerPerformanceData.results.filter(m => m.MIB_Perfomance < 70).length : 0, fill: '#EF4444' }
                              ].filter(item => item.value > 0)}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              dataKey="value"
                            >
                            </Pie>
                            <Tooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <BarChartIcon className="w-5 h-5" />
                          Top 10 Performers
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={marketerPerformanceData?.results ? [...marketerPerformanceData.results].sort((a, b) => b.MIB_Perfomance - a.MIB_Perfomance).slice(0, 10).map(m => ({ name: m.fullnames ? m.fullnames.split(' ')[0] : 'Unknown', performance: m.MIB_Perfomance })) : []}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="performance" fill="#8884d8" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Trends */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <LineChartIcon className="w-5 h-5" />
                        MIB Achievement Trends
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={marketerPerformanceData?.results ? marketerPerformanceData.results.slice(0, 20).map((m, i) => ({ index: i + 1, performance: m.MIB_Perfomance })) : []}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="index" />
                          <YAxis />
                          <Tooltip />
                          <Area type="monotone" dataKey="performance" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                        </AreaChart>
                      </ResponsiveContainer>
                      <div className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800/50 p-3 rounded-lg">
                        <p className="font-medium text-gray-900 dark:text-gray-100 mb-1">How this chart is calculated:</p>
                        <p>This area chart shows the MIB achievement trends for individual marketers. It is calculated by plotting the MIB_Perfomance percentage from each marketer's performance data, displaying how well each marketer is performing against their MIB targets over time.</p>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>

            {/* Offices Tab */}
            <TabsContent value="offices" className="space-y-6">
              {!isLoading && !error && officeData?.offices && (
                <>
                  {/* KPIs */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Building2 className="w-4 h-4" />
                          Total Offices
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{officeData.offices.length}</div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Total Marketers
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {officeData.offices.reduce((sum: any, office: any) => sum + office.total_marketers, 0)}
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          Avg Progress
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {(officeData.offices.reduce((sum: any, office: any) => sum + office.current_period.progress, 0) / officeData.offices.length).toFixed(1)}%
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Award className="w-4 h-4" />
                          Best Office
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {[...officeData.offices].sort((a, b) => b.current_period.progress - a.current_period.progress)[0]?.office}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <BarChartIcon className="w-5 h-5" />
                          Progress by Office
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={officeData.offices.map(office => ({ name: office.office, progress: office.current_period.progress }))}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="progress" fill="#82ca9d" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <PieChartIcon className="w-5 h-5" />
                          Marketers by Office
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={officeData.offices.filter(office => office.total_marketers > 0).map(office => ({
                                name: office.office,
                                value: office.total_marketers,
                              }))}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {officeData.offices.filter(office => office.total_marketers > 0).map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'][index % 5]} />
                              ))}
                            </Pie>
                            <Tooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Office Details Table */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle>Office Performance Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Office</th>
                              <th className="text-left p-2">Marketers</th>
                              <th className="text-left p-2">Progress (%)</th>
                              <th className="text-left p-2">Current Period</th>
                              <th className="text-left p-2">Target</th>
                              <th className="text-left p-2">Achieved</th>
                            </tr>
                          </thead>
                          <tbody>
                            {officeData.offices.map((office, index) => (
                              <tr key={index} className="border-b">
                                <td className="p-2 font-medium">{office.office}</td>
                                <td className="p-2">{office.total_marketers}</td>
                                <td className="p-2">{office.current_period.progress ? office.current_period.progress.toFixed(1) : '0.0'}%</td>
                                <td className="p-2">{office.current_period.period_name || 'N/A'}</td>
                                <td className="p-2">{office.current_period.target ? office.current_period.target.toLocaleString() : '0'}</td>
                                <td className="p-2">{office.current_period.achieved ? office.current_period.achieved.toLocaleString() : '0'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>

            {/* Teams Tab */}
            <TabsContent value="teams" className="space-y-6">
              {!isLoading && !error && teamsData?.results && (
                <>
                  {/* KPIs */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Total Teams
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{teamsData.results.length}</div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          Avg Progress
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{
                          teamsData.results.length > 0
                            ? (() => {
                              const avg =
                                teamsData.results.reduce(
                                  (sum, t) => sum + (Number(t.progress) || 0),
                                  0
                                ) / teamsData.results.length;

                              return isNaN(avg) ? 0 : avg.toFixed(1);
                            })()
                            : 0
                        }%
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <TrendingUp className="w-4 h-4" />
                          Avg MIB Achievement
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {teamsData.results.length > 0 ? (teamsData.results.reduce((sum, t) => sum + t.MIB_Perfomance, 0) / teamsData.results.length).toFixed(1) : 0}%
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-white">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <Award className="w-4 h-4" />
                          Top Team
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {teamsData.results.length > 0 ? [...teamsData.results].sort((a, b) => b.MIB_Perfomance - a.MIB_Perfomance)[0]?.team : 'N/A'}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <BarChartIcon className="w-5 h-5" />
                          Team Progress Comparison
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={teamsData.results.slice(0, 10).map(t => ({ name: t.team, progress: t.progress }))}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="progress" fill="#8884d8" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <LineChartIcon className="w-5 h-5" />
                          MIB Achievement by Team
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <ResponsiveContainer width="100%" height={300}>
                          <LineChart data={teamsData.results.slice(0, 10).map(t => ({ team: t.team, mib: t.MIB_Perfomance }))}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="team" />
                            <YAxis />
                            <Tooltip />
                            <Line type="monotone" dataKey="mib" stroke="#8884d8" />
                          </LineChart>
                        </ResponsiveContainer>
                        <div className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800/50 p-3 rounded-lg">
                          <p className="font-medium text-gray-900 dark:text-gray-100 mb-1">How this chart is calculated:</p>
                          <p>This line chart displays the MIB achievement percentage for each team.</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Teams Table */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle>Teams Performance Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Team</th>
                              <th className="text-left p-2">Marketers</th>
                              <th className="text-left p-2">Progress</th>
                              <th className="text-left p-2">MIB Achievement</th>
                              <th className="text-left p-2">Target</th>
                              <th className="text-left p-2">Achieved</th>
                              <th className="text-left p-2">Period</th>
                            </tr>
                          </thead>
                          <tbody>
                            {teamsData.results.map((team, index) => (
                              <tr key={index} className="border-b">
                                <td className="p-2 font-medium">{team.team || 'Unknown'}</td>
                                <td className="p-2">{team.total_marketers || 0}</td>
                                <td className="p-2">{team.progress ? team.progress.toFixed(1) : '0.0'}%</td>
                                <td className="p-2">{team.MIB_Perfomance ? team.MIB_Perfomance.toFixed(1) : '0.0'}%</td>
                                <td className="p-2">{team.monthly_target ? team.monthly_target.toLocaleString() : '0'}</td>
                                <td className="p-2">{team.MIB_achieved ? team.MIB_achieved.toLocaleString() : '0'}</td>
                                <td className="p-2">{team.period_name || 'N/A'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>

          </Tabs>

          {/* Error State */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-red-200 dark:border-red-700 p-6"
            >
              <div className="flex items-center gap-4">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <Activity className="w-6 h-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">Connection Error</h3>
                  <p className="text-red-600 dark:text-red-400">Failed to load office data. Please try again later.</p>
                </div>
              </div>
            </motion.div>
          )}

          {/* No Data State */}
          {!isLoading && !error && (!officeData?.offices || officeData.offices.length === 0) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8"
            >
              <div className="text-center">
                <div className="p-4 bg-white dark:bg-gray-700 rounded-xl w-fit mx-auto mb-4">
                  <BarChart3 className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">No Data Available</h3>
                <p className="text-gray-500 dark:text-gray-400">No office data available at the moment.</p>
              </div>
            </motion.div>
          )}



          {/* Modals */}
          <ReportsModal open={showReports} onOpenChange={setShowReports} />
          <OfficeReportsModal open={showOfficeReports} onOpenChange={setShowOfficeReports} />
          <RegionReportsModal open={showEmployeeByRegion} onOpenChange={setShowEmployeeByRegion} />
        </div>
      </div>
    </Screen >
  );
}

export default HrDashboard;