import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";

import {
  Activity,

  TrendingUp,
  Users,
  BarChart3,
  Building2
} from "lucide-react";
import ReportsModal from "./ReportsModal";
import OfficeReportsModal from "./OfficesselectModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import RegionReportsModal from "./RegionSelect";
import TeamsManagementModal from "./TeamsManagementModal";
import { useGethrDashboardQuery } from "@/redux/slices/hrDashboardApiSlice";
import { motion } from "framer-motion";


// TypeScript interfaces for API response
interface CurrentPeriod {
  period_name: string;
  start_date: string;
  end_date: string;
  target: number;
  achieved: number;
  progress: number;
}

interface OfficeData {
  office: string;
  total_marketers: number;
  current_period: CurrentPeriod;
}










function HrDashboard() {
  const { data, isLoading, error } = useGethrDashboardQuery({});
  const [showReports, setShowReports] = useState(false);
  const [showOfficeReports, setShowOfficeReports] = useState(false);
  const [showEmployeeByRegion, setShowEmployeeByRegion] = useState(false);
  const [showTeamsPerformance, setShowTeamsPerformance] = useState(false);
  console.log(data);





  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 py-4 space-y-4">
          {/* Header with Office Cards */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4"
          >
            {/* Header Section */}
            <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
                  <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                    HR Dashboard
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 text-base sm:text-lg">
                    Performance Management Dashboard
                  </p>
                </div>
              </div>

              {!isLoading && !error && data?.offices && (
                <div className="flex flex-col sm:flex-row flex-wrap gap-4 w-full lg:w-auto">
                  <div className="flex-1 min-w-[180px] bg-blue-50 dark:bg-blue-900/20 rounded-xl px-4 py-3 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-3">
                      <Building2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      <div>
                        <p className="text-blue-600 dark:text-blue-400 text-xs sm:text-sm font-medium">Total Offices</p>
                        <p className="text-blue-900 dark:text-blue-100 text-lg sm:text-xl font-bold">{data.offices.length}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 min-w-[180px] bg-green-50 dark:bg-green-900/20 rounded-xl px-4 py-3 border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
                      <div>
                        <p className="text-green-600 dark:text-green-400 text-xs sm:text-sm font-medium">Total Marketers</p>
                        <p className="text-green-900 dark:text-green-100 text-lg sm:text-xl font-bold">
                          {data.offices.reduce((sum: number, office: OfficeData) => sum + office.total_marketers, 0)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 min-w-[180px] bg-purple-50 dark:bg-purple-900/20 rounded-xl px-4 py-3 border border-purple-200 dark:border-purple-800">
                    <div className="flex items-center gap-3">
                      <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                      <div>
                        <p className="text-purple-600 dark:text-purple-400 text-xs sm:text-sm font-medium">Avg Performance</p>
                        <p className="text-purple-900 dark:text-purple-100 text-lg sm:text-xl font-bold">
                          {(data.offices.reduce((sum: number, office: OfficeData) => sum + office.current_period.progress, 0) / data.offices.length).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>


          </motion.div>

          {/* Loading State */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 w-1/3"></div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
                  {Array.from({ length: 7 }).map((_, idx) => (
                    <div key={idx} className="bg-gray-200 dark:bg-gray-700 rounded-xl h-32"></div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Error State */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-red-200 dark:border-red-700 p-6"
            >
              <div className="flex items-center gap-4">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <Activity className="w-6 h-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">Connection Error</h3>
                  <p className="text-red-600 dark:text-red-400">Failed to load office data. Please try again later.</p>
                </div>
              </div>
            </motion.div>
          )}

          {/* No Data State */}
          {!isLoading && !error && (!data?.offices || data.offices.length === 0) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8"
            >
              <div className="text-center">
                <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-xl w-fit mx-auto mb-4">
                  <BarChart3 className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">No Data Available</h3>
                <p className="text-gray-500 dark:text-gray-400">No office data available at the moment.</p>
              </div>
            </motion.div>
          )}

          {/* Action Cards Section - Compact Design */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            {/* Marketers Performance Card */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
                    <Activity className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Marketers Performance</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">View MIB and achievements</p>
                  </div>
                </div>
                <PrimaryButton
                  onClick={() => setShowReports(true)}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  size="sm"
                >
                  {/* <BarChart3 className="w-4 h-4 mr-2" /> */}
                  View Reports
                </PrimaryButton>
              </div>
            </motion.div>

            {/* Offices Performance Card */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <Building2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Offices Performance</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Monitor office metrics</p>
                  </div>
                </div>
                <PrimaryButton
                  onClick={() => setShowOfficeReports(true)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  size="sm"
                >
                  {/* <House className="w-4 h-4 mr-2" /> */}
                  Check Offices
                </PrimaryButton>
              </div>
            </motion.div>

            {/* Teams Performance Card */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <Users className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Teams Performance</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Monitor team productivity</p>
                  </div>
                </div>
                <PrimaryButton
                  onClick={() => setShowTeamsPerformance(true)}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                  size="sm"
                >
                  {/* <Users className="w-4 h-4 mr-2" /> */}
                  View Teams
                </PrimaryButton>
              </div>
            </motion.div>
          </motion.div>

          {/* Modals */}
          <ReportsModal open={showReports} onOpenChange={setShowReports} />
          <OfficeReportsModal open={showOfficeReports} onOpenChange={setShowOfficeReports} />
          <RegionReportsModal open={showEmployeeByRegion} onOpenChange={setShowEmployeeByRegion} />
          <TeamsManagementModal open={showTeamsPerformance} onOpenChange={setShowTeamsPerformance} />
        </div>
      </div>
    </Screen>
  );
}

export default HrDashboard;