import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";

interface Leads {
  name: string;
  clientphonenumber: string;
  Region: string;
  Date: Date;
  status: string;
}

interface EmployeeTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function LeadsTableModal({ open, onOpenChange }: EmployeeTableModalProps) {
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false); // State for Register Modal
  const [data, setData] = useState<Leads[]>([
    {
      name: "<PERSON>",
      clientphonenumber: "0712345678",
      Region: "Nairobi",
      Date: new Date("2023-10-01"),
      status: "Verified",
    },
    {
      name: "Jane Wanjiru",
      clientphonenumber: "0723456789",
      Region: "Mombasa",
      Date: new Date("2023-10-02"),
      status: "Call Not Taken",
    },
    {
      name: "John Kamau",
      clientphonenumber: "0734567890",
      Region: "Kisumu",
      Date: new Date("2023-10-03"),
      status: "Unverified",
    },
  ]);

  const [newDSR, setNewDSR] = useState<Leads>({
    name: "",
    clientphonenumber: "",
    Region: "",
    Date: new Date(),
    status: "Unverified", // Default status
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewDSR((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setData((prevData) => [...prevData, { ...newDSR, Date: new Date() }]); // Add new DSR to the table
    setIsRegisterModalOpen(false); // Close the modal
    setNewDSR({ name: "", clientphonenumber: "", Region: "", Date: new Date(), status: "Unverified" }); // Reset form
  };

  const columns: ColumnDef<Leads>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "clientphonenumber",
      header: "Client Phone Number",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "Region",
      header: "Region",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "Date",
      header: "Date",
      cell: (info) => (info.getValue() as Date).toLocaleDateString(),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-semibold ${
            info.getValue() === "Verified"
              ? "bg-green-100 text-green-800"
              : info.getValue() === "Call Not Taken"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {info.getValue() as string}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit row ${row.original.name}`);
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Delete row ${row.original.name}`);
              }}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <>
      <BaseModal
        open={open}
        onOpenChange={onOpenChange}
        title="HR Reports"
        description="View and manage employee performance records"
        className="max-w-4xl"
        size="full"
      >
        <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
          <div className="flex justify-end mb-4">
            {/* Button to open Register Modal */}
            <PrimaryButton onClick={() => setIsRegisterModalOpen(true)}>Register New DSR</PrimaryButton>
          </div>
          <DataTable<Leads>
            data={data}
            columns={columns}
            title="Office Reports"
            enableExportToExcel={true}
            enablePrintPdf={true}
            enableColumnFilters={true}
            enablePagination={true}
            enableSorting={true}
            enableToolbar={true}
            containerClassName="max-w-full"
            tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300 shadow-md rounded-lg overflow-hidden"
            tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
            tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
            tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
            tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            tBodyCellsClassName="px-4 py-2"
          />
        </div>
      </BaseModal>

      {/* Register New DSR Modal */}
      <BaseModal
        open={isRegisterModalOpen}
        onOpenChange={setIsRegisterModalOpen}
        title="Register New DSR"
        description="Fill in the details to register a new DSR"
        className="max-w-lg"
      >
        <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Name
              </label>
              <input
                type="text"
                name="name"
                value={newDSR.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                placeholder="Enter DSR name"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phone Number
              </label>
              <input
                type="text"
                name="clientphonenumber"
                value={newDSR.clientphonenumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                placeholder="Enter phone number"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Region
              </label>
              <input
                type="text"
                name="Region"
                value={newDSR.Region}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                placeholder="Enter region"
                required
              />
            </div>
            <div className="flex justify-end">
              <PrimaryButton type="submit">Submit</PrimaryButton>
            </div>
          </form>
        </div>
      </BaseModal>
    </>
  );
}