import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Phone,
  DollarSign,
  Target,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Flame,
  Thermometer,
  Snowflake,
  Search,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  User,
  Mail,
  Calendar,
  MapPin,
  Building,
  CreditCard,
  Activity,
  UserPlus,
  Edit3,
  MessageSquare
} from 'lucide-react';
import { 
  useGetTelemarketingCustomersQuery,
  useGetAllProspectsQuery,
  useGetTeleteamSalesQuery,
  useGetAllSalesQuery,
  useGetUnallocatedLeadsQuery
} from '@/redux/slices/telemarketingApiSlice';

// Types
interface Customer {
  customer_no: string;
  name: string;
  phone_number: string;
  email?: string;
  employee_no: string;
  marketer: string;
  national_id?: string;
  kra?: string;
}

interface Prospect {
  prospect_id: string;
  name: string;
  phone_number: string;
  category: 'Hot' | 'Warm' | 'Cold';
  status: string;
  leadsource_title: string;
  pipeline_level: string;
  allocated_marketer?: string;
  created_date: string;
  actions?: {
    edit_url?: string;
    add_feedback_url?: string;
  };
}

interface Sale {
  lead_file_no: string;
  customer_name: string;
  customer_no: string;
  status: 'active' | 'completed';
  selling_price?: number;
  total_paid?: number;
  balance?: number;
  booking_date: string;
  marketer_name: string;
  marketer_employee_no: string;
}

interface Lead {
  id?: string;
  lead_id?: string;
  name?: string;
  lead_name?: string;
  phone_number?: string;
  phone?: string;
  email?: string;
  source?: string;
  lead_source?: string;
  location?: string;
  address?: string;
  created_date?: string;
  date_created?: string;
}

interface TeleteamModalsProps {
  telemarketingCustomersOpen: boolean;
  setTelemarketingCustomersOpen: (open: boolean) => void;
  hotProspectsOpen: boolean;
  setHotProspectsOpen: (open: boolean) => void;
  warmProspectsOpen: boolean;
  setWarmProspectsOpen: (open: boolean) => void;
  coldProspectsOpen: boolean;
  setColdProspectsOpen: (open: boolean) => void;
  teleteamSalesOpen: boolean;
  setTeleteamSalesOpen: (open: boolean) => void;
  unallocatedLeadsOpen: boolean;
  setUnallocatedLeadsOpen: (open: boolean) => void;
  teleteamStats?: any;
}

const TeleteamModals: React.FC<TeleteamModalsProps> = ({
  telemarketingCustomersOpen,
  setTelemarketingCustomersOpen,
  hotProspectsOpen,
  setHotProspectsOpen,
  warmProspectsOpen,
  setWarmProspectsOpen,
  coldProspectsOpen,
  setColdProspectsOpen,
  teleteamSalesOpen,
  setTeleteamSalesOpen,
  unallocatedLeadsOpen,
  setUnallocatedLeadsOpen,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [customerSearch, setCustomerSearch] = useState('');
  const [prospectSearch, setProspectSearch] = useState('');
  const [salesSearch, setSalesSearch] = useState('');
  const [leadsSearch, setLeadsSearch] = useState('');

  // API calls
  const {
    data: telemarketingCustomers,
    isLoading: customersLoading,
    error: customersError,
    refetch: refetchCustomers
  } = useGetTelemarketingCustomersQuery({
    search: customerSearch,
    page: currentPage,
    page_size: pageSize
  }, { skip: !telemarketingCustomersOpen });

  const {
    data: hotProspects,
    isLoading: hotProspectsLoading,
    refetch: refetchHotProspects
  } = useGetAllProspectsQuery({
    search: prospectSearch,
    category: 'Hot',
    page: currentPage,
    page_size: pageSize
  }, { skip: !hotProspectsOpen });

  const {
    data: warmProspects,
    isLoading: warmProspectsLoading,
    refetch: refetchWarmProspects
  } = useGetAllProspectsQuery({
    search: prospectSearch,
    category: 'Warm',
    page: currentPage,
    page_size: pageSize
  }, { skip: !warmProspectsOpen });

  const {
    data: coldProspects,
    isLoading: coldProspectsLoading,
    refetch: refetchColdProspects
  } = useGetAllProspectsQuery({
    search: prospectSearch,
    category: 'Cold',
    page: currentPage,
    page_size: pageSize
  }, { skip: !coldProspectsOpen });

  const {
    data: teleteamSales,
    isLoading: teleteamSalesLoading,
    refetch: refetchTeleteamSales
  } = useGetTeleteamSalesQuery({
    search: salesSearch,
    page: currentPage,
    page_size: pageSize
  }, { skip: !teleteamSalesOpen });


  const {
    data: unallocatedLeads,
    isLoading: unallocatedLeadsLoading,
    refetch: refetchUnallocatedLeads
  } = useGetUnallocatedLeadsQuery({
    search: leadsSearch,
    page: currentPage,
    page_size: pageSize
  }, { skip: !unallocatedLeadsOpen });

  useEffect(() => {
    setCurrentPage(1);
  }, [telemarketingCustomersOpen, hotProspectsOpen, warmProspectsOpen, coldProspectsOpen, teleteamSalesOpen, unallocatedLeadsOpen]);

  // Reusable Components
  const SearchHeader: React.FC<{
    searchValue: string;
    onSearchChange: (value: string) => void;
    placeholder: string;
    onRefresh?: () => void;
    isLoading?: boolean;
  }> = ({ searchValue, onSearchChange, placeholder, onRefresh, isLoading }) => (
    <div className="flex items-center justify-between p-4 border-b bg-gray-50/50">
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>
      {onRefresh && (
        <Button variant="outline" size="sm" onClick={onRefresh} disabled={isLoading}>
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      )}
    </div>
  );

  const Pagination: React.FC<{
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    isLoading?: boolean;
  }> = ({ currentPage, totalPages, onPageChange, isLoading }) => (
    <div className="flex items-center justify-between p-4 border-t bg-gray-50/50">
      <div className="text-sm text-gray-600">
        Page {currentPage} of {totalPages}
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1 || isLoading}
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages || isLoading}
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );

  const LoadingState: React.FC = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
        <p className="text-gray-600">Loading data...</p>
      </div>
    </div>
  );

  // Table Components
  const CustomersTable: React.FC<{ customers: Customer[] }> = ({ customers }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Customer</TableHead>
          <TableHead>Contact</TableHead>
          <TableHead>Marketer</TableHead>
          <TableHead>Employee No</TableHead>
          <TableHead>Details</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {customers.map((customer) => (
          <TableRow key={customer.customer_no}>
            <TableCell>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-indigo-600" />
                </div>
                <div>
                  <div className="font-medium">{customer.name}</div>
                  <div className="text-sm text-gray-500">ID: {customer.customer_no}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div className="space-y-1">
                <div className="flex items-center text-sm">
                  <Phone className="w-3 h-3 mr-1" />
                  {customer.phone_number}
                </div>
                {customer.email && (
                  <div className="flex items-center text-sm text-gray-500">
                    <Mail className="w-3 h-3 mr-1" />
                    {customer.email}
                  </div>
                )}
              </div>
            </TableCell>
            <TableCell>{customer.marketer}</TableCell>
            <TableCell>
              <Badge variant="secondary">{customer.employee_no}</Badge>
            </TableCell>
            <TableCell>
              <div className="text-xs text-gray-500 space-y-1">
                {customer.national_id && <div>NID: {customer.national_id}</div>}
                {customer.kra && <div>KRA: {customer.kra}</div>}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  const ProspectsTable: React.FC<{ prospects: Prospect[] }> = ({ prospects }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Prospect</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Contact</TableHead>
          <TableHead>Source</TableHead>
          <TableHead>Pipeline</TableHead>
          <TableHead>Marketer</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {prospects.map((prospect) => (
          <TableRow key={prospect.prospect_id}>
            <TableCell>
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  prospect.category === 'Hot' ? 'bg-red-100' :
                  prospect.category === 'Warm' ? 'bg-yellow-100' : 'bg-cyan-100'
                }`}>
                  {prospect.category === 'Hot' ? 
                    <Flame className="w-4 h-4 text-red-600" /> :
                    prospect.category === 'Warm' ?
                    <Thermometer className="w-4 h-4 text-yellow-600" /> :
                    <Snowflake className="w-4 h-4 text-cyan-600" />
                  }
                </div>
                <div>
                  <div className="font-medium">{prospect.name}</div>
                  <div className="text-sm text-gray-500">ID: {prospect.prospect_id}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <Badge 
                variant={prospect.category === 'Hot' ? 'destructive' : 
                       prospect.category === 'Warm' ? 'default' : 'secondary'}
              >
                {prospect.category}
              </Badge>
            </TableCell>
            <TableCell>
              <div className="flex items-center text-sm">
                <Phone className="w-3 h-3 mr-1" />
                {prospect.phone_number}
              </div>
            </TableCell>
            <TableCell>{prospect.leadsource_title}</TableCell>
            <TableCell>{prospect.pipeline_level}</TableCell>
            <TableCell>{prospect.allocated_marketer || 'Unassigned'}</TableCell>
            <TableCell>
              <div className="flex items-center space-x-1">
                {prospect.actions?.edit_url && (
                  <Button variant="ghost" size="sm">
                    <Edit3 className="w-3 h-3" />
                  </Button>
                )}
                {prospect.actions?.add_feedback_url && (
                  <Button variant="ghost" size="sm">
                    <MessageSquare className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  const SalesTable: React.FC<{ sales: Sale[] }> = ({ sales }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Customer</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Selling Price</TableHead>
          <TableHead>Total Paid</TableHead>
          <TableHead>Balance</TableHead>
          <TableHead>Booking Date</TableHead>
          <TableHead>Marketer</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {sales.map((sale) => (
          <TableRow key={sale.lead_file_no}>
            <TableCell>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <div className="font-medium">{sale.customer_name}</div>
                  <div className="text-sm text-gray-500">File: {sale.lead_file_no}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <Badge variant={sale.status === 'active' ? 'default' : 'secondary'}>
                {sale.status}
              </Badge>
            </TableCell>
            <TableCell className="font-medium text-green-600">
              KSh {sale.selling_price?.toLocaleString() || 0}
            </TableCell>
            <TableCell className="font-medium text-blue-600">
              KSh {sale.total_paid?.toLocaleString() || 0}
            </TableCell>
            <TableCell className="font-medium">
              KSh {sale.balance?.toLocaleString() || 0}
            </TableCell>
            <TableCell>{new Date(sale.booking_date).toLocaleDateString()}</TableCell>
            <TableCell>{sale.marketer_name}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  const LeadsTable: React.FC<{ leads: Lead[] }> = ({ leads }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Lead</TableHead>
          <TableHead>Contact</TableHead>
          <TableHead>Source</TableHead>
          <TableHead>Location</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {leads.map((lead) => (
          <TableRow key={lead.id || lead.lead_id}>
            <TableCell>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                </div>
                <div>
                  <div className="font-medium">{lead.name || lead.lead_name}</div>
                  <div className="text-sm text-gray-500">ID: {lead.id || lead.lead_id}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div className="space-y-1">
                {(lead.phone_number || lead.phone) && (
                  <div className="flex items-center text-sm">
                    <Phone className="w-3 h-3 mr-1" />
                    {lead.phone_number || lead.phone}
                  </div>
                )}
                {lead.email && (
                  <div className="flex items-center text-sm text-gray-500">
                    <Mail className="w-3 h-3 mr-1" />
                    {lead.email}
                  </div>
                )}
              </div>
            </TableCell>
            <TableCell>{lead.source || lead.lead_source}</TableCell>
            <TableCell>{lead.location || lead.address}</TableCell>
            <TableCell>{new Date(lead.created_date || lead.date_created || '').toLocaleDateString()}</TableCell>
            <TableCell>
              <div className="flex items-center space-x-1">
                <Button variant="ghost" size="sm">
                  <UserPlus className="w-3 h-3" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Edit3 className="w-3 h-3" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  return (
    <>
      {/* Telemarketing Customers Modal */}
      <Dialog open={telemarketingCustomersOpen} onOpenChange={setTelemarketingCustomersOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Phone className="w-5 h-5" />
              <span>Telemarketing Customers</span>
              <Badge variant="secondary">{telemarketingCustomers?.count || 0}</Badge>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col h-full">
            <SearchHeader
              searchValue={customerSearch}
              onSearchChange={setCustomerSearch}
              placeholder="Search customers..."
              onRefresh={refetchCustomers}
              isLoading={customersLoading}
            />
            <div className="flex-1 overflow-y-auto">
              {customersLoading ? (
                <LoadingState />
              ) : (
                <CustomersTable customers={telemarketingCustomers?.results || []} />
              )}
            </div>
            {telemarketingCustomers && (
              <Pagination
                currentPage={telemarketingCustomers.current_page}
                totalPages={telemarketingCustomers.total_pages}
                onPageChange={setCurrentPage}
                isLoading={customersLoading}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Hot Prospects Modal */}
      <Dialog open={hotProspectsOpen} onOpenChange={setHotProspectsOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Flame className="w-5 h-5 text-red-600" />
              <span>Hot Prospects</span>
              <Badge variant="destructive">{hotProspects?.count || 0}</Badge>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col h-full">
            <SearchHeader
              searchValue={prospectSearch}
              onSearchChange={setProspectSearch}
              placeholder="Search hot prospects..."
              onRefresh={refetchHotProspects}
              isLoading={hotProspectsLoading}
            />
            <div className="flex-1 overflow-y-auto">
              {hotProspectsLoading ? (
                <LoadingState />
              ) : (
                <ProspectsTable prospects={hotProspects?.results || []} />
              )}
            </div>
            {hotProspects && (
              <Pagination
                currentPage={hotProspects.current_page}
                totalPages={hotProspects.total_pages}
                onPageChange={setCurrentPage}
                isLoading={hotProspectsLoading}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Warm Prospects Modal */}
      <Dialog open={warmProspectsOpen} onOpenChange={setWarmProspectsOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Thermometer className="w-5 h-5 text-yellow-600" />
              <span>Warm Prospects</span>
              <Badge variant="default">{warmProspects?.count || 0}</Badge>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col h-full">
            <SearchHeader
              searchValue={prospectSearch}
              onSearchChange={setProspectSearch}
              placeholder="Search warm prospects..."
              onRefresh={refetchWarmProspects}
              isLoading={warmProspectsLoading}
            />
            <div className="flex-1 overflow-y-auto">
              {warmProspectsLoading ? (
                <LoadingState />
              ) : (
                <ProspectsTable prospects={warmProspects?.results || []} />
              )}
            </div>
            {warmProspects && (
              <Pagination
                currentPage={warmProspects.current_page}
                totalPages={warmProspects.total_pages}
                onPageChange={setCurrentPage}
                isLoading={warmProspectsLoading}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Cold Prospects Modal */}
      <Dialog open={coldProspectsOpen} onOpenChange={setColdProspectsOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Snowflake className="w-5 h-5 text-cyan-600" />
              <span>Cold Prospects</span>
              <Badge variant="secondary">{coldProspects?.count || 0}</Badge>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col h-full">
            <SearchHeader
              searchValue={prospectSearch}
              onSearchChange={setProspectSearch}
              placeholder="Search cold prospects..."
              onRefresh={refetchColdProspects}
              isLoading={coldProspectsLoading}
            />
            <div className="flex-1 overflow-y-auto">
              {coldProspectsLoading ? (
                <LoadingState />
              ) : (
                <ProspectsTable prospects={coldProspects?.results || []} />
              )}
            </div>
            {coldProspects && (
              <Pagination
                currentPage={coldProspects.current_page}
                totalPages={coldProspects.total_pages}
                onPageChange={setCurrentPage}
                isLoading={coldProspectsLoading}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Teleteam Sales Modal */}
      <Dialog open={teleteamSalesOpen} onOpenChange={setTeleteamSalesOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-emerald-600" />
              <span>Teleteam Sales</span>
              <Badge variant="default">{teleteamSales?.count || 0}</Badge>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col h-full">
            <SearchHeader
              searchValue={salesSearch}
              onSearchChange={setSalesSearch}
              placeholder="Search teleteam sales..."
              onRefresh={refetchTeleteamSales}
              isLoading={teleteamSalesLoading}
            />
            <div className="flex-1 overflow-y-auto">
              {teleteamSalesLoading ? (
                <LoadingState />
              ) : (
                <SalesTable sales={teleteamSales?.results || []} />
              )}
            </div>
            {teleteamSales && (
              <Pagination
                currentPage={teleteamSales.current_page}
                totalPages={teleteamSales.total_pages}
                onPageChange={setCurrentPage}
                isLoading={teleteamSalesLoading}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Unallocated Leads Modal */}
      <Dialog open={unallocatedLeadsOpen} onOpenChange={setUnallocatedLeadsOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <span>Unallocated Leads</span>
              <Badge variant="secondary">{unallocatedLeads?.count || 0}</Badge>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col h-full">
            <SearchHeader
              searchValue={leadsSearch}
              onSearchChange={setLeadsSearch}
              placeholder="Search unallocated leads..."
              onRefresh={refetchUnallocatedLeads}
              isLoading={unallocatedLeadsLoading}
            />
            <div className="flex-1 overflow-y-auto">
              {unallocatedLeadsLoading ? (
                <LoadingState />
              ) : (
                <LeadsTable leads={unallocatedLeads?.results || []} />
              )}
            </div>
            {unallocatedLeads && (
              <Pagination
                currentPage={unallocatedLeads.current_page}
                totalPages={unallocatedLeads.total_pages}
                onPageChange={setCurrentPage}
                isLoading={unallocatedLeadsLoading}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TeleteamModals;