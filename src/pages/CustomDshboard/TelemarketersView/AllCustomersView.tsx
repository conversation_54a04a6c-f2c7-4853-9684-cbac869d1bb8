import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1"; 


interface Customers {
  region: string;
  target: string;
  mibachieved: string;
  achievement: string;
}

interface CustomerTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function CustomerTableModal({ open, onOpenChange }: CustomerTableModalProps) {
 
  const data: Customers[] = [
    {
      
      region: "Global",
      target: "7000000",
      mibachieved: "5000000",
      achievement: "71.43%",
    },
    {
      
      
      region: "East Africa",
      target: "8000000",
      mibachieved: "6000000",
      achievement: "75%",
    },
    {
     
      region: "West Africa",
      
      target: "5000000",
      mibachieved: "4500000",
      achievement: "90%",
    },
  ];

 
  const columns: ColumnDef<Customers>[] = [
    
    {
      accessorKey: "region",
      header: "Region",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    
    {
      accessorKey: "target",
      header: "Target",
      cell: (info) => {
        const value = info.getValue() as string;
        return `kes${parseFloat(value).toLocaleString()}`;
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "mibachieved",
      header: "MIB Achieved",
      cell: (info) => {
        const value = info.getValue() as string;
        return `kes${parseFloat(value).toLocaleString()}`;
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "achievement",
      header: "Achievement",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger className="">
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit row ${row.original.region}`);
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Delete row ${row.original.region}`);
              }}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableColumnFilter: false,
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="HR Reports"
      description="View and manage employee performance records"
      className="max-w-4xl"
      size="full"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
        <DataTable<Customers>
          data={data}
          columns={columns}
          title="Office Reprts"
          enableExportToExcel={true}
          enablePrintPdf={true}
          enableColumnFilters={true}
          enablePagination={true}
          enableSorting={true}
         
          enableToolbar={true}
          containerClassName="max-w-full"
          tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300 shadow-md rounded-lg overflow-hidden"
          tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
          tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
          tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
          tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          tBodyCellsClassName="px-4 py-2"
        />
      </div>
    </BaseModal>
  );
}