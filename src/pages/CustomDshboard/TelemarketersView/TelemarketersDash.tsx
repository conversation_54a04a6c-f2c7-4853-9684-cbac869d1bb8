import { useState, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Phone,
  DollarSign,
  Target,
  TrendingUp,
  BarChart3,
  FileText,
  ArrowUpRight,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  UserCheck,
  Flame,
  Thermometer,
  Snowflake,
  Calendar,
  Filter,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import TeleteamModals from "./TeleteamModals";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// Import the telemarketing API hooks
import {
  useGetTeleteamDashboardQuery,
  useGetTeleteamDailyQuery,
  useGetTeleteamDailyDetailedQuery,
  useGetTeleteamSalesPerformanceQuery,
} from "@/redux/slices/telemarketingApiSlice";
import UnallocatedLeadsReport from "@/pages/Reports/TelemarketingReports/UnallocatedLeadsReport";
import TelemarketingSalesReport from "@/pages/Reports/TelemarketingReports/TelemarketingSalesReport";
import TelemarketingCustomersReport from "@/pages/Reports/TelemarketingReports/TelemarketingCustomersReport";

const reportTemplates = [
  {
    id: 1,
    name: "Unallocated Leads Report",
    description: "Unallocated leads that need assignment",
    icon: TrendingUp,
    color: "from-green-400 to-emerald-600",
    metrics: ["Phone Number", "Name"],
  },
  {
    id: 2,
    name: "Telemarketing Sales Report",
    description: "Sales for telemarketing team",
    icon: UserCheck,
    color: "from-blue-400 to-cyan-600",
    metrics: ["Active", "Dropped"],
  },
  {
    id: 3,
    name: "Telemarketing Customers Report",
    description: "Telemarketing customer details",
    icon: Target,
    color: "from-purple-400 to-pink-600",
    metrics: ["Phone Number", "Name"],
  },
  // {
  //   id: 4,
  //   name: 'Sales Performance Report',
  //   description: 'Teleteam sales performance and revenue analysis',
  //   icon: HandCoins,
  //   color: 'from-orange-400 to-red-600',
  //   metrics: ['Total Revenue', 'Active Sales', 'Completed Sales', 'Performance Trends']
  // }
];

const TelemarketersDash = () => {
  // Modal states
  const [telemarketingCustomersOpen, setTelemarketingCustomersOpen] =
    useState(false);
  const [hotProspectsOpen, setHotProspectsOpen] = useState(false);
  const [warmProspectsOpen, setWarmProspectsOpen] = useState(false);
  const [coldProspectsOpen, setColdProspectsOpen] = useState(false);
  const [teleteamSalesOpen, setTeleteamSalesOpen] = useState(false);
  const [unallocatedLeadsOpen, setUnallocatedLeadsOpen] = useState(false);

  // Report filter states
  const [departmentId, setDepartmentId] = useState<string>("21"); // 21 for Telemarketing
  const [startDate, setStartDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [departmentMemberId, setDepartmentMemberId] = useState<string>("ALL");
  const [leadSourceId, setLeadSourceId] = useState<string>("0");
  const [activeReportTab, setActiveReportTab] = useState<string>("daily");
  const [mainTabView, setMainTabView] = useState<string>("performance"); // "performance" or "generator"
  const [dateValidationError, setDateValidationError] = useState<string>("");

  // API call for teleteam dashboard stats
  const {
    data: teleteamStats,
    isLoading: teleteamStatsLoading,
    error: teleteamStatsError,
    refetch: refetchTeleteamStats,
  } = useGetTeleteamDashboardQuery({});

  // API calls for teleteam reports
  const {
    data: teleteamDailyData,
    isLoading: teleteamDailyLoading,
    error: teleteamDailyError,
    refetch: refetchTeleteamDaily,
  } = useGetTeleteamDailyQuery({
    DEPARTMENT_ID: departmentId,
    START_DATE: startDate,
    END_DATE: endDate,
    DEPARTMENT_MEMBER_ID: departmentMemberId,
    LEAD_SOURCE_ID: leadSourceId,
    page: 1,
    page_size: 50,
  });

  const {
    data: teleteamDailyDetailedData,
    isLoading: teleteamDailyDetailedLoading,
    error: teleteamDailyDetailedError,
    refetch: refetchTeleteamDailyDetailed,
  } = useGetTeleteamDailyDetailedQuery({
    DEPARTMENT_ID: departmentId,
    START_DATE: startDate,
    END_DATE: endDate,
    DEPARTMENT_MEMBER_ID: departmentMemberId,
    LEAD_SOURCE_ID: leadSourceId,
  });

  const {
    data: teleteamSalesPerformanceData,
    isLoading: teleteamSalesPerformanceLoading,
    error: teleteamSalesPerformanceError,
    refetch: refetchTeleteamSalesPerformance,
  } = useGetTeleteamSalesPerformanceQuery({
    DEPARTMENT_ID: departmentId,
    START_DATE: startDate,
    END_DATE: endDate,
    DEPARTMENT_MEMBER_ID: departmentMemberId,
    LEAD_SOURCE_ID: leadSourceId,
    page: 1,
    page_size: 50,
  });

  console.log("Teleteam Stats:", teleteamStats);
  console.log("Teleteam Daily:", teleteamDailyData);
  console.log("Teleteam Daily Detailed:", teleteamDailyDetailedData);
  console.log("Teleteam Sales Performance:", teleteamSalesPerformanceData);

  // Format currency
  const formatCurrency = (amount: any) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Create metrics array using the teleteam stats data
  const metrics = [
    {
      title: "Telemarketing Customers",
      value:
        teleteamStats?.customers?.telemarketing_customers?.toLocaleString() ||
        "0",
      icon: Phone,
      iconBg: "bg-slate-100",
      iconColor: "text-slate-600",
      cardBg: "bg-gradient-to-br from-slate-50 to-slate-100/50",
      borderColor: "border-slate-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
          ? "Error"
          : "Active TM",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setTelemarketingCustomersOpen(true),
      modal: true,
    },
    {
      title: "Hot Prospects",
      value: teleteamStats?.prospects?.hot_prospects?.toLocaleString() || "0",
      icon: Flame,
      iconBg: "bg-rose-100",
      iconColor: "text-rose-600",
      cardBg: "bg-gradient-to-br from-rose-50 to-rose-100/50",
      borderColor: "border-rose-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
          ? "Error"
          : "High Priority",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setHotProspectsOpen(true),
      modal: true,
    },
    {
      title: "Warm Prospects",
      value: teleteamStats?.prospects?.warm_prospects?.toLocaleString() || "0",
      icon: Thermometer,
      iconBg: "bg-amber-100",
      iconColor: "text-amber-600",
      cardBg: "bg-gradient-to-br from-amber-50 to-amber-100/50",
      borderColor: "border-amber-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
          ? "Error"
          : "Medium Priority",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setWarmProspectsOpen(true),
      modal: true,
    },
    {
      title: "Cold Prospects",
      value: teleteamStats?.prospects?.cold_prospects?.toLocaleString() || "0",
      icon: Snowflake,
      iconBg: "bg-sky-100",
      iconColor: "text-sky-600",
      cardBg: "bg-gradient-to-br from-sky-50 to-sky-100/50",
      borderColor: "border-sky-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
          ? "Error"
          : "Low Priority",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setColdProspectsOpen(true),
      modal: true,
    },
    {
      title: "Teleteam Sales",
      value: teleteamStats?.sales?.teleteam_sales?.toLocaleString() || "0",
      icon: DollarSign,
      iconBg: "bg-emerald-100",
      iconColor: "text-emerald-600",
      cardBg: "bg-gradient-to-br from-emerald-50 to-emerald-100/50",
      borderColor: "border-emerald-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
          ? "Error"
          : "TM Sales",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setTeleteamSalesOpen(true),
      modal: true,
    },
    {
      title: "Unallocated Leads",
      value: teleteamStats?.leads?.unallocated_leads?.toLocaleString() || "0",
      icon: AlertTriangle,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100/50",
      borderColor: "border-orange-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
          ? "Error"
          : "Unallocated",
      changeType: teleteamStatsError ? "negative" : "neutral",
      action: () => setUnallocatedLeadsOpen(true),
      modal: true,
    },
  ];

  const handleMetricClick = (metric: any) => {
    if (metric.action) {
      metric.action();
    }
  };

  // Debug logging
  useEffect(() => {
    console.log("Teleteam Stats:", teleteamStats);
  }, [teleteamStats]);

  // Date validation
  useEffect(() => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start > end) {
        setDateValidationError("Start date cannot be later than end date. Please adjust your date range.");
      } else {
        setDateValidationError("");
      }
    }
  }, [startDate, endDate]);

  // Check for API errors related to date validation
  useEffect(() => {
    const checkApiErrors = () => {
      const errors = [
        teleteamDailyError,
        teleteamDailyDetailedError,
        teleteamSalesPerformanceError
      ];

      for (const error of errors) {
        if (error && 'data' in error) {
          const errorData = error.data as any;
          if (errorData?.error === "START_DATE cannot be later than END_DATE.") {
            setDateValidationError("Start date cannot be later than end date. Please adjust your date range.");
            return;
          }
        }
      }
    };

    checkApiErrors();
  }, [teleteamDailyError, teleteamDailyDetailedError, teleteamSalesPerformanceError]);

  //report modals
  const [isUnallocatedLeadsModalOpen, setIsUnallocatedLeadsModalOpen] =
    useState<boolean>(false);
  const [isTelemarketingSalesModalOpen, setIsTelemarketingSalesModalOpen] =
    useState<boolean>(false);
  const [
    isTelemarketingCustomersModalOpen,
    setIsTelemarketingCustomersModalOpen,
  ] = useState<boolean>(false);

  const whichModal = (id: number) => {
    switch (id) {
      case 0:
        return setIsUnallocatedLeadsModalOpen(true);
      case 1:
        return setIsTelemarketingSalesModalOpen(true);
      case 2:
        return setIsTelemarketingCustomersModalOpen(true);
      default:
        return null;
    }
  };

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Compact Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Telemarketing Dashboard
            </h1>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-600">
                Telemarketing team overview and insights
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchTeleteamStats()}
                disabled={teleteamStatsLoading}
              >
                {teleteamStatsLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Comprehensive Metrics Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {metrics.map((metric, index) => (
              <Card
                key={metric.title}
                className={`${metric.borderColor} border shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-[1.02] ${metric.cardBg}`}
                onClick={() => handleMetricClick(metric)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className={`w-9 h-9 rounded-lg flex items-center justify-center ${metric.iconBg}`}
                    >
                      <metric.icon className={`w-4 h-4 ${metric.iconColor}`} />
                    </div>
                    {metric.modal && (
                      <ArrowUpRight className="w-3 h-3 text-gray-400" />
                    )}
                  </div>

                  <div className="space-y-0.5">
                    <p className="text-xs font-medium text-gray-600">
                      {metric.title}
                    </p>
                    <p className="text-xl font-bold text-gray-900">
                      {metric.value}
                    </p>
                    <div className="flex items-center text-[10px]">
                      {metric.changeType === "positive" &&
                        !teleteamStatsLoading &&
                        !teleteamStatsError && (
                          <TrendingUp className="w-2.5 h-2.5 text-emerald-600 mr-1" />
                        )}
                      <span
                        className={`font-medium ${metric.changeType === "positive"
                          ? "text-emerald-600"
                          : metric.changeType === "negative"
                            ? "text-rose-600"
                            : "text-gray-500"
                          }`}
                      >
                        {metric.change}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Loading State */}
          {teleteamStatsLoading && (
            <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
              <div className="flex items-center">
                <RefreshCw className="w-4 h-4 text-slate-600 mr-2 animate-spin" />
                <span className="text-slate-700 text-sm">
                  Loading telemarketing dashboard statistics...
                </span>
              </div>
            </div>
          )}

          {/* Error State */}
          {teleteamStatsError && (
            <div className="bg-rose-50 border border-rose-200 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <span className="text-rose-700 text-sm">
                  Failed to load telemarketing dashboard statistics. Please try
                  again.
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchTeleteamStats()}
                  className="text-rose-600 border-rose-300 hover:bg-rose-50"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Retry
                </Button>
              </div>
            </div>
          )}

          {/* Success State with Data Summary */}
          {!teleteamStatsLoading && !teleteamStatsError && teleteamStats && (
            <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-emerald-600 rounded-full mr-2 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-emerald-700 text-sm">
                  Telemarketing dashboard data loaded successfully - Last
                  updated: {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}

          {/* Key Performance Indicators Section */}
          {!teleteamStatsLoading && !teleteamStatsError && teleteamStats && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Card className="bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-slate-600 text-xs font-medium">
                        Telemarketing Customer %
                      </p>
                      <p className="text-2xl font-bold mt-1 text-slate-900">
                        {teleteamStats.customers?.telemarketing_customers &&
                          teleteamStats.customers?.total_optiven_customers
                          ? Math.round(
                            (teleteamStats.customers.telemarketing_customers /
                              teleteamStats.customers
                                .total_optiven_customers) *
                            100
                          )
                          : 0}
                        %
                      </p>
                    </div>
                    <Users className="w-7 h-7 text-slate-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-violet-50 to-violet-100 border-violet-200">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-violet-600 text-xs font-medium">
                        Prospect Allocation
                      </p>
                      <p className="text-2xl font-bold mt-1 text-violet-900">
                        {teleteamStats.prospects?.allocated_prospects || 0}/
                        {teleteamStats.prospects?.total_prospects || 0}
                      </p>
                    </div>
                    <Target className="w-7 h-7 text-violet-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-emerald-600 text-xs font-medium">Sales Completion</p>
                      <p className="text-2xl font-bold mt-1 text-emerald-900">
                        {teleteamStats.sales?.completed_sales &&
                          teleteamStats.sales?.total_sales
                          ? Math.round(
                            (teleteamStats.sales.completed_sales /
                              teleteamStats.sales.total_sales) *
                            100
                          )
                          : 0}
                        %
                      </p>
                    </div>
                    <CheckCircle className="w-7 h-7 text-emerald-400" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Reports Section with Tabs */}
          <Card className="border-0 shadow-sm bg-white">
            <div className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
              <div className="flex items-center justify-between p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-9 h-9 bg-slate-200 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-4 h-4 text-slate-700" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-slate-900">
                      Reports & Analytics
                    </h2>
                    <p className="text-slate-600 text-xs mt-0.5">
                      Team performance insights and report generation
                    </p>
                  </div>
                </div>
                {mainTabView === "performance" && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      refetchTeleteamDaily();
                      refetchTeleteamDailyDetailed();
                      refetchTeleteamSalesPerformance();
                    }}
                    className="bg-white border-slate-300 text-slate-700 hover:bg-slate-50"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Refresh
                  </Button>
                )}
              </div>

              {/* Main Tabs */}
              <div className="flex space-x-1 px-4">
                <button
                  onClick={() => setMainTabView("performance")}
                  className={`px-4 py-2 text-sm font-medium transition-colors ${mainTabView === "performance"
                    ? "border-b-2 border-slate-700 text-slate-900"
                    : "text-slate-600 hover:text-slate-900"
                    }`}
                >
                  Team Performance
                </button>
                <button
                  onClick={() => setMainTabView("generator")}
                  className={`px-4 py-2 text-sm font-medium transition-colors ${mainTabView === "generator"
                    ? "border-b-2 border-slate-700 text-slate-900"
                    : "text-slate-600 hover:text-slate-900"
                    }`}
                >
                  Report Generator
                </button>
              </div>
            </div>

            <div className="p-4">
              {/* Team Performance Tab Content */}
              {mainTabView === "performance" && (
                <>
                  {/* Filters Section */}
                  <Card className="bg-slate-50 border-slate-200 mb-4">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-4 h-4 text-gray-600" />
                        <h3 className="text-sm font-semibold text-gray-700">
                          Report Filters
                        </h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="department" className="text-xs">
                            Department
                          </Label>
                          <Select
                            value={departmentId}
                            onValueChange={setDepartmentId}
                          >
                            <SelectTrigger id="department" className="h-9">
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="21">Telemarketing</SelectItem>
                              <SelectItem value="9">Customer Service</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="startDate" className="text-xs">
                            Start Date
                          </Label>
                          <Input
                            id="startDate"
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                            className="h-9"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="endDate" className="text-xs">
                            End Date
                          </Label>
                          <Input
                            id="endDate"
                            type="date"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                            className="h-9"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="memberId" className="text-xs">
                            Member ID
                          </Label>
                          <Input
                            id="memberId"
                            type="text"
                            placeholder="ALL or specific ID"
                            value={departmentMemberId}
                            onChange={(e) => setDepartmentMemberId(e.target.value)}
                            className="h-9"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="leadSource" className="text-xs">
                            Lead Source ID
                          </Label>
                          <Input
                            id="leadSource"
                            type="text"
                            placeholder="0 for all"
                            value={leadSourceId}
                            onChange={(e) => setLeadSourceId(e.target.value)}
                            className="h-9"
                          />
                        </div>
                      </div>

                      {/* Date Validation Error Message */}
                      {dateValidationError && (
                        <div className="mt-4 bg-rose-50 border border-rose-200 rounded-lg p-3 flex items-start space-x-2">
                          <AlertTriangle className="w-4 h-4 text-rose-600 mt-0.5 flex-shrink-0" />
                          <div className="flex-1">
                            <p className="text-xs font-medium text-rose-700">
                              {dateValidationError}
                            </p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Report Tabs */}
                  <div className="space-y-3">
                    <div className="flex space-x-1 border-b border-slate-200">
                      <button
                        onClick={() => setActiveReportTab("daily")}
                        className={`px-3 py-2 text-xs font-medium transition-colors ${activeReportTab === "daily"
                          ? "border-b-2 border-slate-700 text-slate-900"
                          : "text-slate-600 hover:text-slate-900"
                          }`}
                      >
                        Daily Report
                      </button>
                      <button
                        onClick={() => setActiveReportTab("detailed")}
                        className={`px-3 py-2 text-xs font-medium transition-colors ${activeReportTab === "detailed"
                          ? "border-b-2 border-slate-700 text-slate-900"
                          : "text-slate-600 hover:text-slate-900"
                          }`}
                      >
                        Detailed Report
                      </button>
                      <button
                        onClick={() => setActiveReportTab("performance")}
                        className={`px-3 py-2 text-xs font-medium transition-colors ${activeReportTab === "performance"
                          ? "border-b-2 border-slate-700 text-slate-900"
                          : "text-slate-600 hover:text-slate-900"
                          }`}
                      >
                        Sales Performance
                      </button>
                    </div>

                    {/* Daily Report Tab */}
                    {activeReportTab === "daily" && (
                      <div className="space-y-4">
                        {teleteamDailyLoading ? (
                          <div className="flex items-center justify-center p-8">
                            <RefreshCw className="w-6 h-6 animate-spin text-slate-600" />
                            <span className="ml-2 text-slate-600">
                              Loading daily report...
                            </span>
                          </div>
                        ) : teleteamDailyError ? (
                          <div className="bg-rose-50 border border-rose-200 rounded-lg p-4 text-center">
                            <p className="text-rose-700">
                              Failed to load daily report. Please try again.
                            </p>
                          </div>
                        ) : teleteamDailyData?.data?.length > 0 ? (
                          <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                              <thead className="bg-gray-100">
                                <tr>
                                  <th className="px-4 py-2 text-left">Member ID</th>
                                  <th className="px-4 py-2 text-left">Name</th>
                                  <th className="px-4 py-2 text-left">
                                    Prospects Handled
                                  </th>
                                  <th className="px-4 py-2 text-left">
                                    Date Range
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {teleteamDailyData.data.map((item: any, idx: number) => (
                                  <tr
                                    key={idx}
                                    className="border-b border-gray-200 hover:bg-gray-50"
                                  >
                                    <td className="px-4 py-3">
                                      {item.department_member_id}
                                    </td>
                                    <td className="px-4 py-3">
                                      {item.department_member_name}
                                    </td>
                                    <td className="px-4 py-3">
                                      <Badge variant="secondary">
                                        {item.prospects_handled}
                                      </Badge>
                                    </td>
                                    <td className="px-4 py-3 text-xs text-gray-600">
                                      {item.start_date} to {item.end_date}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                            {teleteamDailyData.pagination && (
                              <div className="mt-4 text-sm text-gray-600 text-center">
                                Showing {teleteamDailyData.pagination.total_count}{" "}
                                records
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center p-8 text-gray-500">
                            No data available for the selected filters.
                          </div>
                        )}
                      </div>
                    )}

                    {/* Detailed Report Tab */}
                    {activeReportTab === "detailed" && (
                      <div className="space-y-4">
                        {teleteamDailyDetailedLoading ? (
                          <div className="flex items-center justify-center p-8">
                            <RefreshCw className="w-6 h-6 animate-spin text-slate-600" />
                            <span className="ml-2 text-slate-600">
                              Loading detailed report...
                            </span>
                          </div>
                        ) : teleteamDailyDetailedError ? (
                          <div className="bg-rose-50 border border-rose-200 rounded-lg p-4 text-center">
                            <p className="text-rose-700">
                              Failed to load detailed report. Please try again.
                            </p>
                          </div>
                        ) : teleteamDailyDetailedData?.data?.length > 0 ? (
                          <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                              <thead className="bg-gray-100">
                                <tr>
                                  <th className="px-4 py-2 text-left">Member ID</th>
                                  <th className="px-4 py-2 text-left">Name</th>
                                  <th className="px-4 py-2 text-left">Office</th>
                                  <th className="px-4 py-2 text-left">
                                    Lead Source
                                  </th>
                                  <th className="px-4 py-2 text-left">Hot</th>
                                  <th className="px-4 py-2 text-left">Warm</th>
                                  <th className="px-4 py-2 text-left">Cold</th>
                                  <th className="px-4 py-2 text-left">Active</th>
                                  <th className="px-4 py-2 text-left">Dormant</th>
                                  <th className="px-4 py-2 text-left">Total</th>
                                </tr>
                              </thead>
                              <tbody>
                                {teleteamDailyDetailedData.data.map(
                                  (item: any, idx: number) => (
                                    <tr
                                      key={idx}
                                      className="border-b border-gray-200 hover:bg-gray-50"
                                    >
                                      <td className="px-4 py-3">
                                        {item.department_member_id}
                                      </td>
                                      <td className="px-4 py-3">
                                        {item.department_member_name}
                                      </td>
                                      <td className="px-4 py-3">
                                        {item.member_office}
                                      </td>
                                      <td className="px-4 py-3 text-xs">
                                        {item.lead_source_name}
                                      </td>
                                      <td className="px-4 py-3">
                                        <Badge className="bg-rose-50 text-rose-700 border border-rose-200">
                                          {item.hot_prospects}
                                        </Badge>
                                      </td>
                                      <td className="px-4 py-3">
                                        <Badge className="bg-amber-50 text-amber-700 border border-amber-200">
                                          {item.warm_prospects}
                                        </Badge>
                                      </td>
                                      <td className="px-4 py-3">
                                        <Badge className="bg-sky-50 text-sky-700 border border-sky-200">
                                          {item.cold_prospects}
                                        </Badge>
                                      </td>
                                      <td className="px-4 py-3">
                                        <Badge className="bg-emerald-50 text-emerald-700 border border-emerald-200">
                                          {item.active_prospects}
                                        </Badge>
                                      </td>
                                      <td className="px-4 py-3">
                                        <Badge className="bg-slate-50 text-slate-600 border border-slate-200">
                                          {item.dormant_prospects}
                                        </Badge>
                                      </td>
                                      <td className="px-4 py-3">
                                        <Badge variant="secondary">
                                          {item.prospects_handled}
                                        </Badge>
                                      </td>
                                    </tr>
                                  )
                                )}
                              </tbody>
                            </table>
                            {teleteamDailyDetailedData.total_records && (
                              <div className="mt-4 text-sm text-gray-600 text-center">
                                Showing {teleteamDailyDetailedData.total_records}{" "}
                                records
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center p-8 text-gray-500">
                            No data available for the selected filters.
                          </div>
                        )}
                      </div>
                    )}

                    {/* Sales Performance Tab */}
                    {activeReportTab === "performance" && (
                      <div className="space-y-4">
                        {teleteamSalesPerformanceLoading ? (
                          <div className="flex items-center justify-center p-8">
                            <RefreshCw className="w-6 h-6 animate-spin text-indigo-600" />
                            <span className="ml-2 text-gray-600">
                              Loading sales performance report...
                            </span>
                          </div>
                        ) : teleteamSalesPerformanceError ? (
                          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                            <p className="text-red-600">
                              Failed to load sales performance report. Please try
                              again.
                            </p>
                          </div>
                        ) : teleteamSalesPerformanceData?.data?.length > 0 ? (
                          <>
                            {/* Summary Cards */}
                            {teleteamSalesPerformanceData.summary && (
                              <div className="grid grid-cols-1 md:grid-cols-5 gap-3 mb-4">
                                <Card className="bg-slate-50 border-slate-200">
                                  <CardContent className="p-2.5">
                                    <p className="text-xs text-slate-600 font-medium">
                                      Total Members
                                    </p>
                                    <p className="text-lg font-bold text-slate-900">
                                      {teleteamSalesPerformanceData.summary.total_members}
                                    </p>
                                  </CardContent>
                                </Card>
                                <Card className="bg-violet-50 border-violet-200">
                                  <CardContent className="p-2.5">
                                    <p className="text-xs text-violet-600 font-medium">
                                      Prospects Handled
                                    </p>
                                    <p className="text-lg font-bold text-violet-900">
                                      {teleteamSalesPerformanceData.summary.total_prospects_handled}
                                    </p>
                                  </CardContent>
                                </Card>
                                <Card className="bg-emerald-50 border-emerald-200">
                                  <CardContent className="p-2.5">
                                    <p className="text-xs text-emerald-600 font-medium">
                                      Sales Generated
                                    </p>
                                    <p className="text-lg font-bold text-emerald-900">
                                      {teleteamSalesPerformanceData.summary.total_sales_generated}
                                    </p>
                                  </CardContent>
                                </Card>
                                <Card className="bg-amber-50 border-amber-200">
                                  <CardContent className="p-2.5">
                                    <p className="text-xs text-amber-600 font-medium">
                                      Total Sales Value
                                    </p>
                                    <p className="text-lg font-bold text-amber-900">
                                      {formatCurrency(
                                        teleteamSalesPerformanceData.summary
                                          .total_sales_value
                                      )}
                                    </p>
                                  </CardContent>
                                </Card>
                                <Card className="bg-blue-50 border-blue-200">
                                  <CardContent className="p-2.5">
                                    <p className="text-xs text-blue-600 font-medium">
                                      Conversion Rate
                                    </p>
                                    <p className="text-lg font-bold text-blue-900">
                                      {teleteamSalesPerformanceData.summary.overall_conversion_rate}
                                      %
                                    </p>
                                  </CardContent>
                                </Card>
                              </div>
                            )}

                            {/* Performance Table */}
                            <div className="overflow-x-auto">
                              <table className="w-full text-sm">
                                <thead className="bg-gray-100">
                                  <tr>
                                    <th className="px-4 py-2 text-left">
                                      Member ID
                                    </th>
                                    <th className="px-4 py-2 text-left">Name</th>
                                    <th className="px-4 py-2 text-left">
                                      Prospects
                                    </th>
                                    <th className="px-4 py-2 text-left">Sales</th>
                                    <th className="px-4 py-2 text-left">
                                      Conversion %
                                    </th>
                                    <th className="px-4 py-2 text-left">
                                      Sales Value
                                    </th>
                                    <th className="px-4 py-2 text-left">Paid</th>
                                    <th className="px-4 py-2 text-left">Balance</th>
                                    <th className="px-4 py-2 text-left">
                                      Avg Sale
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {teleteamSalesPerformanceData.data.map(
                                    (item: any, idx: number) => (
                                      <tr
                                        key={idx}
                                        className="border-b border-gray-200 hover:bg-gray-50"
                                      >
                                        <td className="px-4 py-3">
                                          {item.department_member_id}
                                        </td>
                                        <td className="px-4 py-3">
                                          {item.department_member_name}
                                        </td>
                                        <td className="px-4 py-3">
                                          <Badge variant="secondary">
                                            {item.prospects_handled}
                                          </Badge>
                                        </td>
                                        <td className="px-4 py-3">
                                          <Badge className="bg-emerald-50 text-emerald-700 border border-emerald-200">
                                            {item.sales_generated}
                                          </Badge>
                                        </td>
                                        <td className="px-4 py-3">
                                          <Badge
                                            className={
                                              item.conversion_rate > 50
                                                ? "bg-emerald-50 text-emerald-700 border border-emerald-200"
                                                : item.conversion_rate > 25
                                                  ? "bg-amber-50 text-amber-700 border border-amber-200"
                                                  : "bg-rose-50 text-rose-700 border border-rose-200"
                                            }
                                          >
                                            {item.conversion_rate}%
                                          </Badge>
                                        </td>
                                        <td className="px-4 py-3 text-xs">
                                          {formatCurrency(item.total_sales_value)}
                                        </td>
                                        <td className="px-4 py-3 text-xs">
                                          {formatCurrency(item.total_paid)}
                                        </td>
                                        <td className="px-4 py-3 text-xs">
                                          {formatCurrency(item.balance_remaining)}
                                        </td>
                                        <td className="px-4 py-3 text-xs">
                                          {formatCurrency(item.average_sale_value)}
                                        </td>
                                      </tr>
                                    )
                                  )}
                                </tbody>
                              </table>
                              {teleteamSalesPerformanceData.pagination && (
                                <div className="mt-4 text-sm text-gray-600 text-center">
                                  Showing{" "}
                                  {
                                    teleteamSalesPerformanceData.pagination
                                      .total_count
                                  }{" "}
                                  records
                                </div>
                              )}
                            </div>
                          </>
                        ) : (
                          <div className="text-center p-8 text-gray-500">
                            No data available for the selected filters.
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Report Generator Tab Content */}
              {mainTabView === "generator" && (
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {reportTemplates.map((template, index) => (
                      <Card
                        key={template.id}
                        className="border border-slate-200 hover:border-slate-300 hover:shadow-sm transition-all duration-200 group cursor-pointer bg-white"
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-9 h-9 bg-gradient-to-br ${template.color} opacity-80 rounded-lg flex items-center justify-center`}
                            >
                              <template.icon className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <CardTitle className="text-sm font-semibold text-slate-900">
                                {template.name}
                              </CardTitle>
                              <CardDescription className="text-xs text-slate-600">
                                {template.description}
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex flex-wrap gap-1">
                            {template.metrics.slice(0, 3).map((metric) => (
                              <Badge
                                key={metric}
                                variant="secondary"
                                className="text-xs bg-slate-100 text-slate-700"
                              >
                                {metric}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                        <CardFooter className="pt-2">
                          <Button
                            size="sm"
                            className="w-full bg-slate-700 hover:bg-slate-800 text-white"
                            onClick={() => whichModal(index)}
                          >
                            <FileText className="w-3 h-3 mr-1" />
                            Generate Report
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Modal Components for Detailed Views */}

          <TeleteamModals
            // Modal state props
            telemarketingCustomersOpen={telemarketingCustomersOpen}
            setTelemarketingCustomersOpen={setTelemarketingCustomersOpen}
            hotProspectsOpen={hotProspectsOpen}
            setHotProspectsOpen={setHotProspectsOpen}
            warmProspectsOpen={warmProspectsOpen}
            setWarmProspectsOpen={setWarmProspectsOpen}
            coldProspectsOpen={coldProspectsOpen}
            setColdProspectsOpen={setColdProspectsOpen}
            teleteamSalesOpen={teleteamSalesOpen}
            setTeleteamSalesOpen={setTeleteamSalesOpen}
            unallocatedLeadsOpen={unallocatedLeadsOpen}
            setUnallocatedLeadsOpen={setUnallocatedLeadsOpen}
          />

          <UnallocatedLeadsReport
            isModalOpen={isUnallocatedLeadsModalOpen}
            setIsModalOpen={setIsUnallocatedLeadsModalOpen}
          />
          <TelemarketingSalesReport
            isModalOpen={isTelemarketingSalesModalOpen}
            setIsModalOpen={setIsTelemarketingSalesModalOpen}
          />
          <TelemarketingCustomersReport
            isModalOpen={isTelemarketingCustomersModalOpen}
            setIsModalOpen={setIsTelemarketingCustomersModalOpen}
          />
        </div>
      </div>
    </Screen>
  );
};

export default TelemarketersDash;
