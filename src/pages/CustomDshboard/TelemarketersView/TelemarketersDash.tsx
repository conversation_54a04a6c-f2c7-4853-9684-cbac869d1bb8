import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON><PERSON>,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  Phone,
  DollarSign,
  Target,
  TrendingUp,
  BarChart3,
  FileText,
  Download,
  ArrowUpRight,
  Zap,
  Star,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Eye,
  UserCheck,
  HandCoins,
  Flame,
  Thermometer,
  Snowflake,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import TeleteamModals from "./TeleteamModals";

// Import the telemarketing API hooks
import { useGetTeleteamDashboardQuery } from "@/redux/slices/telemarketingApiSlice";
import UnallocatedLeadsReport from "@/pages/Reports/TelemarketingReports/UnallocatedLeadsReport";
import TelemarketingSalesReport from "@/pages/Reports/TelemarketingReports/TelemarketingSalesReport";
import TelemarketingCustomersReport from "@/pages/Reports/TelemarketingReports/TelemarketingCustomersReport";

const reportTemplates = [
  {
    id: 1,
    name: "Unallocated Leads Report",
    description: "Unallocated leads that need assignment",
    icon: TrendingUp,
    color: "from-green-400 to-emerald-600",
    metrics: ["Phone Number", "Name"],
  },
  {
    id: 2,
    name: "Telemarketing Sales Report",
    description: "Sales for telemarketing team",
    icon: UserCheck,
    color: "from-blue-400 to-cyan-600",
    metrics: ["Active", "Dropped"],
  },
  {
    id: 3,
    name: "Telemarketing Customers Report",
    description: "Telemarketing customer details",
    icon: Target,
    color: "from-purple-400 to-pink-600",
    metrics: ["Phone Number", "Name"],
  },
  // {
  //   id: 4,
  //   name: 'Sales Performance Report',
  //   description: 'Teleteam sales performance and revenue analysis',
  //   icon: HandCoins,
  //   color: 'from-orange-400 to-red-600',
  //   metrics: ['Total Revenue', 'Active Sales', 'Completed Sales', 'Performance Trends']
  // }
];

const TelemarketersDash = () => {
  const [selectedReportType, setSelectedReportType] = useState("");
  const [selectedDateRange, setSelectedDateRange] = useState("");
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [reportName, setReportName] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Modal states
  const [telemarketingCustomersOpen, setTelemarketingCustomersOpen] =
    useState(false);
  const [hotProspectsOpen, setHotProspectsOpen] = useState(false);
  const [warmProspectsOpen, setWarmProspectsOpen] = useState(false);
  const [coldProspectsOpen, setColdProspectsOpen] = useState(false);
  const [teleteamSalesOpen, setTeleteamSalesOpen] = useState(false);
  const [unallocatedLeadsOpen, setUnallocatedLeadsOpen] = useState(false);

  // API call for teleteam dashboard stats
  const {
    data: teleteamStats,
    isLoading: teleteamStatsLoading,
    error: teleteamStatsError,
    refetch: refetchTeleteamStats,
  } = useGetTeleteamDashboardQuery({});

  console.log("Teleteam Stats:", teleteamStats);

  // Format currency
  const formatCurrency = (amount: any) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Create metrics array using the teleteam stats data
  const metrics = [
    {
      title: "Total Customers",
      value:
        teleteamStats?.customers?.total_optiven_customers?.toLocaleString() ||
        "0",
      icon: Users,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "All Customers",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () =>
        (window.location.href = "/customers/overview#all-customers"),
      modal: true,
    },
    {
      title: "Telemarketing Customers",
      value:
        teleteamStats?.customers?.telemarketing_customers?.toLocaleString() ||
        "0",
      icon: Phone,
      iconBg: "bg-indigo-100",
      iconColor: "text-indigo-600",
      cardBg: "bg-gradient-to-br from-indigo-50 to-indigo-100",
      borderColor: "border-indigo-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Active TM",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setTelemarketingCustomersOpen(true),
      modal: true,
    },
    {
      title: "Total Prospects",
      value: teleteamStats?.prospects?.total_prospects?.toLocaleString() || "0",
      icon: Target,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "All Prospects",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => (window.location.href = "/prospects"),
      modal: true,
    },
    {
      title: "Hot Prospects",
      value: teleteamStats?.prospects?.hot_prospects?.toLocaleString() || "0",
      icon: Flame,
      iconBg: "bg-red-100",
      iconColor: "text-red-600",
      cardBg: "bg-gradient-to-br from-red-50 to-red-100",
      borderColor: "border-red-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Hot Leads",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setHotProspectsOpen(true),
      modal: true,
    },
    {
      title: "Warm Prospects",
      value: teleteamStats?.prospects?.warm_prospects?.toLocaleString() || "0",
      icon: Thermometer,
      iconBg: "bg-yellow-100",
      iconColor: "text-yellow-600",
      cardBg: "bg-gradient-to-br from-yellow-50 to-yellow-100",
      borderColor: "border-yellow-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Warm Leads",
      changeType: teleteamStatsError ? "negative" : "neutral",
      action: () => setWarmProspectsOpen(true),
      modal: true,
    },
    {
      title: "Cold Prospects",
      value: teleteamStats?.prospects?.cold_prospects?.toLocaleString() || "0",
      icon: Snowflake,
      iconBg: "bg-cyan-100",
      iconColor: "text-cyan-600",
      cardBg: "bg-gradient-to-br from-cyan-50 to-cyan-100",
      borderColor: "border-cyan-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Cold Leads",
      changeType: teleteamStatsError ? "negative" : "neutral",
      action: () => setColdProspectsOpen(true),
      modal: true,
    },
    {
      title: "Total Sales",
      value: teleteamStats?.sales?.total_sales?.toLocaleString() || "0",
      icon: BarChart3,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "All Sales",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => (window.location.href = "/sales/overview#all-sales"),
      modal: true,
    },
    {
      title: "Teleteam Sales",
      value: teleteamStats?.sales?.teleteam_sales?.toLocaleString() || "0",
      icon: DollarSign,
      iconBg: "bg-emerald-100",
      iconColor: "text-emerald-600",
      cardBg: "bg-gradient-to-br from-emerald-50 to-emerald-100",
      borderColor: "border-emerald-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "TM Sales",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => setTeleteamSalesOpen(true),
      modal: true,
    },
    {
      title: "Active Sales",
      value: teleteamStats?.sales?.active_sales?.toLocaleString() || "0",
      icon: TrendingUp,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Active",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => (window.location.href = "/sales/overview#on-going-sales"),
      modal: true,
    },
    {
      title: "Completed Sales",
      value: teleteamStats?.sales?.completed_sales?.toLocaleString() || "0",
      icon: CheckCircle,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Completed",
      changeType: teleteamStatsError ? "negative" : "positive",
      action: () => (window.location.href = "/sales/overview#completed-sales"),
      modal: true,
    },
    {
      title: "Total Leads",
      value: teleteamStats?.leads?.total_leads?.toLocaleString() || "0",
      icon: Target,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "All Leads",
      changeType: teleteamStatsError ? "negative" : "neutral",
      action: () => (window.location.href = "/prospects"),
      modal: false,
    },
    {
      title: "Unallocated Leads",
      value: teleteamStats?.leads?.unallocated_leads?.toLocaleString() || "0",
      icon: AlertTriangle,
      iconBg: "bg-red-100",
      iconColor: "text-red-600",
      cardBg: "bg-gradient-to-br from-red-50 to-red-100",
      borderColor: "border-red-200",
      change: teleteamStatsLoading
        ? "Loading..."
        : teleteamStatsError
        ? "Error"
        : "Unallocated",
      changeType: teleteamStatsError ? "negative" : "neutral",
      action: () => setUnallocatedLeadsOpen(true),
      modal: true,
    },
  ];

  const handleMetricClick = (metric: any) => {
    if (metric.action) {
      metric.action();
    }
  };

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    // Simulate report generation
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsGenerating(false);
    // Reset form
    setSelectedReportType("");
    setSelectedDateRange("");
    setSelectedMetrics([]);
    setReportName("");
  };

  const toggleMetric = (metric: string) => {
    setSelectedMetrics((prev: string[]) =>
      prev.includes(metric)
        ? prev.filter((m: string) => m !== metric)
        : [...prev, metric]
    );
  };

  // Debug logging
  useEffect(() => {
    console.log("Teleteam Stats:", teleteamStats);
  }, [teleteamStats]);

  //report modals
  const [isUnallocatedLeadsModalOpen, setIsUnallocatedLeadsModalOpen] =
    useState<boolean>(false);
  const [isTelemarketingSalesModalOpen, setIsTelemarketingSalesModalOpen] =
    useState<boolean>(false);
  const [
    isTelemarketingCustomersModalOpen,
    setIsTelemarketingCustomersModalOpen,
  ] = useState<boolean>(false);

  const whichModal = (id: number) => {
    switch (id) {
      case 0:
        return setIsUnallocatedLeadsModalOpen(true);
      case 1:
        return setIsTelemarketingSalesModalOpen(true);
      case 2:
        return setIsTelemarketingCustomersModalOpen(true);
      default:
        return null;
    }
  };

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Compact Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Telemarketing Dashboard
            </h1>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-600">
                Telemarketing team overview and insights
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchTeleteamStats()}
                disabled={teleteamStatsLoading}
              >
                {teleteamStatsLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Comprehensive Metrics Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {metrics.map((metric, index) => (
              <Card
                key={metric.title}
                className={`${metric.borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${metric.cardBg}`}
                onClick={() => handleMetricClick(metric)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${metric.iconBg}`}
                    >
                      <metric.icon className={`w-4 h-4 ${metric.iconColor}`} />
                    </div>
                    {metric.modal && (
                      <ArrowUpRight className="w-3 h-3 text-gray-400" />
                    )}
                  </div>

                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-600">
                      {metric.title}
                    </p>
                    <p className="text-lg font-bold text-gray-900">
                      {metric.value}
                    </p>
                    <div className="flex items-center text-xs">
                      {metric.changeType === "positive" &&
                        !teleteamStatsLoading &&
                        !teleteamStatsError && (
                          <TrendingUp className="w-3 h-3 text-green-600 mr-1" />
                        )}
                      <span
                        className={`font-medium ${
                          metric.changeType === "positive"
                            ? "text-green-600"
                            : metric.changeType === "negative"
                            ? "text-red-600"
                            : "text-gray-600"
                        }`}
                      >
                        {metric.change}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Loading State */}
          {teleteamStatsLoading && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <RefreshCw className="w-4 h-4 text-blue-600 mr-2 animate-spin" />
                <span className="text-blue-800 text-sm">
                  Loading telemarketing dashboard statistics...
                </span>
              </div>
            </div>
          )}

          {/* Error State */}
          {teleteamStatsError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-red-800 text-sm">
                  Failed to load telemarketing dashboard statistics. Please try
                  again.
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchTeleteamStats()}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Retry
                </Button>
              </div>
            </div>
          )}

          {/* Success State with Data Summary */}
          {!teleteamStatsLoading && !teleteamStatsError && teleteamStats && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded-full mr-2 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-green-800 text-sm">
                  Telemarketing dashboard data loaded successfully - Last
                  updated: {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}

          {/* Key Performance Indicators Section */}
          {!teleteamStatsLoading && !teleteamStatsError && teleteamStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">
                        Telemarketing Customer Percentage
                      </p>
                      <p className="text-2xl font-bold">
                        {teleteamStats.customers?.telemarketing_customers &&
                        teleteamStats.customers?.total_optiven_customers
                          ? Math.round(
                              (teleteamStats.customers.telemarketing_customers /
                                teleteamStats.customers
                                  .total_optiven_customers) *
                                100
                            )
                          : 0}
                        %
                      </p>
                    </div>
                    <Users className="w-8 h-8 text-blue-200" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">
                        Prospect Allocation
                      </p>
                      <p className="text-2xl font-bold">
                        {teleteamStats.prospects?.allocated_prospects || 0}/
                        {teleteamStats.prospects?.total_prospects || 0}
                      </p>
                    </div>
                    <Target className="w-8 h-8 text-purple-200" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Sales Completion</p>
                      <p className="text-2xl font-bold">
                        {teleteamStats.sales?.completed_sales &&
                        teleteamStats.sales?.total_sales
                          ? Math.round(
                              (teleteamStats.sales.completed_sales /
                                teleteamStats.sales.total_sales) *
                                100
                            )
                          : 0}
                        %
                      </p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-200" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100 text-sm">Lead Conversion</p>
                      <p className="text-2xl font-bold">
                        {teleteamStats.leads?.converted_leads &&
                        teleteamStats.leads?.total_leads
                          ? Math.round(
                              (teleteamStats.leads.converted_leads /
                                teleteamStats.leads.total_leads) *
                                100
                            )
                          : 0}
                        %
                      </p>
                    </div>
                    <TrendingUp className="w-8 h-8 text-orange-200" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Dynamic Report Generation Section */}
          <Card className="border-0 shadow-lg bg-white">
            <div className="bg-primary p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-secondary">
                    Telemarketing Report Generator
                  </h2>
                  <p className="text-secondary mt-1">
                    Create comprehensive telemarketing performance reports
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4">
              <Tabs defaultValue="templates" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger
                    value="templates"
                    className="flex items-center space-x-2"
                  >
                    <Star className="w-4 h-4" />
                    <span>Generat Report</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="custom"
                    className="flex items-center space-x-2"
                  >
                    <Zap className="w-4 h-4" />
                    <span>Custom Report</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="templates" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {reportTemplates.map((template, index) => (
                      <Card
                        key={template.id}
                        className="border-2 border-gray-100 hover:border-indigo-200 transition-all duration-200 group cursor-pointer"
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-8 h-8 bg-gradient-to-br ${template.color} rounded-lg flex items-center justify-center`}
                            >
                              <template.icon className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <CardTitle className="text-base">
                                {template.name}
                              </CardTitle>
                              <CardDescription className="text-xs">
                                {template.description}
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex flex-wrap gap-1">
                            {template.metrics.slice(0, 3).map((metric) => (
                              <Badge
                                key={metric}
                                variant="secondary"
                                className="text-xs"
                              >
                                {metric}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                        <CardFooter className="pt-2">
                          <Button
                            size="sm"
                            className="w-full"
                            variant="outline"
                            onClick={() => whichModal(index)}
                          >
                            <FileText className="w-3 h-3 mr-1" />
                            Generate
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="custom" className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="reportName"
                          className="text-sm font-medium"
                        >
                          Report Name
                        </Label>
                        <Input
                          id="reportName"
                          placeholder="Enter report name..."
                          value={reportName}
                          onChange={(e) => setReportName(e.target.value)}
                          className="h-10"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Report Type
                          </Label>
                          <Select
                            value={selectedReportType}
                            onValueChange={setSelectedReportType}
                          >
                            <SelectTrigger className="h-10">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="telemarketing">
                                Telemarketing Performance
                              </SelectItem>
                              <SelectItem value="prospects">
                                Prospect Analysis
                              </SelectItem>
                              <SelectItem value="customers">
                                Customer Acquisition
                              </SelectItem>
                              <SelectItem value="sales">
                                Sales Performance
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Date Range
                          </Label>
                          <Select
                            value={selectedDateRange}
                            onValueChange={setSelectedDateRange}
                          >
                            <SelectTrigger className="h-10">
                              <SelectValue placeholder="Select range" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="7days">Last 7 days</SelectItem>
                              <SelectItem value="30days">
                                Last 30 days
                              </SelectItem>
                              <SelectItem value="90days">
                                Last 90 days
                              </SelectItem>
                              <SelectItem value="6months">
                                Last 6 months
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Select Metrics
                        </Label>
                        <div className="grid grid-cols-2 gap-2">
                          {[
                            "Call Volume",
                            "Conversion Rate",
                            "Customer Acquisition",
                            "Prospect Quality",
                            "Sales Revenue",
                            "Team Performance",
                          ].map((metric) => (
                            <div
                              key={metric}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={metric}
                                checked={selectedMetrics.includes(metric)}
                                onCheckedChange={() => toggleMetric(metric)}
                              />
                              <Label htmlFor={metric} className="text-xs">
                                {metric}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 border">
                      <h3 className="text-sm font-medium mb-3 flex items-center">
                        <Eye className="w-4 h-4 mr-2" />
                        Preview
                      </h3>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Name:</span>
                          <span className="text-gray-600">
                            {reportName || "Untitled"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Type:</span>
                          <span className="text-gray-600 capitalize">
                            {selectedReportType || "None"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Range:</span>
                          <span className="text-gray-600">
                            {selectedDateRange || "None"}
                          </span>
                        </div>
                        <div>
                          <span>Metrics ({selectedMetrics.length}):</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedMetrics.length > 0 ? (
                              selectedMetrics.map((metric) => (
                                <Badge
                                  key={metric}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {metric}
                                </Badge>
                              ))
                            ) : (
                              <span className="text-xs text-gray-400">
                                No metrics selected
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <Button
                        onClick={handleGenerateReport}
                        disabled={
                          !reportName ||
                          !selectedReportType ||
                          !selectedDateRange ||
                          selectedMetrics.length === 0 ||
                          isGenerating
                        }
                        size="lg"
                        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                      >
                        {isGenerating ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            Generating Report...
                          </>
                        ) : (
                          <>
                            <Download className="w-4 h-4 mr-2" />
                            Generate Report
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </Card>

          {/* Modal Components for Detailed Views */}

          <TeleteamModals
            // Modal state props
            telemarketingCustomersOpen={telemarketingCustomersOpen}
            setTelemarketingCustomersOpen={setTelemarketingCustomersOpen}
            hotProspectsOpen={hotProspectsOpen}
            setHotProspectsOpen={setHotProspectsOpen}
            warmProspectsOpen={warmProspectsOpen}
            setWarmProspectsOpen={setWarmProspectsOpen}
            coldProspectsOpen={coldProspectsOpen}
            setColdProspectsOpen={setColdProspectsOpen}
            teleteamSalesOpen={teleteamSalesOpen}
            setTeleteamSalesOpen={setTeleteamSalesOpen}
            unallocatedLeadsOpen={unallocatedLeadsOpen}
            setUnallocatedLeadsOpen={setUnallocatedLeadsOpen}
          />

          <UnallocatedLeadsReport
            isModalOpen={isUnallocatedLeadsModalOpen}
            setIsModalOpen={setIsUnallocatedLeadsModalOpen}
          />
          <TelemarketingSalesReport
            isModalOpen={isTelemarketingSalesModalOpen}
            setIsModalOpen={setIsTelemarketingSalesModalOpen}
          />
          <TelemarketingCustomersReport
            isModalOpen={isTelemarketingCustomersModalOpen}
            setIsModalOpen={setIsTelemarketingCustomersModalOpen}
          />
        </div>
      </div>
    </Screen>
  );
};

export default TelemarketersDash;
