import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  TrendingUp,
  TrendingDown,
  UserCheck,
  Calendar,
  Award,
  Activity,
  Target,
  UserPlus,
  Heart,
  Globe
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { sampleData } from "./sampleData";

const PerformanceDashboard: React.FC = () => {
  const totalEmployees = sampleData.departments.reduce((sum, dept) => sum + dept.employees, 0);
  const activeEmployees = sampleData.departments.reduce((sum, dept) => sum + dept.active, 0);

  // Key metrics for the top cards
  const keyMetrics = [
    {
      title: "Active Employees",
      value: activeEmployees,
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      change: "+12 this month",
      trend: "up" as const
    },
    {
      title: "Employee Turnover",
      value: `${sampleData.turnover.overall}%`,
      icon: TrendingDown,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      change: "-1.2% vs last quarter",
      trend: "down" as const
    },
    {
      title: "Performance Rating",
      value: "4.2/5",
      icon: Award,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      change: "+0.3 improvement",
      trend: "up" as const
    }
  ];

  return (
    <div className="space-y-6 animate-fade-in-up custom-scrollbar">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-green-100 dark:border-gray-600">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
              Performance Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive overview of organizational performance metrics and analytics
            </p>
            <div className="flex items-center gap-4 mt-3">
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <Activity className="w-3 h-3 mr-1" />
                Live Data
              </Badge>
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <Calendar className="w-3 h-3 mr-1" />
                Last updated: {new Date().toLocaleTimeString()}
              </Badge>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="hover:bg-green-50 dark:hover:bg-gray-700">
              <Target className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm" className="hover:bg-blue-50 dark:hover:bg-gray-700">
              <Award className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="hover:bg-purple-50 dark:hover:bg-gray-700">
              <Activity className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {keyMetrics.map((metric, index) => (
          <Card
            key={metric.title}
            className={`
              group border-2 ${metric.borderColor} ${metric.bgColor}
              hover:shadow-xl hover:scale-105 transition-all duration-300
              cursor-pointer relative overflow-hidden
              hover:border-opacity-80
            `}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Subtle background pattern */}
            <div className="absolute inset-0 opacity-5 pointer-events-none">
              <div className="absolute top-0 right-0 w-20 h-20 rounded-full bg-current transform translate-x-8 -translate-y-8"></div>
            </div>

            <CardContent className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors">
                    {metric.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 group-hover:scale-105 transition-transform">
                    {metric.value}
                  </p>
                  <div className="flex items-center gap-1">
                    {metric.trend === "up" ? (
                      <TrendingUp className="w-3 h-3 text-green-500 group-hover:animate-pulse" />
                    ) : (
                      <TrendingDown className="w-3 h-3 text-red-500 group-hover:animate-pulse" />
                    )}
                    <span className={`text-xs transition-colors ${metric.trend === "up" ? "text-green-600 group-hover:text-green-700" : "text-red-600 group-hover:text-red-700"}`}>
                      {metric.change}
                    </span>
                  </div>
                </div>
                <div className={`
                  w-12 h-12 rounded-xl flex items-center justify-center
                  ${metric.bgColor} border ${metric.borderColor}
                  group-hover:scale-110 group-hover:rotate-3 transition-all duration-300
                  shadow-md group-hover:shadow-lg
                `}>
                  <metric.icon className={`w-6 h-6 ${metric.color} group-hover:scale-110 transition-transform`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Demographics and Recruitment Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Demographics Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Demographics Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Gender Distribution */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <UserCheck className="w-4 h-4" />
                  Gender Distribution
                </h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{sampleData.demographics.gender.male}</div>
                    <div className="text-sm text-gray-600">Male</div>
                    <div className="text-xs text-gray-500">
                      {((sampleData.demographics.gender.male / totalEmployees) * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-pink-600">{sampleData.demographics.gender.female}</div>
                    <div className="text-sm text-gray-600">Female</div>
                    <div className="text-xs text-gray-500">
                      {((sampleData.demographics.gender.female / totalEmployees) * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{sampleData.demographics.gender.other}</div>
                    <div className="text-sm text-gray-600">Other</div>
                    <div className="text-xs text-gray-500">
                      {((sampleData.demographics.gender.other / totalEmployees) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>

              {/* Marital Status */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Heart className="w-4 h-4" />
                  Marital Status
                </h4>
                <div className="space-y-2">
                  {Object.entries(sampleData.demographics.maritalStatus).map(([status, count]) => (
                    <div key={status} className="flex justify-between items-center">
                      <span className="text-sm">{status}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${(count / totalEmployees) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium w-8">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recruitment Funnel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="w-5 h-5" />
              Recruitment Funnel
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sampleData.recruitment.stages.map((stageData, index) => {
                const percentage = index === 0 ? 100 : stageData.conversionRate;

                return (
                  <div key={stageData.stage} className="flex items-center gap-4">
                    <div className="w-24 text-sm font-medium">
                      {stageData.stage}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm text-gray-600">{percentage.toFixed(1)}%</div>
                        <div className="text-sm font-bold">{stageData.count}</div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ${
                            index === 0 ? 'bg-blue-500' :
                            index === 1 ? 'bg-green-500' :
                            index === 2 ? 'bg-yellow-500' :
                            index === 3 ? 'bg-orange-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 w-16 text-right">
                      {stageData.averageTime > 0 ? `${stageData.averageTime}d` : '-'}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Department Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {sampleData.departments.map((dept) => (
                <div key={dept.name} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <div className="font-medium">{dept.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {dept.employees} employees
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        {dept.active} Active
                      </Badge>
                      {dept.inactive > 0 && (
                        <Badge variant="outline" className="text-red-600 border-red-200">
                          {dept.inactive} Inactive
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Employee Turnover by Department */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="w-5 h-5" />
              Turnover by Department
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {sampleData.turnover.departments.map((dept) => (
                <div key={dept.name} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{dept.name}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          dept.rate > 10 ? 'bg-red-500' :
                          dept.rate > 7 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${(dept.rate / 15) * 100}%` }}
                      ></div>
                    </div>
                    <span className={`text-sm font-bold ${
                      dept.rate > 10 ? 'text-red-600' :
                      dept.rate > 7 ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {dept.rate}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Ethnicity Distribution Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            Ethnicity Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(sampleData.demographics.ethnicity).map(([ethnicity, count]) => (
              <div key={ethnicity} className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-xl font-bold text-blue-600">{count}</div>
                <div className="text-sm text-gray-600">{ethnicity}</div>
                <div className="text-xs text-gray-500">
                  {((count / totalEmployees) * 100).toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Leave Analytics and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Leave Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Leave Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Leave Summary */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">{sampleData.leave.approved}</div>
                  <div className="text-sm text-gray-600">Approved</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{sampleData.leave.pending}</div>
                  <div className="text-sm text-gray-600">Pending</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">{sampleData.leave.rejected}</div>
                  <div className="text-sm text-gray-600">Rejected</div>
                </div>
              </div>

              {/* Leave Types */}
              <div>
                <h4 className="font-semibold mb-3">Leave Types</h4>
                <div className="space-y-2">
                  {Object.entries(sampleData.leave.types).map(([type, days]) => (
                    <div key={type} className="flex justify-between items-center">
                      <span className="text-sm">{type}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${(days / sampleData.leave.totalDays) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium w-8">{days}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Performance Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Performance Distribution */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{sampleData.performance.excellent}</div>
                  <div className="text-sm text-green-700 dark:text-green-400">Excellent</div>
                </div>
                <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{sampleData.performance.good}</div>
                  <div className="text-sm text-blue-700 dark:text-blue-400">Good</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{sampleData.performance.satisfactory}</div>
                  <div className="text-sm text-yellow-700 dark:text-yellow-400">Satisfactory</div>
                </div>
                <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{sampleData.performance.needsImprovement}</div>
                  <div className="text-sm text-red-700 dark:text-red-400">Needs Improvement</div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Overall Performance Score</span>
                  <span className="text-lg font-bold text-green-600">4.2/5.0</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div className="bg-green-500 h-3 rounded-full" style={{ width: '84%' }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Gender Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Department Gender Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sampleData.departments.map((dept) => {
              // Sample gender distribution for each department
              const maleRatio = Math.random() * 0.4 + 0.3; // 30-70%
              const femaleRatio = 1 - maleRatio;
              const maleCount = Math.round(dept.employees * maleRatio);
              const femaleCount = dept.employees - maleCount;

              return (
                <div key={dept.name} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">{dept.name}</span>
                    <span className="text-sm text-gray-600">{dept.employees} total</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <div className="flex justify-between text-xs mb-1">
                        <span>Male: {maleCount}</span>
                        <span>Female: {femaleCount}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 flex overflow-hidden">
                        <div
                          className="bg-blue-500 h-3"
                          style={{ width: `${maleRatio * 100}%` }}
                        ></div>
                        <div
                          className="bg-pink-500 h-3"
                          style={{ width: `${femaleRatio * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-right text-xs">
                      <div className="text-blue-600">{(maleRatio * 100).toFixed(0)}%</div>
                      <div className="text-pink-600">{(femaleRatio * 100).toFixed(0)}%</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Training and Development Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5" />
            Training & Development
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{sampleData.training.totalPrograms}</div>
              <div className="text-sm text-blue-700 dark:text-blue-400">Total Programs</div>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{sampleData.training.completionRate}%</div>
              <div className="text-sm text-green-700 dark:text-green-400">Completion Rate</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{sampleData.training.averageHoursPerEmployee}</div>
              <div className="text-sm text-purple-700 dark:text-purple-400">Avg Hours/Employee</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{sampleData.metrics.trainingHours.toLocaleString()}</div>
              <div className="text-sm text-orange-700 dark:text-orange-400">Total Hours</div>
            </div>
          </div>

          <div className="mt-6">
            <h4 className="font-semibold mb-3">Top Training Programs</h4>
            <div className="space-y-2">
              {sampleData.training.topPrograms.map((program) => (
                <div key={program.name} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <div>
                    <span className="font-medium">{program.name}</span>
                    <span className="text-sm text-gray-600 ml-2">({program.participants} participants)</span>
                  </div>
                  <Badge variant="outline" className={program.completion >= 95 ? "text-green-600 border-green-200" : "text-blue-600 border-blue-200"}>
                    {program.completion}% Complete
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Trends and Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Performance Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">4.2</div>
                <div className="text-sm text-gray-600">Average Rating</div>
                <div className="flex items-center justify-center gap-1 mt-1">
                  <TrendingUp className="w-3 h-3 text-green-500" />
                  <span className="text-xs text-green-600">+0.3 from last quarter</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Goal Achievement</span>
                  <span className="font-medium">87%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Training Completion</span>
                  <span className="font-medium">92%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Quick Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Avg. Tenure</span>
                <span className="font-bold">3.2 years</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Promotion Rate</span>
                <span className="font-bold text-green-600">15%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Training Hours</span>
                <span className="font-bold">2,450</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Satisfaction Score</span>
                <span className="font-bold text-blue-600">4.1/5</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Absenteeism Rate</span>
                <span className="font-bold text-orange-600">3.2%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Action Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
                <div className="text-sm font-medium text-red-800 dark:text-red-400">High Priority</div>
                <div className="text-xs text-red-600 dark:text-red-300">Address turnover in Sales dept</div>
              </div>
              <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border-l-4 border-yellow-500">
                <div className="text-sm font-medium text-yellow-800 dark:text-yellow-400">Medium Priority</div>
                <div className="text-xs text-yellow-600 dark:text-yellow-300">Review IT performance metrics</div>
              </div>
              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-500">
                <div className="text-sm font-medium text-green-800 dark:text-green-400">Low Priority</div>
                <div className="text-xs text-green-600 dark:text-green-300">Update training programs</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Executive Summary */}
      <Card className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-gray-200 dark:border-gray-600">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-800 dark:text-gray-200">
            <Target className="w-5 h-5" />
            Executive Summary & Key Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Workforce Overview */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200">Workforce Overview</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Employees:</span>
                  <span className="font-medium">{totalEmployees}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Rate:</span>
                  <span className="font-medium text-green-600">{((activeEmployees / totalEmployees) * 100).toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Avg. Tenure:</span>
                  <span className="font-medium">{sampleData.metrics.averageTenure} years</span>
                </div>
                <div className="flex justify-between">
                  <span>Retention Rate:</span>
                  <span className="font-medium text-green-600">{sampleData.metrics.retentionRate}%</span>
                </div>
              </div>
            </div>

            {/* Performance Highlights */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200">Performance Highlights</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Avg. Performance:</span>
                  <span className="font-medium text-blue-600">4.2/5.0</span>
                </div>
                <div className="flex justify-between">
                  <span>Goal Achievement:</span>
                  <span className="font-medium text-green-600">87%</span>
                </div>
                <div className="flex justify-between">
                  <span>Training Completion:</span>
                  <span className="font-medium text-purple-600">{sampleData.training.completionRate}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Engagement Score:</span>
                  <span className="font-medium text-blue-600">{sampleData.metrics.employeeEngagement}%</span>
                </div>
              </div>
            </div>

            {/* Key Recommendations */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200">Key Recommendations</h4>
              <div className="space-y-2 text-sm">
                <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border-l-2 border-yellow-400">
                  <span className="text-yellow-800 dark:text-yellow-300">Focus on Sales dept turnover reduction</span>
                </div>
                <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-2 border-blue-400">
                  <span className="text-blue-800 dark:text-blue-300">Expand IT team capacity</span>
                </div>
                <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded border-l-2 border-green-400">
                  <span className="text-green-800 dark:text-green-300">Maintain HR excellence standards</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceDashboard;