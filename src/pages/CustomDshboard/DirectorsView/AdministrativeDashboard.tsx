import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DirectorsAdminStats, useGetDirectorsAdminStatsQuery } from "@/redux/slices/directorsApiSlice";
import SampleOrganogram from "./organizational-chart/SampleOrganogram";
import { 
  Building2, 
  Users, 
  UserCheck, 
  TrendingUp, 
  TrendingDown,
  Eye,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";

const AdministrativeDashboard: React.FC = () => {
  const [showOrgChart, setShowOrgChart] = useState(false);

  const { data: adminRes, isLoading: loading, isError, refetch } = useGetDirectorsAdminStatsQuery();
  const error = isError ? "Failed to fetch administrative statistics. Please try again later." : null;
  const adminStats: DirectorsAdminStats | null = adminRes?.statistics ?? null;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Data</h3>
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={() => refetch()} variant="outline" className="border-red-300 text-red-700 hover:bg-red-100">
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  if (!adminStats) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <h3 className="text-lg font-medium text-yellow-800">No Data Available</h3>
        <p className="text-yellow-600">Administrative statistics are not available at this time.</p>
      </div>
    );
  }

  // Calculate key metrics
  const totalDepartments = adminStats.departments_count;
  const totalEmployees = adminStats.employees_count;
  
  // Gender distribution data
  const maleCount = adminStats.gender_distribution.M || 0;
  const femaleCount = adminStats.gender_distribution.F || 0;
  const unspecifiedCount = adminStats.gender_distribution.null || 0;
  
  const malePercentage = adminStats.gender_distribution.M_perc || "0%";
  const femalePercentage = adminStats.gender_distribution.F_perc || "0%";
  const unspecifiedPercentage = adminStats.gender_distribution.None_perc || "0%";

  return (
    <div className="space-y-6 animate-fade-in-up custom-scrollbar">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-100 dark:border-gray-600">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Administrative Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Overview of organizational structure and administrative metrics
            </p>
          </div>
          <Button 
            onClick={() => refetch()} 
            variant="outline" 
            size="sm" 
            className="hover:bg-blue-50 dark:hover:bg-gray-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-2 border-blue-200 bg-blue-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Total Departments
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {totalDepartments}
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-blue-100 border border-blue-200">
                <Building2 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-2 border-green-200 bg-green-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Total Employees
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {totalEmployees}
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-green-100 border border-green-200">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-2 border-purple-200 bg-purple-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Gender Diversity
                </p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  <span className="text-blue-600">{malePercentage}</span> M / 
                  <span className="text-pink-600"> {femalePercentage}</span> F
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-purple-100 border border-purple-200">
                <UserCheck className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Organization Chart Section */}
      <Card className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-purple-800 dark:text-purple-300">
            <Building2 className="w-5 h-5" />
            Organization Chart
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mb-4">
                <Building2 className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Interactive Organization Chart
              </h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Explore the complete organizational structure with department hierarchies, employee details, and reporting relationships.
              </p>
            </div>

            <Button
              onClick={() => setShowOrgChart(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
              size="lg"
            >
              <Eye className="w-4 h-4 mr-2" />
              View Organization Chart
            </Button>

            <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-md mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{totalDepartments}</div>
                <div className="text-xs text-gray-600">Departments</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{totalEmployees}</div>
                <div className="text-xs text-gray-600">Total Staff</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Full Organization Chart Modal */}
      {showOrgChart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-700">
            <div className="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6 rounded-t-xl">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Organization Chart</h2>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">Interactive organizational structure</p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setShowOrgChart(false)}
                  className="hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-2 focus:ring-blue-500"
                >
                  Close
                </Button>
              </div>
            </div>
            <div className="overflow-auto max-h-[calc(90vh-120px)]">
              <SampleOrganogram />
            </div>
          </div>
        </div>
      )}

      {/* Department Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Department Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {adminStats.department_overview.map((dept) => (
              <div key={dept.department} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <div className="font-medium">{dept.department}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {dept.employees_count} employees
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-green-600 border-green-200">
                      {dept.active_employees_count} Active
                    </Badge>
                    {dept.inactive_employees_count > 0 && (
                      <Badge variant="outline" className="text-red-600 border-red-200">
                        {dept.inactive_employees_count} Inactive
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Gender Distribution by Department */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Gender Distribution by Department
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {adminStats.departments_gender_distribution.map((dept) => {
              // Calculate total employees in department
              const totalDeptEmployees = dept.gender_distribution.reduce(
                (sum, gender) => sum + gender.total_gender_count,
                0
              );

              if (totalDeptEmployees === 0) return null;

              return (
                <div key={dept.department} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">{dept.department}</span>
                    <span className="text-sm text-gray-600">{totalDeptEmployees} total</span>
                  </div>
                  <div className="space-y-2">
                    {dept.gender_distribution.map((gender) => {
                      if (gender.total_gender_count === 0) return null;
                      
                      const percentage = (gender.total_gender_count / totalDeptEmployees) * 100;
                      
                      // Determine color based on gender
                      let colorClass = "bg-gray-500";
                      if (gender.gender === "M") colorClass = "bg-blue-500";
                      else if (gender.gender === "F") colorClass = "bg-pink-500";
                      
                      return (
                        <div key={`${dept.department}-${gender.gender}`} className="flex items-center gap-3">
                          <div className="w-20 text-sm">
                            {gender.gender === "M" ? "Male" : 
                             gender.gender === "F" ? "Female" : "Unspecified"}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between text-xs mb-1">
                              <span>{gender.total_gender_count} employees</span>
                              <span>{gender.gender_perc}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`${colorClass} h-2 rounded-full`}
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdministrativeDashboard;