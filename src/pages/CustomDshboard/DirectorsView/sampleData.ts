// Enhanced sample data for Directors Dashboard

export interface Department {
  name: string;
  employees: number;
  active: number;
  inactive: number;
  manager: string;
  budget: number;
  performance: number;
  genderDistribution: {
    male: number;
    female: number;
  };
}

export interface Employee {
  id: string;
  name: string;
  department: string;
  position: string;
  gender: 'male' | 'female' | 'other';
  ethnicity: string;
  maritalStatus: string;
  hireDate: string;
  salary: number;
  performanceRating: number;
  status: 'active' | 'inactive';
}

export interface RecruitmentStage {
  stage: string;
  count: number;
  conversionRate: number;
  averageTime: number; // in days
}

export interface LeaveRecord {
  type: string;
  days: number;
  approved: number;
  pending: number;
  rejected: number;
  trend: 'up' | 'down' | 'stable';
}

export const sampleData = {
  // Department data with more realistic information
  departments: [
    {
      name: "Sales",
      employees: 45,
      active: 42,
      inactive: 3,
      manager: "<PERSON>",
      budget: 2500000,
      performance: 87.5,
      genderDistribution: { male: 28, female: 17 }
    },
    {
      name: "Marketing",
      employees: 28,
      active: 26,
      inactive: 2,
      manager: "<PERSON>",
      budget: 1800000,
      performance: 92.3,
      genderDistribution: { male: 12, female: 16 }
    },
    {
      name: "Human Resources",
      employees: 15,
      active: 15,
      inactive: 0,
      manager: "<PERSON>",
      budget: 1200000,
      performance: 95.1,
      genderDistribution: { male: 5, female: 10 }
    },
    {
      name: "Finance",
      employees: 20,
      active: 19,
      inactive: 1,
      manager: "David Mwangi",
      budget: 1500000,
      performance: 89.7,
      genderDistribution: { male: 11, female: 9 }
    },
    {
      name: "Information Technology",
      employees: 32,
      active: 30,
      inactive: 2,
      manager: "Peter Ochieng",
      budget: 3200000,
      performance: 91.2,
      genderDistribution: { male: 24, female: 8 }
    },
    {
      name: "Operations",
      employees: 38,
      active: 35,
      inactive: 3,
      manager: "Mary Njeri",
      budget: 2800000,
      performance: 85.9,
      genderDistribution: { male: 22, female: 16 }
    },
    {
      name: "Legal",
      employees: 12,
      active: 12,
      inactive: 0,
      manager: "James Kiprotich",
      budget: 1800000,
      performance: 93.4,
      genderDistribution: { male: 7, female: 5 }
    },
    {
      name: "Customer Service",
      employees: 25,
      active: 23,
      inactive: 2,
      manager: "Lucy Wambui",
      budget: 1400000,
      performance: 88.6,
      genderDistribution: { male: 10, female: 15 }
    }
  ] as Department[],

  // Enhanced demographics data
  demographics: {
    gender: {
      male: 125,
      female: 90,
      other: 0
    },
    ethnicity: {
      "Kikuyu": 68,
      "Luo": 45,
      "Luhya": 38,
      "Kalenjin": 32,
      "Kamba": 25,
      "Other Kenyan": 7
    },
    maritalStatus: {
      "Single": 95,
      "Married": 105,
      "Divorced": 10,
      "Widowed": 5
    },
    ageGroups: {
      "20-29": 58,
      "30-39": 89,
      "40-49": 52,
      "50-59": 14,
      "60+": 2
    }
  },

  // Enhanced recruitment funnel
  recruitment: {
    applications: 450,
    screening: 180,
    interviews: 85,
    offers: 35,
    hired: 28,
    stages: [
      { stage: "Applications", count: 450, conversionRate: 100, averageTime: 0 },
      { stage: "Initial Screening", count: 180, conversionRate: 40, averageTime: 3 },
      { stage: "Technical Interview", count: 85, conversionRate: 47.2, averageTime: 7 },
      { stage: "Final Interview", count: 35, conversionRate: 41.2, averageTime: 5 },
      { stage: "Offer Extended", count: 28, conversionRate: 80, averageTime: 2 }
    ] as RecruitmentStage[]
  },

  // Enhanced leave analytics
  leave: {
    totalDays: 1250,
    approved: 1100,
    pending: 85,
    rejected: 65,
    types: {
      "Annual Leave": 650,
      "Sick Leave": 280,
      "Maternity/Paternity": 120,
      "Emergency Leave": 200
    },
    monthlyTrends: [
      { month: "Jan", days: 95 },
      { month: "Feb", days: 87 },
      { month: "Mar", days: 102 },
      { month: "Apr", days: 118 },
      { month: "May", days: 125 },
      { month: "Jun", days: 134 }
    ]
  },

  // Enhanced turnover data
  turnover: {
    overall: 8.5,
    quarterly: [
      { quarter: "Q1 2024", rate: 7.2 },
      { quarter: "Q2 2024", rate: 8.1 },
      { quarter: "Q3 2024", rate: 9.3 },
      { quarter: "Q4 2024", rate: 8.5 }
    ],
    departments: [
      { name: "Sales", rate: 12.3, trend: "up" },
      { name: "Marketing", rate: 6.8, trend: "down" },
      { name: "Human Resources", rate: 4.2, trend: "stable" },
      { name: "Finance", rate: 5.1, trend: "down" },
      { name: "Information Technology", rate: 9.7, trend: "up" },
      { name: "Operations", rate: 7.9, trend: "stable" },
      { name: "Legal", rate: 3.5, trend: "down" },
      { name: "Customer Service", rate: 11.2, trend: "up" }
    ]
  },

  // Enhanced performance data
  performance: {
    excellent: 45,
    good: 120,
    satisfactory: 40,
    needsImprovement: 10,
    distribution: {
      "5 - Outstanding": 25,
      "4 - Exceeds Expectations": 45,
      "3 - Meets Expectations": 120,
      "2 - Below Expectations": 20,
      "1 - Unsatisfactory": 5
    },
    departmentAverages: [
      { department: "Human Resources", average: 4.2 },
      { department: "Legal", average: 4.1 },
      { department: "Marketing", average: 4.0 },
      { department: "Finance", average: 3.9 },
      { department: "Information Technology", average: 3.8 },
      { department: "Customer Service", average: 3.7 },
      { department: "Operations", average: 3.6 },
      { department: "Sales", average: 3.5 }
    ]
  },

  // Additional metrics
  metrics: {
    averageTenure: 3.2,
    promotionRate: 15,
    trainingHours: 2450,
    satisfactionScore: 4.1,
    absenteeismRate: 3.2,
    overtimeHours: 1250,
    costPerHire: 125000,
    timeToFill: 28, // days
    employeeEngagement: 78.5,
    retentionRate: 91.5
  },

  // Training and development
  training: {
    totalPrograms: 24,
    completionRate: 87,
    averageHoursPerEmployee: 32,
    topPrograms: [
      { name: "Leadership Development", participants: 45, completion: 92 },
      { name: "Technical Skills", participants: 78, completion: 89 },
      { name: "Customer Service", participants: 56, completion: 94 },
      { name: "Safety Training", participants: 215, completion: 98 }
    ]
  }
};

export default sampleData;
