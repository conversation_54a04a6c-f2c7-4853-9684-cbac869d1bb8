// Sample organizational data for the organogram component

export interface Employee {
  employee_no: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  organisation_id?: number;
  department_id?: number;
  group_id?: number;
  job_code: string;
  job_title: string;
  gender: string;
  is_active?: boolean;
}

export interface JobInfo {
  id: number;
  category?: string;
  directorate?: string;
  job_title_code?: string;
  job_title?: string;
  work_location?: string;
  business_unit?: string;
  employee_no: string;
  department?: number;
  teams_code?: number;
}

export interface Department {
  id: number;
  name: string;
  description?: string;
  dep_head?: string;
  dep_head_assistant?: string;
  dep_hr?: string;
  department_status_active: boolean;
  organisation?: number;
  parent_department?: number | null;
}

// Sample Departments Data
export const sampleDepartments: Department[] = [
  // Root/Executive Level
  {
    id: 1,
    name: "Executive Office",
    description: "Chief Executive and strategic leadership",
    dep_head: "EMP001",
    dep_head_assistant: "EMP002",
    dep_hr: "EMP003",
    department_status_active: true,
    organisation: 1,
    parent_department: null,
  },
  
  // Second Level - Main Departments
  {
    id: 2,
    name: "Sales & Marketing",
    description: "Revenue generation and customer acquisition",
    dep_head: "EMP004",
    dep_head_assistant: "EMP005",
    dep_hr: "EMP006",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },
  {
    id: 3,
    name: "Human Resources",
    description: "People management and organizational development",
    dep_head: "EMP007",
    dep_head_assistant: "EMP008",
    dep_hr: "EMP009",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },
  {
    id: 4,
    name: "Finance & Accounting",
    description: "Financial management and reporting",
    dep_head: "EMP010",
    dep_head_assistant: "EMP011",
    dep_hr: "EMP012",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },
  {
    id: 5,
    name: "Information Technology",
    description: "Technology infrastructure and digital solutions",
    dep_head: "EMP013",
    dep_head_assistant: "EMP014",
    dep_hr: "EMP015",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },
  {
    id: 6,
    name: "Operations",
    description: "Day-to-day business operations and logistics",
    dep_head: "EMP016",
    dep_head_assistant: "EMP017",
    dep_hr: "EMP018",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },
  {
    id: 7,
    name: "Legal & Compliance",
    description: "Legal affairs and regulatory compliance",
    dep_head: "EMP019",
    dep_head_assistant: "EMP020",
    dep_hr: "EMP021",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },
  {
    id: 8,
    name: "Customer Service",
    description: "Customer support and relationship management",
    dep_head: "EMP022",
    dep_head_assistant: "EMP023",
    dep_hr: "EMP024",
    department_status_active: true,
    organisation: 1,
    parent_department: 1,
  },

  // Third Level - Sub-departments
  {
    id: 9,
    name: "Digital Marketing",
    description: "Online marketing and social media",
    dep_head: "EMP025",
    dep_head_assistant: "EMP026",
    dep_hr: "EMP027",
    department_status_active: true,
    organisation: 1,
    parent_department: 2,
  },
  {
    id: 10,
    name: "Field Sales",
    description: "Direct sales and client relationships",
    dep_head: "EMP028",
    dep_head_assistant: "EMP029",
    dep_hr: "EMP030",
    department_status_active: true,
    organisation: 1,
    parent_department: 2,
  },
  {
    id: 11,
    name: "Software Development",
    description: "Application development and maintenance",
    dep_head: "EMP031",
    dep_head_assistant: "EMP032",
    dep_hr: "EMP033",
    department_status_active: true,
    organisation: 1,
    parent_department: 5,
  },
  {
    id: 12,
    name: "IT Support",
    description: "Technical support and infrastructure",
    dep_head: "EMP034",
    dep_head_assistant: "EMP035",
    dep_hr: "EMP036",
    department_status_active: true,
    organisation: 1,
    parent_department: 5,
  },
];

// Sample Employees Data
export const sampleEmployees: Employee[] = [
  // Executive Office
  { employee_no: "EMP001", email: "<EMAIL>", username: "ceo", first_name: "George", last_name: "Wachiuri", organisation_id: 1, department_id: 1, job_code: "CEO001", job_title: "Chief Executive Officer", gender: "Male", is_active: true },
  { employee_no: "EMP002", email: "<EMAIL>", username: "exec_asst", first_name: "Grace", last_name: "Wanjiku", organisation_id: 1, department_id: 1, job_code: "EA001", job_title: "Executive Assistant", gender: "Female", is_active: true },
  { employee_no: "EMP003", email: "<EMAIL>", username: "exec_hr", first_name: "Peter", last_name: "Mwangi", organisation_id: 1, department_id: 1, job_code: "HR001", job_title: "Executive HR Coordinator", gender: "Male", is_active: true },

  // Sales & Marketing
  { employee_no: "EMP004", email: "<EMAIL>", username: "sales_head", first_name: "John", last_name: "Kamau", organisation_id: 1, department_id: 2, job_code: "SM001", job_title: "Sales & Marketing Director", gender: "Male", is_active: true },
  { employee_no: "EMP005", email: "<EMAIL>", username: "sales_asst", first_name: "Mary", last_name: "Njeri", organisation_id: 1, department_id: 2, job_code: "SM002", job_title: "Assistant Sales Manager", gender: "Female", is_active: true },
  { employee_no: "EMP006", email: "<EMAIL>", username: "sales_hr", first_name: "David", last_name: "Ochieng", organisation_id: 1, department_id: 2, job_code: "SM003", job_title: "Sales HR Coordinator", gender: "Male", is_active: true },
  { employee_no: "EMP037", email: "<EMAIL>", username: "sales_rep1", first_name: "Sarah", last_name: "Akinyi", organisation_id: 1, department_id: 2, job_code: "SM004", job_title: "Senior Sales Representative", gender: "Female", is_active: true },
  { employee_no: "EMP038", email: "<EMAIL>", username: "sales_rep2", first_name: "James", last_name: "Kiprotich", organisation_id: 1, department_id: 2, job_code: "SM005", job_title: "Sales Representative", gender: "Male", is_active: true },

  // Human Resources
  { employee_no: "EMP007", email: "<EMAIL>", username: "hr_head", first_name: "Grace", last_name: "Achieng", organisation_id: 1, department_id: 3, job_code: "HR001", job_title: "HR Director", gender: "Female", is_active: true },
  { employee_no: "EMP008", email: "<EMAIL>", username: "hr_asst", first_name: "Michael", last_name: "Waweru", organisation_id: 1, department_id: 3, job_code: "HR002", job_title: "Assistant HR Manager", gender: "Male", is_active: true },
  { employee_no: "EMP009", email: "<EMAIL>", username: "hr_coord", first_name: "Lucy", last_name: "Wambui", organisation_id: 1, department_id: 3, job_code: "HR003", job_title: "HR Coordinator", gender: "Female", is_active: true },
  { employee_no: "EMP039", email: "<EMAIL>", username: "hr_recruiter", first_name: "Daniel", last_name: "Mutua", organisation_id: 1, department_id: 3, job_code: "HR004", job_title: "Recruitment Specialist", gender: "Male", is_active: true },

  // Finance & Accounting
  { employee_no: "EMP010", email: "<EMAIL>", username: "finance_head", first_name: "David", last_name: "Mwangi", organisation_id: 1, department_id: 4, job_code: "FN001", job_title: "Finance Director", gender: "Male", is_active: true },
  { employee_no: "EMP011", email: "<EMAIL>", username: "finance_asst", first_name: "Catherine", last_name: "Nyokabi", organisation_id: 1, department_id: 4, job_code: "FN002", job_title: "Assistant Finance Manager", gender: "Female", is_active: true },
  { employee_no: "EMP012", email: "<EMAIL>", username: "finance_hr", first_name: "Robert", last_name: "Kipchoge", organisation_id: 1, department_id: 4, job_code: "FN003", job_title: "Finance HR Coordinator", gender: "Male", is_active: true },
  { employee_no: "EMP040", email: "<EMAIL>", username: "accountant1", first_name: "Agnes", last_name: "Wanjiru", organisation_id: 1, department_id: 4, job_code: "FN004", job_title: "Senior Accountant", gender: "Female", is_active: true },

  // Information Technology
  { employee_no: "EMP013", email: "<EMAIL>", username: "it_head", first_name: "Peter", last_name: "Ochieng", organisation_id: 1, department_id: 5, job_code: "IT001", job_title: "IT Director", gender: "Male", is_active: true },
  { employee_no: "EMP014", email: "<EMAIL>", username: "it_asst", first_name: "Jane", last_name: "Muthoni", organisation_id: 1, department_id: 5, job_code: "IT002", job_title: "Assistant IT Manager", gender: "Female", is_active: true },
  { employee_no: "EMP015", email: "<EMAIL>", username: "it_hr", first_name: "Samuel", last_name: "Kiptoo", organisation_id: 1, department_id: 5, job_code: "IT003", job_title: "IT HR Coordinator", gender: "Male", is_active: true },

  // Operations
  { employee_no: "EMP016", email: "<EMAIL>", username: "ops_head", first_name: "Mary", last_name: "Njeri", organisation_id: 1, department_id: 6, job_code: "OP001", job_title: "Operations Director", gender: "Female", is_active: true },
  { employee_no: "EMP017", email: "<EMAIL>", username: "ops_asst", first_name: "Francis", last_name: "Macharia", organisation_id: 1, department_id: 6, job_code: "OP002", job_title: "Assistant Operations Manager", gender: "Male", is_active: true },
  { employee_no: "EMP018", email: "<EMAIL>", username: "ops_hr", first_name: "Esther", last_name: "Wanjiku", organisation_id: 1, department_id: 6, job_code: "OP003", job_title: "Operations HR Coordinator", gender: "Female", is_active: true },

  // Legal & Compliance
  { employee_no: "EMP019", email: "<EMAIL>", username: "legal_head", first_name: "James", last_name: "Kiprotich", organisation_id: 1, department_id: 7, job_code: "LG001", job_title: "Legal Director", gender: "Male", is_active: true },
  { employee_no: "EMP020", email: "<EMAIL>", username: "legal_asst", first_name: "Ruth", last_name: "Wangari", organisation_id: 1, department_id: 7, job_code: "LG002", job_title: "Assistant Legal Manager", gender: "Female", is_active: true },
  { employee_no: "EMP021", email: "<EMAIL>", username: "legal_hr", first_name: "Paul", last_name: "Mbugua", organisation_id: 1, department_id: 7, job_code: "LG003", job_title: "Legal HR Coordinator", gender: "Male", is_active: true },

  // Customer Service
  { employee_no: "EMP022", email: "<EMAIL>", username: "cs_head", first_name: "Lucy", last_name: "Wambui", organisation_id: 1, department_id: 8, job_code: "CS001", job_title: "Customer Service Director", gender: "Female", is_active: true },
  { employee_no: "EMP023", email: "<EMAIL>", username: "cs_asst", first_name: "Kevin", last_name: "Otieno", organisation_id: 1, department_id: 8, job_code: "CS002", job_title: "Assistant Customer Service Manager", gender: "Male", is_active: true },
  { employee_no: "EMP024", email: "<EMAIL>", username: "cs_hr", first_name: "Nancy", last_name: "Chebet", organisation_id: 1, department_id: 8, job_code: "CS003", job_title: "Customer Service HR Coordinator", gender: "Female", is_active: true },

  // Sub-department heads and staff
  { employee_no: "EMP025", email: "<EMAIL>", username: "digital_head", first_name: "Brian", last_name: "Mwenda", organisation_id: 1, department_id: 9, job_code: "DM001", job_title: "Digital Marketing Manager", gender: "Male", is_active: true },
  { employee_no: "EMP026", email: "<EMAIL>", username: "digital_asst", first_name: "Mercy", last_name: "Wanjala", organisation_id: 1, department_id: 9, job_code: "DM002", job_title: "Digital Marketing Assistant", gender: "Female", is_active: true },
  { employee_no: "EMP027", email: "<EMAIL>", username: "digital_hr", first_name: "Joseph", last_name: "Karanja", organisation_id: 1, department_id: 9, job_code: "DM003", job_title: "Digital Marketing HR Coordinator", gender: "Male", is_active: true },

  { employee_no: "EMP028", email: "<EMAIL>", username: "fieldsales_head", first_name: "Patrick", last_name: "Kimani", organisation_id: 1, department_id: 10, job_code: "FS001", job_title: "Field Sales Manager", gender: "Male", is_active: true },
  { employee_no: "EMP029", email: "<EMAIL>", username: "fieldsales_asst", first_name: "Alice", last_name: "Nyambura", organisation_id: 1, department_id: 10, job_code: "FS002", job_title: "Field Sales Assistant", gender: "Female", is_active: true },
  { employee_no: "EMP030", email: "<EMAIL>", username: "fieldsales_hr", first_name: "Moses", last_name: "Wekesa", organisation_id: 1, department_id: 10, job_code: "FS003", job_title: "Field Sales HR Coordinator", gender: "Male", is_active: true },

  { employee_no: "EMP031", email: "<EMAIL>", username: "dev_head", first_name: "Anthony", last_name: "Muriuki", organisation_id: 1, department_id: 11, job_code: "SD001", job_title: "Software Development Manager", gender: "Male", is_active: true },
  { employee_no: "EMP032", email: "<EMAIL>", username: "dev_asst", first_name: "Priscilla", last_name: "Wanjiru", organisation_id: 1, department_id: 11, job_code: "SD002", job_title: "Senior Developer", gender: "Female", is_active: true },
  { employee_no: "EMP033", email: "<EMAIL>", username: "dev_hr", first_name: "Collins", last_name: "Omondi", organisation_id: 1, department_id: 11, job_code: "SD003", job_title: "Development HR Coordinator", gender: "Male", is_active: true },

  { employee_no: "EMP034", email: "<EMAIL>", username: "support_head", first_name: "Dennis", last_name: "Maina", organisation_id: 1, department_id: 12, job_code: "IS001", job_title: "IT Support Manager", gender: "Male", is_active: true },
  { employee_no: "EMP035", email: "<EMAIL>", username: "support_asst", first_name: "Winnie", last_name: "Auma", organisation_id: 1, department_id: 12, job_code: "IS002", job_title: "IT Support Assistant", gender: "Female", is_active: true },
  { employee_no: "EMP036", email: "<EMAIL>", username: "support_hr", first_name: "Victor", last_name: "Kibet", organisation_id: 1, department_id: 12, job_code: "IS003", job_title: "IT Support HR Coordinator", gender: "Male", is_active: true },
];

// Sample Job Info Data
export const sampleJobInfo: JobInfo[] = sampleEmployees.map((emp, index) => ({
  id: index + 1,
  category: "Full-time",
  directorate: emp.department_id === 1 ? "Executive" : emp.department_id && emp.department_id <= 8 ? "Operations" : "Support",
  job_title_code: emp.job_code,
  job_title: emp.job_title,
  work_location: "Nairobi Office",
  business_unit: "Optiven Limited",
  employee_no: emp.employee_no,
  department: emp.department_id,
  teams_code: emp.department_id,
}));
