import React, { use<PERSON><PERSON>back, useEffect, useState, useMemo } from "react";
import React<PERSON>low, {
  Background,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  Position,
  Node,
  Edge,
  NodeTypes,
  Connection,
  NodeProps,
  Handle,
  Controls,
} from "react-flow-renderer";
import { Screen } from "@/app-components/layout/screen";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import axios from "axios";
import { BASE_URL } from "@/config";
import "@xyflow/react/dist/style.css";
import "tippy.js/dist/tippy.css"; // If using tooltips elsewhere

// Types for API responses
interface Employee {
  employee_no: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  organisation_id?: number;
  department_id?: number;
  group_id?: number;
  job_code: string;
  job_title: string;
  gender: string;
  is_active?: boolean;
}

interface JobInfo {
  id: number;
  category?: string;
  directorate?: string;
  job_title_code?: string;
  job_title?: string;
  work_location?: string;
  business_unit?: string;
  employee_no: string;
  department?: number;
  teams_code?: number;
}

interface Department {
  id: number;
  name: string;
  description?: string;
  dep_head?: string;
  dep_head_assistant?: string;
  dep_hr?: string;
  department_status_active: boolean;
  organisation?: number;
  parent_department?: number | null;
}

// Enhanced Node Components with responsive styling
const DepartmentNode: React.FC<
  NodeProps<{
    title: string;
    label: string;
    isExpanded: boolean;
    employeeCount: number;
    onToggle: () => void;
  }>
> = ({ data, selected }) => {
  return (
    <div
      className={`relative w-[200px] sm:w-[240px] lg:w-[280px] border-2 rounded-xl shadow-lg cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
        selected
          ? "border-green-500 bg-green-50 dark:bg-green-900"
          : data.isExpanded
          ? "border-green-400 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800"
          : "border-gray-300 bg-gradient-to-br from-green-50 to-indigo-100 dark:from-gray-700 dark:to-gray-600"
      }`}
      onClick={data.onToggle}
    >
      {/* Department Header */}
      <div
        className={`text-white p-2 sm:p-3 lg:p-4 rounded-t-xl ${
          data.isExpanded
            ? "bg-gradient-to-r from-green-600 to-green-700"
            : "bg-gradient-to-r from-green-600 to-indigo-700"
        }`}
      >
        <div className="flex items-center justify-between">
          <span className="font-bold text-xs sm:text-sm leading-tight truncate pr-2">
            {data.title}
          </span>
          <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
            <span
              className={`px-1 sm:px-2 py-1 rounded-full text-xs font-bold ${
                data.isExpanded
                  ? "bg-white text-green-600"
                  : "bg-white text-green-600"
              }`}
            >
              {data.employeeCount}
            </span>
            <span className="text-white text-sm sm:text-lg">
              {data.isExpanded ? "📂" : "📁"}
            </span>
          </div>
        </div>
      </div>

      {/* Department Info */}
      <div className="p-2 sm:p-3">
        <div className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-line leading-relaxed">
          {data.label}
        </div>
      </div>

      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 sm:w-4 sm:h-4 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 sm:w-4 sm:h-4 bg-green-500 border-2 border-white"
      />
    </div>
  );
};

// Employee Node Component
const EmployeeNode: React.FC<
  NodeProps<{
    title: string;
    label: string;
    role: "head" | "assistant" | "member";
  }>
> = ({ data, selected }) => {
  const getRoleColor = (role: string) => {
    switch (role) {
      case "head":
        return "from-red-500 to-red-600";
      case "assistant":
        return "from-amber-500 to-orange-500";
      default:
        return "from-emerald-500 to-green-500";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "head":
        return "👑";
      case "assistant":
        return "🎯";
      default:
        return "👤";
    }
  };

  const getRoleBorder = (role: string) => {
    switch (role) {
      case "head":
        return "border-red-300";
      case "assistant":
        return "border-amber-300";
      default:
        return "border-emerald-300";
    }
  };

  return (
    <div
      className={`relative w-[160px] sm:w-[180px] lg:w-[200px] border-2 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg hover:scale-102 ${
        selected
          ? "border-green-500 bg-green-50 dark:bg-green-900"
          : `${getRoleBorder(data.role)} bg-white dark:bg-gray-700`
      }`}
    >
      {/* Employee Header */}
      <div
        className={`bg-gradient-to-r ${getRoleColor(
          data.role
        )} text-white p-2 sm:p-3 rounded-t-lg`}
      >
        <div className="flex items-center space-x-1 sm:space-x-2">
          <span className="text-sm sm:text-lg flex-shrink-0">
            {getRoleIcon(data.role)}
          </span>
          <span className="font-semibold text-xs sm:text-sm leading-tight truncate">
            {data.title}
          </span>
        </div>
      </div>

      {/* Employee Info */}
      <div className="p-2 sm:p-3">
        <div className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-line leading-relaxed">
          {data.label}
        </div>
      </div>

      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-2 h-2 sm:w-3 sm:h-3 bg-gray-400 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-2 h-2 sm:w-3 sm:h-3 bg-gray-400 border-2 border-white"
      />
    </div>
  );
};

// Define node types mapping
const nodeTypes: NodeTypes = {
  department: DepartmentNode,
  employee: EmployeeNode,
};

// Organogram Component
const Organogram: React.FC = () => {
  // Redux state
  const { token } = useSelector((state: RootState) => state.auth);

  // Local state
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [jobInfoData, setJobInfoData] = useState<JobInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedDepartments, setExpandedDepartments] = useState<Set<number>>(
    new Set()
  );
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });

  // Create axios instance with auth headers
  const apiClient = useMemo(() => {
    const instance = axios.create({ baseURL: BASE_URL });
    instance.interceptors.request.use((config) => {
      if (token) {
        config.headers.Authorization = `Token ${token}`;
      }
      return config;
    });
    return instance;
  }, [token]);

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Set initial size
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Fetch data from APIs
  useEffect(() => {
    const fetchData = async () => {
      if (!token) {
        setError("Authentication required");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const [jobInfoResponse, employeesResponse, departmentsResponse] =
          await Promise.all([
            apiClient.get("/users/employee-job-info-details"),
            apiClient.get("/users/all-employees", {
              params: {
                is_active: true,
              },
            }),
            apiClient.get("/users/departments"), // Still fetch departments for complete info
          ]);

        // Check if data is valid and handle different response structures
        let jobInfoData = jobInfoResponse.data || [];
        let empData = employeesResponse.data || [];
        let deptData = departmentsResponse.data || [];

        // Handle paginated responses (common API pattern)
        if (jobInfoData.results && Array.isArray(jobInfoData.results)) {
          jobInfoData = jobInfoData.results;
        }
        if (empData.results && Array.isArray(empData.results)) {
          empData = empData.results;
        }
        if (deptData.results && Array.isArray(deptData.results)) {
          deptData = deptData.results;
        }

        // Ensure we have arrays
        if (!Array.isArray(jobInfoData)) {
          jobInfoData = [jobInfoData];
        }
        if (!Array.isArray(empData)) {
          empData = [empData];
        }
        if (!Array.isArray(deptData)) {
          deptData = [deptData];
        }

        // Filter departments based on job info data - only show departments that have employees
        const departmentIdsWithEmployees = new Set(
          jobInfoData
            .filter((job: JobInfo) => job.department)
            .map((job: JobInfo) => job.department)
        );

        const filteredDepartments = deptData.filter((dept: Department) =>
          departmentIdsWithEmployees.has(dept.id)
        );

        setJobInfoData(jobInfoData);
        setDepartments(filteredDepartments);
        setEmployees(empData);

        console.log("📊 Organization data loaded:", {
          jobInfoCount: jobInfoData.length,
          departmentCount: filteredDepartments.length,
          employeeCount: empData.length,
          departmentIdsWithEmployees: Array.from(departmentIdsWithEmployees),
        });
      } catch (err) {
        console.error("Error fetching organization data:", err);
        setError("Failed to load organization data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiClient, token]);

  // Toggle department expansion (only one department can be open at a time)
  const toggleDepartment = useCallback((deptId: number) => {
    setExpandedDepartments((prev) => {
      const newSet = new Set<number>();
      if (!prev.has(deptId)) {
        // Close all others and open this one
        newSet.add(deptId);
      }
      return newSet;
    });
  }, []);

  // Transform data into ReactFlow nodes and edges
  const { nodes: initialNodes, edges: initialEdges } = useMemo(() => {
    // Create some test nodes if no data is available
    if (departments.length === 0) {
      return {
        nodes: [
          {
            id: "test-1",
            type: "department",
            data: {
              title: "Test Department",
              label:
                "No real data available\nThis is a test node\nCheck console for fixes",
              isExpanded: false,
              employeeCount: 0,
              onToggle: () => console.log("Test toggle"),
            },
            position: { x: 100, y: 100 },
          },
          {
            id: "test-2",
            type: "employee",
            data: {
              title: "Test Employee",
              label: "Test Job Title\nEmp No: TEST001",
              role: "head" as const,
            },
            position: { x: 300, y: 100 },
          },
        ],
        edges: [
          {
            id: "test-edge-1",
            source: "test-1",
            target: "test-2",
            animated: true,
          },
        ],
      };
    }

    const nodes: Node[] = [];
    const edges: Edge[] = [];

    // Create a map of employees by department using job info data
    const employeesByDept = new Map<number, Employee[]>();

    // Create a map of employee_no to job info for quick lookup
    const jobInfoMap = new Map<string, JobInfo>();
    jobInfoData.forEach((job) => {
      jobInfoMap.set(job.employee_no, job);
    });

    employees.forEach((emp) => {
      const jobInfo = jobInfoMap.get(emp.employee_no);
      if (jobInfo && jobInfo.department) {
        if (!employeesByDept.has(jobInfo.department)) {
          employeesByDept.set(jobInfo.department, []);
        }
        employeesByDept.get(jobInfo.department)!.push(emp);
      }
    });

    // Build hierarchy levels
    const rootDepartments = departments.filter(
      (dept) => !dept.parent_department
    );
    const departmentLevels = new Map<number, number>();

    // Assign levels to departments (BFS approach)
    const queue: { dept: Department; level: number }[] = rootDepartments.map(
      (dept) => ({ dept, level: 0 })
    );

    while (queue.length > 0) {
      const { dept, level } = queue.shift()!;
      departmentLevels.set(dept.id, level);

      // Find child departments
      const children = departments.filter(
        (d) => d.parent_department === dept.id
      );
      children.forEach((child) => {
        queue.push({ dept: child, level: level + 1 });
      });
    }

    // Create department nodes and employee nodes (if expanded)
    const departmentNodeIds = new Map<number, string>(); // dept_id -> nodeId

    // Process departments level by level
    const maxLevel = Math.max(...Array.from(departmentLevels.values()));

    for (let currentLevel = 0; currentLevel <= maxLevel; currentLevel++) {
      const departmentsAtLevel = departments.filter(
        (dept) => departmentLevels.get(dept.id) === currentLevel
      );

      departmentsAtLevel.forEach((dept, deptIndexAtLevel) => {
        const deptEmployees = employeesByDept.get(dept.id) || [];
        const depHead = deptEmployees.find(
          (emp) => emp.employee_no === dept.dep_head
        );
        const depAssistant = deptEmployees.find(
          (emp) => emp.employee_no === dept.dep_head_assistant
        );

        // Create department node with dynamic positioning
        const deptNodeId = `dept-${dept.id}`;
        departmentNodeIds.set(dept.id, deptNodeId);

        // Calculate space needed for this department if expanded
        const isExpanded = expandedDepartments.has(dept.id);

        // Responsive positioning based on screen size
        const getResponsiveSpacing = () => {
          if (typeof window !== "undefined") {
            const width = window.innerWidth;
            if (width < 640) {
              // Mobile
              return {
                minSpacing: 250,
                expandedSpacing: 400,
                verticalSpacing: 300,
                memberSpacing: 180,
                membersPerRow: 2,
              };
            } else if (width < 1024) {
              // Tablet
              return {
                minSpacing: 400,
                expandedSpacing: 600,
                verticalSpacing: 400,
                memberSpacing: 200,
                membersPerRow: 2,
              };
            } else {
              // Desktop
              return {
                minSpacing: 600,
                expandedSpacing: 1000,
                verticalSpacing: 500,
                memberSpacing: 240,
                membersPerRow: 3,
              };
            }
          }
          // Default to mobile if window is not available
          return {
            minSpacing: 250,
            expandedSpacing: 400,
            verticalSpacing: 300,
            memberSpacing: 180,
            membersPerRow: 2,
          };
        };

        const spacing = getResponsiveSpacing();

        // Calculate total width needed for this level
        let totalWidthNeeded = 0;
        let departmentWidths: number[] = [];

        departmentsAtLevel.forEach((dept) => {
          const deptEmployees = employeesByDept.get(dept.id) || [];
          const isDeptExpanded = expandedDepartments.has(dept.id);

          let deptWidth = spacing.minSpacing; // Base width

          if (isDeptExpanded && deptEmployees.length > 2) {
            // Calculate expanded width based on employee layout
            const memberCount = deptEmployees.length - 2; // excluding head and assistant
            const memberCols = Math.min(memberCount, spacing.membersPerRow);
            deptWidth = Math.max(
              spacing.expandedSpacing,
              memberCols * spacing.memberSpacing + 200
            );
          }

          departmentWidths.push(deptWidth);
          totalWidthNeeded += deptWidth;
        });

        // Calculate starting X position to center the level
        const startX = -totalWidthNeeded / 2;

        // Calculate this department's X position
        let baseX = startX;
        for (let i = 0; i < deptIndexAtLevel; i++) {
          baseX += departmentWidths[i];
        }

        // Add half of this department's width to center it
        baseX += departmentWidths[deptIndexAtLevel] / 2;

        // Calculate Y position with responsive vertical spacing
        let baseY = currentLevel * spacing.verticalSpacing + 150;

        // Add extra space if there are expanded departments in previous levels
        for (let level = 0; level < currentLevel; level++) {
          const depsAtPrevLevel = departments.filter(
            (d) => departmentLevels.get(d.id) === level
          );
          const maxExpandedHeight = Math.max(
            0,
            ...depsAtPrevLevel.map((d) => {
              const empCount = (employeesByDept.get(d.id) || []).length;
              if (expandedDepartments.has(d.id) && empCount > 0) {
                const empRows = Math.ceil(Math.max(0, empCount - 2) / 3);
                return 180 + 320 + empRows * 120;
              }
              return 0;
            })
          );
          baseY += maxExpandedHeight;
        }

        nodes.push({
          id: deptNodeId,
          type: "department",
          data: {
            title: dept.name,
            label: `${
              depHead
                ? `👤 Head: ${depHead.first_name} ${depHead.last_name}`
                : "👤 No Head Assigned"
            }\n${
              depAssistant
                ? `🎯 Assistant: ${depAssistant.first_name} ${depAssistant.last_name}`
                : ""
            }`.trim(),
            isExpanded: isExpanded,
            employeeCount: deptEmployees.length,
            onToggle: () => toggleDepartment(dept.id),
          },
          position: { x: baseX, y: baseY },
        });

        // If department is expanded, create employee nodes
        if (expandedDepartments.has(dept.id) && deptEmployees.length > 0) {
          // Sort employees: head first, then assistant, then others
          const sortedEmployees = [...deptEmployees].sort((a, b) => {
            if (a.employee_no === dept.dep_head) return -1;
            if (b.employee_no === dept.dep_head) return 1;
            if (a.employee_no === dept.dep_head_assistant) return -1;
            if (b.employee_no === dept.dep_head_assistant) return 1;
            return 0;
          });

          sortedEmployees.forEach((employee) => {
            const empNodeId = `emp-${dept.id}-${employee.employee_no}`;
            const employeeJobInfo = jobInfoMap.get(employee.employee_no);

            // Determine role
            let role: "head" | "assistant" | "member" = "member";
            if (employee.employee_no === dept.dep_head) role = "head";
            else if (employee.employee_no === dept.dep_head_assistant)
              role = "assistant";

            // Responsive employee positioning - organized by role
            let empX, empY;

            const headOffset =
              typeof window !== "undefined" && window.innerWidth < 640
                ? 140
                : 180;
            const assistantOffset =
              typeof window !== "undefined" && window.innerWidth < 640
                ? 180
                : 250;
            const memberBaseOffset =
              typeof window !== "undefined" && window.innerWidth < 640
                ? 260
                : 320;
            const memberRowSpacing =
              typeof window !== "undefined" && window.innerWidth < 640
                ? 100
                : 120;

            if (role === "head") {
              // Head directly below department center
              empX = baseX;
              empY = baseY + headOffset;
            } else if (role === "assistant") {
              // Assistant to the right of head
              empX = baseX + assistantOffset;
              empY = baseY + headOffset;
            } else {
              // Members arranged in a responsive grid below head and assistant
              const memberIndex = sortedEmployees
                .filter(
                  (e) =>
                    e.employee_no !== dept.dep_head &&
                    e.employee_no !== dept.dep_head_assistant
                )
                .indexOf(employee);

              const row = Math.floor(memberIndex / spacing.membersPerRow);
              const col = memberIndex % spacing.membersPerRow;

              // Center the members based on how many are in the row
              const membersInRow = Math.min(
                spacing.membersPerRow,
                sortedEmployees.length - 2 - row * spacing.membersPerRow
              );
              const rowStartX =
                baseX - ((membersInRow - 1) * spacing.memberSpacing) / 2;

              empX = rowStartX + col * spacing.memberSpacing;
              empY = baseY + memberBaseOffset + row * memberRowSpacing;
            }

            nodes.push({
              id: empNodeId,
              type: "employee",
              data: {
                title: `${employee.first_name} ${employee.last_name}`,
                label: `${
                  employeeJobInfo?.job_title ||
                  employee.job_title ||
                  "No Job Title"
                }\n📧 ${employee.email}\n🆔 ${employee.employee_no}`,
                role: role,
              },
              position: { x: empX, y: empY },
            });

            // Connect employee to department
            edges.push({
              id: `edge-dept-emp-${dept.id}-${employee.employee_no}`,
              source: deptNodeId,
              target: empNodeId,
              animated: true,
              style: {
                stroke:
                  role === "head"
                    ? "#ef4444"
                    : role === "assistant"
                    ? "#f97316"
                    : "#22c55e",
              },
            });
          });
        }
      });
    }

    // Create edges for department hierarchy (connect parent departments to child departments)
    departments.forEach((dept) => {
      if (dept.parent_department) {
        const parentNodeId = departmentNodeIds.get(dept.parent_department);
        const childNodeId = departmentNodeIds.get(dept.id);

        if (parentNodeId && childNodeId) {
          edges.push({
            id: `edge-dept-hierarchy-${dept.parent_department}-${dept.id}`,
            source: parentNodeId,
            target: childNodeId,
            animated: true,
            style: { stroke: "#3b82f6", strokeWidth: 3 },
          });
        }
      }
    });

    // Fallback: if we have departments but no nodes were created, create simple nodes
    if (nodes.length === 0 && departments.length > 0) {
      departments.slice(0, 10).forEach((dept, index) => {
        const deptEmployees = employeesByDept.get(dept.id) || [];
        const depHead = deptEmployees.find(
          (emp) => emp.employee_no === dept.dep_head
        );

        nodes.push({
          id: `fallback-dept-${dept.id}`,
          type: "department",
          data: {
            title: dept.name || `Department ${dept.id}`,
            label: `${
              depHead
                ? `Head: ${depHead.first_name} ${depHead.last_name}`
                : "No Head"
            }\nEmployees: ${deptEmployees.length}\nID: ${dept.id}`,
            isExpanded: false,
            employeeCount: deptEmployees.length,
            onToggle: () => toggleDepartment(dept.id),
          },
          position: {
            x: (index % 4) * 300,
            y: Math.floor(index / 4) * 200 + 100,
          },
        });

        // Add simple edges for parent-child relationships
        if (dept.parent_department) {
          const parentIndex = departments.findIndex(
            (d) => d.id === dept.parent_department
          );
          if (parentIndex !== -1) {
            edges.push({
              id: `fallback-edge-${dept.parent_department}-${dept.id}`,
              source: `fallback-dept-${dept.parent_department}`,
              target: `fallback-dept-${dept.id}`,
              animated: true,
              style: { stroke: "#3b82f6", strokeWidth: 2 },
            });
          }
        }
      });
    }

    return { nodes, edges };
  }, [
    departments,
    employees,
    jobInfoData,
    expandedDepartments,
    toggleDepartment,
    windowSize,
  ]);

  // Initialize nodes and edges state using React Flow's hooks
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Update nodes and edges when initialNodes/initialEdges change
  useEffect(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
  }, [initialNodes, initialEdges, setNodes, setEdges]);

  // Handle connecting nodes (disabled for non-editable chart)
  const onConnect = useCallback(() => {
    // Chart is not editable, so we don't allow new connections
  }, []);

  // Show loading state
  if (loading) {
    return (
      <Screen headerContent>
        <div className="h-screen w-full flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">
              Loading organization chart...
            </p>
          </div>
        </div>
      </Screen>
    );
  }

  // Show error state
  if (error) {
    return (
      <Screen headerContent>
        <div className="h-screen w-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg
                className="w-12 h-12 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  // Show empty state when no departments
  if (!loading && departments.length === 0) {
    return (
      <Screen headerContent>
        <div className="h-screen w-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-gray-400 mb-4">
              <svg
                className="w-12 h-12 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-2">
              No organization data available
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Please contact your administrator to set up departments and
              employees.
            </p>
          </div>
        </div>
      </Screen>
    );
  }

  console.log("🎨 Rendering chart with:", {
    nodeCount: nodes.length,
    edgeCount: edges.length,
    loading,
    error,
    departmentCount: departments.length,
    employeeCount: employees.length,
    jobInfoCount: jobInfoData.length,
  });

  return (
    <Screen headerContent>
      <div className="h-[calc(100vh-80px)] sm:h-[calc(100vh-120px)] w-full relative">
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            fitViewOptions={{
              padding:
                typeof window !== "undefined" && window.innerWidth < 640
                  ? 0.05
                  : 0.15,
              minZoom:
                typeof window !== "undefined" && window.innerWidth < 640
                  ? 0.3
                  : 0.1,
              maxZoom:
                typeof window !== "undefined" && window.innerWidth < 640
                  ? 2
                  : 1.5,
            }}
            className="bg-gradient-to-br from-slate-50 via-green-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700"
            nodesDraggable={false}
            nodesConnectable={false}
            elementsSelectable={true}
            minZoom={
              typeof window !== "undefined" && window.innerWidth < 640
                ? 0.2
                : 0.05
            }
            maxZoom={
              typeof window !== "undefined" && window.innerWidth < 640 ? 3 : 3
            }
            attributionPosition="bottom-left"
            panOnScroll={true}
            panOnDrag={true}
            zoomOnScroll={true}
            zoomOnPinch={true}
            zoomOnDoubleClick={true}
          >
            <Background
              color="#e2e8f0"
              size={1}
              className="opacity-30 dark:opacity-10"
            />

            {/* Enhanced Zoom Controls */}
            <Controls
              className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg"
              showZoom={true}
              showFitView={true}
              showInteractive={false}
            />
          </ReactFlow>
        </ReactFlowProvider>

        {/* Responsive Legend in bottom right */}
        <div className="absolute bottom-12 right-1 sm:bottom-16 sm:right-2 lg:bottom-20 lg:right-4 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-lg sm:rounded-xl shadow-lg p-2 sm:p-3 lg:p-4 border border-gray-200 dark:border-gray-700 max-w-[140px] sm:max-w-[160px] lg:max-w-[200px]">
          <h3 className="font-semibold text-xs mb-1 sm:mb-2 text-gray-800 dark:text-gray-200">
            Legend
          </h3>
          <div className="space-y-1 text-xs">
            <div className="flex items-center space-x-1 sm:space-x-2">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-green-600 to-indigo-700 rounded flex-shrink-0"></div>
              <span className="text-gray-700 dark:text-gray-300 truncate">
                Department
              </span>
            </div>
            <div className="flex items-center space-x-1 sm:space-x-2">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-red-500 to-red-600 rounded flex-shrink-0"></div>
              <span className="text-gray-700 dark:text-gray-300 truncate">
                👑 Head
              </span>
            </div>
            <div className="flex items-center space-x-1 sm:space-x-2">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded flex-shrink-0"></div>
              <span className="text-gray-700 dark:text-gray-300 truncate">
                🎯 Assistant
              </span>
            </div>
            <div className="flex items-center space-x-1 sm:space-x-2">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-emerald-500 to-green-500 rounded flex-shrink-0"></div>
              <span className="text-gray-700 dark:text-gray-300 truncate">
                👤 Member
              </span>
            </div>
          </div>
        </div>

        {/* Simple mobile instruction only */}
        <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-3 py-2 rounded-lg backdrop-blur-sm sm:hidden">
          📱 Tap departments to expand
        </div>
      </div>
    </Screen>
  );
};

export default Organogram;
