import React, { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import AdministrativeDashboard from "./AdministrativeDashboard";
import PerformanceDashboard from "./PerformanceDashboard";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Building2, BarChart3 } from "lucide-react";

const DirectorsDashboardTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState("administrative");

  return (
    <Screen>
      <div className="space-y-6 animate-fade-in-up custom-scrollbar">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-100 dark:border-gray-600">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Directors Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Comprehensive overview of organizational metrics and analytics
              </p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-1">
            <TabsTrigger 
              value="administrative" 
              className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 flex items-center gap-2"
            >
              <Building2 className="w-4 h-4" />
              Administrative
            </TabsTrigger>
            <TabsTrigger 
              value="performance" 
              className="data-[state=active]:bg-green-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 flex items-center gap-2"
            >
              <BarChart3 className="w-4 h-4" />
              Performance
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="administrative" className="mt-6 border-0 p-0">
            <AdministrativeDashboard />
          </TabsContent>
          
          <TabsContent value="performance" className="mt-6 border-0 p-0">
            <PerformanceDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};

export default DirectorsDashboardTabs;