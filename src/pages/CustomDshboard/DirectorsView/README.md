# Directors Dashboard

A comprehensive dashboard for directors to monitor organizational metrics and analytics.

## Features

### 📊 Core Metrics
- Total Departments (8)
- Active Employees (202/215)
- Employee Turnover Rate (8.5%)
- Performance Rating (4.2/5)

### 👥 Demographics Analytics
- **Gender Distribution**: Visual breakdown of male/female employees
- **Ethnicity Analysis**: Kenyan ethnic group representation
- **Marital Status**: Single, Married, Divorced, Widowed statistics
- **Age Groups**: Distribution across age ranges

### 🏢 Department Analytics
- Department overview with employee counts
- Department-wise gender distribution
- Performance metrics per department
- Manager assignments and budgets

### 🎯 Recruitment Funnel
- Applications → Screening → Interviews → Offers → Hired
- Conversion rates and average processing time
- Visual progress indicators

### 🏗️ Organization Chart
- Interactive modal with organizational structure
- Hierarchical view from Board to Department level
- External link capability for full chart

### 📅 Leave Analytics
- Leave type breakdown (Annual, Sick, Maternity, Emergency)
- Approval status tracking
- Monthly trends and patterns

### 📈 Performance Analytics
- Performance rating distribution
- Department-wise performance averages
- Goal achievement tracking
- Employee engagement metrics

### 🎓 Training & Development
- Training program overview
- Completion rates and effectiveness
- Hours per employee tracking

### 📋 Executive Summary
- Key workforce statistics
- Performance highlights
- Actionable recommendations

## Technical Implementation

### Files Structure
```
src/pages/CustomDshboard/DirectorsView/
├── DirectorsDashboard.tsx      # Main dashboard component
├── DirectorsDashboard.css      # Custom styles and animations
├── sampleData.ts              # Comprehensive sample data
├── DirectorsDashboard.test.tsx # Unit tests
└── README.md                  # This file
```

### Dependencies
- React 18+
- TypeScript
- Tailwind CSS
- Lucide React (icons)
- Shadcn/ui components
- Recharts (for future chart enhancements)

### Sample Data
The dashboard uses realistic sample data including:
- 215 employees across 8 departments
- Kenyan demographic data
- Performance metrics and trends
- Training and development statistics

## Usage

### Accessing the Dashboard
Navigate to `/directors` in your application or click "Directors" in the sidebar under the Departments section.

### Interactive Features
- **Organization Chart**: Click "View Organization Chart" to open the modal
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark Mode**: Supports light/dark theme switching
- **Hover Effects**: Enhanced interactions with animations

### Key Insights
The dashboard provides directors with:
- Real-time workforce overview
- Performance trends and patterns
- Recruitment effectiveness
- Training program success
- Actionable recommendations

## Testing

Run the test suite:
```bash
npm test DirectorsDashboard.test.tsx
```

Tests cover:
- Component rendering
- Modal functionality
- Data display accuracy
- Accessibility features

## Future Enhancements

Potential improvements:
- Real API integration
- Advanced chart visualizations
- Export functionality
- Drill-down capabilities
- Real-time data updates
- Custom date range filtering

## Troubleshooting

### Common Issues

1. **Objects not valid as React child error**
   - Fixed: Ensure all rendered values are primitives (strings, numbers)
   - Use proper data structure access (e.g., `stages` array instead of object entries)

2. **Missing data properties**
   - Verify sample data structure matches component expectations
   - Check TypeScript interfaces for consistency

3. **Styling issues**
   - Ensure CSS file is properly imported
   - Check Tailwind classes are available
   - Verify dark mode compatibility

## Support

For issues or enhancements, please refer to the main project documentation or contact the development team.
