import React, { useEffect, use<PERSON><PERSON><PERSON>, useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Building2,
  Users,
  TrendingUp,
  TrendingDown,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieIcon,
  RefreshCw,
  Calendar,
  Target,
  AlertTriangle,
  Gauge,
  Coins,
  Wallet,
  ShieldAlert,
  ArrowUpRight,
  ArrowDownRight,
  Percent as PercentIcon,
  Info,
} from "lucide-react";
import {
  useGetDirectorsAdminStatsQuery,
  useGetDirectorsPerformanceStatsQuery,
  DirectorsAdminStats,
  DirectorsPerformanceStats,
} from "@/redux/slices/directorsApiSlice";
import {
  Respons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Composed<PERSON>,
} from "recharts";

// ========= Helpers =========
const safeNum = (n: unknown, d = 0): number => {
  const x = Number(n);
  return Number.isFinite(x) ? x : d;
};

const clamp = (n: number, min = 0, max = 100) => Math.max(min, Math.min(max, n));

const formatNumber = (n: number | undefined | null) => {
  if (n === null || n === undefined || isNaN(Number(n))) return "-";
  return new Intl.NumberFormat("en-KE", { maximumFractionDigits: 0 }).format(Number(n));
};

const formatCurrency = (n: number | undefined | null) => {
  if (n === null || n === undefined || isNaN(Number(n))) return "-";
  return new Intl.NumberFormat("en-KE", {
    style: "currency",
    currency: "KES",
    maximumFractionDigits: 0,
  }).format(Number(n));
};

const percent = (num: number, den: number) => {
  const a = safeNum(num, 0);
  const b = safeNum(den, 0);
  if (!b) return 0;
  const p = (a / b) * 100;
  return Number.isFinite(p) ? p : 0;
};

// Business day helpers for pace calculations
const isBusinessDay = (d: Date) => {
  const day = d.getDay();
  return day !== 0 && day !== 6; // Mon–Fri only
};

const businessDaysInMonth = (year: number, monthIndex0: number) => {
  const start = new Date(year, monthIndex0, 1);
  const end = new Date(year, monthIndex0 + 1, 0);
  let count = 0;
  for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
    if (isBusinessDay(dt)) count++;
  }
  return count;
};

const elapsedBusinessDaysInMonth = (year: number, monthIndex0: number, today: number) => {
  const start = new Date(year, monthIndex0, 1);
  let count = 0;
  for (let d = 1; d <= today; d++) {
    const dt = new Date(year, monthIndex0, d);
    if (isBusinessDay(dt)) count++;
  }
  return count;
};


const KPI: React.FC<{
  title: string;
  value: string | number;
  icon: React.ElementType;
  hint?: string;
  bgClass?: string; // light background tint
}> = ({ title, value, icon: Icon, hint, bgClass = "" }) => (
  <Card className={`border border-slate-200 bg-white shadow-sm ${bgClass}`}>
    <CardContent className="p-5">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-slate-500 tracking-wide">{title}</p>
          <p className="text-2xl font-semibold mt-1 text-slate-900">{value}</p>
          {hint && <p className="text-xs text-slate-500 mt-1">{hint}</p>}
        </div>
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center bg-slate-50 border border-slate-200`}>
          <Icon className={`w-5 h-5 text-slate-700`} />
        </div>
      </div>
    </CardContent>
  </Card>
);

const DirectorsDashboardV2: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch via RTK Query
  const { data: adminRes, isLoading: adminLoading, isError: adminIsError, refetch: refetchAdmin } = useGetDirectorsAdminStatsQuery();
  const { data: perfRes, isLoading: perfLoading, isError: perfIsError, refetch: refetchPerf } = useGetDirectorsPerformanceStatsQuery();

  const loading = adminLoading || perfLoading;
  const error = adminIsError || perfIsError ? "Failed to load directors dashboards. Please try again." : null;
  const adminStats: DirectorsAdminStats | null = adminRes?.statistics ?? null;
  const perfStats: DirectorsPerformanceStats | null = perfRes?.statistics ?? null;

  const fetchAll = async () => {
    await Promise.all([refetchAdmin(), refetchPerf()]);
  };

  // ========= Data shaping =========
  const perf = perfStats?.teams_performance?.[0];
  const expected = safeNum(perf?.Expected_Monthly_Installments, 0);
  const collected = safeNum(perf?.EXPECTED_Monthly_installments_collected, 0);
  const dueToday = safeNum(perf?.Installments_Due_Today, 0);
  const overdueTotal = safeNum(perf?.Overdue_Collections, 0);
  const overdueCollected = safeNum(perf?.Overdue_Collections_Collected, 0);
  const overdueAll = safeNum(perf?.ALL_Overdue_Collections, 0);
  const salesBelowThreshold = safeNum(perf?.Sales_Deposits_Below_Threshold, 0);
  const overdueBelowThreshold = safeNum(perf?.Overdue_Below_Threshold, 0);

  const collectionRate = useMemo(() => percent(collected, expected), [collected, expected]);
  const overdueRecoveryRate = useMemo(() => percent(overdueCollected, overdueTotal), [overdueCollected, overdueTotal]);
  const remainingToExpected = Math.max(expected - collected, 0);
  const remainingOverdue = Math.max(overdueTotal - overdueCollected, 0);
  const belowThresholdRate = useMemo(
    () => percent(overdueBelowThreshold, salesBelowThreshold || (salesBelowThreshold + overdueBelowThreshold)),
    [overdueBelowThreshold, salesBelowThreshold]
  );

  // Executive attainment targets
  const target90Gap = Math.max(expected * 0.9 - collected, 0);
  const target95Gap = Math.max(expected * 0.95 - collected, 0);
  const target100Gap = Math.max(expected - collected, 0);

  // Pace calculations
  const now = new Date();
  const year = now.getFullYear();
  const month0 = now.getMonth();
  const day = now.getDate();
  const totalBizDays = businessDaysInMonth(year, month0);
  const elapsedBizDays = elapsedBusinessDaysInMonth(year, month0, day);
  const remainingBizDays = Math.max(totalBizDays - elapsedBizDays, 1);
  const avgPerBizDay = collected / Math.max(elapsedBizDays, 1);
  const neededPerBizDay = remainingToExpected / remainingBizDays;

  // Admin aggregates
  const deptList = adminStats?.department_overview || [];
  const totals = useMemo(() => {
    return deptList.reduce(
      (acc, d) => {
        acc.total += safeNum(d.employees_count, 0);
        acc.active += safeNum(d.active_employees_count, 0);
        acc.inactive += safeNum(d.inactive_employees_count, 0);
        return acc;
      },
      { total: 0, active: 0, inactive: 0 }
    );
  }, [deptList]);

  const activeRate = percent(totals.active, totals.total);

  // Gender
  const genderPieData = useMemo(() => {
    const male = Number(adminStats?.gender_distribution?.M || 0);
    const female = Number(adminStats?.gender_distribution?.F || 0);
    const unspecified = Number(adminStats?.gender_distribution?.null || 0);
    return [
      { name: "Male", value: male, color: "#93c5fd" }, 
      { name: "Female", value: female, color: "#86efac" }, 
      { name: "Unspecified", value: unspecified, color: "#c7d2fe" }, 
    ];
  }, [adminStats]);

  // Marital distribution
  const maritalData = useMemo(() => {
    const raw = (adminStats?.marital_distribution || {}) as Record<string, number | string>;
    const entries = [
      { key: "Single", color: "#a5b4fc" }, 
      { key: "Married", color: "#fde68a" }, 
      { key: "Divorced", color: "#fca5a5" }, 
      { key: "Separated", color: "#93c5fd" }, 
    ];
    return entries
      .map((e) => ({ name: e.key, value: safeNum(raw[e.key as keyof typeof raw] as number, 0), color: e.color }))
      .filter((d) => d.value > 0);
  }, [adminStats]);

  // Departments bar (top by employees)
  const deptBarData = useMemo(() => {
    const list = adminStats?.department_overview || [];
    return [...list]
      .sort((a, b) => b.employees_count - a.employees_count)
      .slice(0, 8)
      .map((d) => ({
        department: d.department.length > 14 ? `${d.department.slice(0, 14)}…` : d.department,
        employees: d.employees_count,
        active: d.active_employees_count,
        inactive: d.inactive_employees_count,
      }));
  }, [adminStats]);

  // Top departments by active rate
  const deptActiveRateData = useMemo(() => {
    const list = adminStats?.department_overview || [];
    return [...list]
      .map((d) => ({
        department: d.department.length > 14 ? `${d.department.slice(0, 14)}…` : d.department,
        activeRate: percent(d.active_employees_count, d.employees_count),
      }))
      .sort((a, b) => b.activeRate - a.activeRate)
      .slice(0, 8);
  }, [adminStats]);

  // Departments gender distribution (all departments)
  const deptGenderStackData = useMemo(() => {
    const dist = adminStats?.departments_gender_distribution || [];
    // compute totals per department
    const rows = dist
      .map((row) => {
        const male = row.gender_distribution.find((g) => g.gender === "M")?.total_gender_count || 0;
        const female = row.gender_distribution.find((g) => g.gender === "F")?.total_gender_count || 0;
        const none = row.gender_distribution.find((g) => g.gender === null)?.total_gender_count || 0;
        const total = male + female + none;
        return { department: row.department, male, female, none, total };
      })
      .sort((a, b) => b.total - a.total)
      .map((r) => ({
        department: r.department.length > 14 ? `${r.department.slice(0, 14)}…` : r.department,
        Male: r.male,
        Female: r.female,
        Unspecified: r.none,
      }));
    return rows;
  }, [adminStats]);

  // Charts tab: Threshold pressure donut and Overdue scope bars
  const thresholdDonutData = useMemo(() => {
    const notOverdue = Math.max(safeNum(salesBelowThreshold) - safeNum(overdueBelowThreshold), 0);
    return [
      { name: "Overdue < Threshold", value: safeNum(overdueBelowThreshold), color: "#fca5a5" }, // red-300
      { name: "Not Yet Overdue", value: notOverdue, color: "#93c5fd" }, // blue-300
    ];
  }, [salesBelowThreshold, overdueBelowThreshold]);

  const overdueScopeData = useMemo(() => {
    return [
      { name: "All Overdue", value: overdueAll },
      { name: "Core Overdue", value: overdueTotal },
      { name: "Recovered", value: overdueCollected },
    ];
  }, [overdueAll, overdueTotal, overdueCollected]);

  // Risk score (heuristic)
  const riskScore = clamp(
    0.6 * percent(remainingOverdue, expected || overdueAll || 1) +
      0.25 * belowThresholdRate +
      0.15 * (100 - collectionRate),
    0,
    100
  );

  // Scenario helpers
  const attainmentIfCollectDueToday = percent(collected + dueToday, expected);
  const attainmentIfRecover25RemainingOverdue = percent(collected + 0.25 * remainingOverdue, expected);

  if (loading) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-72">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
          <p className="text-red-700 mb-4">{error}</p>
          <Button onClick={fetchAll} variant="outline" className="border-red-300 text-red-700 hover:bg-red-100">
            <RefreshCw className="w-4 h-4 mr-2" /> Retry
          </Button>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header - toned down */}
        <Card className="border border-slate-200 bg-white shadow-sm dark:border-slate-700 dark:bg-slate-800">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
              <div>
                <h1 className="text-xl font-semibold text-slate-900 dark:text-slate-200">Directors Dashboard</h1>
                <p className="text-sm text-slate-500 dark:text-slate-200">Concise, decision-ready view of performance and workforce health</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="border-slate-200 text-slate-600 dark:text-slate-200"><Calendar className="w-3 h-3 mr-1" /> Updated {new Date().toLocaleTimeString()}</Badge>
                </div>
              </div>
              <Button size="sm" onClick={fetchAll} className="bg-slate-900 text-white hover:bg-slate-800">
                <RefreshCw className="w-4 h-4 mr-2" /> Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-white border border-slate-200 rounded-lg p-1 dark:bg-slate-700 dark:border-slate-600">
            <TabsTrigger value="overview" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Overview</TabsTrigger>
            <TabsTrigger value="administrative" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Administrative</TabsTrigger>
            <TabsTrigger value="performance" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Performance</TabsTrigger>
            <TabsTrigger value="charts" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Charts</TabsTrigger>
          </TabsList>

          {/* OVERVIEW */}
          <TabsContent value="overview" className="mt-6 space-y-6">
            {/* Executive summary with light accents */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 dark:bg-slate-800 dark:border-slate-700">
              <Card className="border border-blue-100 bg-blue-50 ">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Wallet className="w-4 h-4" />Expected Monthly Installments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-semibold text-slate-900">{formatCurrency(expected)}</div>
                  <p className="text-xs text-slate-600 mt-1">Target for the current cycle based on active payment schedules.</p>
                </CardContent>
              </Card>

              <Card className="border border-emerald-100 bg-emerald-50">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Coins className="w-4 h-4" />Expected Monthly Installments Collected</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-semibold text-slate-900">{formatCurrency(collected)}</div>
                  <p className="text-xs text-slate-600 mt-1">Actual receipts captured this cycle. Attainment {collectionRate.toFixed(1)}%.</p>
                </CardContent>
              </Card>

              <Card className="border border-amber-100 bg-amber-50">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><ShieldAlert className="w-4 h-4" /> Gap to Target</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-semibold text-slate-900">{formatCurrency(remainingToExpected)}</div>
                    <p className="text-xs text-slate-600 mt-1"> Attainment {collectionRate.toFixed(1)}%.</p>
                </CardContent>
              </Card>
            </div>

            {/* KPI Grid with light accents */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
              <KPI title="Installments Due Today" value={formatNumber(dueToday)} icon={AlertTriangle} hint="Requires immediate follow-up" bgClass="bg-rose-50" />
              <KPI title="Overdue - Total" value={formatCurrency(overdueTotal)} icon={TrendingDown} hint={`Collected: ${formatCurrency(overdueCollected)}`} bgClass="bg-orange-50" />
              <KPI title="Below Threshold (Sales)" value={formatNumber(salesBelowThreshold)} icon={Target} hint={`${formatNumber(overdueBelowThreshold)} overdue < threshold`} bgClass="bg-indigo-50" />
              <KPI title="Active Workforce" value={`${activeRate.toFixed(1)}%`} icon={Users} hint={`${formatNumber(totals.active)} / ${formatNumber(totals.total)} employees`} bgClass="bg-teal-50" />
            </div>

            {/* At-a-glance visuals: 1) Attainment Gauge 2) Pace vs Needed 3) Composite Risk */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* Collection Attainment Gauge */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Gauge className="w-4 h-4" /> Collection Attainment</CardTitle>
                </CardHeader>
                <CardContent style={{ height: 240 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <RadialBarChart innerRadius="60%" outerRadius="100%" data={[{ name: "Attainment", value: collectionRate }]} startAngle={90} endAngle={-270}>
                      <RadialBar minAngle={10} background clockWise dataKey="value" fill="#93c5fd" cornerRadius={8} />
                      <Legend content={() => (
                        <div className="text-center mt-2">
                          <div className="text-2xl font-semibold text-slate-900">{collectionRate.toFixed(1)}%</div>
                          <div className="text-xs text-slate-500">{formatCurrency(collected)} / {formatCurrency(expected)}</div>
                        </div>
                      )} />
                    </RadialBarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Pace vs Needed (replaces duplicate recovery gauge) */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700">Pace vs Needed (Business Days)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-slate-500">Business days: {elapsedBizDays} elapsed / {totalBizDays} total</div>
                  <div className="mt-3 space-y-3">
                    <div>
                      <div className="flex justify-between text-xs text-slate-600"><span>Current pace</span><span>{formatCurrency(avgPerBizDay)}</span></div>
                      <div className="w-full bg-slate-100 rounded-full h-2 mt-1">
                        <div className="h-2 rounded-full bg-blue-300" style={{ width: `${clamp((avgPerBizDay / Math.max(avgPerBizDay, neededPerBizDay)) * 100)}%` }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-xs text-slate-600"><span>Needed pace</span><span>{formatCurrency(neededPerBizDay)}</span></div>
                      <div className="w-full bg-slate-100 rounded-full h-2 mt-1">
                        <div className="h-2 rounded-full bg-amber-300" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-slate-500 mt-3">If current pace &lt; needed, we will miss target unless pace improves.</p>
                </CardContent>
              </Card>

              {/* Risk Indicator */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><ShieldAlert className="w-4 h-4" /> Composite Risk</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-semibold text-rose-700 mb-2">{riskScore.toFixed(0)}%</div>
                  <div className="w-full bg-rose-100 rounded-full h-2">
                    <div className="h-2 rounded-full bg-rose-400" style={{ width: `${riskScore}%` }}></div>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-xs mt-3 text-slate-600">
                    <div className="p-2 rounded border border-slate-200 bg-rose-50/50">Remaining overdue: {formatCurrency(remainingOverdue)}</div>
                    <div className="p-2 rounded border border-slate-200 bg-indigo-50/50">Below-threshold rate: {belowThresholdRate.toFixed(1)}%</div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Overview plain-language summary */}
            <Card className="border border-slate-200 bg-white shadow-sm">
              <CardHeader className="pb-1">
                <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Info className="w-4 h-4" /> Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 text-sm text-slate-700 space-y-1">
                  <li>We have collected {formatCurrency(collected)} out of {formatCurrency(expected)} (attainment {collectionRate.toFixed(1)}%).</li>
                  <li>The shortfall is {formatCurrency(remainingToExpected)}. Required daily pace: {formatCurrency(neededPerBizDay)} vs current {formatCurrency(avgPerBizDay)}.</li>
                  <li>Recovering {formatCurrency(overdueCollected)} of overdue so far; remaining overdue is {formatCurrency(remainingOverdue)}.</li>
                  <li>If we collect today’s due, attainment would be ~{attainmentIfCollectDueToday.toFixed(1)}%. Recovering 25% of remaining overdue lifts to ~{attainmentIfRecover25RemainingOverdue.toFixed(1)}%.</li>
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          {/* ADMINISTRATIVE */}
          <TabsContent value="administrative" className="mt-6 space-y-6">
            {/* Quick admin KPIs */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <KPI title="Departments" value={formatNumber(adminStats?.departments_count)} icon={Building2} bgClass="bg-blue-50" />
              <KPI title="Employees" value={formatNumber(adminStats?.employees_count)} icon={Users} bgClass="bg-emerald-50" />
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-12 gap-6">
              {/* Department Overview (more space + scrollable) */}
              <Card className="border border-slate-200 bg-white shadow-sm xl:col-span-8">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Building2 className="w-4 h-4" /> Department Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="max-h-[420px] overflow-auto">
                    <table className="w-full text-sm">
                      <thead className="sticky top-0 bg-white">
                        <tr className="text-left text-slate-500">
                          <th className="py-2 pr-2 font-medium">Department</th>
                          <th className="py-2 pr-2 font-medium">Total</th>
                          <th className="py-2 pr-2 font-medium">Active</th>
                          <th className="py-2 pr-2 font-medium">Inactive</th>
                          <th className="py-2 pr-2 font-medium">Active %</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(adminStats?.department_overview || []).map((d) => {
                          const total = safeNum(d.employees_count);
                          const active = safeNum(d.active_employees_count);
                          const inactive = safeNum(d.inactive_employees_count);
                          const ar = percent(active, total);
                          return (
                            <tr key={d.department} className="border-t border-slate-100">
                              <td className="py-2 pr-2 text-slate-800">{d.department}</td>
                              <td className="py-2 pr-2">{formatNumber(total)}</td>
                              <td className="py-2 pr-2 text-emerald-700">{formatNumber(active)}</td>
                              <td className="py-2 pr-2 text-rose-700">{formatNumber(inactive)}</td>
                              <td className="py-2 pr-2">{ar.toFixed(1)}%</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Workforce Health & Dept Active Rate */}
              <div className="xl:col-span-4 space-y-6">
                <Card className="border border-slate-200 bg-white shadow-sm">
                  <CardHeader className="pb-1">
                    <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Users className="w-4 h-4" /> Workforce Health</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-xs text-slate-500">Active Workforce</div>
                    <div className="text-2xl font-semibold text-slate-900">{activeRate.toFixed(1)}%</div>
                    <div className="w-full bg-slate-100 rounded-full h-2 mt-2">
                      <div className="h-2 rounded-full bg-emerald-400" style={{ width: `${activeRate}%` }}></div>
                    </div>
                    <div className="text-xs text-slate-500 mt-2">{formatNumber(totals.active)} active of {formatNumber(totals.total)} total</div>
                  </CardContent>
                </Card>

                {/* Top departments by Active % */}
                <Card className="border border-slate-200 bg-white shadow-sm">
                  <CardHeader className="pb-1">
                    <CardTitle className="text-sm font-medium text-slate-700">Top Departments by Active %</CardTitle>
                  </CardHeader>
                  <CardContent style={{ height: 260 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={deptActiveRateData} layout="vertical" margin={{ left: 80 }}>
                        <CartesianGrid stroke="#e2e8f0" strokeDasharray="3 3" />
                        <XAxis type="number" domain={[0, 100]} tickFormatter={(v) => `${v}%`} tick={{ fill: "#475569" }} />
                        <YAxis type="category" dataKey="department" width={120} tick={{ fill: "#475569" }} />
                        <ReTooltip formatter={(v: any) => [`${Number(v).toFixed(1)}%`, 'Active %']} />
                        <Bar dataKey="activeRate" fill="#93c5fd" radius={[6, 6, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Department Gender Distribution */}
            <Card className="border border-slate-200 bg-white shadow-sm">
              <CardHeader className="pb-1">
                <CardTitle className="text-sm font-medium text-slate-700">Department Gender Distribution (All)</CardTitle>
              </CardHeader>
              <CardContent style={{ height: 320 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={deptGenderStackData}>
                    <CartesianGrid stroke="#e2e8f0" strokeDasharray="3 3" />
                    <XAxis dataKey="department" tick={{ fill: "#475569" }} />
                    <YAxis tick={{ fill: "#475569" }} />
                    <ReTooltip />
                    <Bar dataKey="Male" stackId="g" fill="#93c5fd" />
                    <Bar dataKey="Female" stackId="g" fill="#86efac" />
                    <Bar dataKey="Unspecified" stackId="g" fill="#c7d2fe" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* PERFORMANCE */}
          <TabsContent value="performance" className="mt-6 space-y-6">
            {/* KPIs */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <KPI title="Installments Due Today" value={formatNumber(dueToday)} icon={AlertTriangle} hint="Payments expected today" bgClass="bg-rose-50" />
              <KPI title="Overdue (All)" value={formatCurrency(overdueAll || overdueTotal)} icon={TrendingDown} hint={`Core overdue: ${formatCurrency(overdueTotal)}`} bgClass="bg-orange-50" />
              <KPI title="Below Threshold (Sales)" value={formatNumber(salesBelowThreshold)} icon={Target} hint={`${formatNumber(overdueBelowThreshold)} overdue < threshold`} bgClass="bg-indigo-50" />
              <KPI title="Attainment" value={`${collectionRate.toFixed(1)}%`} icon={Gauge} hint={`${formatCurrency(collected)} / ${formatCurrency(expected)}`} bgClass="bg-blue-50" />
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Expected vs Collected vs Gap */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><TrendingUp className="w-4 h-4" /> Expected vs Collected vs Gap</CardTitle>
                  <p className="text-xs text-slate-500">Shows target against actual receipts and the shortfall to close.</p>
                </CardHeader>
                <CardContent style={{ height: 320 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={[{ name: "Current", Expected: expected, Collected: collected, Gap: remainingToExpected }]}>
                      <CartesianGrid stroke="#e2e8f0" strokeDasharray="3 3" />
                      <XAxis dataKey="name" tick={{ fill: "#475569" }} />
                      <YAxis tick={{ fill: "#475569" }} />
                      <ReTooltip formatter={(v: any, n: any) => [formatCurrency(Number(v)), n]} />
                      <Bar dataKey="Expected" fill="#93c5fd" radius={[6, 6, 0, 0]} />
                      <Bar dataKey="Collected" fill="#86efac" radius={[6, 6, 0, 0]} />
                      <Bar dataKey="Gap" fill="#fca5a5" radius={[6, 6, 0, 0]} />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Overdue Composition */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><BarChart3 className="w-4 h-4" /> Overdue Composition</CardTitle>
                  <p className="text-xs text-slate-500">Breaks down overdue into recovered vs remaining to focus collection efforts.</p>
                </CardHeader>
                <CardContent style={{ height: 320 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie data={[{ name: "Recovered", value: overdueCollected }, { name: "Remaining", value: remainingOverdue }]} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={90}>
                        <Cell fill="#86efac" />
                        <Cell fill="#fca5a5" />
                      </Pie>
                      <ReTooltip formatter={(v: any, n: any) => [formatCurrency(Number(v)), n]} />
                    </PieChart>
                  </ResponsiveContainer>
                  <div className="grid grid-cols-3 gap-2 text-xs mt-4">
                    <div className="p-2 rounded border border-slate-200 text-emerald-700 flex items-center gap-1"><ArrowUpRight className="w-3 h-3" /> {formatCurrency(overdueCollected)}</div>
                    <div className="p-2 rounded border border-slate-200 text-rose-700 flex items-center gap-1"><ArrowDownRight className="w-3 h-3" /> {formatCurrency(remainingOverdue)}</div>
                    <div className="p-2 rounded border border-slate-200 text-slate-700 flex items-center gap-1"><PercentIcon className="w-3 h-3" /> {overdueRecoveryRate.toFixed(1)}%</div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Gap Closure Plan */}
            <Card className="border border-slate-200 bg-white shadow-sm">
              <CardHeader className="pb-1">
                <CardTitle className="text-sm font-medium text-slate-700">Gap Closure Plan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-700">
                  <div className="p-3 rounded border border-slate-200 bg-blue-50/50">
                    <div className="text-xs text-slate-500">Required Pace</div>
                    <div className="font-semibold">{formatCurrency(neededPerBizDay)} / business day</div>
                    <div className="text-xs text-slate-500">for the remaining {remainingBizDays} business days</div>
                  </div>
                  <div className="p-3 rounded border border-slate-200 bg-emerald-50/50">
                    <div className="text-xs text-slate-500">Current Pace</div>
                    <div className="font-semibold">{formatCurrency(avgPerBizDay)} / business day</div>
                    <div className="text-xs text-slate-500">{elapsedBizDays} business days elapsed</div>
                  </div>
                  <div className="p-3 rounded border border-slate-200 bg-amber-50/50">
                    <div className="text-xs text-slate-500">Scenarios</div>
                    <div className="text-xs">If all due-today collected: ~{attainmentIfCollectDueToday.toFixed(1)}% attainment</div>
                    <div className="text-xs">If 25% of remaining overdue recovered: ~{attainmentIfRecover25RemainingOverdue.toFixed(1)}%</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* CHARTS */}
          <TabsContent value="charts" className="mt-6 space-y-6">
            {/* Charts Overview description */}
            <Card className="border border-slate-200 bg-white shadow-sm">
              <CardHeader className="pb-1">
                <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><Info className="w-4 h-4" /> Charts Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-700">These visuals highlight workforce composition and collection risk factors not covered in the Overview. Use them to spot capacity constraints and potential pressure on collections.</p>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Gender Distribution */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700 flex items-center gap-2"><PieIcon className="w-4 h-4" /> Gender Distribution</CardTitle>
                  <p className="text-xs text-slate-500">Workforce composition by gender; context for team diversity and deployment.</p>
                </CardHeader>
                <CardContent style={{ height: 320 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie data={genderPieData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={95} label>
                        {genderPieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <ReTooltip formatter={(v: any, n: any) => [v, n]} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Departments Active vs Inactive */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700">Departments (Active vs Inactive)</CardTitle>
                  <p className="text-xs text-slate-500">Headcount health by department. High inactive levels may signal capacity issues.</p>
                </CardHeader>
                <CardContent style={{ height: 320 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={deptBarData}>
                      <CartesianGrid stroke="#e2e8f0" strokeDasharray="3 3" />
                      <XAxis dataKey="department" tick={{ fill: "#475569" }} />
                      <YAxis tick={{ fill: "#475569" }} />
                      <ReTooltip />
                      <Bar dataKey="active" stackId="a" fill="#86efac" name="Active" />
                      <Bar dataKey="inactive" stackId="a" fill="#fca5a5" name="Inactive" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Collection-focused charts */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Threshold Pressure */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700">Below-Threshold Sales: Overdue vs Not Yet Overdue</CardTitle>
                  <p className="text-xs text-slate-500">Share of low-deposit sales already overdue vs those that will require attention soon.</p>
                </CardHeader>
                <CardContent style={{ height: 320 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie data={thresholdDonutData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={90}>
                        {thresholdDonutData.map((d, i) => (
                          <Cell key={`th-${i}`} fill={d.color} />
                        ))}
                      </Pie>
                      <ReTooltip formatter={(v: any, n: any) => [v, n]} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Overdue Scope Overview */}
              <Card className="border border-slate-200 bg-white shadow-sm">
                <CardHeader className="pb-1">
                  <CardTitle className="text-sm font-medium text-slate-700">Overdue Scope Overview</CardTitle>
                  <p className="text-xs text-slate-500">Scale of overdue universe including recoveries. Helps size the effort required.</p>
                </CardHeader>
                <CardContent style={{ height: 320 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={overdueScopeData}>
                      <CartesianGrid stroke="#e2e8f0" strokeDasharray="3 3" />
                      <XAxis dataKey="name" tick={{ fill: "#475569" }} />
                      <YAxis tick={{ fill: "#475569" }} />
                      <ReTooltip formatter={(v: any) => [formatCurrency(Number(v)), "Amount"]} />
                      <Bar dataKey="value" radius={[6, 6, 0, 0]}>
                        {overdueScopeData.map((entry, index) => (
                          <Cell key={`ov-${index}`} fill={["#93c5fd", "#fde68a", "#86efac"][index % 3]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};

export default DirectorsDashboardV2;