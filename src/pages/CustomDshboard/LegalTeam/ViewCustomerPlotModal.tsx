import { PrimaryButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";

interface ViewPlotDetailProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  plot: {
    plotNumber: string;
    customerName: string;
    marketer: string;
    status: string;
  } | null;
}

function ViewPlotDetail({ isOpen, onOpenChange, plot }: ViewPlotDetailProps) {
  if (!plot) return null;

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Offer Letter Results for Plot Number: ${plot.plotNumber}`}
      description={`Booking Payments for this Plot are ${plot.status}`}
      size="md"
      footer={
        <div className="flex justify-between w-full">
          <PrimaryButton variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </PrimaryButton>
          <div className="flex gap-2">
            <PrimaryButton variant="outline">Save as Draft</PrimaryButton>
            <PrimaryButton>Process Lead</PrimaryButton>
          </div>
        </div>
      }
    >
      <div className="py-2 space-y-4">
        <div className="border rounded-md p-4">
          <h3 className="text-sm font-medium mb-2">Customer Details</h3>
          <div className="space-y-3">
            <p>Customer Name: {plot.customerName}</p>
            <p>Marketer: {plot.marketer}</p>
            <p>Status: {plot.status}</p>
          </div>
        </div>

        <div className="border rounded-md p-4">
          <h3 className="text-sm font-medium mb-2">Lead Source</h3>
          <p>MARKETER'S- DATA</p>
        </div>

        <div className="border rounded-md p-4">
          <h3 className="text-sm font-medium mb-2">Payments Plan</h3>
          <div className="space-y-3">
            <p>PLOT NO: {plot.plotNumber}</p>
            <p>Selected Payment Plan</p>
            <p>
              3 Months @ Ksh 3,605,000 ( Deposit of Ksh 700,000.00 + 3 Installments of Ksh 968,333 each )
            </p>
          </div>
        </div>

        <div className="border rounded-md p-4">
          <h3 className="text-sm font-medium mb-2">Terms and Conditions</h3>
          <p>You confirmed that you read and agreed to the terms and conditions provided at step 5.</p>
        </div>
      </div>
    </BaseModal>
  );
}

export default ViewPlotDetail;