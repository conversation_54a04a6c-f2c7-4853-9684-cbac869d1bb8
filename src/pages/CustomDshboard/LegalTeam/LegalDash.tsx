import { useState, useMemo } from "react";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import ViewPlotDetail from "./ViewCustomerPlotModal";
import {
  Scale,
  FileText,
  CheckCircle,
  Eye,
  Building2,
  MapPin,
  DollarSign,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  AlertTriangle,
  Clock,
  Award,
  TrendingUp,
  Home,
  Building,
  Gavel,
  Shield,
  Users,
  BarChart3,
  PieChart,
  Activity
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useGetPlotsQuery, useGetPlotsStatisticsQuery } from "@/redux/slices/inventory";
import { PlotTypes, LegalMetrics } from "@/types/inventory";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { CustomActiveShapePieChart } from "@/components/custom/charts/PieChartVariants";

interface LegalMetric {
  title: string;
  value: number | string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  iconBg: string;
  change?: string;
  changeLabel?: string;
  trend?: "up" | "down" | "neutral";
}

function LegalDashboard() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPlot, setSelectedPlot] = useState<PlotTypes | null>(null);
  const [filters, setFilters] = useState({
    plot_status: "",
    plot_type: "",
    location: "",
    project: "",
  });

  // Fetch plots data
  const {
    data: plotsData,
    isLoading: plotsLoading,
    error: plotsError,
    refetch: refetchPlots
  } = useGetPlotsQuery({
    page: 1,
    page_size: 1000, // Get all plots for comprehensive analysis
    ...filters
  });

  // Calculate legal metrics from plots data
  const legalMetrics: LegalMetric[] = useMemo(() => {
    const plots = plotsData?.data?.results || [];
    const totalPlots = plots.length;
    const soldPlots = plots.filter(p => p.plot_status === 'Sold').length;
    const reservedPlots = plots.filter(p => p.plot_status === 'Reserved').length;
    const openPlots = plots.filter(p => p.plot_status === 'Open').length;
    const residentialPlots = plots.filter(p => p.plot_type === 'Residential').length;
    const commercialPlots = plots.filter(p => p.plot_type === 'Commercial').length;

    // Calculate total value
    const totalValue = plots.reduce((sum, plot) => sum + parseFloat(plot.cash_price || '0'), 0);
    const averagePrice = totalPlots > 0 ? totalValue / totalPlots : 0;

    return [
      {
        title: "Total Plots",
        value: totalPlots,
        icon: Building2,
        color: "text-blue-600 dark:text-blue-400",
        bgColor: "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20",
        iconBg: "bg-blue-500",
        change: `${totalPlots}`,
        changeLabel: "in inventory",
        trend: "neutral",
      },
      {
        title: "Sold Plots",
        value: soldPlots,
        icon: CheckCircle,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
        iconBg: "bg-green-500",
        change: `${((soldPlots / totalPlots) * 100).toFixed(1)}%`,
        changeLabel: "completion rate",
        trend: "up",
      },
      {
        title: "Reserved Plots",
        value: reservedPlots,
        icon: Clock,
        color: "text-amber-600 dark:text-amber-400",
        bgColor: "bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20",
        iconBg: "bg-amber-500",
        change: `${reservedPlots}`,
        changeLabel: "pending completion",
        trend: "neutral",
      },
      {
        title: "Available Plots",
        value: openPlots,
        icon: Home,
        color: "text-purple-600 dark:text-purple-400",
        bgColor: "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",
        iconBg: "bg-purple-500",
        change: `${openPlots}`,
        changeLabel: "ready for sale",
        trend: "up",
      },
      {
        title: "Total Value",
        value: `KES ${(totalValue / 1000000).toFixed(1)}M`,
        icon: DollarSign,
        color: "text-emerald-600 dark:text-emerald-400",
        bgColor: "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20",
        iconBg: "bg-emerald-500",
        change: `KES ${(averagePrice / 1000).toFixed(0)}K`,
        changeLabel: "average price",
        trend: "up",
      },
      {
        title: "Residential",
        value: residentialPlots,
        icon: Home,
        color: "text-indigo-600 dark:text-indigo-400",
        bgColor: "bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20",
        iconBg: "bg-indigo-500",
        change: `${((residentialPlots / totalPlots) * 100).toFixed(1)}%`,
        changeLabel: "of total plots",
        trend: "neutral",
      },
    ];
  }, [plotsData]);

  const columns: ColumnDef<PlotTypes>[] = [
    {
      accessorKey: "plot_no",
      header: "Plot Number",
      cell: ({ row }) => (
        <div className="font-medium text-blue-600 dark:text-blue-400">
          {row.getValue("plot_no")}
        </div>
      ),
    },
    {
      accessorKey: "plot_size",
      header: "Size (Acres)",
      cell: ({ row }) => (
        <div className="text-sm">
          {parseFloat(row.getValue("plot_size") as string).toFixed(2)}
        </div>
      ),
    },
    {
      accessorKey: "plot_type",
      header: "Type",
      cell: ({ row }) => (
        <Badge
          variant={row.getValue("plot_type") === "Commercial" ? "default" : "secondary"}
          className="text-xs"
        >
          {row.getValue("plot_type")}
        </Badge>
      ),
    },
    {
      accessorKey: "plot_status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("plot_status") as string;
        return (
          <Badge
            variant={
              status === "Sold"
                ? "default"
                : status === "Reserved"
                ? "secondary"
                : "outline"
            }
            className={`text-xs ${
              status === "Sold"
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : status === "Reserved"
                ? "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
            }`}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "location",
      header: "Location",
      cell: ({ row }) => (
        <div className="flex items-center space-x-1">
          <MapPin className="w-3 h-3 text-gray-400" />
          <span className="text-sm">{row.getValue("location") || "N/A"}</span>
        </div>
      ),
    },
    {
      accessorKey: "cash_price",
      header: "Price",
      cell: ({ row }) => (
        <div className="font-medium text-green-600 dark:text-green-400">
          KES {parseFloat(row.getValue("cash_price") as string).toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: "lr_no",
      header: "LR Number",
      cell: ({ row }) => (
        <div className="text-xs text-gray-600 dark:text-gray-400 max-w-32 truncate">
          {row.getValue("lr_no") || "Pending"}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <PrimaryButton
          variant="outline"
          size="sm"
          onClick={() => {
            setSelectedPlot(row.original);
            setIsModalOpen(true);
          }}
          aria-label={`View details for plot ${row.getValue("plot_no")}`}
        >
          <Eye className="w-4 h-4 mr-2" />
          View Details
        </PrimaryButton>
      ),
    },
  ];

  if (plotsLoading) {
    return (
      <Screen>
        <div className="space-y-8">
          <div className="flex items-center justify-center py-16">
            <SpinnerTemp type="spinner-double" size="lg" />
          </div>
        </div>
      </Screen>
    );
  }

  if (plotsError) {
    return (
      <Screen>
        <div className="space-y-8">
          <div className="flex items-center justify-center py-16">
            <Card className="max-w-md w-full">
              <CardContent className="p-8 text-center">
                <Activity className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Failed to Load Data
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Unable to fetch inventory data. Please try again later.
                </p>
                <PrimaryButton onClick={() => refetchPlots()}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </PrimaryButton>
              </CardContent>
            </Card>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-8">
        {/* Enhanced Header Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-emerald-600 to-blue-600 dark:from-indigo-800 dark:via-purple-800 dark:to-blue-800 rounded-2xl shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative px-8 py-12">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                    <Gavel className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold text-white">Legal Dashboard</h1>
                    <p className="text-indigo-100 text-lg">Comprehensive inventory and legal management</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-white/90">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span className="text-sm">{new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="w-5 h-5" />
                    <span className="text-sm">Legal Compliance</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <PrimaryButton
                  onClick={() => refetchPlots()}
                  className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30 transition-all duration-300"
                >
                  <RefreshCw className="w-5 h-5 mr-2" />
                  Refresh Data
                </PrimaryButton>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        {/* Enhanced Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {legalMetrics.map((metric, index) => (
            <Card
              key={metric.title}
              className={`${metric.bgColor} border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className={`p-3 rounded-xl ${metric.iconBg} shadow-lg`}>
                    <metric.icon className="h-6 w-6 text-white" />
                  </div>
                  {metric.trend && (
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                      metric.trend === 'up'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : metric.trend === 'down'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                    }`}>
                      {metric.trend === 'up' && <TrendingUp className="w-3 h-3" />}
                      {metric.trend === 'down' && <TrendingUp className="w-3 h-3 rotate-180" />}
                      {metric.trend === 'neutral' && <Activity className="w-3 h-3" />}
                    </div>
                  )}
                </div>
                <CardTitle className={`text-sm font-medium ${metric.color} mt-3`}>
                  {metric.title}
                </CardTitle>
              </CardHeader>

              <CardContent className="pb-2">
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                  {metric.value}
                </div>
              </CardContent>

              <CardFooter className="pt-0">
                {metric.change && metric.changeLabel && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <span className={`font-medium ${
                      metric.trend === 'up' ? 'text-green-600 dark:text-green-400' :
                      metric.trend === 'down' ? 'text-red-600 dark:text-red-400' :
                      'text-gray-600 dark:text-gray-400'
                    }`}>
                      {metric.change}
                    </span>
                    <span>{metric.changeLabel}</span>
                  </div>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* Enhanced Data Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
                    Plot Inventory Management
                  </CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Comprehensive view of all plots and their legal status
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <Download className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
                <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <Filter className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="w-full overflow-x-auto">
              <DataTable
                data={plotsData?.data?.results || []}
                columns={columns}
                enableToolbar={true}
                enableExportToExcel={true}
                enablePagination={true}
                enableColumnFilters={true}
                enableSorting={true}
                enablePrintPdf={true}
                title="Legal Plot Management"
                containerClassName="min-w-[700px] bg-white dark:bg-gray-800"
                tableClassName="w-full"
                tHeadClassName="bg-gray-50 dark:bg-gray-700"
                tHeadCellsClassName="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                tBodyClassName="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
                tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                tBodyCellsClassName="px-6 py-4 text-sm text-gray-900 dark:text-gray-100"
              />
            </div>
          </CardContent>
        </Card>

        {/* Plot Detail Modal */}
        {isModalOpen && selectedPlot && (
          <ViewPlotDetail
            isOpen={isModalOpen}
            onOpenChange={setIsModalOpen}
            plot={{
              plotNumber: selectedPlot.plot_no ?? "",
              customerName: selectedPlot.customer_name ?? "",
              marketer: selectedPlot.marketer ?? "",
              status: selectedPlot.plot_status ?? "",
            }}
          />
        )}
      </div>
    </Screen>
  );
}

export default LegalDashboard;