import { useGetDiasporaRegionTripDashboardQuery } from "@/redux/slices/teams";
import { useState } from "react";
import { useLocation } from "react-router-dom";

const DiasporaRegionTrip = () => {
  const location = useLocation();
  const tripId = location.pathname.split("/")[3];
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [tripsData, setTripsData] = useState([]);
  const [searchTrips, setSearchTrips] = useState("");

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useGetDiasporaRegionTripDashboardQuery({
    LeadSource_id: tripId,
  });

  console.log("object", dashboardData);

  return <div>DiasporaRegionTrip {tripId}</div>;
};

export default DiasporaRegionTrip;
