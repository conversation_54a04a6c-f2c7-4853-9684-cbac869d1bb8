import React, { useState } from "react";
import {
  DollarSign,
  Bar<PERSON>hart3,
  ArrowUpRight,
  RefreshCw,
  Calendar,
  CreditCard,
  AlertCircle,
  FileText,
  TrendingUp,
  Activity,
  Target,
  MapPin,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useGetDiasporaRegionTripDashboardQuery } from "@/redux/slices/teams";
import { useLocation } from "react-router-dom";

// Mock data based on the updated structure for fallback
const mockDashboardData = {
  leadsource: {
    id: 14,
    leadsource_id: 298,
    lead_source_subcategory: 1,
    name: "AFRICA DIASPORA MARKETING",
    description: "Africa Diaspora Marketing",
    qr_code: "",
    ref_code: "LS-Pew6otN",
    link: "https://engage360.optiven.co.ke/lead-form/?ls=TFMtUGV3Nm90Tg==",
    sales: 0,
    ongoing_sales: 0,
    dropped_sales: 0,
    completed_sales: 0,
    active_leads: 0,
    dormant_leads: 0,
    manager: null,
    manager_name: null,
    managing_team: null,
    last_updated: "2025-09-25T11:00:42.235281Z",
    created_at: "2025-09-23T13:52:43.984048Z",
  },
  markreting_month: ["2025-10-20", "2025-09-21"],
  MIB: {
    Daily_MIB: 0.0,
    Monthly_MIB: 0.0,
    Monthly_Transfer_Costs: 0.0,
    Monthly_Deposits: 0.0,
    Monthly_Additional_Deposits: 0.0,
    Monthly_Installments: 0.0,
  },
  Collections: {
    Installments_Due_Today: 0,
    Overdue_Collections_Collected: 0,
    Overdue_Collections: 0,
    ALL_Overdue_Collections: 0,
    Sales_Deposits_Below_Threshold: 0,
    Overdue_Below_Threshold: 0,
  },
  SalesOverview: {
    TotalPurchasePrices: 0,
    TotalPaid: 0,
    TotalOutstandingBalances: 0,
    TotalActiveSales: 0,
  },
  MonthlyExpectedCollections: {
    Total_Expected_Collections: 0,
    CurrentExpectedInstallments: 0,
    AccruedMissedInstallments: 0,
    OverdueInstallments: 0,
  },
};

interface GroupedCardProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  iconBg: string;
  iconColor: string;
  cardBg: string;
  borderColor: string;
  metrics: Array<{
    label: string;
    value: string | number;
    subtext?: string;
  }>;
  action?: () => void;
}

const GroupedCard: React.FC<GroupedCardProps> = ({
  title,
  icon: Icon,
  iconBg,
  iconColor,
  cardBg,
  borderColor,
  metrics,
  action,
}) => {
  const handleClick = () => {
    if (action) {
      action();
    }
  };

  return (
    <Card
      className={`${borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${cardBg}`}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div
            className={`w-8 h-8 rounded-lg flex items-center justify-center ${iconBg}`}
          >
            <Icon className={`w-4 h-4 ${iconColor}`} />
          </div>
          <ArrowUpRight className="w-3 h-3 text-gray-400" />
        </div>
        <div className="space-y-3">
          <p className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide">
            {title}
          </p>
          <div className="space-y-2">
            {metrics.map((metric, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {metric.label}
                </span>
                <div className="text-right">
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {typeof metric.value === "number"
                      ? metric.value.toLocaleString()
                      : metric.value}
                  </span>
                  {metric.subtext && (
                    <p className="text-xs text-gray-500">{metric.subtext}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const DiasporaRegionTrip = () => {
  const location = useLocation();
  const tripId = location.pathname.split("/")[4];
  const [isRefreshing, setIsRefreshing] = useState(false);

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useGetDiasporaRegionTripDashboardQuery({
    LeadSource_id: tripId,
  });

  // Use actual data if available, otherwise fall back to mock data
  const tripData = dashboardData;

  // Create grouped cards for better organization
  const groupedCards = [
    {
      title: "MIB Overview",
      icon: DollarSign,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      metrics: [
        {
          label: "Daily MIB",
          value: `KES ${tripData?.MIB?.Daily_MIB?.toLocaleString() || 0}`,
        },
        {
          label: "Monthly MIB",
          value: `KES ${tripData?.MIB?.Monthly_MIB?.toLocaleString() || 0}`,
        },
        {
          label: "Transfer Costs",
          value: `KES ${
            tripData?.MIB?.Monthly_Transfer_Costs?.toLocaleString() || 0
          }`,
          subtext: "Monthly",
        },
        {
          label: "Monthly Deposits",
          value: `KES ${
            tripData?.MIB?.Monthly_Deposits?.toLocaleString() || 0
          }`,
        },
      ],
    },
    {
      title: "Sales Overview",
      icon: BarChart3,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      metrics: [
        {
          label: "Total Purchase Prices",
          value: `KES ${
            tripData?.SalesOverview?.TotalPurchasePrices?.toLocaleString() || 0
          }`,
        },
        {
          label: "Total Paid",
          value: `KES ${
            tripData?.SalesOverview?.TotalPaid?.toLocaleString() || 0
          }`,
        },
        {
          label: "Outstanding Balances",
          value: `KES ${
            tripData?.SalesOverview?.TotalOutstandingBalances?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Active Sales",
          value: tripData?.SalesOverview?.TotalActiveSales || 0,
        },
      ],
    },
    {
      title: "Collections",
      icon: CreditCard,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      metrics: [
        {
          label: "Due Today",
          value: tripData?.Collections?.Installments_Due_Today || 0,
          subtext: "Installments",
        },
        {
          label: "Overdue Collected",
          value: `KES ${
            tripData?.Collections?.Overdue_Collections_Collected?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "All Overdue",
          value: `KES ${
            tripData?.Collections?.ALL_Overdue_Collections?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Below Threshold",
          value: tripData?.Collections?.Sales_Deposits_Below_Threshold || 0,
          subtext: "Sales deposits",
        },
      ],
    },
    {
      title: "Expected Collections",
      icon: Calendar,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      metrics: [
        {
          label: "Total Expected",
          value: `KES ${
            tripData?.MonthlyExpectedCollections?.Total_Expected_Collections?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Current Expected",
          value: `KES ${
            tripData?.MonthlyExpectedCollections?.CurrentExpectedInstallments?.toLocaleString() ||
            0
          }`,
          subtext: "Installments",
        },
        {
          label: "Missed Installments",
          value: `KES ${
            tripData?.MonthlyExpectedCollections?.AccruedMissedInstallments?.toLocaleString() ||
            0
          }`,
          subtext: "Accrued",
        },
        {
          label: "Overdue",
          value: `KES ${
            tripData?.MonthlyExpectedCollections?.OverdueInstallments?.toLocaleString() ||
            0
          }`,
          subtext: "Installments",
        },
      ],
    },
  ];

  const handleRefresh = () => {
    setIsRefreshing(true);
    refetch();
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  // Calculate key performance indicators
  const totalMIB =
    (tripData?.MIB?.Daily_MIB || 0) + (tripData?.MIB?.Monthly_MIB || 0);
  const collectionEfficiency = tripData?.Collections?.ALL_Overdue_Collections
    ? (
        ((tripData?.Collections?.Overdue_Collections_Collected || 0) /
          tripData?.Collections.ALL_Overdue_Collections) *
        100
      ).toFixed(1)
    : 0;
  const salesCompletion = tripData?.SalesOverview?.TotalPurchasePrices
    ? (
        ((tripData?.SalesOverview?.TotalPaid || 0) /
          tripData?.SalesOverview.TotalPurchasePrices) *
        100
      ).toFixed(1)
    : 0;

  if (isLoading) {
    return (
      <Screen>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Region Trip Dashboard
              </h1>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Region Trip Dashboard
              </h1>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </div>
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6 text-center">
                <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  Failed to load trip dashboard data
                </h3>
                <p className="text-red-600">
                  Please check your connection and try again.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {tripData.leadsource?.name || "Diaspora Region Trip Dashboard"}
              </h1>
              <p className="text-sm text-gray-600">
                {tripData.leadsource?.description || `Trip ID: ${tripId}`} -
                Financial performance and operational overview
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                Refresh
              </Button>
            </div>
          </div>

          {/* Marketing Month Info */}
          {tripData?.markreting_month &&
            tripData?.markreting_month.length > 0 && (
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      Marketing Period:{" "}
                      {tripData?.markreting_month.join(" to ")}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

          {/* Lead Source Information */}
          {tripData.leadsource && (
            <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 text-white">
                  {/* Lead Source Details */}
                  <div className="lg:col-span-2">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                        <MapPin className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold mb-1">
                          {tripData.leadsource.name}
                        </h3>
                        <p className="text-blue-100 text-sm mb-2">
                          {tripData.leadsource.description}
                        </p>
                        <div className="flex items-center space-x-4 text-xs">
                          <span className="bg-white/20 px-2 py-1 rounded">
                            ID: #{tripData.leadsource.leadsource_id}
                          </span>
                          <span className="bg-white/20 px-2 py-1 rounded font-mono">
                            {tripData.leadsource.ref_code}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                    <h4 className="text-sm font-bold mb-3 text-center">
                      Performance
                    </h4>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="text-center">
                        <p className="text-blue-200">Sales</p>
                        <p className="font-bold text-lg">
                          {tripData.leadsource.sales}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-blue-200">Leads</p>
                        <p className="font-bold text-lg">
                          {tripData.leadsource.active_leads}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-blue-200">Ongoing</p>
                        <p className="font-bold text-lg">
                          {tripData.leadsource.ongoing_sales}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-blue-200">Complete</p>
                        <p className="font-bold text-lg">
                          {tripData.leadsource.completed_sales}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Actions & Info */}
                  <div className="space-y-3">
                    {/* Manager */}
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                      <p className="text-blue-200 text-xs">Manager</p>
                      <p className="font-medium text-sm">
                        {tripData.leadsource.manager_name || "Not Assigned"}
                      </p>
                    </div>

                    {/* Lead Form Button */}
                    {tripData.leadsource.link && (
                      <Button
                        size="sm"
                        className="w-full bg-white/20 hover:bg-white/30 text-white border-white/30"
                        onClick={() =>
                          window.open(tripData.leadsource.link, "_blank")
                        }
                      >
                        <Activity className="w-3 h-3 mr-2" />
                        Lead Form
                      </Button>
                    )}

                    {/* Timeline */}
                    <div className="text-xs space-y-1">
                      <div className="flex justify-between">
                        <span className="text-blue-200">Created:</span>
                        <span>
                          {new Date(
                            tripData.leadsource.created_at
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-blue-200">Updated:</span>
                        <span>
                          {new Date(
                            tripData.leadsource.last_updated
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Key Performance Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Total MIB</p>
                    <p className="text-2xl font-bold">
                      KES {totalMIB.toLocaleString()}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">
                      Collection Efficiency
                    </p>
                    <p className="text-2xl font-bold">
                      {collectionEfficiency}%
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-blue-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">Sales Completion</p>
                    <p className="text-2xl font-bold">{salesCompletion}%</p>
                  </div>
                  <Target className="w-8 h-8 text-purple-200" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Grouped Dashboard Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {groupedCards.map((card) => (
              <GroupedCard key={card.title} {...card} />
            ))}
          </div>

          {/* Trip Information Section */}
          <div className="space-y-6">
            {/* Section Header */}
            <div className="text-start">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Trip Performance Overview
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Comprehensive financial and operational metrics for{" "}
                {tripData.leadsource?.name || `diaspora trip #${tripId}`}
              </p>
            </div>

            {/* Performance Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* MIB Summary Card */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-green-50 to-emerald-100 border-l-4 border-l-green-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                      <DollarSign className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-green-600 font-medium uppercase tracking-wide">
                        MIB
                      </p>
                      <p className="text-sm text-green-700">Summary</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Additional Deposits
                        </span>
                        <span className="text-sm font-bold text-green-700">
                          KES{" "}
                          {(
                            tripData?.MIB?.Monthly_Additional_Deposits || 0
                          ).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Monthly Installments
                        </span>
                        <span className="text-sm font-bold text-green-700">
                          KES{" "}
                          {(
                            tripData?.MIB?.Monthly_Installments || 0
                          ).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Collections Summary Card */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-orange-50 to-amber-100 border-l-4 border-l-orange-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center">
                      <CreditCard className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-orange-600 font-medium uppercase tracking-wide">
                        Collections
                      </p>
                      <p className="text-sm text-orange-700">Summary</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Overdue Collections
                        </span>
                        <span className="text-sm font-bold text-orange-700">
                          KES{" "}
                          {(
                            tripData?.Collections?.Overdue_Collections || 0
                          ).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Below Threshold
                        </span>
                        <span className="text-sm font-bold text-orange-700">
                          {tripData?.Collections?.Overdue_Below_Threshold || 0}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Sales Summary Card */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-blue-50 to-cyan-100 border-l-4 border-l-blue-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                      <BarChart3 className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-blue-600 font-medium uppercase tracking-wide">
                        Sales
                      </p>
                      <p className="text-sm text-blue-700">Summary</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Active Sales
                        </span>
                        <span className="text-sm font-bold text-blue-700">
                          {tripData?.SalesOverview?.TotalActiveSales || 0}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Payment Rate
                        </span>
                        <span className="text-sm font-bold text-blue-700">
                          {salesCompletion}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Performance Metrics Card */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-purple-50 to-violet-100 border-l-4 border-l-purple-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                      <TrendingUp className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-purple-600 font-medium uppercase tracking-wide">
                        Performance
                      </p>
                      <p className="text-sm text-purple-700">Metrics</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Collection Rate
                        </span>
                        <span className="text-sm font-bold text-purple-700">
                          {collectionEfficiency}%
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 font-medium">
                          Total MIB
                        </span>
                        <span className="text-sm font-bold text-purple-700">
                          KES {totalMIB.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Report Generation Section */}
          <Card className="border-0 shadow-lg bg-white">
            <div className="bg-primary p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-secondary">
                    Trip Report Generator
                  </h2>
                  <p className="text-secondary mt-1">
                    Generate detailed reports for this diaspora trip
                  </p>
                </div>
              </div>
            </div>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-green-50"
                >
                  <DollarSign className="w-5 h-5" />
                  <span>MIB Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-blue-50"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>Sales Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-orange-50"
                >
                  <CreditCard className="w-5 h-5" />
                  <span>Collections Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-purple-50"
                >
                  <Activity className="w-5 h-5" />
                  <span>Performance Report</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default DiasporaRegionTrip;
