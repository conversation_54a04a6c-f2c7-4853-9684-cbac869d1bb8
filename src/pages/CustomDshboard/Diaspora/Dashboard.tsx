import React, { useState } from "react";
import {
  Users,
  DollarSign,
  BarChart3,
  ArrowUpRight,
  RefreshCw,
  Calendar,
  Globe,
  CreditCard,
  AlertCircle,
  FileText,
  MapPin,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useGetDiasporaDashboardQuery } from "@/redux/slices/teams";
import { Link } from "react-router-dom";

// Mock data based on the provided structure for fallback
const mockDashboardData = {
  markreting_month: ["2025-10-20", "2025-09-21"],
  MIB: {
    Daily_MIB: 0.0,
    Monthly_MIB: 0.0,
    Monthly_Transfer_Costs: 0.0,
    Monthly_Deposits: 0.0,
    Monthly_Additional_Deposits: 0.0,
    Monthly_Installments: 0.0,
  },
  Collections: {
    Installments_Due_Today: 0,
    Overdue_Collections_Collected: 0,
    Overdue_Collections: 0,
    ALL_Overdue_Collections: 0,
    Sales_Deposits_Below_Threshold: 0,
    Overdue_Below_Threshold: 0,
  },
  SalesOverview: {
    TotalPurchasePrices: 0,
    TotalPaid: 0,
    TotalOutstandingBalances: 0,
    TotalActiveSales: 0,
  },
  MonthlyExpectedCollections: {
    Total_Expected_Collections: 0,
    CurrentExpectedInstallments: 0,
    AccruedMissedInstallments: 0,
    OverdueInstallments: 0,
  },
  DiasporaRegions: [
    {
      cat_lead_source_id: 1,
      name: "AFRICA",
      manager_name: "Angela Jeptoo Marindich",
      total_paid_sum: 0.0,
      leadsource_count: 47,
    },
    {
      cat_lead_source_id: 2,
      name: "EUROPE & UK",
      manager_name: "Diana Ross Nyambura",
      total_paid_sum: 0.0,
      leadsource_count: 22,
    },
    {
      cat_lead_source_id: 3,
      name: "AUSTRALIA",
      manager_name: "Lucy Michere Maina",
      total_paid_sum: 0.0,
      leadsource_count: 13,
    },
    {
      cat_lead_source_id: 5,
      name: "USA",
      manager_name: "Fidelis Wanjiku Kariuki",
      total_paid_sum: 0.0,
      leadsource_count: 23,
    },
    {
      cat_lead_source_id: 6,
      name: "CANADA",
      manager_name: "Martha Wanjiku Ng'ang'a",
      total_paid_sum: 0.0,
      leadsource_count: 4,
    },
    {
      cat_lead_source_id: 7,
      name: "ASIA",
      manager_name: "Catherine Khasoa Khaemba",
      total_paid_sum: 0.0,
      leadsource_count: 20,
    },
    {
      cat_lead_source_id: 36,
      name: "AMERICA",
      manager_name: "Teresia Muthoni Njoroge Ms",
      total_paid_sum: 0.0,
      leadsource_count: 7,
    },
    {
      cat_lead_source_id: 37,
      name: "CEO OFFICE",
      manager_name: "William Nagilae Nagilae",
      total_paid_sum: 0.0,
      leadsource_count: 6,
    },
    {
      cat_lead_source_id: 38,
      name: "UK",
      manager_name: "Lilian Njeri Waweru",
      total_paid_sum: 0.0,
      leadsource_count: 16,
    },
  ],
};

interface GroupedCardProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  iconBg: string;
  iconColor: string;
  cardBg: string;
  borderColor: string;
  metrics: Array<{
    label: string;
    value: string | number;
    subtext?: string;
  }>;
  action?: () => void;
}

interface RegionCardProps {
  region: {
    cat_lead_source_id: number;
    name: string;
    manager_name: string;
    total_paid_sum: number;
    leadsource_count: number;
  };
}

const GroupedCard: React.FC<GroupedCardProps> = ({
  title,
  icon: Icon,
  iconBg,
  iconColor,
  cardBg,
  borderColor,
  metrics,
  action,
}) => {
  const handleClick = () => {
    if (action) {
      action();
    }
  };

  return (
    <Card
      className={`${borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${cardBg}`}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div
            className={`w-8 h-8 rounded-lg flex items-center justify-center ${iconBg}`}
          >
            <Icon className={`w-4 h-4 ${iconColor}`} />
          </div>
          <ArrowUpRight className="w-3 h-3 text-gray-400" />
        </div>
        <div className="space-y-3">
          <p className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide">
            {title}
          </p>
          <div className="space-y-2">
            {metrics.map((metric, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {metric.label}
                </span>
                <div className="text-right">
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {typeof metric.value === "number"
                      ? metric.value.toLocaleString()
                      : metric.value}
                  </span>
                  {metric.subtext && (
                    <p className="text-xs text-gray-500">{metric.subtext}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const RegionCard: React.FC<RegionCardProps> = ({ region }) => {
  return (
    <Card className="border-2 border-blue-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 bg-gradient-to-br from-blue-50 to-blue-100">
      <CardContent className="p-4">
        <Link to={`/diaspora-dashboard/regions/${region.cat_lead_source_id}`}>
          <div className="flex items-center justify-between mb-2">
            <div className="w-8 h-8 rounded-lg flex items-center justify-center bg-blue-100">
              <Globe className="w-4 h-4 text-blue-600" />
            </div>
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              {region.leadsource_count} lead sources
            </span>
          </div>
          <div className="space-y-2">
            <h3 className="text-sm font-bold text-gray-900 uppercase tracking-wide">
              {region.name}
            </h3>
            <p
              className="text-xs text-gray-600 truncate"
              title={region.manager_name}
            >
              {region.manager_name}
            </p>
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-500">Total Paid</span>
              <span className="text-sm font-bold text-green-600">
                KES {region.total_paid_sum.toLocaleString()}
              </span>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

const Dashboard = () => {
  const {
    data: diasporaData,
    isLoading,
    error,
    refetch,
  } = useGetDiasporaDashboardQuery({});
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use actual data if available, otherwise fall back to mock data
  const dashboardData = diasporaData || mockDashboardData;

  console.log("Diaspora dashboard data:", dashboardData);

  // Create grouped cards for better organization
  const groupedCards = [
    {
      title: "MIB Overview",
      icon: DollarSign,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      metrics: [
        {
          label: "Daily MIB",
          value: `KES ${dashboardData.MIB?.Daily_MIB?.toLocaleString() || 0}`,
        },
        {
          label: "Monthly MIB",
          value: `KES ${dashboardData.MIB?.Monthly_MIB?.toLocaleString() || 0}`,
        },
        {
          label: "Monthly Transfer Costs",
          value: `KES ${
            dashboardData.MIB?.Monthly_Transfer_Costs?.toLocaleString() || 0
          }`,
          subtext: "",
        },
        {
          label: "Monthly Deposits",
          value: `KES ${
            dashboardData.MIB?.Monthly_Deposits?.toLocaleString() || 0
          }`,
          subtext: "",
        },
      ],
    },
    {
      title: "Sales Overview",
      icon: BarChart3,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      metrics: [
        {
          label: "Total Purchase Prices",
          value: `KES ${
            dashboardData.SalesOverview?.TotalPurchasePrices?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Total Paid",
          value: `KES ${
            dashboardData.SalesOverview?.TotalPaid?.toLocaleString() || 0
          }`,
        },
        {
          label: "Outstanding Balances",
          value: `KES ${
            dashboardData.SalesOverview?.TotalOutstandingBalances?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Active Sales",
          value: dashboardData.SalesOverview?.TotalActiveSales || 0,
        },
      ],
    },
    {
      title: "Collections",
      icon: CreditCard,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      metrics: [
        {
          label: "Installments Due Today",
          value: dashboardData.Collections?.Installments_Due_Today || 0,
          subtext: "",
        },
        {
          label: "Overdue Collected",
          value: dashboardData.Collections?.Overdue_Collections_Collected || 0,
        },
        {
          label: "All Overdue",
          value: dashboardData.Collections?.ALL_Overdue_Collections || 0,
        },
        {
          label: "Sales deposits Below Threshold",
          value: dashboardData.Collections?.Sales_Deposits_Below_Threshold || 0,
          subtext: "",
        },
      ],
    },
    {
      title: "Expected Collections",
      icon: Calendar,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      metrics: [
        {
          label: "Total Expected",
          value: `KES ${
            dashboardData.MonthlyExpectedCollections?.Total_Expected_Collections?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Installments Current Expected",
          value:
            dashboardData.MonthlyExpectedCollections
              ?.CurrentExpectedInstallments || 0,
          subtext: "",
        },
        {
          label: "Accrued Missed Installments",
          value:
            dashboardData.MonthlyExpectedCollections
              ?.AccruedMissedInstallments || 0,
          subtext: "",
        },
        {
          label: "Installments Overdue",
          value:
            dashboardData.MonthlyExpectedCollections?.OverdueInstallments || 0,
          subtext: "",
        },
      ],
    },
  ];

  const handleRefresh = () => {
    setIsRefreshing(true);
    refetch();
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Dashboard
              </h1>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Dashboard
              </h1>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </div>
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6 text-center">
                <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  Failed to load dashboard data
                </h3>
                <p className="text-red-600">
                  Please check your connection and try again.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Dashboard
              </h1>
              <p className="text-sm text-gray-600">
                Global diaspora operations and financial overview
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                Refresh
              </Button>
            </div>
          </div>

          {/* Marketing Month Info */}
          {dashboardData.markreting_month &&
            dashboardData.markreting_month.length > 0 && (
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      Marketing Period:{" "}
                      {dashboardData.markreting_month.join(" to ")}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Total Regions</p>
                    <p className="text-2xl font-bold">
                      {dashboardData.DiasporaRegions?.length || 0}
                    </p>
                  </div>
                  <MapPin className="w-8 h-8 text-green-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">Total Leads</p>
                    <p className="text-2xl font-bold">
                      {dashboardData.DiasporaRegions?.reduce(
                        (sum: any, region: any) =>
                          sum + region.leadsource_count,
                        0
                      ) || 0}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-blue-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">Total Collections</p>
                    <p className="text-2xl font-bold">
                      KES{" "}
                      {dashboardData.DiasporaRegions?.reduce(
                        (sum: any, region: any) => sum + region.total_paid_sum,
                        0
                      ).toLocaleString() || 0}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-200" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Grouped Dashboard Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {groupedCards.map((card) => (
              <GroupedCard key={card.title} {...card} />
            ))}
          </div>

          {/* Diaspora Regions Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Globe className="w-5 h-5 mr-2 text-blue-600" />
                Diaspora Regions
              </h2>
              <span className="text-sm text-gray-600">
                {dashboardData.DiasporaRegions?.length || 0} regions
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {dashboardData.DiasporaRegions?.map((region: any) => (
                <RegionCard key={region.cat_lead_source_id} region={region} />
              ))}
            </div>
          </div>

          {/* Report Generation Section */}
          <Card className="border-0 shadow-lg bg-white">
            <div className="bg-primary p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-secondary">
                    Diaspora Report Generator
                  </h2>
                  <p className="text-secondary mt-1">
                    Generate comprehensive diaspora performance reports
                  </p>
                </div>
              </div>
            </div>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button
                  variant="secondary"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-green-50"
                >
                  <DollarSign className="w-5 h-5" />
                  <span>MIB Report</span>
                </Button>
                <Button
                  variant="secondary"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-blue-50"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>Sales Report</span>
                </Button>
                <Button
                  variant="secondary"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-orange-50"
                >
                  <CreditCard className="w-5 h-5" />
                  <span>Collections Report</span>
                </Button>
                <Button
                  variant="secondary"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-purple-50"
                >
                  <Globe className="w-5 h-5" />
                  <span>Regions Report</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default Dashboard;
