import React, { useEffect, useState } from "react";
import {
  Users,
  DollarSign,
  BarChart3,
  ArrowUpRight,
  RefreshCw,
  Calendar,
  Globe,
  CreditCard,
  AlertCircle,
  FileText,
  MapPin,
  Building,
  Target,
  SearchIcon,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useGetDiasporaRegionsDashboardQuery } from "@/redux/slices/teams";
import { Link, useLocation } from "react-router-dom";

interface GroupedCardProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  iconBg: string;
  iconColor: string;
  cardBg: string;
  borderColor: string;
  metrics: Array<{
    label: string;
    value: string | number;
    subtext?: string;
  }>;
  action?: () => void;
}

interface RegionCardProps {
  region: {
    id: number;
    name: string;
    description: string;
    ongoing_sales: number;
    dropped_sales: number;
    completed_sales: number;
    active_leads: number;
    dormant_leads: number;
    last_updated: string;
    manager_name: string | null;
    manager_id: number | null;
  };
}

const GroupedCard: React.FC<GroupedCardProps> = ({
  title,
  icon: Icon,
  iconBg,
  iconColor,
  cardBg,
  borderColor,
  metrics,
  action,
}) => {
  const handleClick = () => {
    if (action) {
      action();
    }
  };

  return (
    <Card
      className={`${borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${cardBg}`}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div
            className={`w-8 h-8 rounded-lg flex items-center justify-center ${iconBg}`}
          >
            <Icon className={`w-4 h-4 ${iconColor}`} />
          </div>
          <ArrowUpRight className="w-3 h-3 text-gray-400" />
        </div>
        <div className="space-y-3">
          <p className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide">
            {title}
          </p>
          <div className="space-y-2">
            {metrics.map((metric, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {metric.label}
                </span>
                <div className="text-right">
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {typeof metric.value === "number"
                      ? metric.value.toLocaleString()
                      : metric.value}
                  </span>
                  {metric.subtext && (
                    <p className="text-xs text-gray-500">{metric.subtext}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const RegionCard: React.FC<RegionCardProps> = ({ region }) => {
  const totalSales =
    region.ongoing_sales + region.dropped_sales + region.completed_sales;
  const totalLeads = region.active_leads + region.dormant_leads;
  const lastUpdated = new Date(region.last_updated).toLocaleDateString();

  return (
    <Card className="border-2 border-blue-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 bg-gradient-to-br from-blue-50 to-blue-100">
      <CardContent className="p-4">
        <Link to={`/diaspora-dashboard/regions/trip/${region.id}`}>
          <div className="flex items-center justify-between mb-3">
            <div className="w-8 h-8 rounded-lg flex items-center justify-center bg-blue-100">
              <Building className="w-4 h-4 text-blue-600" />
            </div>
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              ID: {region.id}
            </span>
          </div>
          <div className="space-y-2">
            <h3 className="text-sm font-bold text-gray-900 uppercase tracking-wide line-clamp-2">
              {region.name}
            </h3>
            <p
              className="text-xs text-gray-600 line-clamp-2"
              title={region.description}
            >
              {region.description}
            </p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-500">Sales:</span>
                <span className="font-semibold text-green-600">
                  {totalSales}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Leads:</span>
                <span className="font-semibold text-blue-600">
                  {totalLeads}
                </span>
              </div>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Manager:</span>
                <span className="text-xs font-medium text-gray-700">
                  {region.manager_name || "Not Assigned"}
                </span>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span className="text-xs text-gray-500">Updated:</span>
                <span className="text-xs text-gray-600">{lastUpdated}</span>
              </div>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

const DiasporaRegion = () => {
  const location = useLocation();
  const regionId = location.pathname.split("/")[3];
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [tripsData, setTripsData] = useState([]);
  const [searchTrips, setSearchTrips] = useState("");

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useGetDiasporaRegionsDashboardQuery({
    Region_Id: regionId,
  });

  useEffect(() => {
    if (dashboardData && dashboardData.DiasporaRegions) {
      setTripsData(dashboardData.DiasporaRegions);
    }
  }, [dashboardData]);

  // Create grouped cards for better organization
  const groupedCards = [
    {
      title: "MIB Overview",
      icon: DollarSign,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      cardBg: "bg-gradient-to-br from-green-50 to-green-100",
      borderColor: "border-green-200",
      metrics: [
        {
          label: "Daily MIB",
          value: `KES ${dashboardData?.MIB?.Daily_MIB?.toLocaleString() || 0}`,
        },
        {
          label: "Monthly MIB",
          value: `KES ${
            dashboardData?.MIB?.Monthly_MIB?.toLocaleString() || 0
          }`,
        },
        {
          label: "Transfer Costs",
          value: `KES ${
            dashboardData?.MIB?.Monthly_Transfer_Costs?.toLocaleString() || 0
          }`,
          subtext: "Monthly",
        },
        {
          label: "Installments",
          value: `KES ${
            dashboardData?.MIB?.Monthly_Installments?.toLocaleString() || 0
          }`,
          subtext: "Monthly",
        },
      ],
    },
    {
      title: "Sales Overview",
      icon: BarChart3,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      metrics: [
        {
          label: "Total Purchase Prices",
          value: `KES ${
            dashboardData?.SalesOverview?.TotalPurchasePrices?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Total Paid",
          value: `KES ${
            dashboardData?.SalesOverview?.TotalPaid?.toLocaleString() || 0
          }`,
        },
        {
          label: "Outstanding Balances",
          value: `KES ${
            dashboardData?.SalesOverview?.TotalOutstandingBalances?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Active Sales",
          value: dashboardData?.SalesOverview?.TotalActiveSales || 0,
        },
      ],
    },
    {
      title: "Collections",
      icon: CreditCard,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      metrics: [
        {
          label: "Due Today",
          value: dashboardData?.Collections?.Installments_Due_Today || 0,
          subtext: "Installments",
        },
        {
          label: "Overdue Collected",
          value: `KES ${
            dashboardData?.Collections?.Overdue_Collections_Collected?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "All Overdue",
          value: `KES ${
            dashboardData?.Collections?.ALL_Overdue_Collections?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Below Threshold",
          value:
            dashboardData?.Collections?.Sales_Deposits_Below_Threshold || 0,
          subtext: "Sales deposits",
        },
      ],
    },
    {
      title: "Expected Collections",
      icon: Calendar,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      metrics: [
        {
          label: "Total Expected",
          value: `KES ${
            dashboardData?.MonthlyExpectedCollections?.Total_Expected_Collections?.toLocaleString() ||
            0
          }`,
        },
        {
          label: "Current Expected",
          value: `KES ${
            dashboardData?.MonthlyExpectedCollections?.CurrentExpectedInstallments?.toLocaleString() ||
            0
          }`,
          subtext: "Installments",
        },
        {
          label: "Missed Installments",
          value: `KES ${
            dashboardData?.MonthlyExpectedCollections?.AccruedMissedInstallments?.toLocaleString() ||
            0
          }`,
          subtext: "Accrued",
        },
        {
          label: "Overdue",
          value: `KES ${
            dashboardData?.MonthlyExpectedCollections?.OverdueInstallments?.toLocaleString() ||
            0
          }`,
          subtext: "Installments",
        },
      ],
    },
  ];

  useEffect(() => {
    if (tripsData && searchTrips) {
      const filteredRegions = tripsData.filter((region: any) =>
        region.name.toLowerCase().includes(searchTrips.toLowerCase())
      );
      setTripsData(filteredRegions);
    } else if (tripsData && !searchTrips) {
      refetch();
    }
  }, [searchTrips]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    refetch();
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Regions Dashboard
              </h1>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Diaspora Regions Dashboard
              </h1>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </div>
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6 text-center">
                <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  Failed to load dashboard data
                </h3>
                <p className="text-red-600">
                  Please check your connection and try again.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {dashboardData?.Region_Details?.name} Diaspora Regions Dashboard
              </h1>
              <p className="text-sm text-gray-600">
                Regional diaspora operations and financial overview
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                Refresh
              </Button>
            </div>
          </div>

          {/* Marketing Month Info */}
          {dashboardData?.marketing_month &&
            dashboardData?.marketing_month.length > 0 && (
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      Marketing Period:{" "}
                      {dashboardData?.marketing_month.join(" to ")}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Total Regions</p>
                    <p className="text-2xl font-bold">
                      {tripsData?.length || 0}
                    </p>
                  </div>
                  <MapPin className="w-8 h-8 text-green-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">Total Sales</p>
                    <p className="text-2xl font-bold">
                      {tripsData?.reduce(
                        (sum: any, region: any) =>
                          sum +
                          region.ongoing_sales +
                          region.dropped_sales +
                          region.completed_sales,
                        0
                      ) || 0}
                    </p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-blue-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">Total Leads</p>
                    <p className="text-2xl font-bold">
                      {tripsData?.reduce(
                        (sum: any, region: any) =>
                          sum + region.active_leads + region.dormant_leads,
                        0
                      ) || 0}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-purple-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm">Managed Regions</p>
                    <p className="text-2xl font-bold">
                      {tripsData?.filter((region: any) => region.manager_name)
                        .length || 0}
                    </p>
                  </div>
                  <Target className="w-8 h-8 text-orange-200" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Grouped Dashboard Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {groupedCards.map((card) => (
              <GroupedCard key={card.title} {...card} />
            ))}
          </div>

          {/* Diaspora Regions Section */}
          <div className="my-3">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Globe className="w-5 h-5 mr-2 text-blue-600" />
                {dashboardData?.Region_Details?.name} Diaspora Regions & Tours
              </h2>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {tripsData?.length || 0} regions Trips
                </span>
                <div className="flex items-center border border-gray-300 rounded-md ml-4 pr-2 py-1">
                  <input
                    name="searchTrips"
                    value={searchTrips}
                    onChange={(e) => setSearchTrips(e.target.value)}
                    type="search"
                    placeholder="Search Region Trips..."
                    className="border-none text-gray-400 outline-none bg-transparent ml-2 text-sm "
                  />
                  <SearchIcon className="text-gray-300" />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 h-[50vh] overflow-y-auto pr-2">
              {tripsData?.map((region: any) => (
                <RegionCard key={region.id} region={region} />
              ))}
            </div>
          </div>

          {/* Report Generation Section */}
          <Card className="border-0 shadow-lg bg-white">
            <div className="bg-primary p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-secondary">
                    Diaspora Regions Report Generator
                  </h2>
                  <p className="text-secondary mt-1">
                    Generate comprehensive regional performance reports
                  </p>
                </div>
              </div>
            </div>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-green-50"
                >
                  <DollarSign className="w-5 h-5" />
                  <span>MIB Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-blue-50"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>Sales Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-orange-50"
                >
                  <CreditCard className="w-5 h-5" />
                  <span>Collections Report</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-12 flex items-center justify-center space-x-2 hover:bg-purple-50"
                >
                  <Globe className="w-5 h-5" />
                  <span>Regions Report</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default DiasporaRegion;
