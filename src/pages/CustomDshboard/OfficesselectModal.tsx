import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, X, Building2, Calendar, CheckCircle, ArrowLeft, CalendarDays, Clock } from "lucide-react";
import { motion } from "framer-motion";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import OfficeTableModal from "./OfficeModal";
import { useGethrDashboardQuery, useGetOfficePeriodsQuery } from "@/redux/slices/hrDashboardApiSlice";

interface DateRange {
  from?: Date;
  to?: Date;
}

interface ReportsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onViewReport?: (range: DateRange) => void;
}

// TypeScript interfaces for API data
interface OfficeData {
  office: string;
  total_marketers: number;
  current_period: {
    period_name: string;
    start_date: string;
    end_date: string;
    target: number;
    achieved: number;
    progress: number;
  };
}

interface PeriodData {
  period_name: string;
  start_date: string;
  end_date: string;
  target: number;
  achieved: number;
  progress: number;
  period_start_date?: string;
  period_end_date?: string;
}



const DateDisplay: React.FC<{ label: string; date?: Date }> = ({ label, date }) => (
  <div className="flex flex-col">
    <span className="text-xs text-gray-500 dark:text-gray-400">{label}</span>
    <Badge
      variant="outline"
      className={cn(
        "mt-1 font-semibold",
        date
          ? "bg-gray-50 dark:bg-gray-800"
          : "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500"
      )}
    >
      {date ? format(date, "PPP") : "Not selected"}
    </Badge>
  </div>
);

export default function OfficeReportsModal({
  open,
  onOpenChange,
  onViewReport,
}: ReportsModalProps) {
  const [selectedOffice, setSelectedOffice] = useState<string>("");
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showReportsTable, setShowReportsTable] = useState(false);
  const [step, setStep] = useState<"office" | "period">("office");
  const [periodSelectionMode, setPeriodSelectionMode] = useState<"predefined" | "custom">("predefined");
  const [customDateRange, setCustomDateRange] = useState<DateRange>({});
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);

  // Fetch offices data
  const { data: officesData, isLoading: isLoadingOffices, error: officesError } = useGethrDashboardQuery({});

  // Fetch periods for selected office
  const { data: periodsData, isLoading: isLoadingPeriods, error: periodsError } = useGetOfficePeriodsQuery(
    { office: selectedOffice },
    { skip: !selectedOffice }
  );

  const handleOfficeSelect = (office: string) => {
    setSelectedOffice(office);
    setSelectedPeriod(null);
    setStep("period");
  };

  const handlePeriodSelect = (period: PeriodData) => {
    setSelectedPeriod(period);
    // Automatically open the report when a period is selected
    handleViewReport(period);
  };

  const handleViewReport = (period?: PeriodData) => {
    const periodToUse = period || selectedPeriod;

    if (periodSelectionMode === "predefined" && !periodToUse) return;
    if (periodSelectionMode === "custom" && (!customDateRange.from || !customDateRange.to)) return;

    setIsLoading(true);
    try {
      const dateRange: DateRange = periodSelectionMode === "predefined"
        ? {
          from: new Date(periodToUse!.start_date),
          to: new Date(periodToUse!.end_date),
        }
        : customDateRange;

      onViewReport?.(dateRange);
      setShowReportsTable(true);
    } finally {
      setTimeout(() => setIsLoading(false), 500);
    }
  };

  const handleReset = () => {
    setSelectedOffice("");
    setSelectedPeriod(null);
    setCustomDateRange({});
    setShowReportsTable(false);
    setStep("office");
    setPeriodSelectionMode("predefined");
  };

  const handleBack = () => {
    if (step === "period") {
      setStep("office");
      setSelectedPeriod(null);
      setCustomDateRange({});
    }
  };

  const handleCustomDateSelect = (type: "from" | "to", date: Date | undefined) => {
    setCustomDateRange(prev => ({
      ...prev,
      [type]: date
    }));
    if (type === "from") setStartDateOpen(false);
    if (type === "to") setEndDateOpen(false);
  };

  const isValidSelection = () => {
    if (periodSelectionMode === "predefined") {
      return selectedPeriod !== null;
    } else {
      return customDateRange.from && customDateRange.to;
    }
  };

  return (
    <BaseModal
      open={open}
      size="xl"
      onOpenChange={onOpenChange}
      title={showReportsTable ? "Report Table" : "Office Reports"}
      description={
        showReportsTable
          ? "View the generated report"
          : step === "office"
            ? "Select an office to view reports"
            : "Select a period to view reports"
      }
      className="max-w-4xl"
    >
      {showReportsTable ? (
        <OfficeTableModal
          open={showReportsTable}
          onOpenChange={() => setShowReportsTable(false)}
          office={selectedOffice}
          start_date={
            periodSelectionMode === "predefined" && selectedPeriod
              ? selectedPeriod.start_date
              : customDateRange.from
                ? format(customDateRange.from, "yyyy-MM-dd")
                : undefined
          }
          end_date={
            periodSelectionMode === "predefined" && selectedPeriod
              ? selectedPeriod.end_date
              : customDateRange.to
                ? format(customDateRange.to, "yyyy-MM-dd")
                : undefined
          }
          period_name={
            periodSelectionMode === "predefined" && selectedPeriod
              ? selectedPeriod.period_name
              : customDateRange.from && customDateRange.to
                ? `${format(customDateRange.from, "MMM dd")} - ${format(customDateRange.to, "MMM dd, yyyy")}`
                : "Custom Period"
          }
        />
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col gap-6 p-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-md"
        >
          {/* Header with navigation */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {step === "period" && (
                <PrimaryButton

                  size="sm"
                  onClick={handleBack}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Offices
                </PrimaryButton>
              )}
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {step === "office" ? "Select Office" : `Select Period for ${selectedOffice}`}
              </h3>
            </div>
            {(selectedOffice || selectedPeriod) && (
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                Reset
              </PrimaryButton>
            )}
          </div>

          {/* Office Selection Cards */}
          {step === "office" && (
            <div className="space-y-4">
              {isLoadingOffices ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 6 }).map((_, idx) => (
                    <Card key={idx} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-20 bg-gray-200 rounded"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : officesError ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Failed to load offices. Please try again.</p>
                </div>
              ) : officesData?.offices?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {officesData.offices.map((office: OfficeData, idx: number) => (
                    <Card
                      key={office.office}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
                        selectedOffice === office.office
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-200 hover:border-green-300"
                      )}
                      onClick={() => handleOfficeSelect(office.office)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                              <Building2 className="w-5 h-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {office.office}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {office.total_marketers} Marketers
                              </p>
                            </div>
                          </div>
                          {selectedOffice === office.office && (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          )}
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Current Progress</span>
                            <Badge
                              variant={office.current_period.progress >= 70 ? "default" : "secondary"}
                              className={cn(
                                office.current_period.progress >= 70
                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                  : "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                              )}
                            >
                              {office.current_period.progress.toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">No offices available.</p>
                </div>
              )}
            </div>
          )}

          {/* Period Selection Cards */}
          {step === "period" && selectedOffice && (
            <div className="space-y-6">
              {/* Period Selection Mode Toggle */}
              <div className="flex items-center justify-center">

              </div>

              {/* Predefined Periods */}
              {periodSelectionMode === "predefined" && (
                <>
                  {isLoadingPeriods ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Array.from({ length: 4 }).map((_, idx) => (
                        <Card key={idx} className="animate-pulse">
                          <CardContent className="p-4">
                            <div className="h-24 bg-gray-200 rounded"></div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : periodsError ? (
                    <div className="text-center py-8">
                      <p className="text-red-600">Failed to load periods. Please try again.</p>
                    </div>
                  ) : (periodsData?.results?.length ?? 0) > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {periodsData?.results.map((period: any) => (
                        <Card
                          key={`${period.period_start_date}-${period.period_end_date}`}
                          className={cn(
                            "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
                            selectedPeriod?.period_start_date === period.period_start_date
                              ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                              : "border-gray-200 hover:border-green-300"
                          )}
                          onClick={() => handlePeriodSelect({
                            period_name: period.period_start_date && period.period_end_date && !isNaN(new Date(period.period_start_date).getTime()) && !isNaN(new Date(period.period_end_date).getTime()) ? `${format(new Date(period.period_start_date), "MMM dd")} - ${format(new Date(period.period_end_date), "MMM dd, yyyy")}` : "Invalid Period",
                            start_date: period.period_start_date,
                            end_date: period.period_end_date,
                            target: 0,
                            achieved: 0,
                            progress: 0,
                            period_start_date: period.period_start_date,
                            period_end_date: period.period_end_date
                          })}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                  <Calendar className="w-5 h-5 text-green-600 dark:text-green-400" />
                                </div>
                                <div>
                                  <h4 className="font-semibold text-gray-900 dark:text-white">
                                    {period.period_start_date && period.period_end_date && !isNaN(new Date(period.period_start_date).getTime()) && !isNaN(new Date(period.period_end_date).getTime()) ? `${format(new Date(period.period_start_date), "MMM dd")} - ${format(new Date(period.period_end_date), "MMM dd, yyyy")}` : "Invalid Period"}
                                  </h4>
                                  <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Marketing Period
                                  </p>
                                </div>
                              </div>
                              {selectedPeriod?.period_start_date === period.period_start_date && (
                                <CheckCircle className="w-5 h-5 text-green-600" />
                              )}
                            </div>
                            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                              <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                                Click to select this period
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-600">No periods available for this office.</p>
                    </div>
                  )}
                </>
              )}

              {/* Custom Date Range Selection */}
              {periodSelectionMode === "custom" && (
                <div className="space-y-6">
                  <Card className="border-2 border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                          <CalendarDays className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-blue-900 dark:text-blue-100">Custom Date Range</h4>
                          <p className="text-sm text-blue-700 dark:text-blue-300">Select your preferred start and end dates</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Start Date */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                          <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !customDateRange.from && "text-muted-foreground"
                                )}
                              >
                                <CalendarDays className="mr-2 h-4 w-4" />
                                {customDateRange.from ? format(customDateRange.from, "PPP") : "Pick start date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={customDateRange.from}
                                onSelect={(date) => handleCustomDateSelect("from", date)}
                                disabled={(date) =>
                                  date > new Date() || (customDateRange.to ? date > customDateRange.to : false)
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>

                        {/* End Date */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                          <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !customDateRange.to && "text-muted-foreground"
                                )}
                              >
                                <CalendarDays className="mr-2 h-4 w-4" />
                                {customDateRange.to ? format(customDateRange.to, "PPP") : "Pick end date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={customDateRange.to}
                                onSelect={(date) => handleCustomDateSelect("to", date)}
                                disabled={(date) =>
                                  date > new Date() || (!!customDateRange.from && date < customDateRange.from)
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>

                      {/* Date Range Summary */}
                      {customDateRange.from && customDateRange.to && (
                        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-center gap-2 text-sm text-green-800 dark:text-green-200">
                            <CheckCircle className="w-4 h-4" />
                            <span className="font-medium">
                              Selected Range: {format(customDateRange.from, "MMM dd, yyyy")} - {format(customDateRange.to, "MMM dd, yyyy")}
                            </span>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons - Only show for custom date range */}
          {isValidSelection() && periodSelectionMode === "custom" && (
            <div className="flex gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <PrimaryButton
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                disabled={isLoading}
                onClick={() => handleViewReport()}
                aria-label="View report for selected period"
              >
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <svg
                      className="animate-spin h-4 w-4 text-white"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                      />
                    </svg>
                    Loading...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    View Report
                  </span>
                )}
              </PrimaryButton>
            </div>
          )}

          {/* Selected Summary */}
          {selectedOffice && isValidSelection() && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Selected Report Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-green-700 dark:text-green-300">Office:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">{selectedOffice}</p>
                </div>
                <div>
                  <span className="text-green-700 dark:text-green-300">Period Type:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">
                    {periodSelectionMode === "predefined" ? "Predefined Period" : "Custom Date Range"}
                  </p>
                </div>
                {periodSelectionMode === "predefined" && selectedPeriod && (
                  <>
                    <div>
                      <span className="text-green-700 dark:text-green-300">Period:</span>
                      <p className="font-medium text-green-900 dark:text-green-100">{selectedPeriod.period_name}</p>
                    </div>
                    <div>
                      <span className="text-green-700 dark:text-green-300">Target:</span>
                      <p className="font-medium text-green-900 dark:text-green-100">
                        {new Intl.NumberFormat('en-KE', {
                          style: 'currency',
                          currency: 'KES',
                          minimumFractionDigits: 0,
                        }).format(selectedPeriod.target)}
                      </p>
                    </div>
                  </>
                )}
                <div className="md:col-span-2">
                  <span className="text-green-700 dark:text-green-300">Date Range:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">
                    {periodSelectionMode === "predefined" && selectedPeriod
                      ? `${format(new Date(selectedPeriod.start_date), "MMM dd")} - ${format(new Date(selectedPeriod.end_date), "MMM dd, yyyy")}`
                      : customDateRange.from && customDateRange.to
                        ? `${format(customDateRange.from, "MMM dd")} - ${format(customDateRange.to, "MMM dd, yyyy")}`
                        : "No date range selected"
                    }
                  </p>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      )}
    </BaseModal>
  );
}