import BaseModal from "@/components/custom/modals/BaseModal";
import React from "react";
import { toast } from "sonner";

interface propsTypes {
  isOpen: boolean;
  onClose: () => void;
}

const contactsList = [
  { name: "Official Optiven Limited", phone: "0790 300 300" },
  { name: "CEO's OFFICE", phone: "0706 618 141" },
  { name: "Documentation", phone: "0748 953 262" },
  { name: "Accounts", phone: "0717 130 199" },
  { name: "Customer Experience", phone: "0702 831 083" },
  { name: "Credit / Receivables", phone: "0700 073 911" },
  { name: "Procurement", phone: "0748 597 179" },
  { name: "Human Resources", phone: "0715 928 112" },
  { name: "Legal", phone: "0720 919 111" },
  { name: "Digital", phone: "0723 400 500" },
  { name: "Optiven Water", phone: "0111 416 233" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", phone: "0796 000 333" },
  { name: "<PERSON>", phone: "0742 817 927" },
  { name: "Optiven Construction", phone: "0743 404 040" },
  { name: "Foundation", phone: "0718 776 033" },
];

const ContactsModal = ({ isOpen, onClose }: propsTypes) => {
  return (
    <BaseModal
      size="2xl"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Optiven Contacts"
      description="View the list of all Optiven official contacts"
      // onStepChange={setCurrentStep}
      // onComplete={handleAddProspect}
    >
      <div className="grid lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1 p-2">
        {contactsList.map((contact: { name: string; phone: string }) => (
          <div
            key={contact.phone}
            className="flex flex-col border p-3 m-2 rounded-lg hover:shadow-lg hover:bg-gray-50 transition cursor-pointer"
          >
            <span className="text-sm font-medium text-gray-500 text-center">
              {contact.name}
            </span>
            <span className="text-lg font-semibold text-gray-900 text-center mb-2">
              {contact.phone}
            </span>
            <div className="flex items-center justify-center gap-2">
              <button
                className="flex items-center gap-1 px-2 py-1 rounded bg-green-500 text-white text-xs hover:bg-green-600 transition"
                title="WhatsApp"
                onClick={() =>
                  window.open(
                    `https://wa.me/${contact.phone.replace(/\s+/g, "")}`,
                    "_blank"
                  )
                }
              >
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.028-.967-.271-.099-.468-.148-.666.15-.197.297-.767.967-.94 1.166-.173.199-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.298-.018-.458.13-.606.134-.133.298-.347.447-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.666-1.611-.912-2.205-.242-.579-.487-.5-.666-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.075-.792.372-.271.298-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.099 3.2 5.077 4.354.711.306 1.263.489 1.694.625.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.413-.074-.124-.271-.198-.568-.347z" />
                  <path d="M12.004 2c-5.514 0-9.998 4.484-9.998 9.998 0 1.762.463 3.479 1.341 4.991l-1.414 5.186 5.308-1.396c1.464.801 3.125 1.217 4.763 1.217h.001c5.514 0 9.998-4.484 9.998-9.998s-4.484-9.998-9.999-9.998zm0 18.163c-1.522 0-3.025-.401-4.329-1.162l-.31-.179-3.152.829.842-3.078-.202-.316c-.845-1.326-1.292-2.857-1.292-4.437 0-4.411 3.588-7.999 7.999-7.999 4.411 0 7.999 3.588 7.999 7.999 0 4.411-3.588 7.999-7.999 7.999z" />
                </svg>
                WhatsApp
              </button>
              <button
                className="flex items-center gap-1 px-2 py-1 rounded bg-blue-500 text-white text-xs hover:bg-blue-600 transition"
                title="SMS"
                onClick={() =>
                  window.open(
                    `sms:${contact.phone.replace(/\s+/g, "")}`,
                    "_blank"
                  )
                }
              >
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M20 2H4C2.897 2 2 2.897 2 4v16l4-4h14c1.103 0 2-.897 2-2V4c0-1.103-.897-2-2-2zm0 12H6.828L4 18V4h16v10z" />
                </svg>
                SMS
              </button>
              <button
                className="flex items-center gap-1 px-2 py-1 rounded bg-gray-200 text-gray-700 text-xs hover:bg-gray-300 transition"
                title="Copy"
                onClick={() => {
                  navigator.clipboard.writeText(contact.phone);
                  toast.success("Phone number copied to clipboard");
                }}
              >
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M16 1H4c-1.104 0-2 .896-2 2v14h2V3h12V1zm3 4H8c-1.104 0-2 .896-2 2v14c0 1.104.896 2 2 2h11c1.104 0 2-.896 2-2V7c0-1.104-.896-2-2-2zm0 16H8V7h11v14z" />
                </svg>
                Copy
              </button>
            </div>
          </div>
        ))}
      </div>
    </BaseModal>
  );
};

export default ContactsModal;
