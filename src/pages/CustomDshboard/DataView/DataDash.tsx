import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  GitGraph,
  DollarSign,
  Target,
  TrendingUp,
  BarChart3,
  FileText,
  Download,
  ArrowUpRight,
  Zap,
  Star,
  RefreshCw,
  Layers,
  Eye,
  PhoneIcon,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import DataModals from "./DataModals";
// Import the single API hook for dashboard stats
import { useGetDataDashboardStatsQuery } from "@/redux/slices/dashboardStatsApiSlice";
import { useNavigate } from "react-router-dom";
import LeadSourcesPage from "./LeadSourcesPage";
import ContactsModal from "./ContactsModal";
import EmployeeContactsModal from "./EmployeeContactsModal";

const reportTemplates = [
  {
    id: 1,
    name: "Sales Performance Report",
    description: "Comprehensive sales metrics and KPIs",
    icon: TrendingUp,
    color: "from-green-400 to-emerald-600",
    metrics: [
      "Total Sales",
      "Conversion Rate",
      "Revenue Growth",
      "Top Performers",
    ],
  },
  {
    id: 2,
    name: "Lead Analysis Report",
    description: "Detailed lead source and conversion analysis",
    icon: Target,
    color: "from-blue-400 to-cyan-600",
    metrics: [
      "Lead Sources",
      "Quality Score",
      "Conversion Funnel",
      "Cost per Lead",
    ],
  },
  {
    id: 3,
    name: "Customer Insights Report",
    description: "Customer behavior and demographics",
    icon: Users,
    color: "from-purple-400 to-pink-600",
    metrics: [
      "Customer Segments",
      "Lifetime Value",
      "Retention Rate",
      "Satisfaction Score",
    ],
  },
  {
    id: 4,
    name: "Marketing ROI Report",
    description: "Marketing campaign performance and ROI",
    icon: BarChart3,
    color: "from-orange-400 to-red-600",
    metrics: [
      "Campaign Performance",
      "ROI Analysis",
      "Channel Effectiveness",
      "Budget Allocation",
    ],
  },
];

const DataDash = () => {
  const navigate = useNavigate();
  const [selectedReportType, setSelectedReportType] = useState("");
  const [selectedDateRange, setSelectedDateRange] = useState("");
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [reportName, setReportName] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [leadSourcesOpen, setLeadSourcesOpen] = useState(false);
  const [leadCategoriesOpen, setLeadCategoriesOpen] = useState(false);
  const [leadSubCategoriesOpen, setLeadSubCategoriesOpen] = useState(false);
  const [openOptivenContact, setOpenOptivenContact] = useState(false);
  const [openEmployeeContact, setOpenEmployeeContact] = useState(false);

  // Single API call for all dashboard stats
  const {
    data: dashboardStats,
    isLoading: dashboardStatsLoading,
    error: dashboardStatsError,
    refetch: refetchDashboardStats,
  } = useGetDataDashboardStatsQuery({});
  console.log("Dashboard Stats:", dashboardStats);

  // Format currency
  const formatCurrency = (amount: any) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Create metrics array using the dashboard stats data
  const metrics = [
    {
      title: "Total Customers",
      value: dashboardStats?.customers_count?.toLocaleString() || "0",
      icon: Users,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Active",
      changeType: dashboardStatsError ? "negative" : "positive",
      action: () => navigate("/customers/overview#all-customers"),
    },
    {
      title: "All Leads",
      value: dashboardStats?.leads_count?.toLocaleString() || "0",
      icon: Target,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Active",
      changeType: dashboardStatsError ? "negative" : "positive",
      action: () => navigate("/prospects"),
    },
    // {
    //   title: "Total Sales",
    //   value: dashboardStats?.total_sales ? formatCurrency(dashboardStats.total_sales) : "KES 0",
    //   icon: DollarSign,
    //   iconBg: "bg-green-100",
    //   iconColor: "text-green-600",
    //   cardBg: "bg-gradient-to-br from-green-50 to-green-100",
    //   borderColor: "border-green-200",
    //   change: dashboardStatsLoading ? "Loading..." : (dashboardStatsError ? "Error" : "Revenue"),
    //   changeType: dashboardStatsError ? "negative" : "positive",
    //   action: () => window.location.href = "/sales/overview#all-sales"
    // },
    {
      title: "Lead Sources",
      value: dashboardStats?.lead_sources_count?.toLocaleString() || "0",
      icon: GitGraph,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Sources",
      changeType: dashboardStatsError ? "negative" : "neutral",
      // action: () => {
      //   navigate("/data-team/lead-sources");
      // },
      modal: false,
    },
    {
      title: "Source Categories",
      value:
        dashboardStats?.lead_sources_categories_count?.toLocaleString() || "0",
      icon: BarChart3,
      iconBg: "bg-pink-100",
      iconColor: "text-pink-600",
      cardBg: "bg-gradient-to-br from-pink-50 to-pink-100",
      borderColor: "border-pink-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Categories",
      changeType: dashboardStatsError ? "negative" : "neutral",
      action: () => setLeadCategoriesOpen(true),
      modal: true,
    },
    {
      title: "Sub Categories",
      value:
        dashboardStats?.lead_sources_subcategories_count?.toLocaleString() ||
        "0",
      icon: Layers,
      iconBg: "bg-indigo-100",
      iconColor: "text-indigo-600",
      cardBg: "bg-gradient-to-br from-indigo-50 to-indigo-100",
      borderColor: "border-indigo-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Subcategories",
      changeType: dashboardStatsError ? "negative" : "neutral",
      action: () => setLeadSubCategoriesOpen(true),
      modal: true,
    },
  ];

  const handleMetricClick = (metric: any) => {
    if (metric.action) {
      metric.action();
    }
  };

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    // Simulate report generation
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsGenerating(false);
    // Reset form
    setSelectedReportType("");
    setSelectedDateRange("");
    setSelectedMetrics([]);
    setReportName("");
  };

  const toggleMetric = (metric: string) => {
    setSelectedMetrics((prev: string[]) =>
      prev.includes(metric)
        ? prev.filter((m: string) => m !== metric)
        : [...prev, metric]
    );
  };

  // Debug logging
  useEffect(() => {
    console.log("Dashboard Stats:", dashboardStats);
  }, [dashboardStats]);

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Compact Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Data Dashboard</h1>
            <div className="flex items-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setOpenOptivenContact(true)}
              >
                <PhoneIcon /> View Optiven Contacts
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setOpenEmployeeContact(true)}
              >
                <PhoneIcon /> View Employee Contacts
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchDashboardStats()}
                disabled={dashboardStatsLoading}
              >
                {dashboardStatsLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Compact Metrics Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
            {metrics.map((metric, index) => (
              <Card
                key={metric.title}
                className={`${metric.borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${metric.cardBg}`}
                onClick={() => handleMetricClick(metric)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${metric.iconBg}`}
                    >
                      <metric.icon className={`w-4 h-4 ${metric.iconColor}`} />
                    </div>
                    {metric.modal && (
                      <ArrowUpRight className="w-3 h-3 text-gray-400" />
                    )}
                  </div>

                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-600">
                      {metric.title}
                    </p>
                    <p className="text-lg font-bold text-gray-900">
                      {metric.value}
                    </p>
                    <div className="flex items-center text-xs">
                      {metric.changeType === "positive" &&
                        !dashboardStatsLoading &&
                        !dashboardStatsError && (
                          <TrendingUp className="w-3 h-3 text-green-600 mr-1" />
                        )}
                      <span
                        className={`font-medium ${
                          metric.changeType === "positive"
                            ? "text-green-600"
                            : metric.changeType === "negative"
                            ? "text-red-600"
                            : "text-gray-600"
                        }`}
                      >
                        {metric.change}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Loading State */}
          {dashboardStatsLoading && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <RefreshCw className="w-4 h-4 text-blue-600 mr-2 animate-spin" />
                <span className="text-blue-800 text-sm">
                  Loading dashboard statistics...
                </span>
              </div>
            </div>
          )}

          {/* Error State */}
          {dashboardStatsError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-red-800 text-sm">
                  Failed to load dashboard statistics. Please try again.
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchDashboardStats()}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Retry
                </Button>
              </div>
            </div>
          )}

          {/* Success State with Data Summary */}
          {!dashboardStatsLoading && !dashboardStatsError && dashboardStats && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded-full mr-2 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-green-800 text-sm">
                  Dashboard data loaded successfully - Last updated:{" "}
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}

          {/* Dynamic Report Generation Section */}
          <Card className="border-0 shadow-lg bg-white">
            <LeadSourcesPage />
            {/* <div className="bg-primary p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-secondary" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-secondary">
                    Dynamic Report Generator
                  </h2>
                  <p className="text-secondary mt-1">
                    Create custom reports with advanced analytics
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4">
              <Tabs defaultValue="templates" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger
                    value="templates"
                    className="flex items-center space-x-2"
                  >
                    <Star className="w-4 h-4" />
                    <span>Templates</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="custom"
                    className="flex items-center space-x-2"
                  >
                    <Zap className="w-4 h-4" />
                    <span>Custom</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="templates" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {reportTemplates.map((template) => (
                      <Card
                        key={template.id}
                        className="border-2 border-gray-100 hover:border-indigo-200 transition-all duration-200 group cursor-pointer"
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-8 h-8 bg-gradient-to-br ${template.color} rounded-lg flex items-center justify-center`}
                            >
                              <template.icon className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <CardTitle className="text-base">
                                {template.name}
                              </CardTitle>
                              <CardDescription className="text-xs">
                                {template.description}
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex flex-wrap gap-1">
                            {template.metrics.slice(0, 3).map((metric) => (
                              <Badge
                                key={metric}
                                variant="secondary"
                                className="text-xs"
                              >
                                {metric}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                        <CardFooter className="pt-2">
                          <Button
                            size="sm"
                            className="w-full"
                            variant="outline"
                          >
                            <FileText className="w-3 h-3 mr-1" />
                            Generate
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="custom" className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="reportName"
                          className="text-sm font-medium"
                        >
                          Report Name
                        </Label>
                        <Input
                          id="reportName"
                          placeholder="Enter report name..."
                          value={reportName}
                          onChange={(e) => setReportName(e.target.value)}
                          className="h-10"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Report Type
                          </Label>
                          <Select
                            value={selectedReportType}
                            onValueChange={setSelectedReportType}
                          >
                            <SelectTrigger className="h-10">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="sales">
                                Sales Report
                              </SelectItem>
                              <SelectItem value="leads">
                                Lead Analysis
                              </SelectItem>
                              <SelectItem value="customers">
                                Customer Insights
                              </SelectItem>
                              <SelectItem value="marketing">
                                Marketing Performance
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Date Range
                          </Label>
                          <Select
                            value={selectedDateRange}
                            onValueChange={setSelectedDateRange}
                          >
                            <SelectTrigger className="h-10">
                              <SelectValue placeholder="Select range" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="7days">Last 7 days</SelectItem>
                              <SelectItem value="30days">
                                Last 30 days
                              </SelectItem>
                              <SelectItem value="90days">
                                Last 90 days
                              </SelectItem>
                              <SelectItem value="6months">
                                Last 6 months
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Select Metrics
                        </Label>
                        <div className="grid grid-cols-2 gap-2">
                          {[
                            "Revenue",
                            "Conversions",
                            "Lead Quality",
                            "Customer Acquisition",
                            "ROI",
                            "Growth Rate",
                          ].map((metric) => (
                            <div
                              key={metric}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={metric}
                                checked={selectedMetrics.includes(metric)}
                                onCheckedChange={() => toggleMetric(metric)}
                              />
                              <Label htmlFor={metric} className="text-xs">
                                {metric}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 border">
                      <h3 className="text-sm font-medium mb-3 flex items-center">
                        <Eye className="w-4 h-4 mr-2" />
                        Preview
                      </h3>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Name:</span>
                          <span className="text-gray-600">
                            {reportName || "Untitled"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Type:</span>
                          <span className="text-gray-600 capitalize">
                            {selectedReportType || "None"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Range:</span>
                          <span className="text-gray-600">
                            {selectedDateRange || "None"}
                          </span>
                        </div>
                        <div>
                          <span>Metrics ({selectedMetrics.length}):</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedMetrics.length > 0 ? (
                              selectedMetrics.map((metric) => (
                                <Badge
                                  key={metric}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {metric}
                                </Badge>
                              ))
                            ) : (
                              <span className="text-gray-400 text-xs">
                                None selected
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <Button
                        className="w-full mt-4 bg-gradient-to-r from-indigo-600 to-purple-600"
                        onClick={handleGenerateReport}
                        disabled={
                          !reportName ||
                          !selectedReportType ||
                          !selectedDateRange ||
                          selectedMetrics.length === 0 ||
                          isGenerating
                        }
                      >
                        {isGenerating ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Download className="w-4 h-4 mr-2" />
                            Generate Report
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div> */}
          </Card>

          {/* Data Modals */}
          <DataModals
            leadSourcesOpen={leadSourcesOpen}
            setLeadSourcesOpen={setLeadSourcesOpen}
            leadCategoriesOpen={leadCategoriesOpen}
            setLeadCategoriesOpen={setLeadCategoriesOpen}
            leadSubCategoriesOpen={leadSubCategoriesOpen}
            setLeadSubCategoriesOpen={setLeadSubCategoriesOpen}
          />

          <ContactsModal
            isOpen={openOptivenContact}
            onClose={() => setOpenOptivenContact(false)}
          />

          <EmployeeContactsModal
            isOpen={openEmployeeContact}
            onClose={() => setOpenEmployeeContact(false)}
          />
        </div>
      </div>
    </Screen>
  );
};

export default DataDash;
