import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Users,
  GitGraph,
  Target,
  TrendingUp,
  BarChart3,
  Download,
  ArrowUpRight,
  RefreshCw,
  Layers,
  Search,
  ExternalLink,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  PhoneIcon,
} from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import DataModals from "./DataModals";
import { Label } from "recharts";
// Import the single API hook for dashboard stats
import { useGetDataDashboardStatsQuery } from "@/redux/slices/dashboardStatsApiSlice";
import { useNavigate } from "react-router-dom";
import LeadSourcesPage from "./LeadSourcesPage";
import ContactsModal from "./ContactsModal";
import EmployeeContactsModal from "./EmployeeContactsModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  useGetLeadSourceCategoriesQuery,
  useGetLeadSourceQuery,
  useGenerateLeadFormLinkMutation,
} from "@/redux/slices/propects";
import BaseModal from "@/components/custom/modals/BaseModal";
import CustomSelectField from "@/components/CustomSelectField";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import { toast } from "sonner";

// Types for Lead Sources
interface LeadSource {
  id: number;
  name: string;
  description?: string | null;
  ref_code: string;
  link?: string | null;
  sales?: number | null;
  active_leads?: number | null;
  dormant_leads?: number | null;
  ongoing_sales?: number | null;
  completed_sales?: number | null;
  dropped_sales?: number | null;
  lead_source_category?: { id: number; name: string } | null;
  lead_source_subcategory?: {
    id: number;
    name: string;
    lead_source_category: number;
  } | null;
}

interface Category {
  id: number;
  name: string;
  description?: string;
}

interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total_data: number;
  links: { next: string | null; previous: string | null };
}

const DataDash = () => {
  const navigate = useNavigate();
  const [leadSourcesOpen, setLeadSourcesOpen] = useState(false);
  const [leadCategoriesOpen, setLeadCategoriesOpen] = useState(false);
  const [leadSubCategoriesOpen, setLeadSubCategoriesOpen] = useState(false);

  // Lead Sources Table State
  const [sourceSearch, setSourceSearch] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "sales">("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [sourcePage, setSourcePage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Modal and generation
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState<LeadSource | null>(null);
  const [marketer, setMarketer] = useState("");
  const [marketerLabel, setMarketerLabel] = useState("");
  const [fetchMarketer, { data: marketersData, isLoading: marketersLoading }] =
    useLazyGetUsersQuery();
  const [generateLink, { isLoading: generating }] =
    useGenerateLeadFormLinkMutation();
  const [openOptivenContact, setOpenOptivenContact] = useState(false);
  const [openEmployeeContact, setOpenEmployeeContact] = useState(false);

  // Single API call for all dashboard stats
  const {
    data: dashboardStats,
    isLoading: dashboardStatsLoading,
    error: dashboardStatsError,
    refetch: refetchDashboardStats,
  } = useGetDataDashboardStatsQuery({});
  console.log("Dashboard Stats:", dashboardStats);

  // Lead Sources API calls
  const buildSourceParams = () => ({
    page: sourcePage,
    page_size: pageSize,
    search: sourceSearch || undefined,
    ordering: sortOrder === "desc" ? `-${sortBy}` : sortBy,
  });

  const {
    data: leadSourcesResponse,
    isLoading: sourcesLoading,
    error: sourcesError,
    refetch: refetchSources,
  } = useGetLeadSourceQuery(buildSourceParams());
  const { data: allCategoriesResponse } = useGetLeadSourceCategoriesQuery({
    page_size: 1000,
  });

  const leadSources: LeadSource[] = leadSourcesResponse?.data?.results || [];
  const sourcesPagination: PaginationData | null =
    leadSourcesResponse?.data || null;
  const allCategories: Category[] = allCategoriesResponse?.data?.results || [];

  // Format currency
  const formatCurrency = (amount: any) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Create metrics array using the dashboard stats data
  const metrics = [
    {
      title: "Total Customers",
      value: dashboardStats?.customers_count?.toLocaleString() || "0",
      icon: Users,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      cardBg: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Active",
      changeType: dashboardStatsError ? "negative" : "positive",
      action: () => navigate("/customers/overview#all-customers"),
    },
    {
      title: "All Leads",
      value: dashboardStats?.leads_count?.toLocaleString() || "0",
      icon: Target,
      iconBg: "bg-purple-100",
      iconColor: "text-purple-600",
      cardBg: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Active",
      changeType: dashboardStatsError ? "negative" : "positive",
      action: () => navigate("/prospects"),
    },
    // {
    //   title: "Total Sales",
    //   value: dashboardStats?.total_sales ? formatCurrency(dashboardStats.total_sales) : "KES 0",
    //   icon: DollarSign,
    //   iconBg: "bg-green-100",
    //   iconColor: "text-green-600",
    //   cardBg: "bg-gradient-to-br from-green-50 to-green-100",
    //   borderColor: "border-green-200",
    //   change: dashboardStatsLoading ? "Loading..." : (dashboardStatsError ? "Error" : "Revenue"),
    //   changeType: dashboardStatsError ? "negative" : "positive",
    //   action: () => window.location.href = "/sales/overview#all-sales"
    // },
    {
      title: "Lead Sources",
      value: dashboardStats?.lead_sources_count?.toLocaleString() || "0",
      icon: GitGraph,
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600",
      cardBg: "bg-gradient-to-br from-orange-50 to-orange-100",
      borderColor: "border-orange-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Sources",
      changeType: dashboardStatsError ? "negative" : "neutral",
      action: () => {
        // Scroll to lead sources table
        document
          .getElementById("lead-sources-section")
          ?.scrollIntoView({ behavior: "smooth" });
      },
      modal: false,
    },
    {
      title: "Source Categories",
      value:
        dashboardStats?.lead_sources_categories_count?.toLocaleString() || "0",
      icon: BarChart3,
      iconBg: "bg-pink-100",
      iconColor: "text-pink-600",
      cardBg: "bg-gradient-to-br from-pink-50 to-pink-100",
      borderColor: "border-pink-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Categories",
      changeType: dashboardStatsError ? "negative" : "neutral",
      action: () => setLeadCategoriesOpen(true),
      modal: true,
    },
    {
      title: "Sub Categories",
      value:
        dashboardStats?.lead_sources_subcategories_count?.toLocaleString() ||
        "0",
      icon: Layers,
      iconBg: "bg-indigo-100",
      iconColor: "text-indigo-600",
      cardBg: "bg-gradient-to-br from-indigo-50 to-indigo-100",
      borderColor: "border-indigo-200",
      change: dashboardStatsLoading
        ? "Loading..."
        : dashboardStatsError
        ? "Error"
        : "Subcategories",
      changeType: dashboardStatsError ? "negative" : "neutral",
      action: () => setLeadSubCategoriesOpen(true),
      modal: true,
    },
  ];

  const handleMetricClick = (metric: any) => {
    if (metric.action) {
      metric.action();
    }
  };

  // Lead Sources Helper Functions
  const handleSort = (column: "name" | "sales") => {
    if (sortBy === column) setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    else {
      setSortBy(column);
      setSortOrder("asc");
    }
    setSourcePage(1);
  };

  const getTotalLeads = (source: LeadSource): number => {
    const a = source.active_leads ?? 0;
    const d = source.dormant_leads ?? 0;
    return a + d;
  };

  const getConversionRate = (source: LeadSource): number => {
    const totalLeads = getTotalLeads(source);
    const sales = source.sales ?? 0;
    if (!totalLeads) return 0;
    return (sales / totalLeads) * 100;
  };

  const getPerformanceStatus = (conversionRate: number) => {
    if (conversionRate >= 10)
      return { status: "excellent", color: "bg-green-100 text-green-800" };
    if (conversionRate >= 5)
      return { status: "good", color: "bg-blue-100 text-blue-800" };
    if (conversionRate >= 1)
      return { status: "average", color: "bg-yellow-100 text-yellow-800" };
    return { status: "poor", color: "bg-red-100 text-red-800" };
  };

  const handleGenerate = async () => {
    if (!marketer || !selectedSource) return;
    try {
      const res: any = await generateLink({
        marketer: marketer,
        ref_code: selectedSource.ref_code,
      }).unwrap();
      toast.success(
        "An email of the link has been sent to that specific marketer"
      );
      setIsModalOpen(false);
      setMarketer("");
      setMarketerLabel("");
      setSelectedSource(null);
    } catch (error: any) {
      console.error("Error generating link:", error);
      toast.error(error?.data?.message || "Failed to generate link");
    }
  };

  const resolveCategoryName = (source: LeadSource): string => {
    if (source.lead_source_category?.name)
      return source.lead_source_category.name;
    const catId = source.lead_source_subcategory?.lead_source_category;
    if (!catId) return "N/A";
    const cat = allCategories.find((c) => c.id === catId);
    return cat?.name || "N/A";
  };

  const renderTableSkeleton = (rows: number, cols: number) =>
    Array.from({ length: rows }).map((_, i) => (
      <TableRow key={i}>
        {Array.from({ length: cols }).map((_, j) => (
          <TableCell key={j}>
            <Skeleton className="h-4 w-full" />
          </TableCell>
        ))}
      </TableRow>
    ));

  const PaginationControls = ({
    pagination,
    currentPage,
    onPageChange,
  }: {
    pagination: PaginationData | null;
    currentPage: number;
    onPageChange: (page: number) => void;
  }) => {
    if (!pagination) return null;
    const { current_page, last_page, total_data, per_page } = pagination;
    const startItem = (current_page - 1) * per_page + 1;
    const endItem = Math.min(current_page * per_page, total_data);

    return (
      <div className="flex items-center justify-between px-2 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-700">
          <span>
            Showing {startItem.toLocaleString()} to {endItem.toLocaleString()}{" "}
            of {total_data.toLocaleString()} results
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">Rows per page:</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                setPageSize(parseInt(value));
                onPageChange(1);
              }}
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={current_page === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(current_page - 1)}
              disabled={current_page === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center space-x-1">
              <span className="text-sm text-gray-700">
                Page {current_page} of {last_page}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(current_page + 1)}
              disabled={current_page === last_page}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(last_page)}
              disabled={current_page === last_page}
              className="h-8 w-8 p-0"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Debug logging
  useEffect(() => {
    console.log("Dashboard Stats:", dashboardStats);
  }, [dashboardStats]);

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Compact Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Data Dashboard</h1>
            <div className="flex items-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setOpenOptivenContact(true)}
              >
                <PhoneIcon /> View Optiven Contacts
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setOpenEmployeeContact(true)}
              >
                <PhoneIcon /> View Employee Contacts
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchDashboardStats()}
                disabled={dashboardStatsLoading}
              >
                {dashboardStatsLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Compact Metrics Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
            {metrics.map((metric, index) => (
              <Card
                key={metric.title}
                className={`${metric.borderColor} border-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 ${metric.cardBg}`}
                onClick={() => handleMetricClick(metric)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${metric.iconBg}`}
                    >
                      <metric.icon className={`w-4 h-4 ${metric.iconColor}`} />
                    </div>
                    {metric.modal && (
                      <ArrowUpRight className="w-3 h-3 text-gray-400" />
                    )}
                  </div>

                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-600">
                      {metric.title}
                    </p>
                    <p className="text-lg font-bold text-gray-900">
                      {metric.value}
                    </p>
                    <div className="flex items-center text-xs">
                      {metric.changeType === "positive" &&
                        !dashboardStatsLoading &&
                        !dashboardStatsError && (
                          <TrendingUp className="w-3 h-3 text-green-600 mr-1" />
                        )}
                      <span
                        className={`font-medium ${
                          metric.changeType === "positive"
                            ? "text-green-600"
                            : metric.changeType === "negative"
                            ? "text-red-600"
                            : "text-gray-600"
                        }`}
                      >
                        {metric.change}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Loading State */}
          {dashboardStatsLoading && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <RefreshCw className="w-4 h-4 text-blue-600 mr-2 animate-spin" />
                <span className="text-blue-800 text-sm">
                  Loading dashboard statistics...
                </span>
              </div>
            </div>
          )}

          {/* Error State */}
          {dashboardStatsError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-red-800 text-sm">
                  Failed to load dashboard statistics. Please try again.
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchDashboardStats()}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Retry
                </Button>
              </div>
            </div>
          )}

          {/* Success State with Data Summary */}
          {!dashboardStatsLoading && !dashboardStatsError && dashboardStats && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded-full mr-2 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-green-800 text-sm">
                  Dashboard data loaded successfully - Last updated:{" "}
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}

          {/* Lead Sources Performance Section */}
          <div id="lead-sources-section" className="space-y-4">
            <div className="pb-4 border-b">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <GitGraph className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Lead Sources
                  </h1>
                  <p className="text-gray-600 text-sm">
                    Comprehensive analysis of all lead sources and their
                    conversion metrics
                  </p>
                </div>
              </div>
            </div>

            {/* Search + Actions */}
            <div className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <Input
                    placeholder="Search sources..."
                    className="pl-10 w-64"
                    value={sourceSearch}
                    onChange={(e) => {
                      setSourceSearch(e.target.value);
                      setSourcePage(1);
                    }}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center space-x-2"
                  onClick={() => refetchSources()}
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh</span>
                </Button>
              </div>
            </div>

            {/* Table */}
            {sourcesError ? (
              <div className="bg-red-50 border-2 border-red-200 p-6 rounded-xl text-center">
                <div className="text-red-600 font-medium text-lg">
                  Failed to load lead sources data
                </div>
                <p className="text-red-500 mt-2">
                  Please try refreshing the page
                </p>
              </div>
            ) : (
              <div className="border rounded-lg bg-white shadow-sm">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("name")}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Source Name</span>
                          <ArrowUpDown className="w-4 h-4" />
                        </div>
                      </TableHead>
                      <TableHead>Reference Code</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Sub-Category</TableHead>
                      <TableHead>Form</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sourcesLoading
                      ? renderTableSkeleton(pageSize, 5)
                      : leadSources.map((source) => {
                          const categoryName = resolveCategoryName(source);

                          return (
                            <TableRow
                              key={source.id}
                              className="hover:bg-gray-50"
                            >
                              <TableCell className="font-medium">
                                <div>
                                  <div className="font-semibold text-gray-900">
                                    {source.name}
                                  </div>
                                  {source.description && (
                                    <div className="text-sm text-gray-500 mt-1">
                                      {source.description}
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary" className="text-xs">
                                  {source.ref_code}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="text-xs">
                                  {categoryName}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="text-xs">
                                  {source.lead_source_subcategory?.name ||
                                    "N/A"}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center space-x-2"
                                  onClick={() => {
                                    setSelectedSource(source);
                                    setIsModalOpen(true);
                                    fetchMarketer({
                                      department__dp_name: "CONVERSION",
                                    });
                                  }}
                                >
                                  <ExternalLink className="w-4 h-4" />
                                  <span>Generate Lead Form Link</span>
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                  </TableBody>
                </Table>

                <PaginationControls
                  pagination={sourcesPagination}
                  currentPage={sourcePage}
                  onPageChange={setSourcePage}
                />
              </div>
            )}
          </div>

          {/* Data Modals */}
          <DataModals
            leadSourcesOpen={leadSourcesOpen}
            setLeadSourcesOpen={setLeadSourcesOpen}
            leadCategoriesOpen={leadCategoriesOpen}
            setLeadCategoriesOpen={setLeadCategoriesOpen}
            leadSubCategoriesOpen={leadSubCategoriesOpen}
            setLeadSubCategoriesOpen={setLeadSubCategoriesOpen}
          />

          <ContactsModal
            isOpen={openOptivenContact}
            onClose={() => setOpenOptivenContact(false)}
          />

          <EmployeeContactsModal
            isOpen={openEmployeeContact}
            onClose={() => setOpenEmployeeContact(false)}
          />
        </div>
      </div>

      {/* Generate Lead Form Link Modal */}
      <BaseModal
        size="sm"
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        title="Generate Lead Form Link"
        description="Select a marketer to generate the form link"
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="ref_code">Reference Code</Label>
            <Input
              id="ref_code"
              value={selectedSource?.ref_code || ""}
              disabled
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="marketer">Select Marketer</Label>
            <CustomSelectField
              valueField="employee_no"
              labelField="fullnames"
              data={(marketersData as any)?.data?.results}
              queryFunc={fetchMarketer}
              filterDict={{ department__dp_name: "CONVERSION" }}
              setValue={setMarketer}
              loader={marketersLoading}
              useSearchField={true}
              labelSetter={setMarketerLabel}
              placeholder={
                marketerLabel !== "" ? marketerLabel : "Search marketer by name"
              }
            />
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setIsModalOpen(false);
                setMarketer("");
                setMarketerLabel("");
                setSelectedSource(null);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleGenerate} disabled={!marketer || generating}>
              {generating ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                "Generate Link"
              )}
            </Button>
          </div>
        </div>
      </BaseModal>
    </Screen>
  );
};

export default DataDash;
