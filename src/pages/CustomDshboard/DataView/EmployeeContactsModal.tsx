import BaseModal from "@/components/custom/modals/BaseModal";
import CustomSelectField from "@/components/CustomSelectField";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import React, { useState } from "react";
import { toast } from "sonner";

interface propsTypes {
  isOpen: boolean;
  onClose: () => void;
}

const ContactsModal = ({ isOpen, onClose }: propsTypes) => {
  const [marketer, setMarketer] = useState("");
  const [marketerLabel, setMarketerLabel] = useState("");

  const [
    fetchMarketer,
    { data: marketersData, isLoading: marketersLoading, error, isError },
  ] = useLazyGetUsersQuery();

  return (
    <BaseModal
      size="2xl"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Optiven Employees Contacts"
      description="View the list of all Optiven official contacts"
      // onStepChange={setCurrentStep}
      // onComplete={handleAddProspect}
    >
      <div className="min-h-[300px]">
        <div className="p-2">
          <CustomSelectField
            valueField="employee_no"
            labelField="fullnames"
            data={(marketersData as any)?.data?.results}
            queryFunc={fetchMarketer}
            setValue={setMarketer}
            loader={marketersLoading}
            useSearchField={true}
            labelSetter={setMarketerLabel}
            placeholder={marketerLabel ? marketerLabel : "Search by name"}
          />
        </div>
        <div className="grid grid-cols-2 p-2">
          {isError ? (
            <div className="w-full text-center">No Employee Found</div>
          ) : (marketersData as any)?.data?.results.length < 1 ? (
            <div className="w-full text-center">No Employee Found</div>
          ) : (
            (marketersData as any)?.data?.results.map((contact: any) => (
              <div
                key={contact?.employee_no}
                className="flex flex-col border border-gray-200 bg-white p-4 m-2 rounded-xl shadow-sm hover:shadow-lg hover:bg-gray-50 transition cursor-pointer"
              >
                <div className="flex items-start justify-start gap-2 mb-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-tr from-green-400 to-blue-400 flex items-center justify-center text-white text-xl font-bold mb-2">
                    {contact?.fullnames &&
                      contact?.fullnames
                        .split(" ")
                        .map((n: string) => n[0])
                        .join("")
                        .slice(0, 2)}
                  </div>
                  <div className="flex flex-col">
                    <span className="text-base font-semibold text-gray-900 ">
                      {contact?.fullnames}
                    </span>
                    <span className="text-xs font-medium text-gray-500 ">
                      {contact?.department}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col gap-1 mb-3">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Employee No:</span>
                    <span className="text-sm font-semibold text-gray-800">
                      {contact?.employee_no}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Phone:</span>
                    <span className="text-sm font-semibold text-gray-800">
                      {contact?.phone_number ? (
                        String(contact.phone_number)
                      ) : (
                        <span className="text-destructive/60 font-medium">
                          Not Set
                        </span>
                      )}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Email:</span>
                    <span className="text-sm font-semibold text-gray-800">
                      {contact?.email}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Status:</span>
                    <span
                      className={`${
                        contact.status == "Active"
                          ? "text-primary bg-primary/20"
                          : contact.status == "Inactive"
                          ? "text-danger bg-danger/20"
                          : "text-dark bg-dark/20"
                      } py-1 px-2 rounded-full text-sm`}
                    >
                      {contact?.status}
                    </span>
                  </div>
                </div>
                {contact?.phone_number && (
                  <div className="flex items-center justify-center gap-3 mt-2">
                    <button
                      className="flex items-center gap-1 px-3 py-1.5 rounded-lg bg-green-500 text-white text-xs font-medium hover:bg-green-600 transition shadow"
                      title="WhatsApp"
                      onClick={() =>
                        window.open(
                          `https://wa.me/${contact?.phone.replace(/\s+/g, "")}`,
                          "_blank"
                        )
                      }
                    >
                      <svg
                        width="16"
                        height="16"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.028-.967-.271-.099-.468-.148-.666.15-.197.297-.767.967-.94 1.166-.173.199-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.298-.018-.458.13-.606.134-.133.298-.347.447-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.666-1.611-.912-2.205-.242-.579-.487-.5-.666-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.075-.792.372-.271.298-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.099 3.2 5.077 4.354.711.306 1.263.489 1.694.625.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.413-.074-.124-.271-.198-.568-.347z" />
                        <path d="M12.004 2c-5.514 0-9.998 4.484-9.998 9.998 0 1.762.463 3.479 1.341 4.991l-1.414 5.186 5.308-1.396c1.464.801 3.125 1.217 4.763 1.217h.001c5.514 0 9.998-4.484 9.998-9.998s-4.484-9.998-9.999-9.998zm0 18.163c-1.522 0-3.025-.401-4.329-1.162l-.31-.179-3.152.829.842-3.078-.202-.316c-.845-1.326-1.292-2.857-1.292-4.437 0-4.411 3.588-7.999 7.999-7.999 4.411 0 7.999 3.588 7.999 7.999 0 4.411-3.588 7.999-7.999 7.999z" />
                      </svg>
                      WhatsApp
                    </button>
                    <button
                      className="flex items-center gap-1 px-3 py-1.5 rounded-lg bg-blue-500 text-white text-xs font-medium hover:bg-blue-600 transition shadow"
                      title="SMS"
                      onClick={() =>
                        window.open(
                          `sms:${contact?.phone.replace(/\s+/g, "")}`,
                          "_blank"
                        )
                      }
                    >
                      <svg
                        width="16"
                        height="16"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M20 2H4C2.897 2 2 2.897 2 4v16l4-4h14c1.103 0 2-.897 2-2V4c0-1.103-.897-2-2-2zm0 12H6.828L4 18V4h16v10z" />
                      </svg>
                      SMS
                    </button>
                    <button
                      className="flex items-center gap-1 px-3 py-1.5 rounded-lg bg-gray-100 text-gray-700 text-xs font-medium hover:bg-gray-200 transition shadow"
                      title="Copy"
                      onClick={() => {
                        navigator.clipboard.writeText(contact.phone);
                        toast.success("Phone number copied to clipboard");
                      }}
                    >
                      <svg
                        width="16"
                        height="16"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M16 1H4c-1.104 0-2 .896-2 2v14h2V3h12V1zm3 4H8c-1.104 0-2 .896-2 2v14c0 1.104.896 2 2 2h11c1.104 0 2-.896 2-2V7c0-1.104-.896-2-2-2zm0 16H8V7h11v14z" />
                      </svg>
                      Copy
                    </button>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </BaseModal>
  );
};

export default ContactsModal;
