import { SetStateAction, useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";

import { PrimaryButton } from "@/components/custom/buttons/buttons";


interface DigitalLeads {
  department: string;
  phonenumber: string;
  whatsapp: string | null;
  sms: string | null;
}

interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DepartmentalContact({ open, onOpenChange }: ProspectsTableModalProps) {
  const [activeTab, setActiveTab] = useState<"allocated" | "unallocated">("allocated");
  const[isAddLeadModalOpen, setIsAddLeadModalOpen] = useState(false); 

  const data: DigitalLeads[] = [
    {
      department: "CEO OFFICE",
      phonenumber: "0712345678",
      whatsapp: "078965432",
      sms: "0789654321",
    },
    
  ];

 
  const columns: ColumnDef<DigitalLeads>[] = [
    {
      accessorKey: "department",
      header: "department",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "phonenumber",
      header: "Phone Number",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "whatsapp",
      header: "whatsapp",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      accessorKey: "Digital",
      header: "Digital",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit row ${row.original.department}`);
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Delete row ${row.original.department}`);
              }}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Leads Management"
      description="View and manage allocated and unallocated leads"
      className="max-w-4xl"
      size="full"
    >
        
      

       
        
          <DataTable<DigitalLeads>
            data={data}
            columns={columns}
            title={activeTab === "allocated" ? "Allocated Leads" : "Unallocated Leads"}
            enableExportToExcel={true}
            enablePrintPdf={true}
            enableColumnFilters={true}
            enablePagination={true}
            enableSorting={true}
            enableToolbar={true}
            containerClassName="max-w-full"
            tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300 shadow-md rounded-lg overflow-hidden"
            tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
            tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
            tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
            tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            tBodyCellsClassName="px-4 py-2"
          />
        
        
                 
      
    </BaseModal>
  );
}