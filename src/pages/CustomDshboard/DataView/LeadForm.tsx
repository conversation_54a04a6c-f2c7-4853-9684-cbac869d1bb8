import React, { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import logo from "@/assets/logo.svg";
import {
  useGetLeadSourceQuery,
  useAddLeadFromLinkMutation,
} from "@/redux/slices/propects";
import { toast } from "@/components/custom/Toast/MyToast";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import Multiselect from "@/components/custom/forms/Multiselect";
import { countryList } from "@/utils/countryList";

interface LeadFormData {
  name: string;
  phone: string;
  email?: string;
  marketerCode?: string;
  message?: string;
  accepted: boolean;
  leadSource?: string;
  city: string;
  country: string;
}

const LeadForm: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [form, setForm] = useState<LeadFormData>({
    name: "",
    phone: "",
    email: "",
    marketerCode: "",
    message: "",
    accepted: false,
    city: "",
    country: "",
  });

  // submitting is now isLoading from mutation
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  // const [leadSourceId, setLeadSourceId] = useState<number | null>(null);
  const [refCode, setRefCode] = useState<string>("");
  const [leadSourceName, setLeadSourceName] = useState<string>("");
  const [marketerName, setMarketerName] = useState<string>("");

  const [countryOptions, setCountryOptions] = useState<
    { value: any; label: string }[]
  >([]);
  const [selectedCountry, setSelectedCountry] = useState<{
    label: string;
    value: string;
  } | null>(null);

  const [addLead, { isLoading }] = useAddLeadFromLinkMutation();

  useEffect(() => {
    const countrySelectOptionsList = countryList?.map((country: any) => ({
      value: country?.label,
      label: `${country?.icon} ${country?.label}`,
    }));
    setCountryOptions(countrySelectOptionsList);
  }, [countryList]);

  useEffect(() => {
    const ls = searchParams.get("ls");
    if (ls) {
      try {
        const decoded = JSON.parse(atob(ls));
        console.log("Decoded ls param:", decoded);

        const leadSource = decoded?.leadSourceName;
        const marketerCode = decoded?.marketerCode;
        const refCode = decoded?.refCode;
        const marketerName = decoded?.marketerName;

        setRefCode(refCode);
        setMarketerName(marketerName);
        setLeadSourceName(leadSource);

        setForm((prev) => ({ ...prev, marketerCode }));
      } catch (e) {
        console.error("Invalid ls param");
      }
    }
  }, [searchParams]);

  // const { data: leadSourceData } = useGetLeadSourceQuery(
  //   { ref_code: refCode },
  //   { skip: !refCode }
  // );

  // useEffect(() => {
  //   console.log("leadSourceData:", leadSourceData);
  //   if (leadSourceData?.data?.results?.length > 0) {
  //     const source = leadSourceData.data.results[0];
  //     console.log("Setting leadSource:", source.name, "id:", source.id);
  //     setForm((prev) => ({ ...prev, leadSource: source.name }));
  //     setLeadSourceId(source.id);
  //   }
  // }, [leadSourceData]);

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const onToggleAccept = (checked: boolean) => {
    setForm((prev) => ({ ...prev, accepted: checked }));
  };

  const validate = () => {
    if (!form.name.trim()) return "Name is required";
    if (!form.phone.trim()) return "Phone number is required";
    if (form.email && form.email.trim()) {
      const emailOk = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email.trim());
      if (!emailOk) return "Please enter a valid email address";
    }
    if (!selectedCountry?.value) return "Country is required";
    if (!form.accepted) return "You must accept the terms and conditions";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    const validationError = validate();
    if (validationError) {
      setError(validationError);
      return;
    }

    console.log("leadSource ref code before submit:", refCode);
    if (!refCode) {
      setError("Lead source not found");
      return;
    }

    try {
      console.log("Submitting lead with data:", {
        marketer_code: form.marketerCode,
        lead_source_ref_code: refCode,
        name: form.name,
        phone: form.phone,
        email: form.email || "",
        comment: form.message || "",
        city: form.city,
        country: selectedCountry?.value,
      });
      await addLead({
        marketer_code: form.marketerCode,
        lead_source_ref_code: refCode,
        name: form.name,
        phone: form.phone,
        email: form.email || "",
        comment: form.message || "",
        city: form.city,
        country: selectedCountry?.value,
      }).unwrap();

      setSuccess(
        "Thank you for reaching out. Your details have been received successfully."
      );
      toast.success(
        "Thank you for reaching out. Your details have been received successfully."
      );
      setForm({
        name: "",
        phone: "",
        email: "",
        marketerCode: "",
        message: "",
        accepted: false,
        city: "",
        country: "",
      });
      setSelectedCountry(null);
    } catch (err: any) {
      const errorMessage =
        err?.data?.message || "Something went wrong while submitting the form";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-emerald-50 to-emerald-100 text-[15px] sm:text-[16px] text-slate-800">
      {/* Header */}
      <header className="py-6 sm:py-10">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 text-center">
          <img
            src={logo}
            alt="Optiven Logo"
            className="mx-auto h-14 sm:h-16 mb-3"
          />
          <h1 className="text-2xl sm:text-3xl font-semibold tracking-tight text-emerald-900">
            Optiven Contact Form
          </h1>
          {/* <p className="mt-2 text-slate-600">
            Kindly fill in your details below and we will get back to you shortly.
          </p> */}
        </div>
      </header>

      {/* Form Card */}
      <div className="max-w-3xl mx-auto px-4 sm:px-6 pb-10">
        <div className="overflow-hidden rounded-2xl bg-white/95 shadow-lg ring-1 ring-black/5">
          {/* Accent bar */}
          <div className="h-1.5 bg-emerald-600" />

          <div className="p-6 sm:p-8">
            {/* Intro copy */}
            <div className="space-y-2">
              <p className="text-slate-700">
                At{" "}
                <span className="font-semibold text-emerald-700">Optiven</span>,
                we value your inquiries. By filling out this form, you agree to
                share your information with us. We only use your data to respond
                to your request promptly and do not share it with third parties
                without your explicit consent.
              </p>
              <p className="text-slate-700">
                Please review our Privacy Policy to understand how we safeguard
                your personal information.
              </p>
              <p className="text-slate-800">
                <span className="font-medium text-emerald-800">
                  LEAD SOURCE:
                </span>{" "}
                {leadSourceName}
              </p>
              <p className="text-slate-800">
                <span className="font-medium text-emerald-800">
                  SALES MANAGER:
                </span>{" "}
                {marketerName}
              </p>
            </div>

            {/* Alerts */}
            <div className="mt-5 space-y-3">
              {error && (
                <div className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-red-700">
                  {error}
                </div>
              )}
              {success && (
                <div className="rounded-lg border border-green-200 bg-green-50 px-4 py-3 text-green-700">
                  {success}
                </div>
              )}
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="mt-6 space-y-6">
              {/* Name */}
              <div>
                <Label
                  htmlFor="name"
                  className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                >
                  Name <span className="text-rose-600">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Please enter your full name"
                  value={form.name}
                  onChange={onChange}
                  required
                  className="mt-2 h-11 w-full rounded-lg border border-slate-300 bg-white px-4 text-slate-900 placeholder:text-slate-400 shadow-sm focus-visible:ring-2 focus-visible:ring-emerald-600 focus-visible:outline-none"
                />
              </div>

              {/* Email */}
              <div>
                <Label
                  htmlFor="email"
                  className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                >
                  Email
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Please enter a valid email address"
                  value={form.email}
                  onChange={onChange}
                  className="mt-2 h-11 w-full rounded-lg border border-slate-300 bg-white px-4 text-slate-900 placeholder:text-slate-400 shadow-sm focus-visible:ring-2 focus-visible:ring-emerald-600 focus-visible:outline-none"
                />
              </div>

              {/* Phone */}
              <div>
                <Label
                  htmlFor="phone"
                  className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                >
                  Phone Number <span className="text-rose-600">*</span>
                  <span className="ml-1 font-normal text-slate-500">
                    (e.g., +2547...)
                  </span>
                </Label>
                <PhoneInput
                  value={form.phone}
                  onChange={(value) => {
                    setForm((prev) => ({
                      ...prev,
                      phone: `+${value}`,
                    }));
                  }}
                  country={selectedCountry?.value.toLowerCase() || "ke"}
                  enableSearch={true}
                  placeholder="700 111 111"
                  containerClass="w-full"
                  inputClass="w-full h-11 rounded-lg border border-slate-300 bg-white text-slate-900 placeholder:text-slate-400 shadow-sm focus:ring-2 focus:ring-emerald-600 focus:outline-none"
                  buttonClass="border border-slate-300 bg-white"
                />
              </div>

              {/* Country and City */}
              <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="selectedCountry"
                    className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                  >
                    Country <span className="text-rose-600">*</span>
                  </Label>
                  <Multiselect
                    value={selectedCountry}
                    data={countryOptions}
                    setValue={setSelectedCountry}
                    loading={false}
                    isClearable={false}
                    isDisabled={false}
                    isMultiple={false}
                    isSearchable={true}
                    // placeholder="Select country"
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="city"
                    className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                  >
                    City
                  </Label>
                  <Input
                    id="city"
                    name="city"
                    placeholder="Enter your city"
                    value={form.city}
                    onChange={onChange}
                    className="mt-2 h-11 w-full rounded-lg border border-slate-300 bg-white px-4 text-slate-900 placeholder:text-slate-400 shadow-sm focus-visible:ring-2 focus-visible:ring-emerald-600 focus-visible:outline-none"
                  />
                </div>
              </div>

              {/* Marketer Code */}
              <div>
                <Label
                  htmlFor="marketerCode"
                  className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                >
                  Marketer Code
                </Label>
                <Input
                  id="marketerCode"
                  name="marketerCode"
                  placeholder="Please enter a marketer code (as in CRM)"
                  value={form.marketerCode}
                  onChange={onChange}
                  readOnly
                  className="mt-2 h-11 w-full rounded-lg border border-slate-300 bg-slate-100 px-4 text-slate-900 placeholder:text-slate-400 shadow-sm focus-visible:ring-2 focus-visible:ring-emerald-600 focus-visible:outline-none"
                />
              </div>

              {/* Message */}
              <div>
                <Label
                  htmlFor="message"
                  className="text-emerald-900 text-[0.95rem] sm:text-base font-medium"
                >
                  Message / Interest
                </Label>
                <Textarea
                  id="message"
                  name="message"
                  placeholder="Kindly give any message, interest or concern that you have, if any."
                  value={form.message}
                  onChange={onChange}
                  rows={5}
                  className="mt-2 min-h-[120px] w-full rounded-lg border border-slate-300 bg-white px-4 py-3 text-slate-900 placeholder:text-slate-400 shadow-sm focus-visible:ring-2 focus-visible:ring-emerald-600 focus-visible:outline-none"
                />
              </div>

              {/* Acceptance */}
              <div className="flex items-start gap-3 rounded-xl border border-slate-200 bg-slate-50/60 px-4 py-3">
                <Checkbox
                  id="accepted"
                  checked={form.accepted}
                  onCheckedChange={(v) => onToggleAccept(!!v)}
                  className="mt-1"
                />
                <Label
                  htmlFor="accepted"
                  className="cursor-pointer text-slate-700"
                >
                  <span className="font-medium text-slate-800">Acceptance</span>{" "}
                  <span className="text-rose-600">*</span> — I / We confirm that
                  I / we have read and agree to the{" "}
                  <a
                    href="https://www.optiven.co.ke/privacy-policy/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline"
                  >
                    terms and conditions
                  </a>
                </Label>
              </div>

              {/* Submit */}
              <div className="pt-2">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="h-11 px-6 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg shadow-sm disabled:opacity-70"
                >
                  {isLoading ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </form>

            {/* Footer copy */}
            <div className="mt-6 border-t border-slate-200 pt-4 text-slate-500">
              <p className="text-sm">
                A copy of your responses will be emailed to the address you
                provided.
              </p>
              <p className="text-sm mt-1">Optiven Forms</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeadForm;
