# Offer Letter System

A comprehensive multi-step offer letter creation system for Optiven Limited.

## Overview

The offer letter system provides a user-friendly, step-by-step process for creating employment offer letters. It follows a structured flow that guides users through all necessary information collection and document generation.

## Features

### Multi-Step Process
1. **Onboarding** - Welcome screen and position details
2. **Personal Details** - Employee personal information
3. **Next of Kin** - Emergency contact information
4. **Payment Details** - Salary and banking information
5. **Terms & Conditions** - Legal agreements and policies
6. **Review** - Comprehensive review of all information
7. **Completion** - Final confirmation and document generation

### Key Components

- **Responsive Design** - Works on desktop and mobile devices
- **Form Validation** - Ensures all required fields are completed
- **Progress Tracking** - Visual step indicator shows current progress
- **Data Persistence** - Form data is maintained across steps
- **Professional UI** - Clean, modern interface matching company branding

## File Structure

```
src/pages/OfferLetter/
├── OfferLetter.tsx          # Main component
├── index.ts                 # Export file
├── README.md               # This documentation
├── images/                 # UI mockup images
└── steps/                  # Individual step components
    ├── OnboardingStep.tsx
    ├── PersonalDetailsStep.tsx
    ├── NextOfKinStep.tsx
    ├── PaymentDetailsStep.tsx
    ├── TermsConditionsStep.tsx
    ├── ReviewStep.tsx
    └── CompletionStep.tsx
```

## Usage

### Navigation
- Access via `/offer-letter` route
- Can be triggered from booking actions in inventory management

### Data Flow
1. User completes each step sequentially
2. Data is validated at each step
3. All information is reviewed before submission
4. Final document is generated and can be downloaded

### Integration Points
- **Inventory System** - Triggered from booking approvals
- **HR System** - Employee data integration
- **Email System** - Document delivery
- **PDF Generation** - Document creation

## Technical Details

### Dependencies
- React Hook Form for form management
- Multi-step form components
- UI components from the design system
- TypeScript for type safety

### Data Types
The system uses the `OfferLetterData` interface which includes:
- Employee information
- Personal details
- Emergency contacts
- Payment information
- Legal agreements

### Validation
- Required field validation
- Email format validation
- Phone number format validation
- Terms acceptance validation

## Future Enhancements

- API integration for data persistence
- PDF generation and download
- Email delivery system
- Template customization
- Digital signature support
- Document versioning
- Audit trail logging

## Support

For technical support or feature requests, contact the development team.
