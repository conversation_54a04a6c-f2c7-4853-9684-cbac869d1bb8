import React, { useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { But<PERSON> } from '@/components/ui/button';
import {
  Eye,
  User,
  Users,
  CreditCard,
  FileCheck,
  Building2,
  Calendar,
  Phone,
  Mail,
  MapPin,
  CheckCircle,
  AlertTriangle,
  Home,
  DollarSign,
  FileText,
  Shield,
  Download,
  Edit,
  Star
} from 'lucide-react';
import { OfferLetterData } from '../OfferLetter';
import { useFormContext } from '@/components/custom/forms/FormContext';
import { useGenerateOfferLetterPDFMutation } from '../api/offerLetterApi';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

// Initialize pdfMake
pdfMake.vfs = pdfFonts.vfs;

interface ReviewStepProps {
  data: OfferLetterData;
  onUpdate: (data: Partial<OfferLetterData>) => void;
}

const ReviewStep: React.FC<ReviewStepProps> = ({ data, onUpdate }) => {
  const { setStepValidator, goToStep, updateFormData } = useFormContext();
  const [generatePDF, { isLoading: isGeneratingPDF }] = useGenerateOfferLetterPDFMutation();

  // Set up validation for this step
  useEffect(() => {
    const validateStep = () => {
      return isDataComplete();
    };

    setStepValidator(6, validateStep); // Step 6 is the review step
  }, [setStepValidator]); // Remove data dependency to prevent infinite re-renders

  // Auto-confirm when data is complete - use a ref to track previous state
  const previousDataCompleteRef = useRef<boolean>(false);

  useEffect(() => {
    const currentDataComplete = isDataComplete();

    // Only update if the completion status has actually changed
    if (currentDataComplete !== previousDataCompleteRef.current) {
      previousDataCompleteRef.current = currentDataComplete;
      updateFormData({ confirmed: currentDataComplete });
    }
  }, [data, updateFormData]); // Keep data dependency but use ref to prevent unnecessary updates

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (value: string) => {
    if (!value) return 'Not provided';
    return `KES ${value.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
  };

  const handleDownloadPreview = async () => {
    if (!data.offer_letter_id) {
      // Generate PDF preview with current data
      const customerInfo = getCustomerDisplayInfo();
      const currentDate = new Date().toLocaleDateString();

      const docDefinition = {
        content: [
          // Header
          {
            text: 'OPTIVEN LIMITED',
            style: 'header',
            alignment: 'center',
            margin: [0, 0, 0, 10]
          },
          {
            text: 'OFFER LETTER',
            style: 'subheader',
            alignment: 'center',
            margin: [0, 0, 0, 20]
          },

          // Date
          {
            text: `Date: ${currentDate}`,
            alignment: 'right',
            margin: [0, 0, 0, 20]
          },

          // Customer Information Section
          {
            text: 'CUSTOMER INFORMATION',
            style: 'sectionHeader',
            margin: [0, 20, 0, 10]
          },
          {
            table: {
              widths: ['30%', '70%'],
              body: [
                ['Customer Type:', data.customer_type || 'Not specified'],
                ['Name:', customerInfo.name || 'Not provided'],
                ['Phone:', customerInfo.phone || 'Not provided'],
                ['Email:', customerInfo.email || 'Not provided'],
                ['National ID:', customerInfo.nationalId || 'Not provided'],
                ['Address:', customerInfo.address || 'Not provided']
              ]
            },
            layout: 'lightHorizontalLines',
            margin: [0, 0, 0, 20]
          },

          // Plot Information Section
          {
            text: 'PLOT INFORMATION',
            style: 'sectionHeader',
            margin: [0, 20, 0, 10]
          },
          {
            table: {
              widths: ['30%', '70%'],
              body: [
                ['Plot Number:', data.plot_number || 'Not specified'],
                ['Project:', data.project_name || 'Not specified'],
                ['Location:', data.project_location || 'Not specified']
              ]
            },
            layout: 'lightHorizontalLines',
            margin: [0, 0, 0, 20]
          },

          // Payment Details Section
          {
            text: 'PAYMENT DETAILS',
            style: 'sectionHeader',
            margin: [0, 20, 0, 10]
          },
          {
            table: {
              widths: ['30%', '70%'],
              body: [
                ['Total Price:', formatCurrency(data.payment_plan?.total_cash_price || '')],
                ['Initial Deposit:', formatCurrency(data.payment_plan?.deposit || '')],
                ['Payment Plan:', data.pricing?.Payment_Model || 'Not selected'],
                ['Number of Installments:', data.payment_plan?.no_of_instalments?.toString() || 'N/A'],
                ['Monthly Payment:', data.payment_plan?.monthly_installments ? formatCurrency(data.payment_plan.monthly_installments) : 'N/A']
              ]
            },
            layout: 'lightHorizontalLines',
            margin: [0, 0, 0, 20]
          },

          // Next of Kin Section
          {
            text: 'NEXT OF KIN INFORMATION',
            style: 'sectionHeader',
            margin: [0, 20, 0, 10]
          },
          {
            table: {
              widths: ['30%', '70%'],
              body: [
                ['Name:', data.next_of_kin?.full_name || 'Not provided'],
                ['Relationship:', data.next_of_kin?.relationship || 'Not provided'],
                ['Phone:', data.next_of_kin?.phone || 'Not provided'],
                ['Email:', data.next_of_kin?.email || 'Not provided'],
                ['Address:', data.next_of_kin?.address || 'Not provided']
              ]
            },
            layout: 'lightHorizontalLines',
            margin: [0, 0, 0, 20]
          },

          // Terms and Conditions
          {
            text: 'TERMS AND CONDITIONS',
            style: 'sectionHeader',
            margin: [0, 20, 0, 10]
          },
          {
            table: {
              widths: ['50%', '50%'],
              body: [
                ['Terms Accepted:', data.terms_accepted ? 'Yes' : 'No'],
                ['Privacy Policy Accepted:', data.privacyAccepted ? 'Yes' : 'No']
              ]
            },
            layout: 'lightHorizontalLines',
            margin: [0, 0, 0, 30]
          },

          // Footer
          {
            text: 'This is a computer-generated document and does not require a signature.',
            style: 'footer',
            alignment: 'center',
            margin: [0, 30, 0, 0]
          }
        ],
        styles: {
          header: {
            fontSize: 18,
            bold: true,
            color: '#2563eb'
          },
          subheader: {
            fontSize: 16,
            bold: true,
            color: '#1f2937'
          },
          sectionHeader: {
            fontSize: 14,
            bold: true,
            color: '#374151',
            decoration: 'underline'
          },
          footer: {
            fontSize: 10,
            italics: true,
            color: '#6b7280'
          }
        },
        defaultStyle: {
          fontSize: 11,
          lineHeight: 1.3
        }
      };

      // Generate and download PDF
      const fileName = `offer-letter-preview-${data.plot_number || 'draft'}-${new Date().toISOString().split('T')[0]}.pdf`;
      pdfMake.createPdf(docDefinition).download(fileName);

    } else {
      // Generate PDF for completed offer letter
      try {
        const result = await generatePDF(data.offer_letter_id).unwrap();
        if (result.pdf_url) {
          // Force download as PDF file
          const link = document.createElement('a');
          link.href = result.pdf_url;
          link.download = `offer-letter-${data.plot_number || data.offer_letter_id}.pdf`;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } catch (error) {
        console.error('Failed to generate PDF:', error);
        // Fallback to preview download
        handleDownloadPreview();
      }
    }
  };

  const isDataComplete = () => {
    // Check customer type specific requirements
    const hasCustomerInfo = () => {
      switch (data.customer_type) {
        case 'individual':
          return !!(data.individual?.first_name && data.individual?.last_name &&
                   data.individual?.national_id && data.individual?.phone);
        case 'company':
          return !!(data.company?.company_name && data.company?.phone &&
                   data.directors && data.directors.length > 0);
        case 'group':
          return !!(data.group?.group_name && data.group?.phone && data.group?.email);
        case 'partners':
          return true; // Partners form not implemented yet
        default:
          return false;
      }
    };

    return !!(
      data.customer_type &&
      hasCustomerInfo() &&
      data.next_of_kin?.full_name &&
      data.next_of_kin?.relationship &&
      data.next_of_kin?.phone &&
      data.payment_plan?.total_cash_price &&
      data.payment_plan?.deposit &&
      data.pricing?.Payment_Model &&
      data.terms_accepted &&
      data.privacyAccepted
    );
  };

  const getCustomerDisplayInfo = () => {
    switch (data.customer_type) {
      case 'individual':
        return {
          name: `${data.individual?.first_name || ''} ${data.individual?.last_name || ''}`.trim(),
          id: data.individual?.national_id || 'Not provided',
          phone: data.individual?.phone || 'Not provided',
          email: data.individual?.email || 'Not provided',
          type: 'Individual Customer'
        };
      case 'company':
        return {
          name: data.company?.company_name || 'Not provided',
          id: data.company?.company_registration_number || 'Not provided',
          phone: data.company?.phone || 'Not provided',
          email: data.company?.email || 'Not provided',
          type: 'Company Customer'
        };
      case 'group':
        return {
          name: data.group?.group_name || 'Not provided',
          id: data.group?.group_code || 'Not provided',
          phone: data.group?.phone || 'Not provided',
          email: data.group?.email || 'Not provided',
          type: 'Group Customer'
        };
      default:
        return {
          name: 'Not provided',
          id: 'Not provided',
          phone: 'Not provided',
          email: 'Not provided',
          type: 'Unknown'
        };
    }
  };

  const customerInfo = getCustomerDisplayInfo();

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="hidden h-12 w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 font-medium">
                Step 6 of 6
              </div>
              <div className="w-16 h-2 bg-gray-200 rounded-full">
                <div className="w-full h-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 lg:px-8 py-16">
        {/* Welcome Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Review Your Offer
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Please review all information carefully before submitting your land purchase offer letter
          </p>
          <div className="flex justify-center mt-8">
            {isDataComplete() ? (
              <Badge variant="default" className="bg-green-100 text-green-800 px-6 py-3 text-lg font-semibold">
                <CheckCircle className="w-5 h-5 mr-3" />
                Ready to Submit
              </Badge>
            ) : (
              <Badge variant="destructive" className="px-6 py-3 text-lg font-semibold">
                <AlertTriangle className="w-5 h-5 mr-3" />
                Incomplete Information
              </Badge>
            )}
          </div>
        </div>

        {/* Customer Information */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">
          <div className="bg-blue-50 border-b border-blue-200 p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <User className="w-6 h-6 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-blue-800">Customer Information</h2>
              </div>
              <Button
                variant="outline"
                size="lg"
                onClick={() => goToStep(2)}
                className="flex items-center gap-3 text-blue-600 border-blue-600 hover:bg-blue-50 px-6 py-3 rounded-xl"
              >
                <Edit className="w-5 h-5" />
                Edit
              </Button>
            </div>
            <p className="text-blue-700 text-lg mt-3">
              {customerInfo.type} - Land Purchase Details
            </p>
          </div>
          <div className="p-10">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div>
                  <Label>Customer Type</Label>
                  <Value className="capitalize font-semibold text-blue-600 text-xl">
                    {data.customer_type?.replace('_', ' ') || 'Not selected'}
                  </Value>
                </div>
                <div>
                  <Label>Full Name / Company Name</Label>
                  <Value>{customerInfo.name}</Value>
                </div>
                <div>
                  <Label>ID / Registration Number</Label>
                  <Value>{customerInfo.id}</Value>
                </div>
              </div>
              <div className="space-y-6">
                <div>
                  <Label>Phone Number</Label>
                  <Value className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-blue-600" />
                    {customerInfo.phone}
                  </Value>
                </div>
                <div>
                  <Label>Email Address</Label>
                  <Value className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-blue-600" />
                    {customerInfo.email}
                  </Value>
                </div>
                {data.customer_type === 'individual' && data.individual?.DOB && (
                  <div>
                    <Label>Date of Birth</Label>
                    <Value>{formatDate(data.individual.DOB)}</Value>
                  </div>
                )}
              </div>
            </div>

          {/* Directors Information for Company */}
          {data.customer_type === 'company' && data.directors && data.directors.length > 0 && (
            <div className="mt-6 pt-6 border-t border-blue-200">
              <h4 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <Building2 className="w-5 h-5 text-blue-600" />
                Company Directors ({data.directors.length})
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {data.directors.map((director, index) => (
                  <div key={director.director_id || index} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="font-medium text-gray-800">
                      {director.first_name} {director.last_name}
                    </p>
                    <p className="text-sm text-gray-600">ID: {director.national_id}</p>
                    <p className="text-sm text-gray-600">Phone: {director.phone}</p>
                    {director.email && <p className="text-sm text-gray-600">Email: {director.email}</p>}
                  </div>
                ))}
              </div>
            </div>
          )}
          </div>
        </div>

      {/* Next of Kin Information - Matching review2.png */}
      <Card className="border-2 border-purple-200 shadow-lg">
        <CardHeader className="bg-purple-50 border-b border-purple-200">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-purple-800 text-xl">
              <Users className="w-6 h-6" />
              Next of Kin Information
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => goToStep(3)}
              className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
            >
              <Edit className="w-4 h-4" />
              Edit
            </Button>
          </div>
          <p className="text-purple-700 text-sm mt-2">
            Emergency contact and next of kin details
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label>Full Name</Label>
                <Value>{data.next_of_kin?.full_name || 'Not provided'}</Value>
              </div>
              <div>
                <Label>Relationship</Label>
                <Value className="capitalize font-medium text-purple-600">
                  {data.next_of_kin?.relationship || 'Not provided'}
                </Value>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <Label>Phone Number</Label>
                <Value className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-purple-600" />
                  {data.next_of_kin?.phone || 'Not provided'}
                </Value>
              </div>
              <div>
                <Label>Email Address</Label>
                <Value className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-purple-600" />
                  {data.next_of_kin?.email || 'Not provided'}
                </Value>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Plan & Pricing - Matching review2.png */}
      <Card className="border-2 border-green-200 shadow-lg">
        <CardHeader className="bg-green-50 border-b border-green-200">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-green-800 text-xl">
              <CreditCard className="w-6 h-6" />
              Payment Plan & Pricing
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => goToStep(4)}
              className="flex items-center gap-2 text-green-600 border-green-600 hover:bg-green-50"
            >
              <Edit className="w-4 h-4" />
              Edit
            </Button>
          </div>
          <p className="text-green-700 text-sm mt-2">
            Selected payment plan and pricing details
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label>Payment Plan</Label>
                <Value className="capitalize font-semibold text-green-600">
                  {data.pricing?.Payment_Model?.replace('_', ' ') || 'Not selected'}
                </Value>
              </div>
              <div>
                <Label>Total Plot Price</Label>
                <Value className="text-xl font-bold text-gray-800">
                  {formatCurrency(data.payment_plan?.total_cash_price || '')}
                </Value>
              </div>
              <div>
                <Label>Initial Deposit</Label>
                <Value className="text-lg font-semibold text-green-600">
                  {formatCurrency(data.payment_plan?.deposit || '')}
                </Value>
              </div>
            </div>
            <div className="space-y-4">
              {data.payment_plan?.no_of_instalments && (
                <div>
                  <Label>Number of Installments</Label>
                  <Value>{data.payment_plan.no_of_instalments} months</Value>
                </div>
              )}
              <div>
                <Label>Balance Amount</Label>
                <Value className="text-lg font-medium text-orange-600">
                  {data.payment_plan?.total_cash_price && data.payment_plan?.deposit ?
                    formatCurrency(
                      (parseFloat((data.payment_plan.total_cash_price || '0').replace(/,/g, '')) -
                       parseFloat((data.payment_plan.deposit || '0').replace(/,/g, ''))).toString()
                    ) : 'Not calculated'}
                </Value>
              </div>
              {data.payment_plan?.no_of_instalments && data.payment_plan?.total_cash_price && data.payment_plan?.deposit && (
                <div>
                  <Label>Monthly Payment</Label>
                  <Value className="text-lg font-medium text-blue-600">
                    {formatCurrency(
                      Math.round((parseFloat((data.payment_plan.total_cash_price || '0').replace(/,/g, '')) -
                                 parseFloat((data.payment_plan.deposit || '0').replace(/,/g, ''))) /
                                parseInt(data.payment_plan.no_of_instalments)).toString()
                    )}
                  </Value>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Terms & Conditions Status - Matching review3.png */}
      <Card className="border-2 border-orange-200 shadow-lg">
        <CardHeader className="bg-orange-50 border-b border-orange-200">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-orange-800 text-xl">
              <FileCheck className="w-6 h-6" />
              Terms & Conditions
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => goToStep(5)}
              className="flex items-center gap-2 text-orange-600 border-orange-600 hover:bg-orange-50"
            >
              <Edit className="w-4 h-4" />
              Edit
            </Button>
          </div>
          <p className="text-orange-700 text-sm mt-2">
            Legal agreements and consent status
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-orange-200">
              <div className="flex items-center gap-3">
                <CheckCircle className={`w-6 h-6 ${data.terms_accepted ? 'text-green-600' : 'text-gray-400'}`} />
                <div>
                  <p className="font-medium text-gray-800">Land Purchase Terms & Conditions</p>
                  <p className="text-sm text-gray-600">Legal agreement for land purchase</p>
                </div>
              </div>
              <Badge variant={data.terms_accepted ? "default" : "secondary"}
                     className={data.terms_accepted ? "bg-green-100 text-green-800" : ""}>
                {data.terms_accepted ? 'Accepted' : 'Not Accepted'}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-orange-200">
              <div className="flex items-center gap-3">
                <CheckCircle className={`w-6 h-6 ${data.privacyAccepted ? 'text-green-600' : 'text-gray-400'}`} />
                <div>
                  <p className="font-medium text-gray-800">Privacy Policy & Data Consent</p>
                  <p className="text-sm text-gray-600">Data processing and privacy agreement</p>
                </div>
              </div>
              <Badge variant={data.privacyAccepted ? "default" : "secondary"}
                     className={data.privacyAccepted ? "bg-green-100 text-green-800" : ""}>
                {data.privacyAccepted ? 'Accepted' : 'Not Accepted'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Offer Summary - Matching review3.png */}
      <Card className="border-2 border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-gray-100 to-blue-100 border-b">
          <CardTitle className="flex items-center gap-2 text-gray-800 text-xl">
            <Star className="w-6 h-6 text-yellow-500" />
            Offer Letter Summary
          </CardTitle>
          <p className="text-gray-700 text-sm mt-2">
            Complete overview of your land purchase offer
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-800 mb-1">Customer</h4>
              <p className="text-sm text-gray-600 capitalize">{data.customer_type?.replace('_', ' ')}</p>
              <p className="text-xs text-gray-500 mt-1">{customerInfo.name}</p>
            </div>

            <div className="text-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-800 mb-1">Total Price</h4>
              <p className="text-sm font-bold text-green-600">
                {formatCurrency(data.payment_plan?.total_cash_price || '')}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {data.pricing?.Payment_Model?.replace('_', ' ') || 'Payment plan'}
              </p>
            </div>

            <div className="text-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <FileCheck className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-800 mb-1">Status</h4>
              <p className={`text-sm font-medium ${isDataComplete() ? 'text-green-600' : 'text-orange-600'}`}>
                {isDataComplete() ? 'Ready to Submit' : 'Incomplete'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {isDataComplete() ? 'All requirements met' : 'Missing information'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Important Information & Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-amber-800">Important Notice</h4>
                <p className="text-sm text-amber-700 mt-1">
                  Please ensure all information is accurate before submitting. Once submitted,
                  this offer letter will be processed and you will receive a confirmation.
                  Changes after submission may require additional verification.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <Download className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800">Document Actions</h4>
                <p className="text-sm text-blue-700 mt-1 mb-3">
                  You can download a copy of your offer letter for your records.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-blue-600 border-blue-600 hover:bg-blue-100"
                  onClick={handleDownloadPreview}
                  disabled={isGeneratingPDF}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isGeneratingPDF ? 'Generating...' : 'Download Preview'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Final Confirmation */}
      {isDataComplete() ? (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-green-800 mb-2">Ready to Submit</h3>
              <p className="text-green-700 mb-4">
                All required information has been provided. You can now submit your land purchase offer letter.
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-green-600">
                <Shield className="w-4 h-4" />
                <span>Your information is secure and encrypted</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-red-800 mb-2">Incomplete Information</h3>
              <p className="text-red-700 mb-4">
                Please complete all required fields in the previous steps before submitting your offer letter.
              </p>
              <p className="text-sm text-red-600">
                Use the "Edit" buttons above to go back and complete missing information.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </div>
  );
};

// Helper components
const Label: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <span className="text-lg font-semibold text-gray-600 mb-2 block">{children}</span>
);

const Value: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = "" }) => (
  <p className={`text-xl font-medium text-gray-900 ${className}`}>{children}</p>
);

export default ReviewStep;
