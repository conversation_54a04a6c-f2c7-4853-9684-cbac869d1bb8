import React, { useEffect } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, Mail, Phone, MapPin, Building2, Users, Plus, Trash2, AlertCircle } from 'lucide-react';
import { OfferLetterData } from '../OfferLetter';
import { useFormContext } from '@/components/custom/forms/FormContext';
import PhoneInput from '@/components/custom/forms/PhoneInput';
import CountrySelect from '@/components/custom/forms/CountrySelect';
import { countries, type Country } from '@/data/countries';

interface PersonalDetailsStepProps {
  data: OfferLetterData;
  onUpdate: (data: Partial<OfferLetterData>) => void;
}

const PersonalDetailsStep: React.FC<PersonalDetailsStepProps> = ({ data, onUpdate }) => {
  const { setStepValidator } = useFormContext();

  // Set up validation for this step
  useEffect(() => {
    const validateStep = () => {
      if (!data.customer_type) return false;

      switch (data.customer_type) {
        case 'individual':
          return !!(
            data.individual?.first_name &&
            data.individual?.last_name &&
            data.individual?.national_id &&
            data.individual?.phone
          );
        case 'company':
          return !!(
            data.company?.company_name &&
            data.company?.phone &&
            data.directors &&
            data.directors.length > 0 &&
            data.directors.every(director =>
              director.first_name &&
              director.last_name &&
              director.phone &&
              director.national_id
            )
          );
        case 'group':
          return !!(
            data.group?.group_name &&
            data.group?.phone &&
            data.group?.email
          );
        case 'partners':
          // For now, return true since partners form is not implemented
          return true;
        default:
          return false;
      }
    };

    setStepValidator(2, validateStep); // Step 2 is the personal details step
  }, [
    data.customer_type,
    data.individual,
    data.company,
    data.group,
    data.directors,
    setStepValidator
  ]);

  const handleIndividualChange = (field: string, value: string) => {
    onUpdate({
      individual: {
        ...data.individual,
        [field]: value
      }
    });
  };

  const handleCompanyChange = (field: string, value: string) => {
    onUpdate({
      company: {
        ...data.company,
        [field]: value
      }
    });
  };

  const handleGroupChange = (field: string, value: string) => {
    onUpdate({
      group: {
        ...data.group,
        [field]: value
      }
    });
  };

  const addDirector = () => {
    const newDirector = {
      director_id: Date.now(),
      first_name: '',
      last_name: '',
      phone: '',
      email: '',
      national_id: ''
    };
    onUpdate({
      directors: [...(data.directors || []), newDirector]
    });
  };

  const updateDirector = (index: number, field: string, value: string) => {
    const updatedDirectors = [...(data.directors || [])];
    updatedDirectors[index] = { ...updatedDirectors[index], [field]: value };
    onUpdate({ directors: updatedDirectors });
  };

  const removeDirector = (index: number) => {
    const updatedDirectors = data.directors?.filter((_, i) => i !== index) || [];
    onUpdate({ directors: updatedDirectors });
  };

  const getHeaderInfo = () => {
    switch (data.customer_type) {
      case 'individual':
        return { icon: User, title: 'Individual Customer Details', description: 'Personal information for the land buyer' };
      case 'company':
        return { icon: Building2, title: 'Company Information', description: 'Company details and director information' };
      case 'group':
        return { icon: Users, title: 'Group Purchase Details', description: 'Group information and member details' };
      case 'partners':
        return { icon: Users, title: 'Partnership Details', description: 'Partner information for joint purchase' };
      default:
        return { icon: AlertCircle, title: 'Customer Details', description: 'Please select a customer type first' };
    }
  };

  const { icon: HeaderIcon, title, description } = getHeaderInfo();

  if (!data.customer_type) {
    return (
      <Card className="bg-amber-50 border-amber-200">
        <CardContent className="pt-6">
          <div className="flex items-center gap-3 text-amber-800">
            <AlertCircle className="w-5 h-5" />
            <p>Please select a customer type in the previous step to continue.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="hidden h-12 w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 font-medium">
                Step 2 of 6
              </div>
              <div className="w-16 h-2 bg-gray-200 rounded-full">
                <div className="w-6 h-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto px-6 lg:px-8 py-16">
        {/* Welcome Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            {data.customer_type === 'individual' ? 'Personal Information' :
             data.customer_type === 'company' ? 'Company Information' :
             data.customer_type === 'group' ? 'Group Information' : 'Partnership Information'}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Please provide your {data.customer_type} details to create your personalized offer letter
          </p>
        </div>

        {/* Individual Customer Form */}
        {data.customer_type === 'individual' && (
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            <div className="p-10">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <User className="w-6 h-6 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Personal Information</h2>
              </div>

              <div className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label htmlFor="first_name" className="text-lg font-semibold text-gray-800">
                      First Name *
                    </Label>
                    <Input
                      id="first_name"
                      placeholder="Enter your first name"
                      value={data.individual?.first_name || ''}
                      onChange={(e) => handleIndividualChange('first_name', e.target.value)}
                      required
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="last_name" className="text-lg font-semibold text-gray-800">
                      Last Name *
                    </Label>
                    <Input
                      id="last_name"
                      placeholder="Enter your last name"
                      value={data.individual?.last_name || ''}
                      onChange={(e) => handleIndividualChange('last_name', e.target.value)}
                      required
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label htmlFor="national_id" className="text-lg font-semibold text-gray-800">
                      National ID/Passport *
                    </Label>
                    <Input
                      id="national_id"
                      placeholder="Enter your ID/Passport number"
                      value={data.individual?.national_id || ''}
                      onChange={(e) => handleIndividualChange('national_id', e.target.value)}
                      required
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="DOB" className="text-lg font-semibold text-gray-800">
                      Date of Birth
                    </Label>
                    <Input
                      id="DOB"
                      type="date"
                      value={data.individual?.DOB || ''}
                      onChange={(e) => handleIndividualChange('DOB', e.target.value)}
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Phone Number *</Label>
                    <PhoneInput
                      value={data.individual?.phone || ''}
                      onChange={(value) => handleIndividualChange('phone', value)}
                      onCountryChange={(country) => handleIndividualChange('country_code', country.phoneCode)}
                      required
                      defaultCountryCode={data.individual?.country_code?.replace('+', '') === '254' ? 'KE' : 'KE'}
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="email" className="text-lg font-semibold text-gray-800">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={data.individual?.email || ''}
                      onChange={(e) => handleIndividualChange('email', e.target.value)}
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Country *</Label>
                    <CountrySelect
                      value={data.individual?.country_code ?
                        countries.find(c => c.name === data.individual?.country)?.code ||
                        countries.find(c => c.phoneCode === data.individual?.country_code)?.code ||
                        'KE' : 'KE'}
                      onChange={(countryCode) => {
                        const country = countries.find(c => c.code === countryCode);
                        if (country) {
                          handleIndividualChange('country', country.name);
                          handleIndividualChange('country_code', country.phoneCode);
                        }
                      }}
                      required
                      showFlag={true}
                      showPhoneCode={true}
                      className="h-14 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="city" className="text-lg font-semibold text-gray-800">
                      City
                    </Label>
                    <Input
                      id="city"
                      placeholder="Enter your city"
                      value={data.individual?.city || ''}
                      onChange={(e) => handleIndividualChange('city', e.target.value)}
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="KRA_Pin" className="text-lg font-semibold text-gray-800">
                    KRA PIN (Optional)
                  </Label>
                  <Input
                    id="KRA_Pin"
                    placeholder="Enter your KRA PIN"
                    value={data.individual?.KRA_Pin || ''}
                    onChange={(e) => handleIndividualChange('KRA_Pin', e.target.value)}
                    className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Company Customer Form */}
        {data.customer_type === 'company' && (
          <>
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
              <div className="p-10">
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-purple-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Company Information</h2>
                </div>

                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-3">
                      <Label htmlFor="company_name" className="text-lg font-semibold text-gray-800">
                        Company Name *
                      </Label>
                      <Input
                        id="company_name"
                        placeholder="Enter company name"
                        value={data.company?.company_name || ''}
                        onChange={(e) => handleCompanyChange('company_name', e.target.value)}
                        required
                        className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label htmlFor="company_registration_number" className="text-lg font-semibold text-gray-800">
                        Registration Number
                      </Label>
                      <Input
                        id="company_registration_number"
                        placeholder="Enter registration number"
                        value={data.company?.company_registration_number || ''}
                        onChange={(e) => handleCompanyChange('company_registration_number', e.target.value)}
                        className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                      />
                    </div>
                  </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label htmlFor="company_phone" className="text-lg font-semibold text-gray-800">Phone Number *</Label>
                    <Input
                      id="company_phone"
                      placeholder="+*********** 000"
                      value={data.company?.phone || ''}
                      onChange={(e) => handleCompanyChange('phone', e.target.value)}
                      required
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="company_email" className="text-lg font-semibold text-gray-800">Email Address</Label>
                    <Input
                      id="company_email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={data.company?.email || ''}
                      onChange={(e) => handleCompanyChange('email', e.target.value)}
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="company_address" className="text-lg font-semibold text-gray-800">Company Address</Label>
                  <Textarea
                    id="company_address"
                    placeholder="Enter company address"
                    value={data.company?.address || ''}
                    onChange={(e) => handleCompanyChange('address', e.target.value)}
                    rows={4}
                    className="border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg resize-none"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Country</Label>
                    <CountrySelect
                      value={data.company?.company_country_code ?
                        countries.find(c => c.name === data.company?.country)?.code ||
                        countries.find(c => c.phoneCode === data.company?.company_country_code)?.code ||
                        'KE' : 'KE'}
                      onChange={(countryCode) => {
                        const country = countries.find(c => c.code === countryCode);
                        if (country) {
                          handleCompanyChange('country', country.name);
                          handleCompanyChange('company_country_code', country.phoneCode);
                        }
                      }}
                      showFlag={true}
                      showPhoneCode={true}
                      className="h-14 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="company_kra" className="text-lg font-semibold text-gray-800">KRA PIN</Label>
                    <Input
                      id="company_kra"
                      placeholder="Enter KRA PIN"
                      value={data.company?.company_kra || ''}
                      onChange={(e) => handleCompanyChange('company_kra', e.target.value)}
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>
                </div>
              </div>
            </div>

            {/* Company Directors */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mt-8">
              <div className="p-10">
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900">Company Directors</h3>
                  </div>
                  <Button
                    onClick={addDirector}
                    size="lg"
                    className="flex items-center gap-3 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl"
                  >
                    <Plus className="w-5 h-5" />
                    Add Director
                  </Button>
                </div>

                <div className="space-y-8">
                  {data.directors?.map((director, index) => (
                    <div key={index} className="bg-gray-50 rounded-xl p-8 border border-gray-200">
                      <div className="flex items-center justify-between mb-6">
                        <h4 className="text-xl font-bold text-gray-900">Director {index + 1}</h4>
                        <Button
                          onClick={() => removeDirector(index)}
                          size="sm"
                          variant="destructive"
                          className="flex items-center gap-2 px-4 py-2 rounded-lg"
                        >
                          <Trash2 className="w-4 h-4" />
                          Remove
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="space-y-3">
                          <Label className="text-lg font-semibold text-gray-800">First Name *</Label>
                          <Input
                            placeholder="Enter first name"
                            value={director.first_name || ''}
                            onChange={(e) => updateDirector(index, 'first_name', e.target.value)}
                            required
                            className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                          />
                        </div>
                        <div className="space-y-3">
                          <Label className="text-lg font-semibold text-gray-800">Last Name *</Label>
                          <Input
                            placeholder="Enter last name"
                            value={director.last_name || ''}
                            onChange={(e) => updateDirector(index, 'last_name', e.target.value)}
                            required
                            className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                          />
                        </div>
                        <div className="space-y-3">
                          <Label className="text-lg font-semibold text-gray-800">Phone Number *</Label>
                          <Input
                            placeholder="+*********** 000"
                            value={director.phone || ''}
                            onChange={(e) => updateDirector(index, 'phone', e.target.value)}
                            required
                            className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                          />
                        </div>
                        <div className="space-y-3">
                          <Label className="text-lg font-semibold text-gray-800">Email Address</Label>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            value={director.email || ''}
                            onChange={(e) => updateDirector(index, 'email', e.target.value)}
                            className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                          />
                        </div>
                        <div className="space-y-3">
                          <Label className="text-lg font-semibold text-gray-800">National ID *</Label>
                          <Input
                            placeholder="Enter national ID"
                            value={director.national_id || ''}
                            onChange={(e) => updateDirector(index, 'national_id', e.target.value)}
                            required
                            className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                  {(!data.directors || data.directors.length === 0) && (
                    <div className="text-center py-12 text-gray-500">
                      <Building2 className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg">No directors added yet. Click "Add Director" to get started.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

      {/* Group Customer Form */}
      {data.customer_type === 'group' && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-10">
            <div className="flex items-center gap-4 mb-8">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">Group Information</h2>
            </div>

            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="group_name" className="text-lg font-semibold text-gray-800">Group Name *</Label>
                  <Input
                    id="group_name"
                    placeholder="Enter group name"
                    value={data.group?.group_name || ''}
                    onChange={(e) => handleGroupChange('group_name', e.target.value)}
                    required
                    className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="group_code" className="text-lg font-semibold text-gray-800">Group Code</Label>
                  <Input
                    id="group_code"
                    placeholder="Enter group code"
                    value={data.group?.group_code || ''}
                    onChange={(e) => handleGroupChange('group_code', e.target.value)}
                    className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="group_phone" className="text-lg font-semibold text-gray-800">Group Phone *</Label>
                  <Input
                    id="group_phone"
                    placeholder="+*********** 000"
                    value={data.group?.group_phone || ''}
                    onChange={(e) => handleGroupChange('group_phone', e.target.value)}
                    required
                    className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="group_email" className="text-lg font-semibold text-gray-800">Group Email</Label>
                  <Input
                    id="group_email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={data.group?.group_email || ''}
                    onChange={(e) => handleGroupChange('group_email', e.target.value)}
                    className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label className="text-lg font-semibold text-gray-800">Country</Label>
                  <CountrySelect
                    value={data.group?.Group_country ?
                      countries.find(c => c.name === data.group?.Group_country)?.code || 'KE' : 'KE'}
                    onChange={(countryCode) => {
                      const country = countries.find(c => c.code === countryCode);
                      if (country) {
                        handleGroupChange('Group_country', country.name);
                      }
                    }}
                    showFlag={true}
                    showPhoneCode={true}
                    className="h-14 rounded-xl text-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="Group_KRA_PIN" className="text-lg font-semibold text-gray-800">Group KRA PIN</Label>
                  <Input
                    id="Group_KRA_PIN"
                    placeholder="Enter KRA PIN"
                    value={data.group?.Group_KRA_PIN || ''}
                    onChange={(e) => handleGroupChange('Group_KRA_PIN', e.target.value)}
                    className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Partners Form */}
      {data.customer_type === 'partners' && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-10">
            <div className="flex items-center gap-4 mb-8">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">Partnership Information</h2>
            </div>
            <p className="text-lg text-gray-600 mb-10">
              Add details for all partners involved in this land purchase
            </p>

            <div className="space-y-8">
              {/* Partner 1 */}
              <div className="bg-gray-50 rounded-xl p-8 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Partner 1</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">First Name *</Label>
                    <Input
                      placeholder="Enter first name"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Last Name *</Label>
                    <Input
                      placeholder="Enter last name"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">National ID/Passport *</Label>
                    <Input
                      placeholder="Enter ID/Passport number"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Phone Number *</Label>
                    <PhoneInput
                      value=""
                      onChange={() => {}}
                      onCountryChange={() => {}}
                      required
                      defaultCountryCode="KE"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>
              </div>

              {/* Partner 2 */}
              <div className="bg-gray-50 rounded-xl p-8 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Partner 2</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">First Name *</Label>
                    <Input
                      placeholder="Enter first name"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Last Name *</Label>
                    <Input
                      placeholder="Enter last name"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">National ID/Passport *</Label>
                    <Input
                      placeholder="Enter ID/Passport number"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-lg font-semibold text-gray-800">Phone Number *</Label>
                    <PhoneInput
                      value=""
                      onChange={() => {}}
                      onCountryChange={() => {}}
                      required
                      defaultCountryCode="KE"
                      className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Important Note */}
      <div className="bg-amber-50 rounded-2xl border border-amber-200 p-8 mt-12">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center flex-shrink-0">
            <AlertCircle className="w-6 h-6 text-amber-600" />
          </div>
          <div>
            <h4 className="text-xl font-bold text-amber-800 mb-3">Important Information</h4>
            <p className="text-lg text-amber-700 leading-relaxed">
              Please ensure all information is accurate as it will be used for legal documentation,
              land title processing, and official records. Any changes after submission may require
              additional verification and processing time.
            </p>
            {data.customer_type === 'company' && (
              <p className="text-lg text-amber-700 mt-4 leading-relaxed">
                <strong>Note:</strong> All company directors must be listed as they will be required
                for legal documentation and signing processes.
              </p>
            )}
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default PersonalDetailsStep;
