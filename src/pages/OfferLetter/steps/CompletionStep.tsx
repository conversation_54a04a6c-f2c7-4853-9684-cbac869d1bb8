import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Download,
  Mail,
  Calendar,
  FileText,
  Star,
  ArrowRight,
  Home,
  Phone
} from 'lucide-react';
import { OfferLetterData } from '../OfferLetter';
import { useGenerateOfferLetterPDFMutation, useSendOfferLetterEmailMutation } from '../api/offerLetterApi';

interface CompletionStepProps {
  data: OfferLetterData;
}

const CompletionStep: React.FC<CompletionStepProps> = ({ data }) => {
  const [generatePDF, { isLoading: isGeneratingPDF }] = useGenerateOfferLetterPDFMutation();
  const [sendEmail, { isLoading: isSendingEmail }] = useSendOfferLetterEmailMutation();

  const handleDownloadPDF = async () => {
    if (!data.id) return;

    try {
      const result = await generatePDF(data.id).unwrap();
      // Force download as PDF file
      const link = document.createElement('a');
      link.href = result.pdf_url;
      link.download = `offer-letter-${data.plot_number || data.id}.pdf`;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      // Handle error (show toast, etc.)
    }
  };

  const handleSendEmail = async () => {
    if (!data.id) return;

    try {
      const email = data.individual?.email || data.company?.email || data.group?.group_email;
      await sendEmail({ id: data.id, email }).unwrap();
      // Show success message
      alert('Offer letter sent successfully!');
    } catch (error) {
      console.error('Failed to send email:', error);
      // Handle error (show toast, etc.)
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="hidden h-12 w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-lg text-green-600 font-bold">
                ✓ Completed
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto px-6 lg:px-8 py-16">
        {/* Success Header */}
        <div className="text-center space-y-12 mb-16">
          <div className="mx-auto w-40 h-40 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-2xl animate-bounce">
            <CheckCircle className="w-20 h-20 text-white" />
          </div>
          <div className="space-y-6">
            <h1 className="text-6xl font-bold text-green-800">Congratulations! 🎉</h1>
            <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Your offer letter has been successfully completed and submitted. We're excited to help you with your land purchase journey!
            </p>
            <div className="flex justify-center mt-8">
              <Badge variant="default" className="bg-green-100 text-green-800 px-8 py-4 text-xl font-bold">
                <Star className="w-6 h-6 mr-3" />
                Application Complete
              </Badge>
            </div>
          </div>
        </div>

      {/* Summary Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-blue-50 border-b border-blue-200 p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-blue-800">Offer Summary</h3>
            </div>
          </div>
          <div className="p-8 space-y-6">
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Plot Number:</span>
              <p className="text-xl font-bold text-gray-900">{data.plot_number || 'Not specified'}</p>
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Project:</span>
              <p className="text-xl font-bold text-gray-900">{data.project_name || 'Not specified'}</p>
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Customer Type:</span>
              <p className="text-xl font-bold text-gray-900 capitalize">{data.customer_type || 'Not specified'}</p>
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Total Price:</span>
              <p className="text-2xl font-bold text-green-600">KES {data.payment_plan?.total_cash_price || 'Not specified'}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-green-50 border-b border-green-200 p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <Mail className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-green-800">Contact Information</h3>
            </div>
          </div>
          <div className="p-8 space-y-6">
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Customer Name:</span>
              <p className="text-xl font-bold text-gray-900">
                {data.customer_type === 'individual'
                  ? `${data.individual?.first_name || ''} ${data.individual?.last_name || ''}`.trim() || 'Not specified'
                  : data.customer_type === 'company'
                  ? data.company?.company_name || 'Not specified'
                  : data.customer_type === 'group'
                  ? data.group?.group_name || 'Not specified'
                  : 'Not specified'
                }
              </p>
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Email:</span>
              <p className="text-xl font-bold text-gray-900">
                {data.individual?.email || data.company?.email || data.group?.group_email || 'Not specified'}
              </p>
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Phone:</span>
              <p className="text-xl font-bold text-gray-900">
                {data.individual?.phone || data.company?.phone || data.group?.group_phone || 'Not specified'}
              </p>
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-600 block mb-2">Lead File:</span>
              <p className="text-xl font-bold text-gray-900">{data.lead_file || 'To be assigned'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-purple-50 border-b border-purple-200 p-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Download className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-purple-800">Download & Share</h3>
              <p className="text-lg text-purple-700 mt-1">
                Get a copy of your offer letter and share it as needed.
              </p>
            </div>
          </div>
        </div>
        <div className="p-8">
          <div className="flex flex-col sm:flex-row gap-6">
            <Button
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF || !data.id}
              className="flex items-center gap-3 bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl text-lg font-semibold"
              size="lg"
            >
              <Download className="w-5 h-5" />
              {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
            </Button>
            <Button
              variant="outline"
              onClick={handleSendEmail}
              disabled={isSendingEmail || !data.id}
              className="flex items-center gap-3 border-2 border-green-600 text-green-600 hover:bg-green-50 px-8 py-4 rounded-xl text-lg font-semibold"
              size="lg"
            >
              <Mail className="w-5 h-5" />
              {isSendingEmail ? 'Sending...' : 'Email Copy'}
            </Button>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Calendar className="w-5 h-5" />
            What Happens Next?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                1
              </div>
              <div>
                <h4 className="font-medium text-blue-800">HR Review</h4>
                <p className="text-sm text-blue-700">
                  Our HR team will review your application within 2-3 business days.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                2
              </div>
              <div>
                <h4 className="font-medium text-blue-800">Background Verification</h4>
                <p className="text-sm text-blue-700">
                  We'll conduct background checks and verify your references.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                3
              </div>
              <div>
                <h4 className="font-medium text-blue-800">Final Confirmation</h4>
                <p className="text-sm text-blue-700">
                  You'll receive final confirmation and onboarding instructions via email.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                4
              </div>
              <div>
                <h4 className="font-medium text-blue-800">Welcome to Optiven!</h4>
                <p className="text-sm text-blue-700">
                  Begin your journey with us on your specified start date.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className="border-gray-200 bg-gray-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-800">
            <Phone className="w-5 h-5" />
            Need Help?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-800">HR Department</h4>
              <p className="text-sm text-gray-600"><EMAIL></p>
              <p className="text-sm text-gray-600">+254 790 300 300</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-800">Office Hours</h4>
              <p className="text-sm text-gray-600">Monday - Friday: 8:00 AM - 5:00 PM</p>
              <p className="text-sm text-gray-600">Saturday: 9:00 AM - 1:00 PM</p>
            </div>
          </div>
        </CardContent>
      </Card>

        {/* Navigation */}
        <div className="flex justify-center pt-6">
          <Button variant="outline" onClick={() => window.location.href = '/'} className="flex items-center gap-2">
            <Home className="w-4 h-4" />
            Return to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CompletionStep;
