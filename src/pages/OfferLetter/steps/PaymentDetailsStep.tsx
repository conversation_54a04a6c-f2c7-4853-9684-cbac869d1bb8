import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { CreditCard, DollarSign, Home, Shield, AlertTriangle, Banknote, Calculator, MapPin, Calendar, Percent, FileText, Loader2 } from 'lucide-react';
import { OfferLetterData } from '../OfferLetter';
import { useFormContext } from '@/components/custom/forms/FormContext';
import { useGetPlotPaymentOptionsQuery, useGetOfferLetterPricingQuery, useGetDepositPaidQuery } from '../api/offerLetterApi';

interface PaymentDetailsStepProps {
  data: OfferLetterData;
  onUpdate: (data: Partial<OfferLetterData>) => void;
}

const PaymentDetailsStep: React.FC<PaymentDetailsStepProps> = ({ data, onUpdate }) => {
  const [selectedPaymentPlan, setSelectedPaymentPlan] = useState<string>(data.pricing?.Payment_Model || '');
  const { setStepValidator } = useFormContext();

  // Fetch plot payment options for the selected plot
  const { data: plotPaymentOptions, isLoading: plotOptionsLoading } = useGetPlotPaymentOptionsQuery(
    { PLOT_NO: data.plot_number || 'ALL' },
    { skip: !data.plot_number }
  );

  // Fetch pricing data
  const { data: pricingData, isLoading: pricingLoading } = useGetOfferLetterPricingQuery({});

  // Fetch deposit paid information
  const { data: depositPaidData } = useGetDepositPaidQuery(
    {
      plot_no: data.plot_number
    },
    { skip: !data.plot_number }
  );

  // Auto-populate payment details when pricing data is available
  useEffect(() => {
    if (data.pricing && !data.payment_plan?.total_cash_price) {
      // Get deposit paid amount, default to 0 if no data available
      const depositPaidAmount = depositPaidData?.total_paid || 0;

      const defaultPaymentPlan = {
        plot_no: data.plot_number || '',
        total_cash_price: data.pricing.Deposit || '0',
        deposit: depositPaidAmount.toString(),
        no_of_instalments: 12, // Default to 12 months
        monthly_installments: '0'
      };

      // Calculate monthly installments if we have total price and deposit
      if (data.pricing.Deposit) {
        const totalPrice = parseFloat(data.pricing.Deposit.replace(/,/g, ''));
        const balance = totalPrice - depositPaidAmount;
        const monthlyPayment = balance / 12;
        defaultPaymentPlan.monthly_installments = monthlyPayment.toFixed(2);
      }

      onUpdate({
        payment_plan: defaultPaymentPlan,
        pricing: data.pricing
      });
    }
  }, [data.pricing, data.plot_number, data.payment_plan?.total_cash_price, depositPaidData, onUpdate]);

  // Set up validation for this step
  useEffect(() => {
    const validateStep = () => {
      const hasBasicInfo = !!(
        selectedPaymentPlan &&
        data.payment_plan?.total_cash_price &&
        data.payment_plan?.deposit
      );

      // For installment plans, also check number of installments
      if (selectedPaymentPlan && selectedPaymentPlan !== 'cash' && selectedPaymentPlan !== '30days') {
        return hasBasicInfo && !!(data.payment_plan?.no_of_instalments);
      }

      return hasBasicInfo;
    };

    setStepValidator(4, validateStep); // Step 4 is the payment details step
  }, [selectedPaymentPlan, data.payment_plan, setStepValidator]);

  // Auto-populate pricing when plot and payment plan are selected
  useEffect(() => {
    if (selectedPaymentPlan && pricingData?.results && data.plot_number) {
      // Find matching pricing for the selected plot and payment model
      const matchingPricing = pricingData.results.find((pricing: any) =>
        pricing.Payment_Model === selectedPaymentPlan &&
        pricing.Project_No === data.project_id
      );

      if (matchingPricing) {
        // Auto-populate pricing information
        onUpdate({
          pricing: {
            ...data.pricing,
            ...matchingPricing
          }
        });

        // Auto-populate payment plan based on pricing
        const installmentField = `_${selectedPaymentPlan.split('_')[1]}M_IN`;
        const totalPrice = (matchingPricing as any)[installmentField] || matchingPricing.Deposit;

        if (totalPrice) {
          onUpdate({
            payment_plan: {
              ...data.payment_plan,
              total_cash_price: totalPrice,
              plot_no: data.plot_number,
            }
          });
        }
      }
    }
  }, [selectedPaymentPlan, pricingData, data.plot_number, data.project_id]);

  const handlePaymentPlanChange = (field: string, value: string) => {
    onUpdate({
      payment_plan: {
        ...data.payment_plan,
        [field]: value
      }
    });
  };

  const handlePricingChange = (field: string, value: string) => {
    onUpdate({
      pricing: {
        ...data.pricing,
        [field]: value
      }
    });
  };

  const handlePaymentModelChange = (value: string) => {
    setSelectedPaymentPlan(value);
    handlePricingChange('Payment_Model', value);

    // Auto-populate payment details based on selected plan and actual API data
    const plans = getPaymentPlans();
    const selectedPlan = plans.find(plan => plan.id === value);

    if (selectedPlan && selectedPlan.data) {
      const planData = selectedPlan.data;

      let paymentPlanData = {
        plot_no: data.plot_number || '',
        total_cash_price: planData.cash_price?.toString() || '0',
        deposit: planData.deposit?.toString() || '0',
        no_of_instalments: planData.no_of_ins || 1,
        monthly_installments: planData.monthly_install?.toString() || '0'
      };

      // For cash and 30days payments, set monthly installments to the full amount
      if (value === 'cash' || value === '30days') {
        paymentPlanData.monthly_installments = planData.cash_price?.toString() || '0';
        paymentPlanData.no_of_instalments = 1;
      }

      // Update the form data with actual API values
      onUpdate({
        payment_plan: paymentPlanData,
        pricing: {
          ...data.pricing,
          Payment_Model: value === 'cash' ? 'cash' : value === '30days' ? '30days' : `${value}_months`,
          Deposit: planData.deposit?.toString() || '0'
        }
      });
    }
  };

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^\d.]/g, '');
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleCurrencyChange = (field: string, value: string) => {
    const formatted = formatCurrency(value);
    handlePaymentPlanChange(field, formatted);
  };

  // Generate payment plan options based on actual API data
  const getPaymentPlans = () => {
    // First priority: Use plot payment options if available
    if (plotPaymentOptions && (plotPaymentOptions as any).Options) {
      try {
        // Parse the Options string which contains JSON arrays
        const optionsString = (plotPaymentOptions as any).Options;
        const optionMatches = optionsString.match(/\[{[^}]+}\]/g);

        if (optionMatches) {
          const plans: any[] = [];

          optionMatches.forEach((optionStr: string) => {
            try {
              const option = JSON.parse(optionStr)[0]; // Each match is an array with one object

              if (option.no_of_ins === 1) {
                // Cash payment option
                plans.push({
                  id: 'cash',
                  title: 'Cash Payment',
                  subtitle: 'Full payment upfront',

                  description: option.statement || 'Pay the full amount at once and enjoy benefits.',
                  features: ['Immediate ownership', 'No interest charges', 'Quick processing', 'Complete ownership'],
                  price: `KES ${option.cash_price?.toLocaleString() || '0'}`,
                  data: option
                });
              } else {
                // Installment payment options
                const months = option.no_of_ins;
                const monthlyAmount = option.monthly_install;

                plans.push({
                  id: months.toString(),
                  title: `${months} Months Plan`,
                  subtitle: months <= 6 ? 'Short-term installments' :
                           months <= 12 ? 'Medium-term installments' : 'Long-term installments',

                  description: `Spread your payment over ${months} months with flexible terms.`,
                  features: [
                    'Flexible payments',
                    months <= 12 ? 'Manageable installments' : 'Extended timeline',
                    'Secure processing',
                    months <= 6 ? 'Quick ownership' : 'Maximum flexibility'
                  ],
                  price: `KES ${monthlyAmount?.toLocaleString() || '0'}/month`,
                  data: option
                });
              }
            } catch (parseError) {
              console.error('Error parsing option:', parseError);
            }
          });

          if (plans.length > 0) {
            return plans;
          }
        }
      } catch (error) {
        console.error('Error parsing plot payment options:', error);
      }
    }

    // Second priority: Use pricing data if available
    if (pricingData?.results && pricingData.results.length > 0) {
      const relevantPricing = pricingData.results.find((pricing: any) =>
        pricing.Project_No === data.project_id ||
        pricing.Plot_Type === data.plot_type
      ) || pricingData.results[0];

      const plans: any[] = [];
      const deposit = relevantPricing.Deposit ? parseFloat(relevantPricing.Deposit) : 0;
      // Get deposit paid amount, default to 0 if no data available
      const depositPaidAmount = depositPaidData?.total_paid || 0;

      // Cash payment option
      if (deposit > 0) {
        plans.push({
          id: 'cash',
          title: 'Cash Payment',
          subtitle: 'Full payment upfront',
          description: 'Pay the full amount at once for immediate ownership.',
          features: ['Immediate ownership', 'No interest charges', 'Quick processing', 'Complete ownership'],
          price: `KES ${deposit.toLocaleString()}`,
          data: { cash_price: deposit, deposit: depositPaidAmount, no_of_ins: 1 }
        });
      }

      // 30-day payment option using plot price (Deposit field)
      if (deposit > 0) {
        plans.push({
          id: '30days',
          title: '30 Days Payment',
          subtitle: 'Short-term payment',
          description: 'Pay the full plot price within 30 days.',
          features: ['30-day payment window', 'Full plot ownership', 'Secure processing', 'Quick completion'],
          price: `KES ${deposit.toLocaleString()}`,
          data: {
            cash_price: deposit,
            deposit: depositPaidAmount,
            monthly_install: deposit,
            no_of_ins: 1,
            payment_period: '30 days'
          }
        });
      }

      // Installment options based on available data
      const installmentOptions = [
        { months: 3, field: '_3M_IN' },
        { months: 6, field: '_6M_IN' },
        { months: 12, field: '_12M_IN' },
        { months: 15, field: '_15M_IN' },
        { months: 24, field: '_24M_IN' }
      ];

      installmentOptions.forEach(option => {
        const monthlyAmount = (relevantPricing as any)[option.field];
        if (monthlyAmount && parseFloat(monthlyAmount) > 0) {
          plans.push({
            id: option.months.toString(),
            title: `${option.months} Months Plan`,
            subtitle: option.months <= 6 ? 'Short-term installments' :
                     option.months <= 12 ? 'Medium-term installments' : 'Long-term installments',
            description: `Spread your payment over ${option.months} months with flexible terms.`,
            features: [
              'Flexible payments',
              option.months <= 12 ? 'Manageable installments' : 'Extended timeline',
              'Secure processing',
              option.months <= 6 ? 'Quick ownership' : 'Maximum flexibility'
            ],
            price: `KES ${parseFloat(monthlyAmount).toLocaleString()}/month`,
            data: {
              cash_price: deposit,
              deposit: depositPaidAmount,
              monthly_install: parseFloat(monthlyAmount),
              no_of_ins: option.months
            }
          });
        }
      });

      if (plans.length > 0) {
        return plans;
      }
    }

    // Fallback: Default plans with zero values if no data available
    return [
      {
        id: 'cash',
        title: 'Cash Payment',
        subtitle: 'Full payment upfront',
        description: 'Pay the full amount at once for immediate ownership.',
        features: ['Immediate ownership', 'No interest charges', 'Quick processing', 'Complete ownership'],
        price: 'KES 0',
        data: { cash_price: 0, deposit: 0, no_of_ins: 1 }
      },
      {
        id: '6',
        title: '6 Months Plan',
        subtitle: 'Short-term installments',
        description: 'Spread your payment over 6 months with flexible terms.',
        features: ['Flexible payments', 'Manageable installments', 'Secure processing', 'Quick ownership'],
        price: 'KES 0/month',
        data: { cash_price: 0, deposit: 0, monthly_install: 0, no_of_ins: 6 }
      },
      {
        id: '12',
        title: '12 Months Plan',
        subtitle: 'Medium-term installments',
        description: 'Pay over 12 months with manageable monthly installments.',
        features: ['Affordable monthly payments', 'Manageable installments', 'Secure processing', 'Balanced approach'],
        price: 'KES 0/month',
        data: { cash_price: 0, deposit: 0, monthly_install: 0, no_of_ins: 12 }
      },
      {
        id: '24',
        title: '24 Months Plan',
        subtitle: 'Long-term installments',
        description: 'Extended payment period with the lowest monthly payments.',
        features: ['Lowest monthly payments', 'Extended timeline', 'Flexible terms', 'Maximum flexibility'],
        price: 'KES 0/month',
        data: { cash_price: 0, deposit: 0, monthly_install: 0, no_of_ins: 24 }
      }
    ];
  };

  const paymentPlans = getPaymentPlans();

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4 sm:py-6 lg:py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-8 sm:h-10 lg:h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              <div className="hidden h-8 sm:h-10 lg:h-12 w-32 sm:w-36 lg:w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-lg sm:text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-4">
              <div className="text-xs sm:text-sm text-gray-500 font-medium">
                Step 4 of 6
              </div>
              <div className="w-12 sm:w-16 h-2 bg-gray-200 rounded-full">
                <div className="w-9 sm:w-12 h-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16">
        {/* Welcome Section */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight px-4">
            Choose Your Payment Plan
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
            Select the payment option that works best for you. All plans include secure processing and flexible terms.
          </p>
          {data.plot_number && (
            <div className="flex justify-center mt-4 sm:mt-6 lg:mt-8 px-4">
              <Badge variant="outline" className="text-green-700 border-green-600 bg-green-50 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-sm sm:text-base lg:text-lg font-semibold">
                <Home className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3" />
                <span className="truncate">Plot {data.plot_number} - {data.project_name}</span>
              </Badge>
            </div>
          )}
        </div>

      {/* Loading State */}
      {(pricingLoading || plotOptionsLoading) && (
        <div className="bg-blue-50 rounded-2xl border border-blue-200 p-4 sm:p-6 lg:p-8 mx-4 sm:mx-0">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4">
            <Loader2 className="w-6 h-6 sm:w-8 sm:h-8 animate-spin text-blue-600" />
            <p className="text-blue-800 text-base sm:text-lg font-medium text-center">Loading payment options for your selected plot...</p>
          </div>
        </div>
      )}

      {/* Payment Plan Selection */}
      <div className="space-y-8 lg:space-y-12">
        <div className="text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Available Payment Options</h2>
          <p className="text-lg sm:text-xl text-gray-600">Choose the plan that fits your budget and timeline</p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-4 sm:p-6 lg:p-10">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6 lg:mb-10">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                <CreditCard className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Select Your Payment Plan</h3>
            </div>

            <RadioGroup
              value={selectedPaymentPlan}
              onValueChange={handlePaymentModelChange}
              className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8"
            >
              {paymentPlans.map((plan: any) => (
                <div
                  key={plan.id}
                  className={`relative p-4 sm:p-6 lg:p-8 border-2 rounded-2xl transition-all duration-300 cursor-pointer hover:shadow-xl ${
                    selectedPaymentPlan === plan.id
                      ? 'border-green-500 bg-green-50 shadow-xl'
                      : 'border-gray-200 hover:border-green-300 hover:shadow-lg'
                  }`}
                >
                  <div className="flex items-start space-x-3 sm:space-x-4">
                    <RadioGroupItem value={plan.id} id={plan.id} className="mt-2 sm:mt-3 w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <Label htmlFor={plan.id} className="cursor-pointer">
                        <div className="space-y-4 sm:space-y-6">
                          {/* Plan Header */}
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <div className="min-w-0">
                              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate">{plan.title}</h3>
                              <p className="text-sm sm:text-base lg:text-lg text-gray-600 mt-1">{plan.subtitle}</p>
                            </div>
                            {plan.id === 'cash' && (
                              <Badge
                                variant="default"
                                className="bg-green-600 px-2 sm:px-3 py-1 text-xs sm:text-sm font-semibold self-start sm:self-auto"
                              >
                                Recommended
                              </Badge>
                            )}
                          </div>

                          {/* Plan Description */}
                          <p className="text-gray-700 text-sm sm:text-base lg:text-lg leading-relaxed">
                            {plan.description}
                          </p>

                          {/* Plan Features */}
                          <div className="space-y-2 sm:space-y-3">
                            <h4 className="font-bold text-gray-800 text-sm sm:text-base lg:text-lg">Key Benefits:</h4>
                            <ul className="space-y-1 sm:space-y-2">
                              {plan.features.map((feature: string, index: number) => (
                                <li key={index} className="flex items-start text-sm sm:text-base lg:text-lg text-gray-600">
                                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 sm:mr-3 mt-1.5 sm:mt-2 flex-shrink-0"></div>
                                  <span className="break-words">{feature}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Plan Pricing Preview */}
                          <div className="bg-gray-100 rounded-xl p-3 sm:p-4 mt-4 sm:mt-6">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0">
                              <span className="text-gray-600 text-sm sm:text-base lg:text-lg">Starting from:</span>
                              <span className="font-bold text-green-600 text-lg sm:text-xl break-all">
                                {plan.price}
                              </span>
                            </div>
                          </div>
                        </div>
                      </Label>
                    </div>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        </div>

      {/* Payment Details Form */}
      {selectedPaymentPlan && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-4 sm:p-6 lg:p-10">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6 lg:mb-8">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <div className="min-w-0">
                <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Payment Details & Pricing</h3>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600 mt-1">
                  Configure your payment details for the selected {paymentPlans.find((p: any) => p.id === selectedPaymentPlan)?.title}
                </p>
              </div>
            </div>

            <div className="space-y-6 lg:space-y-10">
              {/* Plot Pricing */}
              <div className="space-y-4 lg:space-y-6">
                <h4 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">Plot Pricing Information</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                  <div className="space-y-2 sm:space-y-3">
                    <Label htmlFor="total_cash_price" className="text-sm sm:text-base lg:text-lg font-semibold text-gray-800">Total Plot Price (KES) *</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 sm:left-4 top-3 sm:top-4 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      <Input
                        id="total_cash_price"
                        placeholder="2,500,000"
                        className="pl-10 sm:pl-12 h-12 sm:h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-base sm:text-lg"
                        value={data.payment_plan?.total_cash_price || ''}
                        onChange={(e) => handleCurrencyChange('total_cash_price', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2 sm:space-y-3">
                    <Label htmlFor="deposit" className="text-sm sm:text-base lg:text-lg font-semibold text-gray-800">Initial Deposit (KES) *</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 sm:left-4 top-3 sm:top-4 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      <Input
                        id="deposit"
                        placeholder="500,000"
                        className="pl-10 sm:pl-12 h-12 sm:h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-base sm:text-lg"
                        value={data.payment_plan?.deposit || ''}
                        onChange={(e) => handleCurrencyChange('deposit', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>

            {/* Installment Details (only for installment plans) */}
            {selectedPaymentPlan !== 'cash' && selectedPaymentPlan !== '30days' && (
              <div className="space-y-4 lg:space-y-6">
                <h4 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">Installment Configuration</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                  <div className="space-y-2 sm:space-y-3">
                    <Label htmlFor="no_of_instalments" className="text-sm sm:text-base lg:text-lg font-semibold text-gray-800">Number of Installments</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 sm:left-4 top-3 sm:top-4 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      <Input
                        id="no_of_instalments"
                        type="number"
                        placeholder={selectedPaymentPlan === '6' ? '6' :
                                   selectedPaymentPlan === '12' ? '12' :
                                   selectedPaymentPlan === '24' ? '24' : selectedPaymentPlan}
                        className="pl-10 sm:pl-12 h-12 sm:h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-base sm:text-lg"
                        value={data.payment_plan?.no_of_instalments || ''}
                        onChange={(e) => handlePaymentPlanChange('no_of_instalments', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2 sm:space-y-3">
                    <Label htmlFor="Monthly_Interest" className="text-sm sm:text-base lg:text-lg font-semibold text-gray-800">Interest Rate (% per month)</Label>
                    <div className="relative">
                      <Percent className="absolute left-3 sm:left-4 top-3 sm:top-4 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      <Input
                        id="Monthly_Interest"
                        type="number"
                        step="0.1"
                        placeholder="1.5"
                        className="pl-10 sm:pl-12 h-12 sm:h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-base sm:text-lg"
                        value={data.pricing?.Monthly_Interest || ''}
                        onChange={(e) => handlePricingChange('Monthly_Interest', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Payment Summary */}
            {data.payment_plan?.total_cash_price && data.payment_plan?.deposit && (
              <div className="bg-gray-50 rounded-xl p-4 sm:p-6 lg:p-8 border-2 border-gray-200">
                <h4 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center gap-3">
                  <Calculator className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
                  Payment Summary
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  <div className="text-center p-4 sm:p-6 bg-blue-50 rounded-xl border border-blue-200">
                    <p className="text-blue-600 font-semibold text-sm sm:text-base lg:text-lg">Total Price</p>
                    <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mt-2 break-all">KES {data.payment_plan.total_cash_price}</p>
                  </div>
                  <div className="text-center p-4 sm:p-6 bg-green-50 rounded-xl border border-green-200">
                    <p className="text-green-600 font-semibold text-sm sm:text-base lg:text-lg">Initial Deposit</p>
                    <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mt-2 break-all">KES {data.payment_plan.deposit}</p>
                  </div>
                  <div className="text-center p-4 sm:p-6 bg-orange-50 rounded-xl border border-orange-200">
                    <p className="text-orange-600 font-semibold text-sm sm:text-base lg:text-lg">Balance</p>
                    <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mt-2 break-all">
                      KES {(parseFloat((data.payment_plan.total_cash_price || '0').replace(/,/g, '')) -
                           parseFloat((data.payment_plan.deposit || '0').replace(/,/g, ''))).toLocaleString()}
                    </p>
                  </div>
                  {selectedPaymentPlan !== 'cash' && selectedPaymentPlan !== '30days' && (
                    <div className="text-center p-4 sm:p-6 bg-purple-50 rounded-xl border border-purple-200 sm:col-span-2 lg:col-span-1">
                      <p className="text-purple-600 font-semibold text-sm sm:text-base lg:text-lg">Monthly Payment</p>
                      <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mt-2 break-all">
                        KES {data.payment_plan.no_of_instalments ?
                          Math.round((parseFloat((data.payment_plan.total_cash_price || '0').replace(/,/g, '')) -
                                    parseFloat((data.payment_plan.deposit || '0').replace(/,/g, ''))) /
                                   parseInt(data.payment_plan.no_of_instalments.toString())).toLocaleString() : '0'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
            </div>
          </div>
        </div>
      )}

      {/* Security & Important Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
        <div className="bg-green-50 rounded-2xl border border-green-200 p-4 sm:p-6 lg:p-8">
          <div className="flex items-start gap-3 sm:gap-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
            </div>
            <div className="min-w-0">
              <h4 className="text-lg sm:text-xl font-bold text-green-800 mb-2 sm:mb-3">Secure Payment Processing</h4>
              <p className="text-sm sm:text-base lg:text-lg text-green-700 leading-relaxed">
                Your payment information is protected with bank-level encryption. All transactions
                are processed through secure channels and monitored for your safety.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-2xl border border-blue-200 p-4 sm:p-6 lg:p-8">
          <div className="flex items-start gap-3 sm:gap-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
            </div>
            <div className="min-w-0">
              <h4 className="text-lg sm:text-xl font-bold text-blue-800 mb-2 sm:mb-3">Payment Terms</h4>
              <ul className="text-sm sm:text-base lg:text-lg text-blue-700 space-y-1 sm:space-y-2">
                <li>• Payments are due on the same date each month</li>
                <li>• Flexible payment schedules available</li>
                <li>• Late payment fees apply after 7 days</li>
                <li>• Payment methods: Bank transfer, Mobile money</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      {!selectedPaymentPlan && (
        <div className="text-center py-8 sm:py-12 bg-gray-50 rounded-2xl border-2 border-dashed border-gray-300">
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
            <CreditCard className="w-8 h-8 sm:w-10 sm:h-10 text-gray-400" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-gray-700 mb-2 sm:mb-3 px-4">Choose Your Payment Plan</h3>
          <p className="text-lg sm:text-xl text-gray-600 px-4">
            Please select a payment option above to continue with your offer letter
          </p>
        </div>
      )}
      </div>
      </div>
    </div>
  );
};

export default PaymentDetailsStep;
