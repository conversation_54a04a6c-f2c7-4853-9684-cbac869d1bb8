import React from 'react';
import { Check } from 'lucide-react';

interface Step {
  id: number;
  title: string;
  completed: boolean;
  current: boolean;
}

interface ProgressStepperProps {
  steps: Step[];
}

const OLProgressStepper: React.FC<ProgressStepperProps> = ({ steps }) => {
  // Get visible steps for mobile (current step in middle, with previous and next)
  const currentStepIndex = steps.findIndex(step => step.current);
  
  const getVisibleSteps = () => {
    const prevStep = currentStepIndex > 0 ? steps[currentStepIndex - 1] : null;
    const currentStep = steps[currentStepIndex];
    const nextStep = currentStepIndex < steps.length - 1 ? steps[currentStepIndex + 1] : null;
    
    return [prevStep, currentStep, nextStep].filter(Boolean);
  };

  const visibleSteps = getVisibleSteps();

  return (
    <>
      {/* Desktop View - Show all steps */}
      <div className="hidden md:block w-full max-w-4xl mx-auto mb-8">
        <div className="flex items-center justify-between relative">
          {steps.map((step, index) => (
            <div key={step.id} className="flex flex-col items-center relative z-10">
              {/* Step Circle */}
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center text-sm font-bold transition-colors ${
                  step.completed || step.current
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {step.completed ? <Check size={16} /> : step.id}
              </div>
              
              {/* Step Title */}
              <div className="mt-3 text-xs font-medium text-gray-600 text-center whitespace-nowrap max-w-20">
                {step.title}
              </div>
            </div>
          ))}
          
          {/* Connection Lines */}
          <div className="absolute top-6 left-0 right-0 flex items-center justify-between px-5 -z-0">
            {steps.slice(0, -1).map((step, index) => (
              <div
                key={`line-${index}`}
                className="flex-1 h-0.5 bg-gray-200 mx-2"
                style={{
                  marginLeft: index === 0 ? '0' : '8px',
                  marginRight: index === steps.length - 2 ? '0' : '2px'
                }}
              >
                <div
                  className={`h-full transition-all duration-300 ${
                    step.completed ? 'bg-green-500' : 'bg-gray-200'
                  }`}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile View - Show only 3 steps */}
      <div className="md:hidden w-full max-w-sm mx-auto mb-8">
        <div className="flex items-center justify-between relative">
          {visibleSteps.map((step:any, index) => (
            <div key={step.id} className="flex flex-col items-center relative z-10">
              {/* Step Circle */}
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                  step.completed || step.current
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {step.completed ? <Check size={16} /> : step.id}
              </div>
              
              {/* Step Title */}
              <div className="mt-3 text-xs font-medium text-gray-600 text-center whitespace-nowrap max-w-20">
                {step.title}
              </div>
            </div>
          ))}
          
          {/* Connection Lines for mobile */}
          {visibleSteps.length > 1 && (
            <div className="absolute top-5 left-0 right-0 flex items-center justify-between px-5 -z-0">
              {visibleSteps.slice(0, -1).map((step:any, index) => (
                <div
                  key={`mobile-line-${index}`}
                  className="flex-1 h-0.5 bg-gray-200 mx-2"
                >
                  <div
                    className={`h-full transition-all duration-300 ${
                      step.completed ? 'bg-green-500' : 'bg-gray-200'
                    }`}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Step indicator for mobile */}
        <div className="text-center mt-4 text-sm text-gray-500">
          Step {currentStepIndex + 1} of {steps.length}
        </div>
      </div>
    </>
  );
};

export default OLProgressStepper;