import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import {
  Building2,
  User,
  Users,
  Star,
  CheckCircle,
  Home,
  Heart,
  Shield,
  MapPin,
  Search,
  UserCheck,
  Building,
} from "lucide-react";
import { OfferLetterData, CustomerType } from "../OfferLetter";
import { useFormContext } from "@/components/custom/forms/FormContext";
import { useGetProjectsQuery, useGetPlotsQuery } from "@/redux/slices/projects";
import {
  useGetPlotPaymentOptionsQuery,
  useGetOfferLetterPricingQuery,
} from "../api/offerLetterApi";

interface OnboardingStepProps {
  data: OfferLetterData;
  onUpdate: (data: Partial<OfferLetterData>) => void;
}

const OnboardingStep: React.FC<OnboardingStepProps> = ({ data, onUpdate }) => {
  const { setStepValidator, updateFormData } = useFormContext();
  const [selectedProject, setSelectedProject] = useState<string>(
    data.project_id || ""
  );
  const [selectedPlot, setSelectedPlot] = useState<string>(
    data.plot_number || ""
  );

  // Fetch projects
  const { data: projects, isLoading: projectsLoading } = useGetProjectsQuery(
    {}
  );

  // Fetch plots for selected project
  const { data: plots, isLoading: plotsLoading } = useGetPlotsQuery(
    { project_id: selectedProject },
    { skip: !selectedProject }
  );

  // Fetch plot payment options when plot is selected
  const { data: plotPaymentOptions } = useGetPlotPaymentOptionsQuery(
    { PLOT_NO: selectedPlot },
    { skip: !selectedPlot }
  );

  // Fetch pricing information
  const { data: pricingData } = useGetOfferLetterPricingQuery({});

  // Check if project and plot are already available from booking
  const hasBookingData = !!(data.project_id && data.plot_number);

  // Set up validation for this step
  useEffect(() => {
    const validateStep = () => {
      if (hasBookingData) {
        // If we have booking data, only customer type is required
        return !!data.customer_type;
      } else {
        // If no booking data, require customer type, project, and plot selection
        return !!(data.customer_type && selectedProject && selectedPlot);
      }
    };

    setStepValidator(1, validateStep); // Step 1 is the onboarding step
  }, [
    data.customer_type,
    selectedProject,
    selectedPlot,
    hasBookingData,
    setStepValidator,
  ]);

  const handleCustomerTypeChange = (value: CustomerType) => {
    const updateData = { customer_type: value };
    onUpdate(updateData);
    updateFormData(updateData);
  };

  const handleProjectChange = (projectId: string) => {
    setSelectedProject(projectId);
    setSelectedPlot(""); // Reset plot selection when project changes

    // Find project details
    const project = projects?.find((p: any) => p.projectId === projectId);
    const updateData = {
      project_id: projectId,
      project_name: project?.name || "",
      plot_number: "", // Reset plot number
    };
    onUpdate(updateData);
    updateFormData(updateData);
  };

  const handlePlotChange = (plotNumber: string) => {
    setSelectedPlot(plotNumber);

    // Find plot details
    const plot = plots?.data?.results?.find(
      (p: any) => p.plot_no === plotNumber
    );
    const updateData = {
      plot_number: plotNumber,
      plot_size: plot?.plot_size || "",
      plot_type: plot?.plot_type || "",
      plot_location: plot?.location || "",
    };
    onUpdate(updateData);
    updateFormData(updateData);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  e.currentTarget.nextElementSibling.style.display = "flex";
                }}
              />
              <div className="hidden h-12 w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 font-medium">
                Step 1 of 6
              </div>
              <div className="w-16 h-2 bg-gray-200 rounded-full">
                <div className="w-3 h-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 lg:px-8 py-16">
        {/* Welcome Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Welcome to Your Land Purchase Journey
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Let's get started by selecting your customer type to personalize
            your experience and create the perfect offer letter for your land
            purchase.
          </p>
        </div>

        {/* Customer Type Selection */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-10">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              Select Customer Type
            </h2>
            <p className="text-gray-600 text-center mb-10 text-lg">
              Choose the option that best describes your purchase
            </p>

            <RadioGroup
              value={data.customer_type || ""}
              onValueChange={handleCustomerTypeChange}
              className="grid grid-cols-1 md:grid-cols-2 gap-6"
            >
              {/* Individual Option */}
              <div
                className={`relative border-2 rounded-xl p-8 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  data.customer_type === "individual"
                    ? "border-green-500 bg-green-50 shadow-lg"
                    : "border-gray-200 hover:border-green-300 hover:bg-gray-50"
                }`}
              >
                <div className="flex items-start space-x-4">
                  <RadioGroupItem
                    value="individual"
                    id="individual"
                    className="mt-2 w-5 h-5"
                  />
                  <div className="flex-1">
                    <Label htmlFor="individual" className="cursor-pointer">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center">
                          <User className="w-7 h-7 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            Individual
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            Personal land purchase
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 leading-relaxed">
                        Perfect for individuals looking to purchase land for
                        personal use, investment, or development.
                      </p>
                    </Label>
                  </div>
                </div>
              </div>

              {/* Company Option */}
              <div
                className={`relative border-2 rounded-xl p-8 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  data.customer_type === "company"
                    ? "border-green-500 bg-green-50 shadow-lg"
                    : "border-gray-200 hover:border-green-300 hover:bg-gray-50"
                }`}
              >
                <div className="flex items-start space-x-4">
                  <RadioGroupItem
                    value="company"
                    id="company"
                    className="mt-2 w-5 h-5"
                  />
                  <div className="flex-1">
                    <Label htmlFor="company" className="cursor-pointer">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="w-14 h-14 bg-purple-100 rounded-xl flex items-center justify-center">
                          <Building2 className="w-7 h-7 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            Company
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            Corporate land purchase
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 leading-relaxed">
                        Ideal for businesses and corporations looking to acquire
                        land for commercial purposes or investment.
                      </p>
                    </Label>
                  </div>
                </div>
              </div>

              {/* Group Option */}
              <div
                className={`relative border-2 rounded-xl p-8 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  data.customer_type === "group"
                    ? "border-green-500 bg-green-50 shadow-lg"
                    : "border-gray-200 hover:border-green-300 hover:bg-gray-50"
                }`}
              >
                <div className="flex items-start space-x-4">
                  <RadioGroupItem
                    value="group"
                    id="group"
                    className="mt-2 w-5 h-5"
                  />
                  <div className="flex-1">
                    <Label htmlFor="group" className="cursor-pointer">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="w-14 h-14 bg-orange-100 rounded-xl flex items-center justify-center">
                          <Users className="w-7 h-7 text-orange-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            Group
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            Collective land purchase
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 leading-relaxed">
                        Perfect for groups of people coming together to purchase
                        land collectively for shared benefits.
                      </p>
                    </Label>
                  </div>
                </div>
              </div>

              {/* Partners Option */}
              <div
                className={`relative border-2 rounded-xl p-8 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  data.customer_type === "partners"
                    ? "border-green-500 bg-green-50 shadow-lg"
                    : "border-gray-200 hover:border-green-300 hover:bg-gray-50"
                }`}
              >
                <div className="flex items-start space-x-4">
                  <RadioGroupItem
                    value="partners"
                    id="partners"
                    className="mt-2 w-5 h-5"
                  />
                  <div className="flex-1">
                    <Label htmlFor="partners" className="cursor-pointer">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="w-14 h-14 bg-green-100 rounded-xl flex items-center justify-center">
                          <UserCheck className="w-7 h-7 text-green-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            Partnership
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            Joint land purchase
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 leading-relaxed">
                        Great for partners or spouses looking to jointly
                        purchase and own land together.
                      </p>
                    </Label>
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>
        </div>

        {/* Project and Plot Selection - Only show if no booking data available */}
        {data.customer_type && !hasBookingData && (
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 mt-12 overflow-hidden">
            <div className="p-10">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Select Project & Plot
              </h2>
              <p className="text-gray-600 text-center mb-10 text-lg">
                Choose your preferred project and available plot
              </p>
              <div className="space-y-8">
                {/* Project Selection */}
                <div className="space-y-3">
                  <Label
                    htmlFor="project"
                    className="text-lg font-semibold text-gray-800"
                  >
                    Select Project *
                  </Label>
                  <Select
                    value={selectedProject}
                    onValueChange={handleProjectChange}
                  >
                    <SelectTrigger className="w-full h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg">
                      <SelectValue placeholder="Choose a project..." />
                    </SelectTrigger>
                    <SelectContent>
                      {projectsLoading ? (
                        <SelectItem value="loading" disabled>
                          Loading projects...
                        </SelectItem>
                      ) : (
                        projects?.map((project: any) => (
                          <SelectItem
                            key={project.projectId}
                            value={project.projectId}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span className="font-medium text-lg">
                                {project.name}
                              </span>
                              <Badge variant="outline" className="ml-2 text-sm">
                                {project.open_plots} plots
                              </Badge>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Plot Selection */}
                {selectedProject && (
                  <div className="space-y-3">
                    <Label
                      htmlFor="plot"
                      className="text-lg font-semibold text-gray-800"
                    >
                      Select Plot *
                    </Label>
                    <Select
                      value={selectedPlot}
                      onValueChange={handlePlotChange}
                    >
                      <SelectTrigger className="w-full h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg">
                        <SelectValue placeholder="Choose a plot..." />
                      </SelectTrigger>
                      <SelectContent>
                        {plotsLoading ? (
                          <SelectItem value="loading" disabled>
                            Loading plots...
                          </SelectItem>
                        ) : plots?.data?.results?.length === 0 ? (
                          <SelectItem value="no-plots" disabled>
                            No available plots
                          </SelectItem>
                        ) : (
                          plots?.data?.results?.map((plot: any) => (
                            <SelectItem key={plot.plot_no} value={plot.plot_no}>
                              <div className="flex items-center justify-between w-full">
                                <span className="font-medium text-lg">
                                  Plot {plot.plot_no}
                                </span>
                                <div className="flex gap-2">
                                  <Badge variant="outline" className="text-sm">
                                    {plot.plot_size}
                                  </Badge>
                                  <Badge
                                    variant={
                                      plot.plot_status === "Open"
                                        ? "default"
                                        : "secondary"
                                    }
                                    className={`text-sm ${
                                      plot.plot_status === "Open"
                                        ? "bg-green-600"
                                        : ""
                                    }`}
                                  >
                                    {plot.plot_status}
                                  </Badge>
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Plot Details Preview */}
                {selectedPlot && (
                  <div className="bg-green-50 rounded-xl p-6 border border-green-200">
                    <h4 className="font-bold text-green-800 mb-4 flex items-center gap-3 text-lg">
                      <Home className="w-5 h-5" />
                      Selected Plot Details
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-white rounded-xl border border-green-100 shadow-sm">
                        <p className="text-green-600 font-semibold text-sm">
                          Plot Number
                        </p>
                        <p className="text-2xl font-bold text-gray-800 mt-1">
                          {selectedPlot}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-white rounded-xl border border-green-100 shadow-sm">
                        <p className="text-green-600 font-semibold text-sm">
                          Plot Size
                        </p>
                        <p className="text-2xl font-bold text-gray-800 mt-1">
                          {data.plot_size || "N/A"}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-white rounded-xl border border-green-100 shadow-sm">
                        <p className="text-green-600 font-semibold text-sm">
                          Plot Type
                        </p>
                        <p className="text-2xl font-bold text-gray-800 mt-1">
                          {data.plot_type || "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Booking Information Display - When booking data is available */}
        {data.customer_type && hasBookingData && (
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 mt-12 overflow-hidden">
            <div className="p-10">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center flex items-center justify-center gap-3">
                <CheckCircle className="w-8 h-8 text-green-600" />
                Booking Information
              </h2>
              <div className="bg-green-50 rounded-xl p-8 border border-green-200">
                <h4 className="font-bold text-green-800 mb-6 flex items-center gap-3 text-xl">
                  <Home className="w-6 h-6" />
                  Pre-selected Plot Details
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 bg-white rounded-xl border border-green-100 shadow-sm">
                    <p className="text-green-600 font-semibold text-sm">
                      Project
                    </p>
                    <p className="text-2xl font-bold text-gray-800 mt-2">
                      {data.project_name || "N/A"}
                    </p>
                  </div>
                  <div className="text-center p-6 bg-white rounded-xl border border-green-100 shadow-sm">
                    <p className="text-green-600 font-semibold text-sm">
                      Plot Number
                    </p>
                    <p className="text-2xl font-bold text-gray-800 mt-2">
                      {data.plot_number || "N/A"}
                    </p>
                  </div>
                  <div className="text-center p-6 bg-white rounded-xl border border-green-100 shadow-sm">
                    <p className="text-green-600 font-semibold text-sm">
                      Plot Size
                    </p>
                    <p className="text-2xl font-bold text-gray-800 mt-2">
                      {data.plot_size || "N/A"}
                    </p>
                  </div>
                </div>
                <div className="mt-8 text-center">
                  <Badge
                    variant="outline"
                    className="text-green-600 border-green-600 bg-green-100 px-6 py-2 text-lg"
                  >
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Booking Confirmed
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OnboardingStep;
