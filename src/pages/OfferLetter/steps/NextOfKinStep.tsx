import React, { useEffect } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, Phone, Mail, MapPin, AlertCircle, Heart } from 'lucide-react';
import { OfferLetterData } from '../OfferLetter';
import { useFormContext } from '@/components/custom/forms/FormContext';

interface NextOfKinStepProps {
  data: OfferLetterData;
  onUpdate: (data: Partial<OfferLetterData>) => void;
}

const NextOfKinStep: React.FC<NextOfKinStepProps> = ({ data, onUpdate }) => {
  const { setStepValidator } = useFormContext();

  // Set up validation for this step
  useEffect(() => {
    const validateStep = () => {
      return !!(
        data.next_of_kin?.full_name &&
        data.next_of_kin?.relationship &&
        data.next_of_kin?.phone
      );
    };

    setStepValidator(3, validateStep); // Step 3 is the next of kin step
  }, [data.next_of_kin, setStepValidator]);

  const handleNextOfKinChange = (field: string, value: string) => {
    onUpdate({
      next_of_kin: {
        ...data.next_of_kin,
        [field]: value
      }
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="hidden h-12 w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 font-medium">
                Step 3 of 6
              </div>
              <div className="w-16 h-2 bg-gray-200 rounded-full">
                <div className="w-9 h-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto px-6 lg:px-8 py-16">
        {/* Welcome Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Next of Kin Information
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Please provide details of your emergency contact person. This information is kept confidential
            and will only be used for important communications regarding your land purchase.
          </p>
        </div>

        {/* Next of Kin Form */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-10">
            <div className="flex items-center gap-4 mb-8">
              <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <Heart className="w-6 h-6 text-red-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">Emergency Contact Details</h2>
            </div>

            <div className="space-y-8">
              <div className="space-y-3">
                <Label htmlFor="full_name" className="text-lg font-semibold text-gray-800">
                  Full Name *
                </Label>
                <Input
                  id="full_name"
                  placeholder="Enter full name of your next of kin"
                  value={data.next_of_kin?.full_name || ''}
                  onChange={(e) => handleNextOfKinChange('full_name', e.target.value)}
                  required
                  className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                />
              </div>

              <div className="space-y-3">
                <Label htmlFor="relationship" className="text-lg font-semibold text-gray-800">
                  Relationship *
                </Label>
                <Select
                  value={data.next_of_kin?.relationship || ''}
                  onValueChange={(value) => handleNextOfKinChange('relationship', value)}
                >
                  <SelectTrigger className="h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg">
                    <SelectValue placeholder="Select relationship" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="spouse">Spouse</SelectItem>
                    <SelectItem value="parent">Parent</SelectItem>
                    <SelectItem value="child">Child</SelectItem>
                    <SelectItem value="sibling">Sibling</SelectItem>
                    <SelectItem value="guardian">Guardian</SelectItem>
                    <SelectItem value="friend">Friend</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="kin_phone" className="text-lg font-semibold text-gray-800">
                    Phone Number *
                  </Label>
                  <div className="relative">
                    <Phone className="absolute left-4 top-4 w-5 h-5 text-gray-400" />
                    <Input
                      id="kin_phone"
                      type="tel"
                      placeholder="+254 700 000 000"
                      className="pl-12 h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                      value={data.next_of_kin?.phone || ''}
                      onChange={(e) => handleNextOfKinChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="kin_email" className="text-lg font-semibold text-gray-800">
                    Email Address (Optional)
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-4 w-5 h-5 text-gray-400" />
                    <Input
                      id="kin_email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="pl-12 h-14 border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-xl text-lg"
                      value={data.next_of_kin?.email || ''}
                      onChange={(e) => handleNextOfKinChange('email', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Privacy Notice */}
              <div className="mt-10 p-8 bg-blue-50 border border-blue-200 rounded-xl">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <AlertCircle className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-blue-800 mb-3">Privacy & Confidentiality</h4>
                    <p className="text-lg text-blue-700 leading-relaxed">
                      Your next of kin information is strictly confidential and will only be accessed by authorized
                      personnel for important communications regarding your land purchase.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NextOfKinStep;
