import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import BookingApprovalCard from "../inventory/BookingApproval/BookingApprovalCard";

interface BookingType {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  marketer_name: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
  office: string;
  deadline: string;
}

type Props = {
  data: any;
  loading: boolean;
};

const DiasporaBookingReport = ({ data, loading }: Props) => {
  return (
    <div>
      <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3 pt-4 border-t border-t-blue-400">
        {loading ? (
          <div className="flex justify-center items-center w-full h-full">
            <SpinnerTemp type="spinner-double" size="lg" />
          </div>
        ) : data?.data?.results?.length < 1 ? (
          <p>No Diaspora Booking Data Found</p>
        ) : (
          data?.data?.results.map((booking: BookingType) => (
            <BookingApprovalCard key={booking.booking_id} rowData={booking} />
          ))
        )}
      </div>
    </div>
  );
};

export default DiasporaBookingReport;
