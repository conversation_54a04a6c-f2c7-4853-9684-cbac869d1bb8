import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import SimpleTable, {
  ColumnDefinitionST,
} from "@/components/custom/tables/SimpleTable";
import BookingApprovalCard from "../inventory/BookingApproval/BookingApprovalCard";
import { Filter } from "lucide-react";

interface BookingType {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  marketer_name: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
  office: string;
  deadline: string;
}

type Props = {
  data: any;
  loading: boolean;
  filterStatus: string;
  setFilterStatus: (e: string) => void;
};

const AllBookingsReport = ({
  data,
  loading,
  filterStatus,
  setFilterStatus,
}: Props) => {
  return (
    <div className="border-t border-t-blue-400 w-full">
      <div className="flex flex-wrap gap-2 items-center justify-start py-4">
        <div className="flex items-center text-md font-bold">
          <Filter /> Filter booking with status
        </div>
        <select
          name="filterStatus"
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-4 py-2 text-white border rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-primary "
        >
          <Filter className="w-5 h-5" />
          <option value="" disabled>
            <span>Select Status</span>
          </option>
          <option value="OPEN">Open</option>
          <option value="WAITING">Awaiting Approval</option>
          <option value="SUSPENDED">Diaspora Bookings</option>
          <option value="TIMED">Timed Out</option>
          <option value="DONE">Approved</option>
        </select>
      </div>
      <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3 pt-4 ">
        {loading ? (
          <div className="flex justify-center items-center w-full h-full">
            <SpinnerTemp type="spinner-double" size="lg" />
          </div>
        ) : data?.data?.results?.length < 1 ? (
          <p>No Plot Booking Data Found</p>
        ) : (
          data?.data?.results.map((booking: BookingType) => (
            <BookingApprovalCard key={booking.booking_id} rowData={booking} />
          ))
        )}
      </div>
    </div>
  );
};

export default AllBookingsReport;
