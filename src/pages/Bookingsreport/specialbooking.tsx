import Spinner from "@/components/custom/spinners/Spinner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import SimpleTable, {
  ColumnDefinitionST,
} from "@/components/custom/tables/SimpleTable";
import { CircleDot } from "lucide-react";
import BookingApprovalCard from "../inventory/BookingApproval/BookingApprovalCard";

interface BookingType {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  lead: string;
  lead_name: string;
  marketer: string;
  marketer_name: string;
  customer: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
  office: string;
  deadline: string;
}

type Props = {
  data: any;
  loading: boolean;
};

const SpecialBookingsReport = ({ data, loading }: Props) => {
  // const columns: ColumnDefinitionST<(typeof data)[0]>[] = [
  //   {
  //     key: "plots",
  //     header: "Plots",
  //     headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
  //     cellClassName: "px-6 py-4 text-gray-600",
  //   },
  //   {
  //     key: "amount",
  //     header: "Amount",
  //     headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
  //     cellClassName: "px-6 py-4 text-gray-600",
  //   },
  //   {
  //     key: "marketer_name",
  //     header: "Marketer",
  //     headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
  //     cellClassName: "px-6 py-4 text-gray-600",
  //   },
  //   {
  //     key: "customer_name",
  //     header: "Customer",
  //     headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
  //     cellClassName: "px-6 py-4 text-gray-600",
  //   },

  //   {
  //     key: "status",
  //     header: "Status",
  //     headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
  //     cellClassName: "px-6 py-4",
  //     renderCell: (row) => (
  //       <span
  //         className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium gap-1 ${
  //           row.status === "OPEN"
  //             ? "bg-blue-100 text-blue-800"
  //             : row.status === "TIMED"
  //             ? "bg-orange-100 text-orange-800"
  //             : row.status === "DONE"
  //             ? "bg-green-100 text-green-800"
  //             : row.status === "WAITING"
  //             ? "bg-yellow-100 text-yellow-800"
  //             : "bg-red-100 text-red-800"
  //         }`}
  //       >
  //         <CircleDot className="w-4 h-4" />
  //         {row.status}
  //       </span>
  //     ),
  //   },
  // ];

  return (
    <div>
      <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3 pt-4 border-t border-t-blue-400">
        {loading ? (
          <div className="flex justify-center items-center w-full h-full">
            <SpinnerTemp type="spinner-double" size="lg" />
          </div>
        ) : data?.data?.results?.length < 1 ? (
          <p>No Special Booking Data Found</p>
        ) : (
          data?.data?.results.map((booking: BookingType) => (
            <BookingApprovalCard key={booking.booking_id} rowData={booking} />
          ))
        )}
      </div>
      {/* {data?.loading ? (
        <div className="w-full flex items-center justify-center py-4 border">
          <div>
            <SpinnerTemp type="spinner-double" />
          </div>
        </div>
      ) : ( */}
      {/* <SimpleTable
        data={data?.data}
        columns={columns}
        containerClassName="rounded-lg overflow-hidden border border-gray-200"
        tableClassName="min-w-full border-collapse bg-white"
        hoverable={true}
        striped={true}
        tRowClassName="transition-colors duration-150"
      /> */}
      {/* )} */}
    </div>
  );
};

export default SpecialBookingsReport;
