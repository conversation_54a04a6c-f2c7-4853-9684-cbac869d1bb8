import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useCreateEngagementMutation,
  usePartialUpdateEngagementMutation,
} from "@/redux/slices/engagementsApiSlice";

type Props = {
  prospect_id?: string;
  employee_no?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const EngagementForm = ({
  prospect_id,
  onOpenChange,
  updateData,
  employee_no,
}: Props) => {
  const [addEngagement, { isLoading: submitting }] =
    useCreateEngagementMutation();
  const [updateEngagement, { isLoading: updating }] =
    usePartialUpdateEngagementMutation();
  // form schema
  const formSchema = z
    .object({
      engagement_type: z
        .string()
        .min(1, { message: "Engagement Type is required." }),
      subject: z.string().min(1, { message: "subject is required." }),
      description: z.string().min(1, { message: "description is required." }),
      follow_up_required: z.boolean().default(false),
      follow_up_date: z.string().optional(),
      set_reminder: z.boolean().default(false),
      reminder_time: z.string().optional(),
    })
    .refine(
      (data) => {
        if (data.follow_up_required && !data.follow_up_date) {
          return false;
        }
        return true;
      },
      {
        message: "Follow up date is required when follow up is set",
        path: ["follow_up_date"],
      }
    )
    .refine(
      (data) => {
        if (data.set_reminder && !data.reminder_time) {
          return false;
        }
        return true;
      },
      {
        message: "Reminder time is required when reminder is set",
        path: ["reminder_time"],
      }
    );

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      engagement_type: updateData?.engagement_type || "",
      subject: updateData?.subject || "",
      description: updateData?.description || "",
      follow_up_required: updateData?.follow_up_required || false,
      follow_up_date: updateData?.follow_up_date || "",
      set_reminder: updateData?.set_reminder || false,
      reminder_time: updateData?.reminder_time || "",
    },
  });

  const handleEngagementSubmit = async (data: any) => {
    // Convert datetime-local to ISO format and remove empty fields
    const processedData = { ...data };

    if (processedData.reminder_time) {
      processedData.reminder_time = new Date(
        processedData.reminder_time
      ).toISOString();
    } else {
      delete processedData.reminder_time; // Remove empty field
    }

    if (processedData.follow_up_date) {
      processedData.follow_up_date = new Date(
        processedData.follow_up_date + "T00:00:00"
      ).toISOString();
    } else {
      delete processedData.follow_up_date; // Remove empty field
    }

    let formData = updateData
      ? { ...processedData, engagement_id: updateData?.engagement_id }
      : {
          ...processedData,
          prospect: prospect_id,
          client_type: "Prospect",
          created_by: employee_no,
        };
    console.log("formData", formData);
    try {
      let res;
      if (updateData) {
        res = await updateEngagement(formData).unwrap();
      } else {
        res = await addEngagement(formData).unwrap();
      }
      if (res?.engagement_id) {
        toast.success(
          `Engagement ${updateData ? "updated" : "added"} successfully`
        );
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Somethe went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleEngagementSubmit)}>
        <div className="space-y-2">
          <div>
            <label className="ml-1 text-sm">Engagement Type*</label>
            <div className="relative pt-1">
              <select
                {...register("engagement_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Call">Call</option>
                <option value="Email">Email</option>
                <option value="Meeting">Meeting</option>
                <option value="SMS">SMS</option>
                <option value="Chat">Chat</option>
                <option value="Visit">Visit</option>
                <option value="Event">Event</option>
                <option value="Follow-up">Follow-up</option>
              </select>
            </div>
            {errors.engagement_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.engagement_type.message}
              </p>
            )}
          </div>

          <div>
            <label className="ml-1 text-sm">Subject*</label>
            <div className="relative pt-1">
              <input
                {...register("subject")}
                placeholder="Subject"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.subject && (
              <p className="text-red-500 text-xs pl-3">
                {errors.subject.message}
              </p>
            )}
          </div>

          <div>
            <label className="ml-1 text-sm">Descriptions*</label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="Description"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("follow_up_required")} />
              </div>
              <label className="ml-1 text-sm">Follow Up Required</label>
              {errors.follow_up_required && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_required.message}
                </p>
              )}
            </div>
          </div>

          {watch().follow_up_required && (
            <div>
              <label className="ml-1 text-sm">
                Follow Up Date{" "}
                {updateData?.follow_up_date
                  ? `(Current set on: ${new Date(
                      updateData.follow_up_date
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="date"
                  {...register("follow_up_date")}
                  placeholder="Your Email"
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.follow_up_date && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_date.message}
                </p>
              )}
            </div>
          )}

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("set_reminder")} />
              </div>
              <label className="ml-1 text-sm">Set Reminder</label>
              {errors.set_reminder && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.set_reminder.message}
                </p>
              )}
            </div>
          </div>

          {watch().set_reminder && (
            <div>
              <label className="ml-1 text-sm">
                Reminder Date{" "}
                {updateData?.reminder_time
                  ? `(Currently set at: ${new Date(
                      updateData.reminder_time
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="datetime-local"
                  {...register("reminder_time")}
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.reminder_time && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.reminder_time.message}
                </p>
              )}
            </div>
          )}

          <div className="w-full">
            {submitting || updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default EngagementForm;
