import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useCreateComplaintMutation } from "@/redux/slices/complaintsApiSlice";

type Props = {
  prospect_id: string;
  onOpenChange: (e: boolean) => void;
};

const ComplaintsForm = ({ prospect_id, onOpenChange }: Props) => {
  const [addComplaint, { isLoading: submitting }] =
    useCreateComplaintMutation();
  // form schema
  const formSchema = z.object({
    category: z.string().min(1, { message: "Category is required." }),
    title: z.string().min(1, { message: "title is required." }),
    description: z.string().min(1, { message: "description is required." }),
    priority: z.string().min(1, { message: "priority is required." }),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const handleEngagementSubmit = async (data: any) => {
    let formData = { ...data, entity_type: "prospect", entity_id: prospect_id };
    try {
      const res = await addComplaint(formData).unwrap();
      if (res?.complaint_id) {
        toast.success("Complaint added successfully");
        onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleEngagementSubmit)}>
        <div className="space-y-2">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Complain Category*
            </label>
            <div className="relative pt-1">
              <select
                {...register("category")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Product">Product</option>
                <option value="Service">Service</option>
                <option value="Billing">Billing</option>
                <option value="Technical">Technical</option>
                <option value="General">General</option>
              </select>
            </div>
            {errors.category && (
              <p className="text-red-500 text-xs pl-3">
                {errors.category.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Descriptions*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="Description"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Priority
            </label>
            <div className="relative pt-1">
              <select
                {...register("priority")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
                <option value="Critical">Critical</option>
              </select>
            </div>
            {errors.priority && (
              <p className="text-red-500 text-xs pl-3">
                {errors.priority.message}
              </p>
            )}
          </div>

          <div className="w-full">
            {submitting ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default ComplaintsForm;
