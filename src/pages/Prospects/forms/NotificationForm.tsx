import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import Multiselect from "@/components/custom/forms/Multiselect";
import CustomSelectField from "@/components/CustomSelectField";
import {
  useCreateNotificationMutation,
  usePartialUpdateNotificationMutation,
} from "@/redux/slices/notificationsApiSlice";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const NotificationForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addNotification, { isLoading: submitting }] =
    useCreateNotificationMutation();
  const [updateNotification, { isLoading: updating }] =
    usePartialUpdateNotificationMutation();
  const [recipient, setRecipient] = useState("");

  const [fetchUsers, { data: recipientsData, isLoading: rLoading }] =
    useLazyGetUsersQuery({});

  // form schema
  const formSchema = z.object({
    notification_type: z
      .string()
      .min(1, { message: "Notification Type is required." }),
    title: z.string().min(1, { message: "title is required." }),
    message: z.string().min(1, { message: "message is required." }),
    priority: z.string().min(1, { message: "priority is required." }),
    is_read: z.boolean().default(false),
    read_at: z.string().optional(),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      notification_type: updateData?.notification_type || "Info",
      title: updateData?.title || "",
      message: updateData?.message || "",
      priority: updateData?.priority || "Normal",
      is_read: updateData?.is_read ?? false,
      read_at: updateData?.read_at || "",
    },
  });

  const handleNotificationSubmit = async (data: any) => {
    // Convert datetime-local to ISO format and remove empty fields
    const processedData = { ...data };

    if (processedData.read_at) {
      processedData.read_at = new Date(
        processedData.read_at + "T00:00:00"
      ).toISOString();
    } else {
      delete processedData.read_at; // Remove empty field
    }

    let formData = updateData
      ? {
          ...processedData,
          recipient: recipient || updateData?.recipient,
          notification_id: updateData?.notification_id,
        }
      : {
          ...processedData,
          recipient,
          prospect: prospect_id,
          client_type: "Prospect",
        };
    try {
      let res;
      if (updateData) {
        res = await updateNotification(formData).unwrap();
      } else {
        res = await addNotification(formData).unwrap();
      }
      if (res?.notification_id) {
        toast.success(
          `Notification ${updateData ? "updated" : "added"} successfully`
        );
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Somethe went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleNotificationSubmit)}>
        <div className="space-y-2 pb-20">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Notification Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("notification_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Info">Info </option>
                <option value="Warning">Warning</option>
                <option value="Error">Error</option>
                <option value="Success">Success</option>
                <option value="Reminder">Reminder</option>
                <option value="Alert">Alert</option>
              </select>
            </div>
            {errors.notification_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.notification_type.message}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <label className="ml-1 text-sm">
              Recipient{" "}
              {updateData?.recipient && `(Current: ${updateData.recipient})`}
            </label>
            <div className="space-y-2 flex flex-col my-4">
              <CustomSelectField
                valueField="employee_no"
                labelField="fullnames"
                data={(recipientsData as any)?.data?.results}
                queryFunc={fetchUsers}
                setValue={setRecipient}
                loader={rLoading}
                useSearchField={true}
              />
            </div>
            {/* <Multiselect
              value={
                updateData
                  ? {
                      label: `${updateData?.recipient_name} (${updateData?.recipient_display})`,
                      value: updateData?.recipient_display,
                    }
                  : selectedRecipient
              }
              data={recipientOptions}
              setValue={setSelectedRecipient}
              loading={rLoading}
              isClearable={false}
              isDisabled={false}
              isMultiple={false}
              isSearchable={true}
            /> */}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Message*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("message")}
                rows={3}
                placeholder="message"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.message && (
              <p className="text-red-500 text-xs pl-3">
                {errors.message.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Priority
            </label>
            <div className="relative pt-1">
              <select
                {...register("priority")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Low">Low</option>
                <option value="Normal">Normal</option>
                <option value="High">High</option>
                <option value="Urgent">Urgent</option>
              </select>
            </div>
            {errors.priority && (
              <p className="text-red-500 text-xs pl-3">
                {errors.priority.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("is_read")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Mark As Read
              </label>
              {errors.is_read && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_read.message}
                </p>
              )}
            </div>
          </div>

          {watch().is_read && (
            <div>
              <label className="ml-1 text-sm">
                Follow Up Date{" "}
                {updateData?.read_at
                  ? `(Current set on: ${new Date(
                      updateData.read_at
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="date"
                  {...register("read_at")}
                  placeholder="Your Email"
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.read_at && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.read_at.message}
                </p>
              )}
            </div>
          )}

          <div className="w-full">
            {submitting ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default NotificationForm;
