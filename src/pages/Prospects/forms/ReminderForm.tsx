import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useAddReminderMutation,
  useUpdateReminderMutation,
} from "@/redux/slices/reminderApiSlice";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const ReminderForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addReminder, { isLoading: submitting }] = useAddReminderMutation();
  const [updateReminder, { isLoading: updating }] = useUpdateReminderMutation();

  // form schema
  const formSchema = z.object({
    reminder_type: z.string().min(1, { message: "reminder Type is required." }),
    title: z.string().min(1, { message: "title is required." }),
    priority: z.string().min(1, { message: "priority is required." }),
    description: z.string().optional(),
    reminder_notes: z.string().optional(),
    status: z.string().optional(),
    reminder_date: z.string().min(1, { message: "reminder date is required." }),
    reminder_time: z.string().min(1, { message: "reminder time is required." }),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reminder_type: updateData?.reminder_type || "Info",
      reminder_date: updateData?.reminder_date || "",
      reminder_time: updateData?.reminder_time || "",
      title: updateData?.title || "",
      description: updateData?.description || "",
      status: updateData?.status || "",
      priority: updateData?.priority || "Normal",
      reminder_notes: updateData?.reminder_notes || "",
    },
  });

  const handleReminderSubmit = async (data: any) => {
    let formData = updateData
      ? { ...data, reminder_id: updateData?.reminder_id }
      : {
          ...data,
          prospect: prospect_id,
          client_type: "Prospect",
        };
    try {
      let res;
      if (updateData) {
        res = await updateReminder(formData).unwrap();
      } else {
        res = await addReminder(formData).unwrap();
      }
      if (res?.reminder_id) {
        toast.success(
          `Reminder ${updateData ? "updated" : "added"} successfully`
        );
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleReminderSubmit)}>
        <div className="space-y-2 pb-20">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Reminder Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("reminder_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="General">General</option>
                <option value="Task">Task</option>
                <option value="Follow-up">Follow-up</option>
                <option value="Meeting">Meeting</option>
                <option value="Deadline">Deadline</option>
                <option value="Payment">Payment</option>
                <option value="Review">Review</option>
                <option value="Call">Call</option>
                <option value="Email">Email</option>
              </select>
            </div>
            {errors.reminder_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.reminder_type.message}
              </p>
            )}
          </div>
          {/* <div className="space-y-2">
            <label className="ml-1 text-sm">Assign to</label>

            <CustomSelectField
              valueField="employee_no"
              labelField="fullnames"
              data={(recipientsData as any)?.data?.results}
              queryFunc={fetchEmployees}
              setValue={setSelectedRecipient}
              loader={rLoading}
              useSearchField={true}
            />
          </div> */}

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Reminder Date*
            </label>
            <div className="relative pt-1">
              <input
                type="date"
                {...register("reminder_date")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.reminder_date && (
              <p className="text-red-500 text-xs pl-3">
                {errors.reminder_date.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Reminder Time*
            </label>
            <div className="relative pt-1">
              <input
                type="time"
                {...register("reminder_time")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.reminder_time && (
              <p className="text-red-500 text-xs pl-3">
                {errors.reminder_time.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Priority
            </label>
            <div className="relative pt-1">
              <select
                {...register("priority")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Low">Low</option>
                <option value="Normal">Normal</option>
                <option value="High">High</option>
                <option value="Urgent">Urgent</option>
              </select>
            </div>
            {errors.priority && (
              <p className="text-red-500 text-xs pl-3">
                {errors.priority.message}
              </p>
            )}
          </div>
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              status
            </label>
            <div className="relative pt-1">
              <select
                {...register("status")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Active">Active</option>
                <option value="Snoozed">Snoozed</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>
            {errors.status && (
              <p className="text-red-500 text-xs pl-3">
                {errors.status.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Description*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="description..."
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Reminder Notes
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("reminder_notes")}
                rows={3}
                placeholder="reminder notes..."
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.reminder_notes && (
              <p className="text-red-500 text-xs pl-3">
                {errors.reminder_notes.message}
              </p>
            )}
          </div>

          <div className="w-full">
            {submitting ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default ReminderForm;
