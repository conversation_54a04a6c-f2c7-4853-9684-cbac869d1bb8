import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useCreateFeedbackMutation,
  usePartialUpdateFeedbackMutation,
} from "@/redux/slices/feedbackApiSlice";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const FeedbackForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addFeedback, { isLoading: submitting }] = useCreateFeedbackMutation();
  const [updateFeedback, { isLoading: updating }] =
    usePartialUpdateFeedbackMutation();
  // form schema
  const formSchema = z.object({
    feedback_type: z.string().min(1, { message: "Feedback Type is required." }),
    subject: z.string().min(1, { message: "subject is required." }),
    message: z.string().min(1, { message: "message is required." }),
    rating: z.string().min(1, { message: "rating is required." }),
    is_public: z.boolean().default(false),
    is_anonymous: z.boolean().default(false),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const handleFeedbackSubmit = async (data: any) => {
    let formData = updateData
      ? { ...data, feedback_id: updateData?.feedback_id }
      : { ...data, entity_type: "prospect", entity_id: prospect_id };
    try {
      let res;
      if (updateData) {
        res = await updateFeedback(formData).unwrap();
      } else {
        res = await addFeedback(formData).unwrap();
      }
      if (res?.feedback_id) {
        toast.success(
          `Feedback ${updateData ? "updated" : "added"} successfully`
        );
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Somethe went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleFeedbackSubmit)}>
        <div className="space-y-2">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Feedback Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("feedback_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Compliment">Compliment</option>
                <option value="Suggestion">Suggestion</option>
                <option value="Complaint">Complaint</option>
                <option value="General">General</option>
                <option value="Product Review">Product Review</option>
                <option value="Service Review">Service Review</option>
              </select>
            </div>
            {errors.feedback_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.feedback_type.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Subject*
            </label>
            <div className="relative pt-1">
              <input
                {...register("subject")}
                placeholder="Subject"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.subject && (
              <p className="text-red-500 text-xs pl-3">
                {errors.subject.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Message*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("message")}
                rows={3}
                placeholder="message"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.message && (
              <p className="text-red-500 text-xs pl-3">
                {errors.message.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Rating
            </label>
            <div className="relative pt-1">
              <select
                {...register("rating")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value={1}>1</option>
                <option value={2}>2</option>
                <option value={3}>3</option>
                <option value={4}>4</option>
                <option value={5}>5</option>
              </select>
            </div>
            {errors.rating && (
              <p className="text-red-500 text-xs pl-3">
                {errors.rating.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("is_public")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Is Public
              </label>
              {errors.is_public && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_public.message}
                </p>
              )}
            </div>
          </div>

          <div className="pb-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded mb-3">
              <div className="relative">
                <input type="checkbox" {...register("is_anonymous")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Is Anonymous
              </label>
              {errors.is_anonymous && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_anonymous.message}
                </p>
              )}
            </div>
          </div>

          <div className="w-full">
            {submitting || updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default FeedbackForm;
