import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useState } from "react";
import {
  usePartialUpdateNoteMutation,
  useCreateNoteMutation,
} from "@/redux/slices/notesApiSlice";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const NotesForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addNote, { isLoading: submitting }] = useCreateNoteMutation();
  const [updateNote, { isLoading: updating }] = usePartialUpdateNoteMutation();
  const [tags, setTags] = useState<string[]>(
    updateData?.tags ? updateData.tags.split(",") : []
  );
  const [inputValue, setInputValue] = useState("");

  // form schema
  const formSchema = z
    .object({
      note_type: z.string().min(1, { message: "Note Type is required." }),
      title: z.string().min(1, { message: "title is required." }),
      content: z.string().min(1, { message: "content is required." }),
      is_private: z.boolean().default(false),
      is_pinned: z.boolean().default(false),
      follow_up_required: z.boolean().default(false),
      follow_up_date: z.string().optional(),
      set_reminder: z.boolean().default(false),
      reminder_time: z.string().optional(),
      tags: z.string().optional(),
    })
    .refine(
      (data) => {
        if (data.follow_up_required && !data.follow_up_date) {
          return false;
        }
        return true;
      },
      {
        message: "Follow up date is required when follow up is set",
        path: ["follow_up_date"],
      }
    )
    .refine(
      (data) => {
        if (data.set_reminder && !data.reminder_time) {
          return false;
        }
        return true;
      },
      {
        message: "Reminder time is required when reminder is set",
        path: ["reminder_time"],
      }
    );

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      note_type: updateData?.note_type || "",
      title: updateData?.title || "",
      content: updateData?.content || "",
      is_private: updateData?.is_private ?? false,
      is_pinned: updateData?.is_pinned ?? false,
      follow_up_required: updateData?.follow_up_required || false,
      follow_up_date: updateData?.follow_up_date || "",
      set_reminder: updateData?.set_reminder || false,
      reminder_time: updateData?.reminder_time || "",
      tags: updateData?.tags || "",
    },
  });

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === "Enter" || e.key === ",") && inputValue.trim()) {
      e.preventDefault();
      const newTag = inputValue.trim();
      if (!tags.includes(newTag)) {
        const newTags = [...tags, newTag];
        setTags(newTags);
        setValue("tags", newTags.join(","));
      }
      setInputValue("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(newTags);
    setValue("tags", newTags.join(","));
  };

  const handleNoteSubmit = async (data: any) => {
    // Convert datetime-local to ISO format and remove empty fields
    const processedData = { ...data };

    if (processedData.reminder_time) {
      processedData.reminder_time = new Date(
        processedData.reminder_time
      ).toISOString();
    } else {
      delete processedData.reminder_time; // Remove empty field
    }

    if (processedData.follow_up_date) {
      // Convert date to datetime with time set to start of day
      processedData.follow_up_date = new Date(
        processedData.follow_up_date + "T00:00:00"
      ).toISOString();
    } else {
      delete processedData.follow_up_date; // Remove empty field
    }

    let formData = updateData
      ? { ...processedData, note_id: updateData?.note_id }
      : { ...processedData, client_type: "Prospect", prospect: prospect_id };

    // Convert tags array to comma-separated string if tags exist and are not empty
    if (tags && tags.length > 0) {
      formData.tags = tags.join(",");
    }

    try {
      let res;
      if (updateData) {
        res = await updateNote(formData).unwrap();
      } else {
        res = await addNote(formData).unwrap();
      }
      if (res?.note_id) {
        toast.success(`Note ${updateData ? "updated" : "added"} successfully"`);
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleNoteSubmit)}>
        <div className="space-y-2 pb-20">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Notes Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("note_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="General">General</option>
                <option value="Important">Important</option>
                <option value="Reminder">Reminder</option>
                <option value="Follow-up">Follow-up</option>
                <option value="Internal">Internal</option>
                <option value="Customer Facing">Customer Facing</option>
              </select>
            </div>
            {errors.note_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.note_type.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Contents*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("content")}
                rows={3}
                placeholder="content"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.content && (
              <p className="text-red-500 text-xs pl-3">
                {errors.content.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("follow_up_required")} />
              </div>
              <label className="ml-1 text-sm">Follow Up Required</label>
              {errors.follow_up_required && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_required.message}
                </p>
              )}
            </div>
          </div>

          {watch().follow_up_required && (
            <div>
              <label className="ml-1 text-sm">
                Follow Up Date{" "}
                {updateData?.follow_up_date
                  ? `(Current set on: ${new Date(
                      updateData.follow_up_date
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="date"
                  {...register("follow_up_date")}
                  placeholder="Your Email"
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.follow_up_date && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_date.message}
                </p>
              )}
            </div>
          )}

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("set_reminder")} />
              </div>
              <label className="ml-1 text-sm">Set Reminder</label>
              {errors.set_reminder && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.set_reminder.message}
                </p>
              )}
            </div>
          </div>

          {watch().set_reminder && (
            <div>
              <label className="ml-1 text-sm">
                Reminder Date{" "}
                {updateData?.reminder_time
                  ? `(Currently set at: ${new Date(
                      updateData.reminder_time
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="datetime-local"
                  {...register("reminder_time")}
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.reminder_time && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.reminder_time.message}
                </p>
              )}
            </div>
          )}

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("is_private")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Mark As Private
              </label>
              {errors.is_private && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_private.message}
                </p>
              )}
            </div>
          </div>

          <div className="pb-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded mb-3">
              <div className="relative">
                <input type="checkbox" {...register("is_pinned")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Pin Note
              </label>
              {errors.is_pinned && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_pinned.message}
                </p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Tags (separate with comma)
            </label>
            <div className="relative pt-1">
              <div className="flex flex-wrap gap-2 p-2 border border-gray-300 rounded-lg">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm flex items-center"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-2 text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter tags..."
                  className="flex-grow p-1 focus:outline-none text-gray-600"
                />
              </div>
            </div>
            {errors.tags && (
              <p className="text-red-500 text-xs pl-3">{errors.tags.message}</p>
            )}
          </div>

          <div className="w-full">
            {submitting ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default NotesForm;
