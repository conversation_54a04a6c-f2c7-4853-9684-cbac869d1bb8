import { But<PERSON> } from "@/components/ui/button";

import {
  Bell,
  CircleAlert,
  Clock,
  Flag,
  Handshake,
  MessageSquareIcon,
  NotepadTextIcon,
  PlusCircle,
  Ticket,
  TriangleAlert,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import ProspectDrawer from "./ProspectDrawer";
import EngagementList from "./listings/EngagementList";
import { toast } from "sonner";
import { useLazyGetEngagementsQuery } from "@/redux/slices/engagementsApiSlice";
import { useAuthHook } from "@/utils/useAuthHook";
import NotificationsList from "./listings/NotificationsList";
import FlagsList from "./listings/FlagsList";
import NotesList from "./listings/NotesList";
// import FeedbacksList from "./listings/FeedbacksList";
// import ComplaintsList from "./listings/ComplaintsList";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import TicketsList from "./listings/TicketsList";
import RemindersList from "./listings/RemindersList";
import { useLazyFetchTicketsQuery } from "@/redux/slices/tickets";
import { useLazyGetFlagsQuery } from "@/redux/slices/flagsApiSlice";
import { useLazyGetNotesQuery } from "@/redux/slices/notesApiSlice";
import { useLazyGetNotificationsQuery } from "@/redux/slices/notificationsApiSlice";
import { useLazyGetRemindersQuery } from "@/redux/slices/reminderApiSlice";

type Props = {
  prospect_id: string;
};

const PropectDetailsBody = ({ prospect_id }: Props) => {
  const { user_details } = useAuthHook();
  const employee_no = user_details?.employee_no ? user_details.employee_no : "";
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<string>("engagements");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // services getters
  const [getEngagements, { data: engagementsData, isLoading: eloading }] =
    useLazyGetEngagementsQuery();
  const [getTickets, { data: ticketsData, isLoading: tloading }] =
    useLazyFetchTicketsQuery();
  // const [getComplaints, { data: complaintsData, isLoading: cloading }] =
  //   useLazyGetComplaintsQuery();
  // const [getFeedbacks, { data: feedbacksData, isLoading: fbloading }] =
  //   useLazyGetFeedbacksQuery();
  const [getFlags, { data: flagsData, isLoading: floading }] =
    useLazyGetFlagsQuery();
  const [getNotes, { data: notesData, isLoading: nloading }] =
    useLazyGetNotesQuery();
  const [getNotifications, { data: notificationsData, isLoading: nfloading }] =
    useLazyGetNotificationsQuery();
  const [getReminders, { data: remindersData, isLoading: rloading }] =
    useLazyGetRemindersQuery();

  const fetchServices = async (activeTab: string) => {
    try {
      let res;

      if (activeTab == "engagements") {
        res = await getEngagements({ prospect: prospect_id }).unwrap();
      }

      if (activeTab == "reminders") {
        res = await getReminders({ prospect: prospect_id }).unwrap();
      }

      if (activeTab == "tickets") {
        res = await getTickets({ prospect: prospect_id }).unwrap();
      }

      // if (activeTab == "complaints") {
      //   res = await getComplaints({ prospect_id }).unwrap();
      // }

      // if (activeTab == "feedback") {
      //   res = await getFeedbacks({ prospect_id }).unwrap();
      // }

      if (activeTab == "flags") {
        res = await getFlags({ prospect: prospect_id }).unwrap();
      }

      if (activeTab == "notes") {
        res = await getNotes({ prospect: prospect_id }).unwrap();
      }

      if (activeTab == "notifications") {
        res = await getNotifications({ prospect: prospect_id }).unwrap();
      }

      console.log("res", res);

      // if (!res?.data) {
      //   toast.error(`No ${activeTab} were found"`);
      // }
    } catch (error) {
      console.log("error", error);
      // toast.error("something went wrong");
      return;
    }
  };

  useEffect(() => {
    fetchServices(activeTab);
  }, [activeTab]);

  useEffect(() => {
    const hashItem = location.hash;
    if (hashItem) {
      const hash = hashItem.replace("#", "");
      if (hash) {
        setActiveTab(hash);
        navigate(`#${hash}`, { replace: true });
      }
    } else {
      setActiveTab("engagements");
      navigate(`#engagements`, { replace: true });
    }
  }, [location.hash]);

  // Update URL hash when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`#${value}`, { replace: true });
  };
  return (
    <>
      <div className="h-full overflow-auto border rounded-md p-2">
        <small className="md:hidden sm:flex text-blue-400 flex items-center gap-1 ml-2">
          <CircleAlert size={16} /> Slide left to right to view all services
        </small>
        <div className="w-full flex items-center overflow-x-auto sm:overflow-x-auto scrollbar-hide border-b">
          <div
            className={`${
              activeTab == "engagements"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("engagements")}
            title="Engagements"
          >
            <Handshake className="w-5 h-5 text-[#2f58d2]" />{" "}
            <p className="md:flex hidden">Engagements</p>
          </div>
          <div
            className={`${
              activeTab == "reminders"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("reminders")}
            title="Reminders"
          >
            <Clock className="w-5 h-5 text-[lime]" />{" "}
            <p className="md:flex hidden">Reminders</p>
          </div>
          <div
            className={`${
              activeTab == "tickets"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("tickets")}
            title="tickets Booking"
          >
            <Ticket className="w-5 h-5 text-[dodgerblue]" />{" "}
            <p className="md:flex hidden">Tickets</p>
          </div>
          {/* <div
            className={`${
              activeTab == "complaints"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("complaints")}
            title="Complaints"
          >
            <TriangleAlert className="w-5 h-5 text-destructive" />{" "}
            <p className="md:flex hidden">Complaints</p>
          </div>
          <div
            className={`${
              activeTab == "feedback"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("feedback")}
            title="Feedback"
          >
            <MessageSquareIcon className="w-5 h-5 text-[purple]" />{" "}
            <p className="md:flex hidden">Feedback</p>
          </div> */}
          <div
            className={`${
              activeTab == "notes"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("notes")}
            title="Notes"
          >
            <NotepadTextIcon className="w-5 h-5 text-primary" />{" "}
            <p className="md:flex hidden">Notes</p>
          </div>
          <div
            className={`${
              activeTab == "flags"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("flags")}
            title="Flags"
          >
            <Flag className="w-5 h-5 text-[#ff6200]" />{" "}
            <p className="md:flex hidden">Flags</p>
          </div>
          <div
            className={`${
              activeTab == "notifications"
                ? "font-bold border-b-4 border-b-blue-400"
                : " text-gray-500"
            } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
            onClick={() => handleTabChange("notifications")}
            title="Notifications"
          >
            <Bell className="w-5 h-5 text-[orange]" />{" "}
            <p className="md:flex hidden">Notifications</p>
          </div>
        </div>

        <div className="p-4 md:p-6 mx-auto">
          {activeTab === "engagements" && (
            <div>
              {/* Render Engagements Component */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <h2 className="text-lg font-semibold mb-4">Engagements</h2>
                <div
                  onClick={() => setIsDrawerOpen(true)}
                  className="flex items-center border rounded-lg gap-x-2 px-5 py-2 cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-500"
                >
                  <PlusCircle size={16} /> Add Engagement
                </div>
              </div>
              {eloading ? (
                <div className="w-full flex items-center justify-center">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : (
                <EngagementList data={(engagementsData as any)?.results} />
              )}
            </div>
          )}
          {activeTab === "reminders" && (
            <div>
              {/* Render Engagements Component */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <h2 className="text-lg font-semibold mb-4">Reminders</h2>
                <div
                  onClick={() => setIsDrawerOpen(true)}
                  className="flex items-center border rounded-lg gap-x-2 px-5 py-2 cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-500"
                >
                  <PlusCircle size={16} /> Add Reminder
                </div>
              </div>
              {rloading ? (
                <div className="w-full flex items-center justify-center">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : (
                <RemindersList data={remindersData?.results} />
              )}
            </div>
          )}
          {activeTab === "tickets" && (
            <div>
              {/* Render tickets Component */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Tickets</h2>
                <div
                  onClick={() => setIsDrawerOpen(true)}
                  className="flex items-center border rounded-lg gap-x-2 px-5 py-2 cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-500"
                >
                  <PlusCircle size={16} /> Add Ticket
                </div>
              </div>
              {tloading ? (
                <div className="w-full flex items-center justify-center">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : (
                <TicketsList data={ticketsData?.data?.results} />
              )}
            </div>
          )}
          {activeTab === "notes" && (
            <div>
              {/* Render Notes Component */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Notes</h2>
                <div
                  onClick={() => setIsDrawerOpen(true)}
                  className="flex items-center border rounded-lg gap-x-2 px-5 py-2 cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-500"
                >
                  <PlusCircle size={16} /> Add Note
                </div>
              </div>
              {nloading ? (
                <div className="w-full flex items-center justify-center">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : (
                <NotesList data={notesData?.results} />
              )}
            </div>
          )}
          {activeTab === "flags" && (
            <div>
              {/* Render Flags Component */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Flags</h2>
                <div
                  onClick={() => setIsDrawerOpen(true)}
                  className="flex items-center border rounded-lg gap-x-2 px-5 py-2 cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-500"
                >
                  <PlusCircle size={16} /> Add Flags
                </div>
              </div>
              {floading ? (
                <div className="w-full flex items-center justify-center">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : (
                <FlagsList data={flagsData?.results} />
              )}
            </div>
          )}
          {activeTab === "notifications" && (
            <div>
              {/* Render Notifications Component */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Notifications</h2>
                <div
                  onClick={() => setIsDrawerOpen(true)}
                  className="flex items-center border rounded-lg gap-x-2 px-5 py-2 cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-500"
                >
                  <PlusCircle size={16} /> Add Notification
                </div>
              </div>
              {nfloading ? (
                <div className="w-full flex items-center justify-center">
                  <SpinnerTemp type="spinner-double" size="sm" />
                </div>
              ) : (
                <NotificationsList data={notificationsData?.results} />
              )}
            </div>
          )}
        </div>

        <ProspectDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          serviceType={activeTab}
          prospect_id={prospect_id}
          employee_no={employee_no}
        />
      </div>
    </>
  );
};

export default PropectDetailsBody;
