import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import CustomerInfoHeader from "@/components/customer-section/CustomerInfoHeader";
import CustomerSidebar from "@/components/customer-section/CustomerSidebar";
import LeftSideBar from "@/components/prospect/LeftSideBar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useGetProspectDetailsQuery } from "@/redux/slices/propects";
import { ScrollArea } from "@radix-ui/react-scroll-area";
import { useState } from "react";
import { useLocation } from "react-router-dom";
import ProspectDetailsBody from "./ProspectDetailsBody";
import Pipeline from "./Pipeline";

type Props = {};

const PropectDetails = ({}: Props) => {
  const location = useLocation();
  const prospect_id = location.pathname.split("/")[2];
  const [activeDrawer, setActiveDrawer] = useState<string | null>(null);
  const [isSidebarDrawerOpen, setIsSidebarDrawerOpen] = useState(false);

  const {
    data: prospect,
    isLoading,
    isError,
  } = useGetProspectDetailsQuery(prospect_id);

  const handleOpenDrawer = (drawer: string) => {
    setActiveDrawer(drawer);
    if (drawer === "sidebar") {
      setIsSidebarDrawerOpen(true);
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-[calc(100vh-100px)]">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      </Screen>
    );
  }

  if (isError) {
    return (
      <Screen>
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>
              Error loading propect details. Please try again later.
            </AlertDescription>
          </Alert>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="min-h-screen flex flex-col">
        <div className="flex flex-1 md:flex-row flex-col">
          <div className="w-full md:w-72 xl:w-72 border-r shrink-0 md:h-[calc(100vh-44px)] overflow-auto">
            {prospect && (
              <LeftSideBar
                prospect={prospect}
                onEdit={() => handleOpenDrawer("edit")}
              />
            )}
          </div>

          <div className=" flex flex-1 flex-col bg-gray-50 md:h-[calc(100vh-44px)] overflow-hidden">
            <div className="flex-1 overflow-auto dark:bg-gray-900">
              <div className="p-4 md:p-6 mx-auto space-y-4">
                {/* Body sections */}

                <Pipeline
                  prospect_id={prospect?.id ? String(prospect.id) : ""}
                  pipeline_level={prospect?.pipeline_level ?? "New"}
                />

                <ProspectDetailsBody prospect_id={prospect_id} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default PropectDetails;
