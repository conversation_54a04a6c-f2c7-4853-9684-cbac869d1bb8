// src/components/Prospects.tsx
import { useEffect, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";

import { format } from "date-fns";
import { Screen } from "@/app-components/layout/screen";
import {
  OutlinedButton,
  PrimaryButton,
} from "@/components/custom/buttons/buttons";
import EditProspects from "./edit";

import AddProspects from "./addlead";
import ReallocateProspects from "./reallocate";
import { motion } from "framer-motion";
import {
  useGetPropectsQuery,
  useLazyGetLeadSourceQuery,
  useLazyGetPropectsQuery,
} from "@/redux/slices/propects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import {
  ArrowLeftRight,
  Edit,
  AlertTriangle,
  RefreshCcwIcon,
  RefreshCw,
} from "lucide-react";
import { Link } from "react-router-dom";
import { ProspectTypes } from "@/types/prospects";
import { searchDebouncer } from "@/utils/debouncers";
import CustomSelectField from "@/components/CustomSelectField";
import { Button } from "@/components/ui/button";

const hasAnyProspectAccess: boolean = true; // Replace with actual permission check

const Prospects = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isReallocateModalOpen, setIsReallocateModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API
  const [selectedProspect, setSelectedProspect] = useState<any | null>(null);
  const [actionTitle, setActionTitle] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [lead_source, setLeadSource] = useState("");
  const [lead_source_label, setLeadSourceLabel] = useState("");
  const [
    fetchLeadSource,
    { data: leadSourcesData, isLoading: loadingLeadSources },
  ] = useLazyGetLeadSourceQuery({});

  const {
    data: prospects,
    isLoading: loadingProspects,
    isFetching,
    isError,
    error,
    refetch,
  } = useGetPropectsQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
    lead_source__id: lead_source,
  });

  const refetchData = async () => {
    setSearchInput("");
    setSearchValue("");
    setLeadSource("");
    setLeadSourceLabel("");
    refetch();
  };

  const handleViewProspect = (prospectId: number) => {
    const prospect = prospects?.data?.results.find(
      (p: ProspectTypes) => p.id == prospectId
    );
    if (prospect) {
      setSelectedProspect(prospect);
      setIsEditModalOpen(true);
    }
  };

  const handleReallocateProspect = (prospectId: number, actionName: string) => {
    const prospect = prospects?.data?.results.find(
      (p: ProspectTypes) => p.id === prospectId
    );
    if (prospect) {
      setSelectedProspect(prospect);
      setActionTitle(actionName);
      setIsReallocateModalOpen(true);
    }
  };

  const [cols, setCols] = useState<any>([]);

  useEffect(() => {
    setCols(columns);
  }, [loadingProspects]);

  const columns: ColumnDef<ProspectTypes>[] = [
    // {
    //   accessorKey: "id",
    //   header: "#",
    //   cell: (info) => (
    //     <span className="font-medium">{info.getValue() as string}</span>
    //   ),
    //   enableColumnFilter: false,
    // },
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <Link
          to={`/prospects/${info?.row?.original?.id}`}
          title="View Prospect"
        >
          <span className="font-medium underline capitalize text-blue-400">
            {info.getValue() as string}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "phone",
      header: "Phone",
      cell: (info) =>
        (info.getValue() as string) ? (
          <Link
            to={`/prospects/${info?.row?.original?.id}`}
            title="View Prospect"
          >
            <span className="font-medium underline capitalize text-blue-400">
              {info.getValue() as string}
            </span>
          </Link>
        ) : (
          "N/A"
        ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => {
        const pin = info.getValue() as string;
        return <span className="font-medium">{pin ? pin : "--"}</span>;
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "department_member_name",
      header: "Allocated By",
      cell: (info) => (
        <span className="text-green-700 bg-green-300 px-2 py-1 rounded-full">
          {(info.getValue() as string) || "Self"}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "lead_source_name",
      header: "Lead Source",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "project_name",
      header: "Project",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "Hot"
              ? "bg-destructive text-white px-3"
              : info?.getValue() === "Warm"
              ? "bg-yellow-400 text-black px-3"
              : "bg-blue-400 text-white"
          } text-center px-2 py-1 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const prospect = row.original;
        return (
          <div className="flex space-x-2 justify-start">
            <PrimaryButton
              variant="outline"
              size="sm"
              onClick={() => handleViewProspect(prospect.id)}
              className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
            >
              <span title="Edit">
                <Edit />
              </span>
            </PrimaryButton>

            {!prospect.marketer ? (
              <PrimaryButton
                variant="primary"
                size="sm"
                onClick={() =>
                  handleReallocateProspect(prospect.id, "Allocate")
                }
                className="text-white bg-green-500 border-green-300 hover:bg-green-50 flex items-center space-x-1"
              >
                <span>Allocate</span>
              </PrimaryButton>
            ) : (
              <PrimaryButton
                variant="primary"
                size="sm"
                onClick={() =>
                  handleReallocateProspect(prospect.id, "Reallocate")
                }
                className="bg-white !text-green-500 border border-green-300 hover:bg-green-300 hover:!text-white flex items-center space-x-1"
              >
                <span title="Reallocate">
                  <ArrowLeftRight />
                </span>
              </PrimaryButton>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  // function SearchComponent() {
  //   return (
  //     <input
  //       value={searchValue}
  //       name="searchValue"
  //       type="search"
  //       onWanjikuChange={(e) => setSearchValue(e.target.value)}
  //       className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
  //       placeholder="Search prospects..."
  //     />
  //   );
  // }

  return (
    <Screen>
      <div>
        {/* Permission Indicator */}
        {/* <div className="mb-4">
          <ProspectPermissionIndicator />
        </div> */}

        <>
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 sm:grid-cols-1 mb-4">
              <h1 className="text-3xl font-bold text-gray-800">Prospects</h1>
              <div className="flex items-center md:justify-end sm:justify-start gap-2 md:col-span-1 sm:col-span-1 flex-wrap">
                <Button
                  variant="default"
                  className=""
                  onClick={() => setIsAddModalOpen(true)}
                >
                  Add lead
                </Button>

                <Button
                  variant="secondary"
                  onClick={refetchData}
                  disabled={loadingProspects || isFetching}
                >
                  {loadingProspects || isFetching ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <div className="flex gap-2 items-center   ">
                      <RefreshCw className="w-4 h-4" /> Refresh
                    </div>
                  )}
                </Button>
                <CustomSelectField
                  valueField="id"
                  labelField="name"
                  data={leadSourcesData?.data?.results}
                  queryFunc={fetchLeadSource}
                  setValue={setLeadSource}
                  loader={loadingLeadSources}
                  useSearchField={true}
                  placeholder={
                    lead_source_label != ""
                      ? lead_source_label
                      : "Filter lead source by name"
                  }
                  labelSetter={setLeadSourceLabel}
                />
              </div>
            </div>
            <div className="my-4">
              <input
                value={searchInput}
                name="searchInput"
                type="search"
                onChange={(e) =>
                  searchDebouncer(
                    e.target.value,
                    setSearchInput,
                    setSearchValue
                  )
                }
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Search prospects..."
              />
            </div>
            <div className="overflow-x-auto relative">
              {(loadingProspects || isFetching) && (
                <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                  <SpinnerTemp type="spinner-double" size="md" />
                  loading...
                </div>
              )}
              {isError ? (
                <div className="bg-red-100 text-red-800 p-4 rounded-md mb-3">
                  <AlertTriangle className="inline-block mr-2" />
                  <span>
                    Error loading prospects:{" "}
                    {(() => {
                      if (!error) return "Unknown error";
                      if ("message" in error) return (error as any).message;
                      if ("error" in error) return (error as any).error;
                      if (
                        error &&
                        typeof error === "object" &&
                        "data" in error &&
                        (error as any).data &&
                        "error" in (error as any).data
                      )
                        return (error as any).data.error;
                      if (
                        "data" in error &&
                        typeof error.data === "object" &&
                        error.data !== null &&
                        "detail" in error.data
                      )
                        return JSON.stringify(
                          (error.data as { detail?: unknown }).detail
                        );
                      return "Unknown error";
                    })()}
                  </span>
                </div>
              ) : prospects?.data?.results?.length === 0 &&
                !loadingProspects ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg font-medium">
                    {searchInput
                      ? "No prospect found matching your search"
                      : "No prospect found"}
                  </div>
                  <p className="text-gray-400 mt-2">
                    {searchInput
                      ? "Try adjusting your search terms or search with at least 3 characters"
                      : "Prospect will appear here once they are added to the system"}
                  </p>
                  {searchInput && (
                    <div className="md:w-[40%] sm:w-[70%] mx-auto my-3">
                      <input
                        value={searchInput}
                        name="searchInput"
                        type="search"
                        onChange={(e) =>
                          searchDebouncer(
                            e.target.value,
                            setSearchInput,
                            setSearchValue
                          )
                        }
                        className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        placeholder="Search prospects..."
                      />
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <DataTable<ProspectTypes>
                    data={prospects?.data?.results || []}
                    columns={cols}
                    enableToolbar={true}
                    enableExportToExcel={true}
                    enablePagination={true}
                    enableColumnFilters={true}
                    enableSorting={true}
                    enablePrintPdf={true}
                    tableClassName="border-collapse"
                    tHeadClassName="bg-gray-50"
                    tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
                    tBodyTrClassName="hover:bg-gray-50"
                    tBodyCellsClassName="border-t"
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    itemsPerPage={itemsPerPage}
                    setItemsPerPage={setItemsPerPage}
                    totalItems={prospects?.data?.total_data || 0}
                  />
                </>
              )}
            </div>
          </div>
        </>
        {/* ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>
            <div className="text-red-500 text-lg font-medium mb-2">
              Access Restricted
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              You don't have permission to view prospect data. Contact your
              administrator to request access.
            </p>
          </div>
        )} */}

        {isEditModalOpen && selectedProspect && (
          <EditProspects
            isOpen={isEditModalOpen}
            onClose={() => {
              setIsEditModalOpen(false);
              setSelectedProspect(null);
            }}
            prospect={selectedProspect}
          />
        )}

        {isAddModalOpen && (
          <AddProspects
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}

        {isReallocateModalOpen && selectedProspect && (
          <ReallocateProspects
            isOpen={isReallocateModalOpen}
            onClose={() => {
              setIsReallocateModalOpen(false);
              setSelectedProspect(null);
            }}
            prospect={selectedProspect}
            title={actionTitle}
          />
        )}
      </div>
    </Screen>
  );
};

export default Prospects;
