// src/components/edit.tsx
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import BaseModal from "@/components/custom/modals/BaseModal";
import {
  useLazyGetLeadSourceQuery,
  useUpdateProspectMutation,
} from "@/redux/slices/propects";
import {
  useGetProjectsQuery,
  useLazyGetProjectsQuery,
} from "@/redux/slices/projects";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import { countryList } from "@/utils/countryList";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import Multiselect from "@/components/custom/forms/Multiselect";
import { FormField, FormItem } from "@/components/ui/form";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { ActionButton } from "@/components/custom/buttons/buttons";
import { Send } from "lucide-react";
import CustomSelectField from "@/components/CustomSelectField";

interface Prospects {
  id: string;
  name: string;
  email?: string;
  phone: string;
  alternate_phone: string;
  marketer: string;
  comment?: string;
  lead_source: string;
  lead_source_name: string;
  lead_source_category?: string;
  lead_source_subcategory?: string;
  project?: string;
  project_name?: string;
  city?: string;
  country?: string | null;
  category: string;
  marketer_name?: string;
}

interface EditProspectsProps {
  isOpen: boolean;
  onClose: () => void;
  prospect: Prospects | any;
}

export default function EditProspects({
  isOpen,
  onClose,
  prospect,
}: EditProspectsProps) {
  const form = useForm(); // Initialize form using react-hook-form
  const [updateProspect, { isLoading }] = useUpdateProspectMutation();
  const [marketer, setMarketer] = useState("");
  const [lead_source, setLeadSource] = useState("");
  const [project, setProject] = useState("");

  const [
    fetchLeadSource,
    { data: leadSourcesData, isLoading: loadingLeadSources },
  ] = useLazyGetLeadSourceQuery({});
  const [fetchMarketer, { data: marketersData, isLoading: marketersLoading }] =
    useLazyGetUsersQuery({});
  const [fetchProjects, { data: projects, isLoading: projectsLoading }] =
    useLazyGetProjectsQuery({});

  const [countryOptions, setCountryOptions] = useState<
    { value: any; label: string }[]
  >([]);
  const [selectedCountry, setSelectedCountry] = useState<{
    label: string;
    value: string;
  } | null>({ label: prospect?.country || "", value: prospect?.country || "" });

  const [prospectData, setProspectData] = useState<any>({
    id: prospect?.id,
    name: prospect?.name,
    email: prospect?.email,
    alternate_phone: prospect?.alternate_phone,
    marketer: prospect?.marketer,
    lead_source: prospect?.lead_source,
    project: prospect?.project,
    comment: prospect?.comment,
    category: prospect?.category,
    city: prospect?.city,
    country: prospect?.country,
    project_name: prospect?.project_name,
    lead_source_name: prospect?.lead_source_name,
  });

  useEffect(() => {
    const countrySelectOptionsList = countryList?.map((country: any) => ({
      value: country?.label,
      label: `${country?.icon} ${country?.label}`,
    }));
    setCountryOptions(countrySelectOptionsList);
  }, [countryList]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProspectData((prev: any) => ({
      ...prev,
      [name]: name === "Date" ? new Date(value) : value,
    }));
  };

  const handleUpdateProspect = async (e: React.FormEvent) => {
    e.preventDefault();

    const formData: any = {
      ...prospectData,
      id: prospect?.id,
      country: selectedCountry?.value,
      lead_source: lead_source ? lead_source : prospect.lead_source,
      marketer: marketer ? marketer : prospect.marketer,
      project: project ? project : prospect.project,
    };
    console.log("formdata", formData);

    try {
      const res = await updateProspect(formData).unwrap();
      console.log("Prospect res:", res);
      if (res?.id) {
        toast.success("Prospect Updated successfully");
        onClose();
      }
    } catch (error) {
      console.error("Error Updating prospect:", error);
      toast.error("Failed to add prospect");
    }
  };

  return (
    <BaseModal
      size="xl"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Edit Prospect"
    >
      <form onSubmit={handleUpdateProspect}>
        <div>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="Name">Client Full Name*</Label>
              <Input
                id="Name"
                name="name"
                value={prospectData.name}
                onChange={handleInputChange}
                placeholder="Enter client name"
              />
            </div>
            <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="PhoneNo">Phone Number*</Label>
                <Input
                  id="PhoneNo"
                  name="phone"
                  disabled
                  value={prospect.phone}
                  onChange={handleInputChange}
                  placeholder="Enter phone number (e.g., +2547XXXXXXXX)"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="alternate_phone">Alternate Phone</Label>
                <Input
                  id="alternate_phone"
                  name="alternate_phone"
                  value={prospectData.alternate_phone}
                  onChange={handleInputChange}
                  placeholder="Enter alternate phone"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Client Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={prospectData.email}
                onChange={handleInputChange}
                placeholder="Enter Email"
              />
            </div>

            <div className="space-y-2 c">
              <Label htmlFor="ProjectOfIntrest">
                Interested Project &nbsp;
                <small className="text-primary">
                  (Current: {prospect?.project_name})
                </small>
              </Label>
              <div className="space-y-2 flex flex-col my-4">
                <CustomSelectField
                  valueField="projectId"
                  labelField="name"
                  data={projects}
                  queryFunc={fetchProjects}
                  setValue={setProject}
                  loader={projectsLoading}
                  useSearchField={true}
                />
              </div>
              {/* <Multiselect
                value={selectedProject}
                data={projectsOptions}
                setValue={setSelectedProject}
                loading={projectsLoading}
                isClearable={false}
                isDisabled={false}
                isMultiple={false}
                isSearchable={true}
              /> */}
            </div>
            <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="marketer">
                  Allocated Marketer{" "}
                  <small className="text-primary">
                    (Current: {prospect?.marketer})
                  </small>
                </Label>
                <div className="space-y-2 flex flex-col my-4">
                  <CustomSelectField
                    valueField="employee_no"
                    labelField="fullnames"
                    data={(marketersData as any)?.data?.results}
                    queryFunc={fetchMarketer}
                    setValue={setMarketer}
                    loader={marketersLoading}
                    useSearchField={true}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <Label htmlFor="Category">Category</Label>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value); // updates react-hook-form
                          setProspectData((prev: any) => ({
                            ...prev,
                            category: value,
                          }));
                        }}
                        defaultValue={prospect?.category}
                        // defaultValue={field.value}
                      >
                        <SelectTrigger className="w-[100%] border-gray">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Select Category</SelectLabel>
                            <SelectItem value="Hot">Hot</SelectItem>
                            <SelectItem value="Warm">Warm</SelectItem>
                            <SelectItem value="Cold">Cold</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lead_source">
                  Lead Source{" "}
                  <small className="text-primary">
                    (Current: {prospect?.lead_source_name})
                  </small>
                </Label>
                <div className="space-y-2 flex flex-col my-4">
                  <CustomSelectField
                    valueField="id"
                    labelField="name"
                    data={leadSourcesData?.data?.results}
                    queryFunc={fetchLeadSource}
                    setValue={setLeadSource}
                    loader={loadingLeadSources}
                    useSearchField={true}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="selectedCountry">
                  Country{" "}
                  <small className="text-primary">
                    (Current: {prospect?.country})
                  </small>
                </Label>
                <Multiselect
                  value={selectedCountry}
                  data={countryOptions}
                  setValue={setSelectedCountry}
                  loading={false}
                  isClearable={false}
                  isDisabled={false}
                  isMultiple={false}
                  isSearchable={true}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="Message">City</Label>
                <Input
                  id="City"
                  name="city"
                  value={prospectData.city}
                  onChange={handleInputChange}
                  placeholder="Enter City"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="Message">Message*</Label>
              <Input
                id="Message"
                name="comment"
                value={prospectData.comment}
                onChange={handleInputChange}
                placeholder="Enter message"
              />
            </div>
          </div>
        </div>

        {isLoading ? (
          <SpinnerTemp size="sm" />
        ) : (
          <ActionButton
            type="submit"
            icon={<Send />}
            iconPosition="right"
            variant="primary"
            className="text-white"
          >
            {" "}
            Submit{" "}
          </ActionButton>
        )}
      </form>
    </BaseModal>
  );
}
