// src/components/ReallocateProspects.tsx
import { useState, useEffect } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import DropdownButton from "@/components/custom/Dropdowns/dropdown";
import BaseModal from "@/components/custom/modals/BaseModal";
import { useGetUsersQuery, useLazyGetUsersQuery } from "@/redux/slices/user";
import Multiselect from "@/components/custom/forms/Multiselect";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { ActionButton } from "@/components/custom/buttons/buttons";
import { Send } from "lucide-react";
import { useUpdateProspectMutation } from "@/redux/slices/propects";
import { toast } from "sonner";
import CustomSelectField from "@/components/CustomSelectField";

interface Prospects {
  id: string;
  name: string;
  email?: string;
  phone: string;
  alternate_phone: string;
  marketer: string;
  comment?: string;
  lead_source: string;
  lead_source_category?: string;
  lead_source_subcategory?: string;
  project?: string;
  category: string;
  marketer_name?: string;
}

interface ReallocateProspectsProps {
  isOpen: boolean;
  onClose: () => void;
  prospect: Prospects | any;
  title: string;
}

export default function ReallocateProspects({
  isOpen,
  onClose,
  prospect,
  title,
}: ReallocateProspectsProps) {
  const [updateProspect, { isLoading }] = useUpdateProspectMutation();
  const [fetchUsers, { data: usersList, isLoading: loadinMkt }] =
    useLazyGetUsersQuery();

  const [newUser, setNewUser] = useState("");

  const handleReallocate = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData: any = {
      id: prospect?.id,
      marketer: newUser,
    };
    try {
      const res = await updateProspect(formData).unwrap();
      console.log("object", res);
      if (res?.id) {
        toast.success("Allocation Success");
        onClose();
      } else {
        toast.error("Failed to allocated");
        return;
      }
    } catch (error) {
      toast.error("Could no reallocate marketer");
      return;
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title={`${title} Lead`}
      description="Reassign a marketer to the prospect"
    >
      <form
        onSubmit={handleReallocate}
        className="space-y-4 py-2 min-h-[25vh] flex flex-col justify-center"
      >
        <p className="text-sm text-gray-600">
          <b className="text-primary">Prospect:</b> &nbsp; {prospect?.name}{" "}
          <br />
          <b className="text-primary">Current Marketer:</b> &nbsp;{" "}
          {prospect?.marketer ? prospect?.marketer : ""}
        </p>
        <div className="space-y-2">
          <Label htmlFor="marketer">Allocate Marketer</Label>
          <div className="space-y-2 flex flex-col my-4">
            <CustomSelectField
              valueField="employee_no"
              labelField="fullnames"
              data={usersList?.data?.results}
              queryFunc={fetchUsers}
              setValue={setNewUser}
              loader={loadinMkt}
              useSearchField={true}
            />
          </div>
          {/* <Multiselect
            value={selectedMarketer}
            data={marketerOptions}
            setValue={setSelectedMarketer}
            loading={marketersLoading}
            isClearable={false}
            isDisabled={false}
            isMultiple={false}
            isSearchable={true}
          /> */}
        </div>
        {isLoading ? (
          <SpinnerTemp size="sm" />
        ) : (
          <ActionButton
            type="submit"
            icon={<Send size={15} />}
            iconPosition="right"
            variant="primary"
            className="text-white w-full"
          >
            {" "}
            Submit{" "}
          </ActionButton>
        )}
      </form>
    </BaseModal>
  );
}
