import BaseModal from "@/components/custom/modals/BaseModal";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Edit } from "lucide-react";
import { useState } from "react";
import NotesForm from "../forms/NotesForm";
import { formatDate, formatDateTime } from "@/utils/formatDate";

type Props = {
  data: any;
};

const NotesList = ({ data }: Props) => {
  const [notesModalOpen, setNotesModalOpen] = useState<any | null>(null);

  return (
    <div className="border  rounded">
      <Table>
        <TableHeader className="bg-accent">
          <TableRow className="!font-bold">
            <TableHead className="!font-bold">#</TableHead>
            <TableHead className="!font-bold">Type</TableHead>
            <TableHead className="!font-bold">Title</TableHead>
            <TableHead className="!font-bold">Content</TableHead>
            <TableHead className="!font-bold">Follow Up</TableHead>
            <TableHead className="!font-bold">Reminder</TableHead>
            <TableHead className="!font-bold">Tags</TableHead>
            <TableHead className="!font-bold">Privacy</TableHead>
            <TableHead className="!font-bold">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((rowData: any) => (
            <TableRow
              key={rowData?.note_id}
              className="hover:!bg-transparent bg-white dark:bg-transparent dark:hover:!bg-gray-300/10"
            >
              <TableCell className="!py-2.5 text-xs">
                {rowData?.is_pinned ? " 📌 " : "-"}
                {/* <Badge variant="outline" className="px-4">
                </Badge> */}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.note_type}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.title}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.content}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.follow_up_date
                  ? formatDate(rowData?.follow_up_date)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.reminder_time
                  ? formatDateTime(rowData?.reminder_time)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Badge variant="outline" className="px-4">
                  {rowData?.tags ? (
                    rowData?.tags.replace(",", ", ")
                  ) : (
                    <small className="text-destructive">No tags founf</small>
                  )}
                </Badge>
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Badge variant="outline" className="px-4">
                  {rowData?.is_private ? "🔒 Private" : "🔓 Public"}
                </Badge>
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Edit
                  className="cursor-pointer text-blue-500 hover:text-blue-700"
                  onClick={() => setNotesModalOpen(rowData)}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <BaseModal
        isOpen={!!notesModalOpen}
        onOpenChange={() => setNotesModalOpen(null)}
        title="Notes"
        description="Manage your notes here"
      >
        <NotesForm updateData={notesModalOpen} />
      </BaseModal>
    </div>
  );
};

export default NotesList;
