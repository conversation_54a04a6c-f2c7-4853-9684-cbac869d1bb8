


import React from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileText, Users, CheckCircle } from "lucide-react";


interface Survey {
  id: string;
  title: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  change: string;
  changeLabel: string;
  positive: boolean;
}

// Props for the modal
interface CreateSurveyModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onAddSurvey: (survey: Survey) => void;
  existingSurveys: Survey[];
}

// Custom hook for form handling
const useSurveyForm = (
  existingSurveys: Survey[],
  onAddSurvey: (survey: Survey) => void,
) => {
  const [newSurvey, setNew<PERSON>urvey] = React.useState({
    title: "",
    value: "Draft",
    icon: "FileText",
  });
  const [error, setError] = React.useState<string | null>(null);

  const handleInputChange = (field: keyof typeof newSurvey, value: string) => {
    setNewSurvey((prev) => ({ ...prev, [field]: value }));
    setError(null); 
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newSurvey.title.trim()) {
      setError("Survey title is required");
      return;
    }
    if (existingSurveys.some((survey) => survey.title === newSurvey.title)) {
      setError("Survey title must be unique");
      return;
    }

    const iconMap: { [key: string]: React.ComponentType<{ className?: string }> } = {
      FileText,
      Users,
      CheckCircle,
    };

    const newSurveyEntry: Survey = {
      id: (existingSurveys.length + 1).toString(),
      title: newSurvey.title,
      value: newSurvey.value,
      icon: iconMap[newSurvey.icon] || FileText,
      change: "0",
      changeLabel: "responses",
      positive: true,
    };

    onAddSurvey(newSurveyEntry);
    setNewSurvey({ title: "", value: "Draft", icon: "FileText" });
    setError(null);
  };

  return { newSurvey, error, handleInputChange, handleSubmit };
};

const CreateSurveyModal: React.FC<CreateSurveyModalProps> = ({
  isOpen,
  onOpenChange,
  onAddSurvey,
  existingSurveys,
}) => {
  const { newSurvey, error, handleInputChange, handleSubmit } = useSurveyForm(
    existingSurveys,
    onAddSurvey,
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Create New Survey"
      description="Enter details to create a new survey."
      footer={
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Add Survey</Button>
        </div>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-4 py-4">
        <div>
          <Label htmlFor="surveyTitle">Survey Title</Label>
          <Input
            id="surveyTitle"
            type="text"
            value={newSurvey.title}
            onChange={(e) => handleInputChange("title", e.target.value)}
            placeholder="Enter survey title"
            aria-describedby={error ? "title-error" : undefined}
            required
          />
          {error && (
            <p id="title-error" className="mt-1 text-sm text-red-500">
              {error}
            </p>
          )}
        </div>
        <div>
          <Label htmlFor="surveyStatus">Status</Label>
          <Select
            value={newSurvey.value}
            onValueChange={(value) => handleInputChange("value", value)}
          >
            <SelectTrigger id="surveyStatus">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Draft">Draft</SelectItem>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="surveyIcon">Icon</Label>
          <Select
            value={newSurvey.icon}
            onValueChange={(value) => handleInputChange("icon", value)}
          >
            <SelectTrigger id="surveyIcon">
              <SelectValue placeholder="Select icon" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FileText">File Text</SelectItem>
              <SelectItem value="Users">Users</SelectItem>
              <SelectItem value="CheckCircle">Check Circle</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </form>
    </BaseModal>
  );
};

export default CreateSurveyModal;