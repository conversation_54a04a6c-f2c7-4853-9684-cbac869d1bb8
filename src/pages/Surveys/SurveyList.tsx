
import React, { useState } from "react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Card6 } from "@/components/custom/cards/Card6";
import { FileText, Users, CheckCircle, BoldIcon } from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import { SurveyDataTable } from "./SurveyTable";
import CreateSurveyModal from "./CreateSurvey";


interface Survey {
  id: string;
  title: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  change: string;
  changeLabel: string;
  positive: boolean;
}

const initialSurveys: Survey[] = [
  {
    id: "1",
    title: "Customer Satisfaction Survey",
    value: "Active",
    icon: Users,
    change: "+15%",
    changeLabel: "completion rate",
    positive: true,
  },
  {
    id: "2",
    title: "Employee Engagement Survey",
    value: "Draft",
    icon: FileText,
    change: "0",
    changeLabel: "responses",
    positive: true,
  },
  {
    id: "3",
    title: "Product Feedback Survey",
    value: "Completed",
    icon: CheckCircle,
    change: "245",
    changeLabel: "total responses",
    positive: true,
  },
];

function Surveys() {
  const [selectedSurveyId, setSelectedSurveyId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [surveys, setSurveys] = useState<Survey[]>(initialSurveys);

  const handleViewSurvey = (surveyId: string) => {
    setSelectedSurveyId(selectedSurveyId === surveyId ? null : surveyId);
  };

  const handleAddSurvey = (newSurvey: Survey) => {
    setSurveys((prev) => [...prev, newSurvey]);
    setIsModalOpen(false);
  };

  const selectedSurvey = surveys.find((survey) => survey.id === selectedSurveyId);

  return (
    <Screen>
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-foreground">
            Check Detailed Surveys
          </h1>
          <div className="flex items-center">
            <PrimaryButton
              variant="primary"
              onClick={() => setIsModalOpen(true)}
            >
              Create a Survey
            </PrimaryButton>
          </div>
        </div>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {surveys.map((survey) => (
          <div key={survey.id}>
            <Card6
                    title={survey.title}
                    value={survey.value}
                    icon={BoldIcon}
                    cardBg="bg-yellow-100"
                    change={survey.change}
                    changeLabel={survey.changeLabel}
                    positive={survey.positive}
                    actions={<PrimaryButton
                        variant="destructive"
                        onClick={() => handleViewSurvey(survey.id)}
                        aria-expanded={selectedSurveyId === survey.id}
                        aria-controls={`survey-table-${survey.id}`}
                    >
                        {selectedSurveyId === survey.id ? "Hide Details" : "View Survey"}
                    </PrimaryButton>}   />
          </div>
        ))}
      </div>
      {selectedSurvey && (
        <div id={`survey-table-${selectedSurvey.id}`} className="mt-6">
          <SurveyDataTable data={[selectedSurvey]} />
        </div>
      )}
      <CreateSurveyModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        onAddSurvey={handleAddSurvey}
        existingSurveys={surveys}
      />
    </Screen>
  );
}

export default Surveys;