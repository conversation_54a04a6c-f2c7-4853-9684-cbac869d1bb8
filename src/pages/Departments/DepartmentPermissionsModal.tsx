import { Badge } from "@/components/custom/badges/badges";
import BaseModal from "@/components/custom/modals/BaseModal";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import {
  useCreateTeams2TeamsPermissionMutation,
  useDeleteTeams2TeamsPermissionMutation,
  useGetTeams2TeamsPermissionsQuery,
  useLazyGetTeamPermissionsQuery,
} from "@/redux/slices/permissions";
import { Square, SquareCheckBigIcon, TriangleAlert, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface DepartmentTypes {
  dp_id: number;
  dp_name: string;
  dep_head_name?: string;
  dep_head_code?: string;
  inactive?: boolean;
}

interface PermType {
  id: number;
  permission: string;
  permission_name: string;
  department: string;
}

type Props = {
  isOpen: boolean;
  onClose: () => void;
  department: DepartmentTypes;
};

const getAvatarUrl = (name: string) => {
  const initials = name
    ?.split(" ")
    .map((n) => n[0])
    .join("");
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(
    initials
  )}&background=random`;
};

const getStatusBadge = (status: string) => {
  const statusLower = status?.toLowerCase();
  if (statusLower === "active") {
    return (
      <Badge
        variant="primary"
        className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      >
        Active
      </Badge>
    );
  } else if (statusLower === "inactive") {
    return (
      <Badge
        variant="destructive"
        className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
      >
        Inactive
      </Badge>
    );
  } else {
    return (
      <Badge
        variant="outline"
        className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
      >
        {status}
      </Badge>
    );
  }
};

const DepartmentPermissionsModal = ({ isOpen, onClose, department }: Props) => {
  const [
    fetchDepartmentPerms,
    { data: departmentPermsResponse, isLoading: loadingDepartmentPerms },
  ] = useLazyGetTeamPermissionsQuery();

  // Extract the results array from the response
  const departmentPerms = Array.isArray(departmentPermsResponse)
    ? departmentPermsResponse
    : departmentPermsResponse?.results || [];
  const {
    data: departmentMaps = [],
    isLoading: loadingDepartmentMaps,
    refetch,
  } = useGetTeams2TeamsPermissionsQuery({
    page: 1,
    page_size: 1000,
    team: department?.dp_id ?? "",
  });
  const [deletePerm, { isLoading: deleting }] =
    useDeleteTeams2TeamsPermissionMutation();
  const [createPerm, { isLoading: creating }] =
    useCreateTeams2TeamsPermissionMutation();

  const [showDeleteModal, setShowDeleteModal] = useState<PermType | null>(null);
  const [showAddPermissionModal, setShowAddPermissionModal] =
    useState<boolean>(false);

  const handleRemovePermission = async (permissionId: number) => {
    try {
      const res = await deletePerm(permissionId).unwrap();
      if (!res) {
        toast.success("Permission removed successfully");
      } else {
        toast.error("Failed to remove permission");
      }
    } catch (error) {
      toast.error("Something went wrong");
      console.error("Error removing permission:", error);
    }
  };

  const handleAddPermission = async (permission: number) => {
    try {
      const res = await createPerm({
        permission,
        team: department?.dp_id ?? "",
      }).unwrap();
      if (res.id) {
        toast.success("Permission added successfully");
        refetch();
      } else {
        toast.error("Failed to add permission");
      }
    } catch (error) {
      toast.error("Something went wrong");
      console.error("Error adding permission:", error);
    }
  };

  useEffect(() => {
    if (showAddPermissionModal) {
      fetchDepartmentPermsHandler();
    }
  }, [showAddPermissionModal]);

  const fetchDepartmentPermsHandler = async () => {
    try {
      await fetchDepartmentPerms({}).unwrap();
    } catch (error) {
      toast.error("Failed to fetch team permissions");
      console.error("Error fetching team permissions:", error);
    }
  };

  const permissionChecker = (permissionId: number) => {
    return departmentMaps.some(
      (perm: PermType) => Number(perm.permission) === permissionId
    ) ? (
      <SquareCheckBigIcon className="text-green-500" />
    ) : (
      <Square className="text-gray-400" />
    );
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Department Permissions"
      description={`Preview of ${department.dp_name}'s permissions`}
    >
      <div className="space-y-6">
        {/* Profile Section */}
        <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <img
            src={getAvatarUrl(department.dp_name)}
            alt={department.dp_name}
            className="w-16 h-16 rounded-full border-2 border-white shadow-md"
          />
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              {department.dp_name}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {department.dep_head_name} <small> - ({department.dep_head_code})</small>
            </p>
            <div className="mt-2">
              {getStatusBadge(department?.inactive ? "inactive" : "active")}
            </div>
          </div>
        </div>

        {loadingDepartmentMaps ? (
          <div className="flex justify-center items-center h-[50px]">
            <SpinnerTemp type="spinner-double" size="sm" />
          </div>
        ) : departmentMaps.length > 0 ? (
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              Permissions
            </h4>
            <ul className=" space-y-2 h-[300px] overflow-y-auto">
              {departmentMaps.map((permission) => (
                <li
                  key={permission.id}
                  className="text-gray-700 dark:text-gray-300 bg-secondary p-2"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">
                      {permission.permission} - Can {permission.permission_name}
                    </span>{" "}
                    <X
                      onClick={() => setShowDeleteModal(permission)}
                      size="30px"
                      className="text-destructive bg-destructive/20 dark:text-yellow-300 dark:bg-yellow-300/20 p-1 rounded cursor-pointer"
                    />
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="gap-4 flex justify-center items-center py-8 text-destructive">
            <TriangleAlert /> No Permissions found
          </div>
        )}
      </div>
      <div
        className="space-y-4 bg-primary text-center rounded p-2 text-secondary cursor-pointer"
        onClick={() => setShowAddPermissionModal(true)}
      >
        Assign Permission
      </div>

      {/* deleteModal  */}
      {showDeleteModal && (
        <ConfirmModal
          isOpen={!!showDeleteModal}
          onOpenChange={() => setShowDeleteModal(null)}
          title="Confirm Permission Removal"
          variant="danger"
          message={
            <div className="space-y-2">
              <p>
                Are you sure you want to remove the department permission <br />{" "}
                <strong>{showDeleteModal.permission_name}</strong>?
              </p>

              <p className="text-sm font-medium text-red-600 dark:text-red-400">
                This action cannot be undone.
              </p>
            </div>
          }
          confirmText="Remove Permission"
          confirmVariant="destructive"
          cancelText="Cancel"
          onConfirm={() => handleRemovePermission(showDeleteModal.id)}
        />
      )}

      <BaseModal
        size="md"
        isOpen={showAddPermissionModal}
        onOpenChange={() => setShowAddPermissionModal(false)}
        title="Assign Department Permissions"
        description={`Assign ${department.dp_name} permissions`}
      >
        <div className="space-y-4">
          {loadingDepartmentPerms ? (
            <div className="flex justify-center items-center">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : creating ? (
            <div className="flex justify-center items-center h-[50px]">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : (
            departmentPerms.map((perm: any) => (
              <div
                key={perm.permission_id}
                className="flex items-center justify-start p-2 bg-secondary rounded hover:bg-secondary/80 cursor-pointer space-x-2"
                onClick={() => handleAddPermission(perm.permission_id)}
              >
                {permissionChecker(perm.permission_id)}
                <Badge variant="outline" className="text-sm">
                  {perm.permission_id}
                </Badge>
                <span className="font-medium">{perm.permission_name}</span>
              </div>
            ))
          )}
        </div>
      </BaseModal>
    </BaseModal>
  );
};

export default DepartmentPermissionsModal;
