import ConfirmModal from "@/components/custom/modals/ConfirmationModal";

interface DeletePermissionProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  isLoading?: boolean;
  permission: {
    id: string;
    permissionName: string;
    permissionCode: string;
    description: string;
  };
  category: string;
}

export default function DeletePermission({
  isOpen,
  onClose,
  onDelete,
  isLoading = false,
  permission,
  category,
}: DeletePermissionProps) {
  return (
    <ConfirmModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title={`Confirm ${category} Permission Deletion`}
      variant="danger"
      message={`Are you sure you want to delete the permission "${permission.permissionName}" (Code: ${permission.permissionCode})? This action cannot be undone.`}
      confirmText="Delete"
      confirmVariant="destructive"
      cancelText="Cancel"
      onConfirm={onDelete}
      confirmDisabled={isLoading}
      confirmLoading={isLoading}
    />
  );
}
