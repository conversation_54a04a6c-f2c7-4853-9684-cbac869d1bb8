import { useState, useEffect } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface EditPermissionProps {
  isOpen: boolean;
  onClose: () => void;
  isLoading?: boolean;
  permission: {
    id: string;
    permissionName: string;
    permissionCode: string;
    description: string;
  };
  onUpdate: (updated: {
    permission_id: number;
    permissionName: string;
    permissionCode: string;
    description: string;
  }) => void;
  category: string;
}

export default function EditPermission({
  isOpen,
  onClose,
  isLoading = false,
  permission,
  onUpdate,
  category,
}: EditPermissionProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [permissionData, setPermissionData] = useState({
    id: "",
    permissionName: "",
    permissionCode: "",
    description: "",
  });

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
      setPermissionData(permission);
    }
  }, [isOpen, permission]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setPermissionData((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpdatePermission = () => {
    onUpdate({
      permission_id: Number(permissionData.id),
      permissionName: permissionData.permissionName,
      permissionCode: permissionData.permissionCode,
      description: permissionData.description,
    });
    // note: parent should close on successful update
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title={`Edit ${category} Permission`}
      description="Complete all steps to update the permission"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleUpdatePermission}
      //isCompleteDisabled={isLoading}
      steps={[
        {
          title: "Basic Information",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="permissionName">Permission Name</Label>
                <Input
                  id="permissionName"
                  name="permissionName"
                  value={permissionData.permissionName}
                  onChange={handleInputChange}
                  placeholder="Enter permission name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="permissionCode">Permission Code</Label>
                <Input
                  id="permissionCode"
                  name="permissionCode"
                  value={permissionData.permissionCode}
                  onChange={handleInputChange}
                  placeholder="Enter permission code (e.g., 777)"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={permissionData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the permission"
                  rows={3}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="space-y-4 py-2">
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Permission Name:</span>
                  <span className="ml-2">{permissionData.permissionName}</span>
                </div>
                <div>
                  <span className="font-medium">Permission Code:</span>
                  <span className="ml-2">{permissionData.permissionCode}</span>
                </div>
                <div>
                  <span className="font-medium">Description:</span>
                  <span className="ml-2">{permissionData.description}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review before updating this permission.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}
