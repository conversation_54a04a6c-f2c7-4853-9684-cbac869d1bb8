import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface SalesPermission {
  id: number;
  name: string;
  code: string;
  description: string;
}

const salesPermissions: SalesPermission[] = [
  // Office-based permissions
  {
    id: 1001,
    name: "VIEW_SALES_HQ",
    code: "1001",
    description: "Permission to view sales data from HQ office"
  },
  {
    id: 1002,
    name: "VIEW_SALES_KAREN",
    code: "1002",
    description: "Permission to view sales data from KAREN office"
  },
  {
    id: 1003,
    name: "VIEW_SALES_ALL_OFFICES",
    code: "1003",
    description: "Permission to view sales data from all offices"
  },
  
  // Marketer-based permissions
  {
    id: 1004,
    name: "VIEW_SALES_OWN_MARKETER",
    code: "1004",
    description: "Permission to view sales data for the logged-in marketer only"
  },
  {
    id: 1005,
    name: "VIEW_SALES_ALL_MARKETERS",
    code: "1005",
    description: "Permission to view sales data for all marketers"
  },
  
  // Organization team-based permissions
  {
    id: 1006,
    name: "VIEW_SALES_DIASPORA_TEAM",
    code: "1006",
    description: "Permission to view sales data from DIASPORA team"
  },
  {
    id: 1007,
    name: "VIEW_SALES_DIGITAL_TEAM",
    code: "1007",
    description: "Permission to view sales data from DIGITAL team"
  },
  {
    id: 1008,
    name: "VIEW_SALES_TELEMARKETING_TEAM",
    code: "1008",
    description: "Permission to view sales data from TELEMARKETING team"
  },
  {
    id: 1009,
    name: "VIEW_SALES_OTHER_TEAM",
    code: "1009",
    description: "Permission to view sales data from OTHER team"
  },
  {
    id: 1010,
    name: "VIEW_SALES_ALL_TEAMS",
    code: "1010",
    description: "Permission to view sales data from all teams"
  },
  
  // Diaspora region-based permissions
  {
    id: 1011,
    name: "VIEW_SALES_DIASPORA_REGION",
    code: "1011",
    description: "Permission to view sales data filtered by diaspora region"
  },
  {
    id: 1012,
    name: "VIEW_SALES_ALL_DIASPORA_REGIONS",
    code: "1012",
    description: "Permission to view sales data from all diaspora regions"
  }
];

const SalesPermissionsTable: React.FC = () => {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Sales Permissions Reference</h2>
      <p className="text-sm text-muted-foreground">
        These permissions control access to the sales views API. Note that only one filter can be used at a time.
      </p>
      
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Permission Name</TableHead>
            <TableHead>Code</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Category</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {salesPermissions.map((permission) => (
            <TableRow key={permission.id}>
              <TableCell className="font-medium">{permission.name}</TableCell>
              <TableCell>
                <Badge variant="secondary">{permission.code}</Badge>
              </TableCell>
              <TableCell>{permission.description}</TableCell>
              <TableCell>
                {permission.id >= 1001 && permission.id <= 1003 && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">Office</Badge>
                )}
                {permission.id >= 1004 && permission.id <= 1005 && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">Marketer</Badge>
                )}
                {permission.id >= 1006 && permission.id <= 1010 && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Team</Badge>
                )}
                {permission.id >= 1011 && permission.id <= 1012 && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">Diaspora</Badge>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default SalesPermissionsTable;