# Sales Permissions Implementation

## Overview

This implementation adds sales permissions to the CRM system, allowing fine-grained control over who can access sales data based on various filters.

## Files Modified/Created

1. **PermissionsSet.tsx**
   - Added a new tab for permissions reference
   - Integrated the sales permissions reference table
   - Added UI for switching between permission management and reference

2. **components/Add.tsx**
   - Added tabs for different permission types (custom, sales, logistics, other)
   - Added templates for quickly adding predefined permissions
   - Improved the UI for permission creation

3. **components/SalesPermissionsTable.tsx** (new file)
   - Created a reference table showing all available sales permissions
   - Categorized permissions by type (office, marketer, team, diaspora)
   - Added descriptions for each permission

4. **sql/sales_permissions.sql** (new file)
   - SQL script to add all sales permissions to the database
   - Includes permissions for user, team, and group levels

5. **README.md** (new file)
   - Documentation explaining the permissions system
   - Details on how to use and implement the permissions

## Permission Structure

The sales permissions follow this structure:

1. **Permission ID**: Numeric identifier (1001-1012 for sales permissions)
2. **Permission Name**: Descriptive name (e.g., VIEW_SALES_HQ)
3. **Description**: Detailed explanation of what the permission allows

## Categories of Sales Permissions

1. **Office-based** (1001-1003)
   - Control access to sales data from specific offices (HQ, KAREN, ALL)

2. **Marketer-based** (1004-1005)
   - Control access to sales data for specific marketers or all marketers

3. **Team-based** (1006-1010)
   - Control access to sales data from specific teams (DIASPORA, DIGITAL, TELEMARKETING, OTHER, ALL)

4. **Diaspora Region-based** (1011-1012)
   - Control access to sales data filtered by diaspora region

## Next Steps

1. **Backend Implementation**:
   - Implement permission checking in the sales views API
   - Apply filters based on user permissions

2. **Testing**:
   - Test the UI for adding and managing permissions
   - Test the permission filtering in the API

3. **Additional Permission Sets**:
   - Implement similar permission structures for other areas (logistics, etc.)
   - Add more templates to the Add Permission component