// src/pages/Permissions.tsx
import { Screen } from "@/app-components/layout/screen";
import { useState, useEffect, useMemo, useCallback } from "react";
import AddPermission from "./components/Add";
import EditComponent from "./components/Edit";
import DeletePermission from "./components/Delete";
import SalesPermissionsTable from "./components/SalesPermissionsTable";
import LogisticsPermissionsTable from "./components/LogisticsPermissionsTable";
import { toast } from "@/components/custom/Toast/MyToast";
import {
  Edit,
  Trash,
  Search,
  Plus,
  Download,
  RefreshCw,
  Users,
  Shield,
  Building2,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  FileText,
} from "lucide-react";

// UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// RTK Query hooks:
import {
  useGetGroupPermissionsQuery,
  useCreateGroupPermissionMutation,
  useUpdateGroupPermissionMutation,
  useDeleteGroupPermissionMutation,
  useGetTeamPermissionsQuery,
  useCreateTeamPermissionMutation,
  useUpdateTeamPermissionMutation,
  useDeleteTeamPermissionMutation,
  useGetUserPermissionsQuery,
  useCreateUserPermissionMutation,
  useUpdateUserPermissionMutation,
  useDeleteUserPermissionMutation,
  useLazyGetGroupPermissionsQuery,
  useLazyGetTeamPermissionsQuery,
  useLazyGetUserPermissionsQuery,
} from "@/redux/slices/permissions";

interface Permission {
  permission_id: number;
  permission_name: string;
  comments: string;
}

type Category = "user" | "team" | "group";

export default function Permissions() {
  // ─── UI State ─────────────────────────────────────────────────────────────
  const [category, setCategory] = useState<Category>("user");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] =
    useState<Permission | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);
  const [isLoadingAll, setIsLoadingAll] = useState(false);
  const [showAllData, setShowAllData] = useState(false);

  // ─── RTK Query Hooks ───────────────────────────────────────────────────────
  const {
    data: groupData,
    isLoading: groupLoading,
    refetch: refetchGroup,
  } = useGetGroupPermissionsQuery(
    { search: searchTerm, page: currentPage, page_size: itemsPerPage },
    { skip: category !== "group", refetchOnMountOrArgChange: true }
  );
  const [createGroup] = useCreateGroupPermissionMutation();
  const [updateGroup] = useUpdateGroupPermissionMutation();
  const [deleteGroup] = useDeleteGroupPermissionMutation();
  const [fetchGroupLazy] = useLazyGetGroupPermissionsQuery();

  const {
    data: teamData,
    isLoading: teamLoading,
    refetch: refetchTeam,
  } = useGetTeamPermissionsQuery(
    { search: searchTerm, page: currentPage, page_size: itemsPerPage },
    { skip: category !== "team", refetchOnMountOrArgChange: true }
  );
  const [createTeam] = useCreateTeamPermissionMutation();
  const [updateTeam] = useUpdateTeamPermissionMutation();
  const [deleteTeam] = useDeleteTeamPermissionMutation();
  const [fetchTeamLazy] = useLazyGetTeamPermissionsQuery();

  const {
    data: userData,
    isLoading: userLoading,
    refetch: refetchUser,
  } = useGetUserPermissionsQuery(
    { search: searchTerm, page: currentPage, page_size: itemsPerPage },
    { skip: category !== "user", refetchOnMountOrArgChange: true }
  );
  const [createUserPerm] = useCreateUserPermissionMutation();
  const [updateUserPerm] = useUpdateUserPermissionMutation();
  const [deleteUserPerm] = useDeleteUserPermissionMutation();
  const [fetchUserLazy] = useLazyGetUserPermissionsQuery();

  // ─── Fetch All Data Function ──────────────────────────────────────────────
  const fetchAllData = useCallback(async () => {
    setIsLoadingAll(true);
    try {
      let allData: Permission[] = [];
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        let response;
        if (category === "group") {
          response = await fetchGroupLazy({
            search: searchTerm,
            page,
            page_size: 100, // Fetch larger chunks
          }).unwrap();
        } else if (category === "team") {
          response = await fetchTeamLazy({
            search: searchTerm,
            page,
            page_size: 100,
          }).unwrap();
        } else {
          response = await fetchUserLazy({
            search: searchTerm,
            page,
            page_size: 100,
          }).unwrap();
        }

        // Handle both array and paginated response formats
        const results = Array.isArray(response)
          ? response
          : response.results || [];
        allData = [...allData, ...results];

        // Check if there's more data
        if (Array.isArray(response)) {
          hasMore = results.length === 100; // If we got full page, there might be more
        } else {
          hasMore = !!response.next;
        }

        page++;

        // Safety break to prevent infinite loops
        if (page > 100) break;
      }

      setAllPermissions(allData);
      setShowAllData(true);
      toast("success", `Loaded ${allData.length} permissions`);
    } catch (error: any) {
      toast("error", error?.data?.message || "Failed to fetch all data");
    } finally {
      setIsLoadingAll(false);
    }
  }, [category, searchTerm, fetchGroupLazy, fetchTeamLazy, fetchUserLazy]);

  // ─── Derive list & loading state ──────────────────────────────────────────
  const permissionsList: Permission[] = useMemo(() => {
    if (showAllData) {
      return allPermissions;
    }

    const src =
      category === "group"
        ? groupData
        : category === "team"
        ? teamData
        : userData;
    if (!src) return [];
    return Array.isArray(src) ? src : src.results ?? [];
  }, [category, groupData, teamData, userData, showAllData, allPermissions]);

  const totalCount = useMemo(() => {
    if (showAllData) {
      return allPermissions.length;
    }

    const src =
      category === "group"
        ? groupData
        : category === "team"
        ? teamData
        : userData;
    if (!src) return 0;
    return Array.isArray(src)
      ? permissionsList.length
      : src.count ?? permissionsList.length;
  }, [
    category,
    groupData,
    teamData,
    userData,
    permissionsList,
    showAllData,
    allPermissions,
  ]);

  const isFetching =
    category === "group"
      ? groupLoading
      : category === "team"
      ? teamLoading
      : userLoading;

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // reset page and clear all data on new search, category change, or page size change
  useEffect(() => {
    setCurrentPage(1);
    setShowAllData(false);
    setAllPermissions([]);
  }, [searchTerm, category, itemsPerPage]);

  // ─── Pagination Component ──────────────────────────────────────────────────
  const PaginationComponent = () => {
    // Don't show pagination if showing all data or if there's only one page or less
    if (showAllData || totalPages <= 1) return null;

    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalCount);

    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
            {/* Items info and page size selector */}
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <div className="text-sm text-muted-foreground">
                Showing {startItem}–{endItem} of {totalCount} permissions
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Show:</span>
                <Select
                  value={itemsPerPage.toString()}
                  onValueChange={(value) => setItemsPerPage(Number(value))}
                >
                  <SelectTrigger className="w-16">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="15">15</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">per page</span>
              </div>
            </div>

            {/* Simple Pagination controls */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
                disabled={currentPage === 1}
                className="flex items-center space-x-1"
              >
                <ChevronLeft className="h-4 w-4" />
                <span>Previous</span>
              </Button>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage((p) => Math.min(p + 1, totalPages))
                }
                disabled={currentPage === totalPages}
                className="flex items-center space-x-1"
              >
                <span>Next</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // ─── Category Icons ──────────────────────────────────────────────────────
  const getCategoryIcon = (cat: Category) => {
    switch (cat) {
      case "user":
        return <Users className="h-4 w-4" />;
      case "team":
        return <Building2 className="h-4 w-4" />;
      case "group":
        return <Shield className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (cat: Category) => {
    switch (cat) {
      case "user":
        return "bg-blue-100 text-blue-800";
      case "team":
        return "bg-green-100 text-green-800";
      case "group":
        return "bg-purple-100 text-purple-800";
    }
  };

  // ─── Handlers ────────────────────────────────────────────────────────────
  const handleAdd = async (p: {
    permissionName: string;
    permissionCode: string;
    description: string;
  }) => {
    const payload = {
      permission_id: Number(p.permissionCode),
      permission_name: p.permissionName,
      comments: p.description,
    };
    try {
      if (category === "group") await createGroup(payload).unwrap();
      if (category === "team") await createTeam(payload).unwrap();
      if (category === "user") await createUserPerm(payload).unwrap();
      toast("success", "Permission added!");
      setIsAddModalOpen(false);
      // Reset all data view if active
      if (showAllData) {
        setShowAllData(false);
        setAllPermissions([]);
      }
    } catch (e: any) {
      toast("error", e?.data?.message || "Add failed");
    }
  };

  const handleUpdate = async (p: {
    permission_id: number;
    permissionName: string;
    permissionCode: string;
    description: string;
  }) => {
    const payload = {
      permission_id: p.permission_id,
      permission_name: p.permissionName,
      comments: p.description,
    };
    try {
      if (category === "group") await updateGroup(payload).unwrap();
      if (category === "team") await updateTeam(payload).unwrap();
      if (category === "user") await updateUserPerm(payload).unwrap();
      toast("success", "Permission updated!");
      setIsEditModalOpen(false);
      setSelectedPermission(null);
      // Reset all data view if active
      if (showAllData) {
        setShowAllData(false);
        setAllPermissions([]);
      }
    } catch (e: any) {
      toast("error", e?.data?.message || "Update failed");
    }
  };

  const handleDelete = async () => {
    if (!selectedPermission) return;
    try {
      const id = selectedPermission.permission_id;
      if (category === "group") await deleteGroup(id).unwrap();
      if (category === "team") await deleteTeam(id).unwrap();
      if (category === "user") await deleteUserPerm(id).unwrap();
      toast("success", "Permission deleted!");
      setIsDeleteModalOpen(false);
      setSelectedPermission(null);
      // Reset all data view if active
      if (showAllData) {
        setShowAllData(false);
        setAllPermissions([]);
      }
    } catch (e: any) {
      toast("error", e?.data?.message || "Delete failed");
    }
  };

  const handleRefresh = () => {
    if (category === "group") refetchGroup();
    else if (category === "team") refetchTeam();
    else refetchUser();

    // Reset all data view
    setShowAllData(false);
    setAllPermissions([]);
  };

  // ─── Render Table Rows ──────────────────────────────────────────────────
  const renderTableRows = () => {
    if (isFetching && !showAllData) {
      return Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <Skeleton className="h-4 w-32" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-16" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-48" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-20" />
          </TableCell>
        </TableRow>
      ));
    }

    if (permissionsList.length === 0) {
      return (
        <TableRow>
          <TableCell
            colSpan={4}
            className="text-center py-8 text-muted-foreground"
          >
            <div className="flex flex-col items-center space-y-2">
              {getCategoryIcon(category)}
              <span>No {category} permissions found</span>
            </div>
          </TableCell>
        </TableRow>
      );
    }

    return permissionsList.map((permission) => (
      <TableRow key={permission.permission_id} className="hover:bg-muted/50">
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={getCategoryColor(category)}>
              {getCategoryIcon(category)}
              <span className="ml-1 capitalize">
                {permission.permission_name}
              </span>
            </Badge>
          </div>
        </TableCell>
        <TableCell>
          <Badge variant="secondary">{permission.permission_id}</Badge>
        </TableCell>
        <TableCell className="max-w-xs">
          <span className="text-sm text-muted-foreground line-clamp-2">
            {permission.comments || "No description"}
          </span>
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedPermission(permission);
                setIsEditModalOpen(true);
              }}
              className="h-8 w-8 p-0"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedPermission(permission);
                setIsDeleteModalOpen(true);
              }}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    ));
  };

  // State for permission reference tabs
  const [referenceTab, setReferenceTab] = useState<string>("manage");

  // ─── Render ──────────────────────────────────────────────────────────────
  return (
    <Screen>
      <div className="container mx-auto py-8 space-y-6">
        {/* Main Tabs: Manage Permissions vs Reference */}
        <Tabs defaultValue="manage" onValueChange={setReferenceTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="manage" className="flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Manage Permissions</span>
            </TabsTrigger>
            <TabsTrigger
              value="reference"
              className="flex items-center space-x-2"
            >
              <FileText className="h-4 w-4" />
              <span>Permissions Reference</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="manage" className="space-y-6 pt-4">
            {/* Header */}
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
              <div className="flex items-center space-x-3">
                {getCategoryIcon(category)}
                <h1 className="text-3xl font-bold tracking-tight">
                  {category.charAt(0).toUpperCase() + category.slice(1)}{" "}
                  Permissions
                </h1>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isFetching}
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-2 ${
                      isFetching ? "animate-spin" : ""
                    }`}
                  />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchAllData}
                  disabled={isLoadingAll || isFetching}
                >
                  <Download
                    className={`h-4 w-4 mr-2 ${
                      isLoadingAll ? "animate-pulse" : ""
                    }`}
                  />
                  {isLoadingAll ? "Loading..." : "Load All"}
                </Button>
                <Button onClick={() => setIsAddModalOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Permission
                </Button>
              </div>
            </div>

            {/* Category Tabs */}
            <Tabs
              value={category}
              onValueChange={(value) => setCategory(value as Category)}
            >
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger
                  value="user"
                  className="flex items-center space-x-2"
                >
                  <Users className="h-4 w-4" />
                  <span>User</span>
                </TabsTrigger>
                <TabsTrigger
                  value="team"
                  className="flex items-center space-x-2"
                >
                  <Building2 className="h-4 w-4" />
                  <span>Team</span>
                </TabsTrigger>
                <TabsTrigger
                  value="group"
                  className="flex items-center space-x-2"
                >
                  <Shield className="h-4 w-4" />
                  <span>Group</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value={category} className="space-y-6">
                {/* Search and Filters */}
                <Card>
                  <CardHeader className="pb-4">
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                      <div className="relative flex-1 max-w-md">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder={`Search ${category} permissions...`}
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        {showAllData && (
                          <Badge
                            variant="secondary"
                            className="flex items-center space-x-1"
                          >
                            <span>
                              Showing all {allPermissions.length} items
                            </span>
                          </Badge>
                        )}
                        {!showAllData && totalCount > 0 && (
                          <Badge variant="outline">
                            Page {currentPage} of {totalPages} ({totalCount}{" "}
                            total)
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </Card>

                {/* Table */}
                <Card>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Permission Name</TableHead>
                        <TableHead>Code</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="w-24">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>{renderTableRows()}</TableBody>
                  </Table>
                </Card>

                {/* Pagination */}
                {/* <PaginationComponent /> */}
                {PaginationComponent()}

                {/* Simple Navigation - Always visible when there are multiple pages */}
                {!showAllData && totalPages > 1 && (
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Showing {(currentPage - 1) * itemsPerPage + 1}–
                      {Math.min(currentPage * itemsPerPage, totalCount)} of{" "}
                      {totalCount} permissions
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          setCurrentPage((p) => Math.max(p - 1, 1))
                        }
                        disabled={currentPage === 1}
                        className="flex items-center space-x-1"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        <span>Previous</span>
                      </Button>

                      <span className="text-sm font-medium">
                        Page {currentPage} of {totalPages}
                      </span>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          setCurrentPage((p) => Math.min(p + 1, totalPages))
                        }
                        disabled={currentPage === totalPages}
                        className="flex items-center space-x-1"
                      >
                        <span>Next</span>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </TabsContent>

          <TabsContent value="reference" className="space-y-6 pt-4">
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
              <div className="flex items-center space-x-3">
                <FileText className="h-6 w-6" />
                <h1 className="text-3xl font-bold tracking-tight">
                  Permissions Reference
                </h1>
              </div>
            </div>

            <Tabs defaultValue="sales">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger
                  value="sales"
                  className="flex items-center space-x-2"
                >
                  <ShoppingBag className="h-4 w-4" />
                  <span>Sales Permissions</span>
                </TabsTrigger>
                <TabsTrigger
                  value="logistics"
                  className="flex items-center space-x-2"
                >
                  <Building2 className="h-4 w-4" />
                  <span>Logistics Permissions</span>
                </TabsTrigger>
                <TabsTrigger
                  value="other"
                  className="flex items-center space-x-2"
                >
                  <Shield className="h-4 w-4" />
                  <span>Other Permissions</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="sales" className="space-y-6 pt-4">
                <Card>
                  <CardContent className="pt-6">
                    <SalesPermissionsTable />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="logistics" className="space-y-6 pt-4">
                <Card>
                  <CardContent className="pt-6">
                    <LogisticsPermissionsTable />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="other" className="space-y-6 pt-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center justify-center py-12">
                      <Shield className="h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-medium">
                        Other Permissions
                      </h3>
                      <p className="mt-2 text-sm text-muted-foreground text-center max-w-md">
                        Additional permission references will be added here.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </TabsContent>
        </Tabs>

        {/* Modals */}
        {isAddModalOpen && (
          <AddPermission
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onAdd={handleAdd}
            category={category}
          />
        )}
        {isEditModalOpen && selectedPermission && (
          <EditComponent
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            permission={{
              id: String(selectedPermission.permission_id),
              permissionName: selectedPermission.permission_name,
              permissionCode: String(selectedPermission.permission_id),
              description: selectedPermission.comments,
            }}
            onUpdate={handleUpdate}
            category={category}
          />
        )}
        {isDeleteModalOpen && selectedPermission && (
          <DeletePermission
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            permission={{
              id: String(selectedPermission.permission_id),
              permissionName: selectedPermission.permission_name,
              permissionCode: String(selectedPermission.permission_id),
              description: selectedPermission.comments,
            }}
            onDelete={handleDelete}
            category={category}
          />
        )}
      </div>
    </Screen>
  );
}
