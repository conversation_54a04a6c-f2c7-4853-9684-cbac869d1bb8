import { Screen } from "@/app-components/layout/screen";
import {
  Target,
  Users,
  User<PERSON><PERSON><PERSON>,
  Crown,
  Award,
  ArrowUpRight,
  <PERSON>rkles,
  Calendar,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import { useState, useMemo } from "react";

import TeamsReport from "./Components/teams/teamsReport";
import HOSReportModal from "./Components/HOSReport";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import ReportTargetModal from "./Components/ReportTarget";
import {
  useGetTeamsReportQuery,
  useGetHOSGMTargetsQuery,
  useGetMarketerTargetsQuery,
} from "@/redux/slices/targetsApiSlice";

// Define types for data structures
interface PerformanceItem {
  MIB_Perfomance: string;
  marketer_name?: string;
  marketer_no?: string;
  // add other fields as needed
}

interface ApiResponse {
  data: {
    results: PerformanceItem[];
  };
}

interface MarketerTargetApiResponse {
  data: {
    results: PerformanceItem[];
    // add other fields if needed (current_page, etc.)
  };
  // add other top-level fields if needed (message, etc.)
}

function MarketsView() {
  const [isMarketerTargetOpen, setIsMarketerTargetOpen] = useState(false);
  const [isTeamsReportOpen, setIsTeamsReportOpen] = useState(false);
  const [isHosModalOpen, setIsHosModalOpen] = useState(false);

  const periodParams = useMemo(
    () => ({
      period_name: "Dec 2023 - Jan 2024",
      start_date: "2023-12-21",
      end_date: "2024-01-20",
      role: "ALL",
    }),
    []
  );

  const { data: teamsData, isLoading: teamsLoading } = useGetTeamsReportQuery({
    period_start_date: periodParams.start_date,
    period_end_date: periodParams.end_date,
    page_size: 100,
  }) as { data: ApiResponse; isLoading: boolean };

  const { data: hosgmData, isLoading: hosgmLoading } = useGetHOSGMTargetsQuery({
    period_start_date: periodParams.start_date,
    period_end_date: periodParams.end_date,
    page_size: 100,
  }) as { data: ApiResponse; isLoading: boolean };

  const { data: marketersData, isLoading: marketersLoading } =
    useGetMarketerTargetsQuery({
      period_start_date: periodParams.start_date,
      period_end_date: periodParams.end_date,
      page_size: 100,
    });

  const dashboardStats = useMemo(() => {
    const teams = teamsData?.data?.results || [];
    const hosgm = hosgmData?.data?.results || [];
    // FIX: Use marketersData?.data?.results for marketers
    const marketers = marketersData?.data?.results || [];

    const activeTeams = teams.length;
    const teamsWithTargetHit = teams.filter((team) => {
      const performance = parseFloat(team.MIB_Perfomance?.replace("%", "") || "0");
      return performance >= 100;
    }).length;
    const avgTeamPerformance =
      teams.length > 0
        ? teams.reduce((sum, team) => {
            const perf = parseFloat(team.MIB_Perfomance?.replace("%", "") || "0");
            return sum + perf;
          }, 0) / teams.length
        : 0;

    const activeMarketers = marketers.length;
    const marketersTargetAchievers = marketers.filter((marketer) => {
      const performance = parseFloat(
        marketer.MIB_Perfomance?.replace("%", "") || "0"
      );
      return performance >= 100;
    }).length;

    const activeHosgm = hosgm.length;
    const topHosgmPerformers = hosgm.filter((h) => {
      const performance = parseFloat(h.MIB_Perfomance?.replace("%", "") || "0");
      return performance >= 90;
    }).length;
    const hosgmExcellenceRating =
      hosgm.length > 0
        ? hosgm.reduce((sum, h) => {
            const perf = parseFloat(h.MIB_Perfomance?.replace("%", "") || "0");
            return sum + perf;
          }, 0) / hosgm.length
        : 0;

    const allPerformances = [...teams, ...marketers, ...hosgm];
    const overallTargetHit =
      allPerformances.length > 0
        ? allPerformances.reduce((sum, item) => {
            const perf = parseFloat(item.MIB_Perfomance?.replace("%", "") || "0");
            return sum + perf;
          }, 0) / allPerformances.length
        : 0;

    return {
      activeTeams,
      teamsWithTargetHit,
      avgTeamPerformance: Math.round(avgTeamPerformance),
      activeMarketers,
      marketersTargetAchievers,
      activeHosgm,
      topHosgmPerformers,
      hosgmExcellenceRating: Math.round(hosgmExcellenceRating),
      overallTargetHit: Math.round(overallTargetHit),
    };
  }, [teamsData, hosgmData, marketersData]);

  const isLoading = teamsLoading || hosgmLoading || marketersLoading;

  // Calculate top marketer
// Map marketersData to PerformanceItem[] if necessary
const marketers: PerformanceItem[] = (marketersData?.data?.results || []).map((marketer: any) => ({
  MIB_Perfomance: marketer.MIB_Perfomance ?? "",
  marketer_name: marketer.marketer_name,
  marketer_no: marketer.marketer_no,
  // add other fields as needed
}));
const topMarketer = useMemo(() => {
  if (!marketers.length) return null;
  return marketers.reduce((top, marketer) => {
    const perf = parseFloat(marketer.MIB_Perfomance?.replace("%", "") || "0");
    const topPerf = parseFloat(top.MIB_Perfomance?.replace("%", "") || "0");
    return perf > topPerf ? marketer : top;
  }, marketers[0]);
}, [marketers]);

  console.log("Top Marketer:", topMarketer);

  if (isLoading) {
    return (
      <Screen>
        <div className="space-y-8">
          {/* Skeleton for header */}
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse" />
            <div>
              <div className="w-48 h-6 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse" />
              <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            </div>
          </div>
          {/* Skeleton for dashboard cards */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 w-full lg:w-auto lg:min-w-[700px]">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="rounded-2xl p-4 border bg-gray-100 dark:bg-gray-800 border-gray-200/50 dark:border-gray-700/50 animate-pulse"
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-700 rounded-xl mb-2" />
                  <div className="w-16 h-4 bg-gray-300 dark:bg-gray-700 rounded mb-1" />
                  <div className="w-10 h-6 bg-gray-300 dark:bg-gray-700 rounded" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-4">
        {/* Header Section */}
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex items-center gap-2">
            <div className="relative p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow">
              <Target className="w-7 h-7 text-white" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse">
                <Sparkles className="w-1.5 h-1.5 text-yellow-800" />
              </div>
            </div>
            <div>
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Targets Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm mt-0.5">
                Track performance across teams and individual marketers
              </p>
            </div>
          </div>

          {/* Quick Stats - Compact */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 w-full lg:w-auto lg:min-w-[400px]">
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 rounded-xl p-2 border border-blue-200/50 dark:border-blue-700/50">
              <div className="flex flex-col items-center text-center">
                <div className="p-1 bg-blue-500 rounded-lg shadow mb-1">
                  <Users className="w-4 h-4 text-white" />
                </div>
                <p className="text-blue-600 dark:text-blue-400 text-[10px] font-semibold">Active Teams</p>
                <p className="text-blue-900 dark:text-blue-100 text-base font-bold">
                  {dashboardStats.activeTeams}
                </p>
              </div>
            </div>
            <div className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 rounded-xl p-2 border border-green-200/50 dark:border-green-700/50">
              <div className="flex flex-col items-center text-center">
                <div className="p-1 bg-green-500 rounded-lg shadow mb-1">
                  <UserCheck className="w-4 h-4 text-white" />
                </div>
                <p className="text-green-600 dark:text-green-400 text-[10px] font-semibold">Marketers</p>
                <p className="text-green-900 dark:text-green-100 text-base font-bold">
                  {dashboardStats.activeMarketers}
                </p>
              </div>
            </div>
            <div className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/20 rounded-xl p-2 border border-purple-200/50 dark:border-purple-700/50">
              <div className="flex flex-col items-center text-center">
                <div className="p-1 bg-purple-500 rounded-lg shadow mb-1">
                  <Crown className="w-4 h-4 text-white" />
                </div>
                <p className="text-purple-600 dark:text-purple-400 text-[10px] font-semibold">HOSGM</p>
                <p className="text-purple-900 dark:text-purple-100 text-base font-bold">
                  {dashboardStats.activeHosgm}
                </p>
              </div>
            </div>
            <div className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/20 rounded-xl p-2 border border-orange-200/50 dark:border-orange-700/50">
              <div className="flex flex-col items-center text-center">
                <div className="p-1 bg-orange-500 rounded-lg shadow mb-1">
                  <Award className="w-4 h-4 text-white" />
                </div>
                <p className="text-orange-600 dark:text-orange-400 text-[10px] font-semibold">Target Hit</p>
                <p className="text-orange-900 dark:text-orange-100 text-base font-bold">
                  {dashboardStats.overallTargetHit}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid - Compact */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Teams Section */}
          <div className="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-xl shadow border border-gray-200/50 dark:border-gray-700/50">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900 dark:text-white mb-0.5">
                      Teams Performance
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      Monitor team targets
                    </p>
                  </div>
                </div>
                <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
                  <ArrowUpRight className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="space-y-2 mb-3">
                <div className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Top Performers</span>
                  <span className="text-base font-bold text-blue-900 dark:text-blue-100">
                    {dashboardStats.teamsWithTargetHit}/{dashboardStats.activeTeams}
                  </span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Avg. Target Hit</span>
                  <span className="text-base font-bold text-gray-900 dark:text-gray-100">
                    {dashboardStats.avgTeamPerformance}%
                  </span>
                </div>
              </div>
              <button
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-2 px-3 rounded-lg shadow hover:shadow-md transition-all duration-300 text-sm"
                onClick={() => setIsTeamsReportOpen(true)}
              >
                View All Teams
              </button>
            </div>
            <div className="absolute -top-6 -right-6 w-16 h-16 bg-blue-200/20 rounded-full blur-2xl" />
          </div>

          {/* Marketers Section */}
          <div className="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-xl shadow border border-gray-200/50 dark:border-gray-700/50">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow">
                    <UserCheck className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900 dark:text-white mb-0.5">
                      Individual Marketers
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      Track individual metrics
                    </p>
                  </div>
                </div>
                <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
                  <PieChart className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="space-y-2 mb-3">
                <div className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <span className="text-xs font-medium text-green-700 dark:text-green-300">Active This Month</span>
                  <span className="text-base font-bold text-green-900 dark:text-green-100">
                    {dashboardStats.activeMarketers}
                  </span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Target Achievers</span>
                  <span className="text-base font-bold text-gray-900 dark:text-gray-100">
                    {dashboardStats.marketersTargetAchievers}
                  </span>
                </div>
              </div>
              <PrimaryButton
                onClick={() => setIsMarketerTargetOpen(true)}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-3 rounded-lg shadow hover:shadow-md transition-all duration-300 text-sm"
              >
                View All Marketers
              </PrimaryButton>
            </div>
            <div className="absolute -top-6 -right-6 w-16 h-16 bg-green-200/20 rounded-full blur-2xl" />
          </div>

          {/* HOSGM Section */}
          <div className="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-xl shadow border border-gray-200/50 dark:border-gray-700/50">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow">
                    <Crown className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900 dark:text-white mb-0.5">
                      HOSGM Leaders
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      Head of Sales & GMs
                    </p>
                  </div>
                </div>
                <div className="p-1 bg-purple-100 dark:bg-purple-900/30 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
                  <PieChart className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <div className="space-y-2 mb-3">
                <div className="flex items-center justify-between p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <span className="text-xs font-medium text-purple-700 dark:text-purple-300">Top Leaders</span>
                  <span className="text-base font-bold text-purple-900 dark:text-purple-100">
                    {dashboardStats.topHosgmPerformers}/{dashboardStats.activeHosgm}
                  </span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Excellence Rating</span>
                  <span className="text-base font-bold text-gray-900 dark:text-gray-100">
                    {dashboardStats.hosgmExcellenceRating}%
                  </span>
                </div>
              </div>
              <button
                className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-semibold py-2 px-3 rounded-lg shadow hover:shadow-md transition-all duration-300 text-sm"
                onClick={() => setIsHosModalOpen(true)}
              >
                View HOSGM Dashboard
              </button>
            </div>
            <div className="absolute -top-6 -right-6 w-16 h-16 bg-purple-200/20 rounded-full blur-2xl" />
          </div>
        </div>

        {/* Additional Performance Insights - Compact */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-base font-bold text-gray-900 dark:text-white">Monthly Progress</h3>
                <p className="text-gray-600 dark:text-gray-400 text-xs">Current month tracking</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-700 dark:text-gray-300">Overall Progress</span>
                <span className="font-bold text-orange-600 text-sm">{dashboardStats.overallTargetHit}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  style={{ width: `${dashboardStats.overallTargetHit}%`, transition: "width 1.5s" }}
                  className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full"
                />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl">
                <Award className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-base font-bold text-gray-900 dark:text-white">Recent Achievements</h3>
                <p className="text-gray-600 dark:text-gray-400 text-xs">Latest milestones</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 p-2 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">
                  {dashboardStats.teamsWithTargetHit} teams exceeded targets
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">
                  {dashboardStats.marketersTargetAchievers} marketers achieved targets
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">
                  {dashboardStats.topHosgmPerformers} HOSGM met excellence criteria
                </span>
              </div>
              {topMarketer && (
                <div
                  className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg animate-bounce"
                  title={`Top Marketer: ${topMarketer.marketer_name} (${topMarketer.MIB_Perfomance}%)`}
                >
                  <span className="text-lg">🎉✨🥇</span>
                  <span className="text-xs font-bold text-yellow-700 dark:text-yellow-300">
                    Top Marketer: <span className="text-base">{topMarketer.marketer_name || "Unknown"}</span>
                    {topMarketer.marketer_no && ` (${topMarketer.marketer_no}) `}
                    <span className="text-xs">({topMarketer.MIB_Perfomance}%)</span>
                  </span>
                  <span className="text-lg">🎉✨</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Modals */}
        <ReportTargetModal
          isModalOpen={isMarketerTargetOpen}
          setIsModalOpen={setIsMarketerTargetOpen}
          params={{
            period_start_date: periodParams.start_date,
            period_end_date: periodParams.end_date,
          }}
        />
        <TeamsReport
          isModalOpen={isTeamsReportOpen}
          setIsModalOpen={setIsTeamsReportOpen}
          params={periodParams}
        />
        <HOSReportModal
          isModalOpen={isHosModalOpen}
          setIsModalOpen={setIsHosModalOpen}
          params={periodParams}
        />
      </div>
    </Screen>
  );
}

export default MarketsView;