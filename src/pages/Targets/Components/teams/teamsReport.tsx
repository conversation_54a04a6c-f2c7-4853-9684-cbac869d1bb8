import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { useGetTeamsReportQuery } from '@/redux/slices/targetsApiSlice';


export interface TeamReport {
  line_no: number;
  team: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: string;
  daily_target: string;
  MIB_achieved: string;
  MIB_Perfomance: string;
}

interface TeamsReportProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  params?: {
    period_name: string;
    start_date: string;
    end_date: string;
    role?: string;
  };
}

const columns: TableColumn<TeamReport>[] = [
  { key: 'line_no', title: 'Line No' },
  { key: 'team', title: 'Team' },
  { key: 'period_start_date', title: 'Period Start' },
  { key: 'period_end_date', title: 'Period End' },
  { key: 'monthly_target', title: 'Monthly Target', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'daily_target', title: 'Daily Target', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'MIB_achieved', title: 'MIB Achieved', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'MIB_Perfomance', title: 'MIB Performance', render: v => <span>{Number(v).toLocaleString()}%</span> },
];

const TeamsReport = ({ isModalOpen, setIsModalOpen, params }: TeamsReportProps) => {
  // Use the API hook for team reports
  const { data, isLoading, error } = useGetTeamsReportQuery({
    period_start_date: params?.start_date,
    period_end_date: params?.end_date,
    page: 1,
    page_size: 20,
  }, {
    skip: !params?.start_date || !params?.end_date,
  });

  // Mount the data for use in the modal table
  const tableData = Array.isArray(data?.data?.results) ? data.data.results : [];
  console.log("teams report", data);
  console.log("teams report tableData", tableData);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <LazyModal<TeamReport>
      isOpen={isModalOpen}
      onClose={handleCloseModal}
      title="Team Reports"
      url="/team-targets/" // <-- match the correct endpoint
      params={params}
      columns={columns}
      size="lg"
      TableComponent={({ ...rest }) => (
        <div>
          {isLoading && <div className="p-4 text-center">Loading...</div>}
          {error && <div className="p-4 text-center text-red-600">Error loading data</div>}
          {!isLoading && !error && (
            <table className="min-w-full bg-white dark:bg-gray-800">
              <thead>
                <tr>
                  {columns.map((col) => (
                    <th key={String(col.key)} className="px-2 py-2">{col.title}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableData.map((row, idx) => (
                  <tr key={row.line_no || idx}>
                    {columns.map((col) => (
                      <td key={String(col.key)} className="px-2 py-2">
                        {col.render
                          ? col.render(row[col.key as keyof TeamReport], row, idx)
                          : row[col.key as keyof TeamReport]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          )}
          {!isLoading && !error && tableData.length === 0 && (
            <div className="p-4 text-center text-gray-500">No data available</div>
          )}
        </div>
      )}
    />
  );
};

export default TeamsReport;