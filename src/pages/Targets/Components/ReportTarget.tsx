import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { useGetMarketerTargetsQuery } from '@/redux/slices/targetsApiSlice';

export interface MarketerTarget {
  id: number;
  period_start_date: string;
  period_end_date: string;
  monthly_target: string;
  daily_target: string;
  MIB_achieved: string;
  MIB_Perfomance: string;
  marketer_no: string;
}

// Define the API response structure
interface MarketerTargetsResponse {
  data: {
    results: MarketerTarget[];
    count?: number;
    next?: string | null;
    previous?: string | null;
  };
  success?: boolean;
  message?: string;
}

interface MarketerTargetModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  params?: {
    marketer_no?: string;
    period_start_date?: string;
    period_end_date?: string;
    search?: string;
    ordering?: string;
    page?: number;
    page_size?: number;
  };
}

const columns: TableColumn<MarketerTarget>[] = [
  { key: 'marketer_no', title: 'Marketer No' },
  { key: 'period_start_date', title: 'Period Start' },
  { key: 'period_end_date', title: 'Period End' },
  { key: 'monthly_target', title: 'Monthly Target', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'daily_target', title: 'Daily Target', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'MIB_achieved', title: 'MIB Achieved', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'MIB_Perfomance', title: 'MIB Performance', render: v => <span>{Number(v).toLocaleString()}%</span> },
];

const ReportTargetModal = ({ isModalOpen, setIsModalOpen, params }: MarketerTargetModalProps) => {
  // Type the query result
  const { data, isLoading, error } = useGetMarketerTargetsQuery({
    marketer_no: params?.marketer_no,
    period_start_date: params?.period_start_date,
    period_end_date: params?.period_end_date,
    search: params?.search,
    ordering: params?.ordering,
    page: params?.page || 1,
    page_size: params?.page_size || 20,
  }, {
    skip: !params?.period_start_date || !params?.period_end_date,
  }) as {
    data: MarketerTargetsResponse | undefined;
    isLoading: boolean;
    error: unknown;
  };

  const handleCloseModal = () => setIsModalOpen(false);

  // Now TypeScript knows the structure of data
  const tableData: MarketerTarget[] = data?.data?.results ?? [];

  console.log("marketer target", data);

  return (
    <LazyModal<MarketerTarget>
      isOpen={isModalOpen}
      onClose={handleCloseModal}
      title="marketer targets"
      url="/team-targets/"
      params={params}
      columns={columns}
      size="lg"
      TableComponent={() => (
        <div>
          {isLoading && <div className="p-4 text-center">Loading...</div>}
          
          {!isLoading && !error && (
            <table className="min-w-full bg-white dark:bg-gray-800">
              <thead>
                <tr>
                  {columns.map((col) => (
                    <th key={String(col.key)} className="px-2 py-2">{col.title}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableData.map((row: MarketerTarget, idx: number) => (
                  <tr key={row.id || idx}>
                    {columns.map((col) => (
                      <td key={String(col.key)} className="px-2 py-2">
                        {col.render
                          ? col.render(row[col.key as keyof MarketerTarget], row, idx)
                          : row[col.key as keyof MarketerTarget]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          )}
          {!isLoading && !error && tableData.length === 0 && (
            <div className="p-4 text-center text-gray-500">No data available</div>
          )}
        </div>
      )}
    />
  );
};

export default ReportTargetModal;