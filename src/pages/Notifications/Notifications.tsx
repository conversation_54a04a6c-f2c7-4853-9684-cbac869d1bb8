import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import InfoModal from "@/components/custom/modals/InfoModal";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Bell,
  FileText,
  Clock,
  Users,
  UserCheck,
  Briefcase,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Zap,
  FileLock,
} from "lucide-react";
import { useLazyGetNotificationsQuery } from "@/redux/slices/notificationsApiSlice";

interface Notification {
  note_id: string;
  note_type: "General" | "Important" | "Reminder" | "Follow-up" | "Internal" | "Customer Facing";
  title: string;
  content: string;
  is_private: boolean;
  is_pinned: boolean;
  tags: string[];
  follow_up_required: boolean;
  follow_up_date: string | null;
  set_reminder: boolean;
  reminder_time: string | null;
  client_type: "Prospect" | "Customer" | "Sale" | null;
  created_at: string;
  created_by: string;
  customer: string | null;
  prospect: string | null;
  sale: string | null;
}

type CategoryType = "all" | "customer" | "sales" | "prospects";

export default function NotificationsList() {
  const [trigger, { data: apiResponse, isLoading, isError, error }] =
    useLazyGetNotificationsQuery();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<Notification | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeCategory, setActiveCategory] = useState<CategoryType>("all");

  // Fetch notifications based on active category
  useEffect(() => {
    const params: any = { page: currentPage };

    if (activeCategory === "customer") {
      params.client_type = "Customer";
    } else if (activeCategory === "sales") {
      params.client_type = "Sale";
    } else if (activeCategory === "prospects") {
      params.client_type = "Prospect";
    }

    trigger(params);
  }, [trigger, currentPage, activeCategory]);

  // Transform API response to match our interface
  useEffect(() => {
    console.log("Raw API Response:", apiResponse);
    // FIXED: Extract data directly from apiResponse.results, not from nested data property
    if (apiResponse?.results) {
      const transformedNotifications: Notification[] =
        apiResponse.results.map((item: any) => ({
          // Map API fields to your interface fields
          note_id: item.notification_id,
          note_type: item.notification_type === "Info" ? "General" : item.notification_type || "General",
          title: item.title,
          content: item.message || "", // API uses 'message' instead of 'content'
          is_private: false, // Not available in API response
          is_pinned: false, // Not available in API response
          tags: [], // Not available in API response
          follow_up_required: false, // Not available in API response
          follow_up_date: null, // Not available in API response
          set_reminder: false, // Not available in API response
          reminder_time: null, // Not available in API response
          client_type: item.client_type || null,
          created_at: item.created_at,
          created_by: item.created_by || item.sender || "System",
          customer: item.customer,
          prospect: item.prospect,
          sale: item.sale,
        }));
      setNotifications((prev) =>
        currentPage === 1
          ? transformedNotifications
          : [...prev, ...transformedNotifications]
      );
    }
  }, [apiResponse, currentPage]);

  // Helper functions
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const timeAgo = (date: string) => {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - notificationDate.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(date);
  };

  const handleViewDetails = (notification: Notification) => {
    setSelectedNote(notification);
    setIsInfoModalOpen(true);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "General":
        return Info;
      case "Important":
        return AlertCircle;
      case "Reminder":
        return Clock;
      case "Follow-up":
        return CheckCircle;
      case "Internal":
        return FileLock;
      case "Customer Facing":
        return Users;
      default:
        return Bell;
    }
  };

  const getCategoryCount = (category: CategoryType) => {
    if (category === "all") return notifications.length;

    const statusMap = {
      customer: "Customer",
      sales: "Sale",
      prospects: "Prospect",
    };

    return notifications.filter((n) => n.client_type === statusMap[category])
      .length;
  };

  const getNoteTypeStyles = (noteType: string) => {
    switch (noteType) {
      case "Important":
        return {
          container: "bg-gradient-to-r from-rose-50 to-rose-100 dark:from-rose-900/20 dark:to-rose-800/20 border-l-4 border-rose-500",
          icon: "text-rose-600 dark:text-rose-400",
          badge: "bg-rose-100 text-rose-800 dark:bg-rose-900/20 dark:text-rose-400",
        };
      case "Reminder":
        return {
          container: "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-l-4 border-blue-500",
          icon: "text-blue-600 dark:text-blue-400",
          badge: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
        };
      case "Follow-up":
        return {
          container: "bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 border-l-4 border-emerald-500",
          icon: "text-emerald-600 dark:text-emerald-400",
          badge: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400",
        };
      case "Internal":
        return {
          container: "bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-l-4 border-purple-500",
          icon: "text-purple-600 dark:text-purple-400",
          badge: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
        };
      case "Customer Facing":
        return {
          container: "bg-gradient-to-r from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20 border-l-4 border-teal-500",
          icon: "text-teal-600 dark:text-teal-400",
          badge: "bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400",
        };
      case "General":
      default:
        return {
          container: "bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 border-l-4 border-gray-500",
          icon: "text-gray-600 dark:text-gray-400",
          badge: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
        };
    }
  };

  const renderNotificationsList = (notificationsList: Notification[]) => {
    if (isLoading) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600 dark:text-slate-400">
            Loading notifications...
          </p>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-red-600 mb-2">
            Error Loading Notifications
          </h3>
          <p className="text-slate-600 dark:text-slate-400">
            {error
              ? JSON.stringify(error)
              : "Failed to load notifications. Please try again."}
          </p>
        </div>
      );
    }

    if (notificationsList.length === 0) {
      return (
        <div className="text-center py-20">
          <Bell className="h-16 w-16 mx-auto text-slate-300 dark:text-slate-600 mb-4" />
          <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
            No notifications found
          </h3>
          <p className="text-slate-500 dark:text-slate-400">
            {activeCategory === "all"
              ? "You don't have any notifications at the moment."
              : `No notifications found for ${activeCategory}.`}
          </p>
        </div>
      );
    }

    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <AnimatePresence>
          {notificationsList.map((notification) => {
            const IconComponent = getNotificationIcon(notification.note_type);
            const styles = getNoteTypeStyles(notification.note_type);
            return (
              <motion.div
                key={notification.note_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={`${styles.container} rounded-lg shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer`}
                onClick={() => handleViewDetails(notification)}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div
                        className={`rounded-full p-2 bg-white dark:bg-slate-800 shadow-sm`}
                      >
                        <IconComponent
                          className={`h-5 w-5 ${styles.icon}`}
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-slate-900 dark:text-slate-100 line-clamp-1">
                          {notification.title}
                        </h3>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {notification.client_type || "General"}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {notification.is_pinned && (
                        <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 text-xs">
                          Pinned
                        </Badge>
                      )}
                      <Badge className={`${styles.badge} text-xs`}>
                        {notification.note_type}
                      </Badge>
                    </div>
                  </div>

                  <p className="text-sm text-slate-700 dark:text-slate-300 mb-4 line-clamp-2">
                    {notification.content}
                  </p>

                  <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {timeAgo(notification.created_at)}
                    </div>
                    <div className="flex items-center gap-2">
                      {notification.is_private && (
                        <Badge variant="outline" className="text-xs">
                          Private
                        </Badge>
                      )}
                      {notification.client_type && (
                        <Badge variant="outline" className="text-xs">
                          {notification.client_type}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    );
  };

  const infoFields = selectedNote
    ? [
        { label: "Title", value: selectedNote.title },
        { label: "Content", value: selectedNote.content },
        { label: "Date", value: formatDate(selectedNote.created_at) },
        { label: "Type", value: selectedNote.note_type },
        { label: "Client Type", value: selectedNote.client_type || "N/A" },
        { label: "Created By", value: selectedNote.created_by || "System" },
        { label: "Private", value: selectedNote.is_private ? "Yes" : "No" },
        { label: "Pinned", value: selectedNote.is_pinned ? "Yes" : "No" },
        { label: "Tags", value: selectedNote.tags?.join(", ") || "None" },
        {
          label: "Follow Up Required",
          value: selectedNote.follow_up_required ? "Yes" : "No",
        },
        {
          label: "Follow Up Date",
          value: selectedNote.follow_up_date
            ? formatDate(selectedNote.follow_up_date)
            : "N/A",
        },
        {
          label: "Reminder",
          value: selectedNote.set_reminder ? "Yes" : "No",
        },
        {
          label: "Reminder Time",
          value: selectedNote.reminder_time || "N/A",
        },
        { label: "Customer", value: selectedNote.customer || "N/A" },
        { label: "Prospect", value: selectedNote.prospect || "N/A" },
        { label: "Sale", value: selectedNote.sale || "N/A" },
      ]
    : [];

  return (
    <Screen>
      <div className="relative bg-gradient-to-r from-green-600 to-purple-600 text-white py-8 px-4 sm:px-6 rounded-b-lg shadow-lg">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Bell className="h-8 w-8" />
            Notifications Dashboard
          </h1>
          <p className="mt-2 text-blue-100">
            Manage and track all notifications across customers, sales, and
            prospects
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 pt-8 pb-20">
        
       <Tabs
  value={activeCategory}
  onValueChange={(value) => setActiveCategory(value as CategoryType)}
  className="w-full"
>
  <TabsList className="grid w-full grid-cols-4 mb-6 border-2 border-green-500 rounded-lg bg-white">
    <TabsTrigger
      value="all"
      className="flex items-center gap-2 border-r-2 border-green-500 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 data-[state=active]:border-green-600 text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors"
    >
      <Bell className="h-4 w-4" />
      All ({notifications.length})
    </TabsTrigger>
    <TabsTrigger
      value="customer"
      className="flex items-center gap-2 border-r-2 border-green-500 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 data-[state=active]:border-green-600 text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors"
    >
      <Users className="h-4 w-4" />
      Customers ({getCategoryCount("customer")})
    </TabsTrigger>
    <TabsTrigger
      value="sales"
      className="flex items-center gap-2 border-r-2 border-green-500 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 data-[state=active]:border-green-600 text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors"
    >
      <Briefcase className="h-4 w-4" />
      Sales ({getCategoryCount("sales")})
    </TabsTrigger>
    <TabsTrigger
      value="prospects"
      className="flex items-center gap-2 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 data-[state=active]:border-green-600 text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors"
    >
      <UserCheck className="h-4 w-4" />
      Prospects ({getCategoryCount("prospects")})
    </TabsTrigger>
  </TabsList>

  <TabsContent value="all" className="mt-6">
    {renderNotificationsList(notifications)}
  </TabsContent>

  <TabsContent value="customer" className="mt-6">
    {renderNotificationsList(
      notifications.filter((n) => n.client_type === "Customer")
    )}
  </TabsContent>

  <TabsContent value="sales" className="mt-6">
    {renderNotificationsList(
      notifications.filter((n) => n.client_type === "Sale")
    )}
  </TabsContent>

  <TabsContent value="prospects" className="mt-6">
    {renderNotificationsList(
      notifications.filter((n) => n.client_type === "Prospect")
    )}
  </TabsContent>
</Tabs>

        {/* Load More Button - FIXED: Use correct API response structure */}
        {apiResponse?.current_page < apiResponse?.num_pages && (
          <div className="text-center mt-8">
            <PrimaryButton
              className="px-6 py-3 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors"
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Load More Notifications
            </PrimaryButton>
          </div>
        )}
      </div>

      {/* Notification Details Modal */}
      <InfoModal
        isOpen={isInfoModalOpen}
        onOpenChange={setIsInfoModalOpen}
        title="Notification Details"
        icon={<FileText className="h-6 w-6 text-blue-500" />}
        fields={infoFields}
      />
    </Screen>
  );
}