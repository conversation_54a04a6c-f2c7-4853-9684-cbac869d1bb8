import React, { useState, useCallback } from "react";
import { motion } from "framer-motion";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus } from "lucide-react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { CardType } from "./types";
import { useCreateTodoMutation } from "@/redux/slices/todoApiSlice";

const USERS = [
  { id: "u1", name: "<PERSON>" },
  { id: "u2", name: "<PERSON>" },
  { id: "u3", name: "<PERSON>" },
  { id: "u4", name: "<PERSON> Assignee" },
];

const formVariants = {
  initial: { opacity: 0, height: 0 },
  animate: { opacity: 1, height: "auto", transition: { duration: 0.3, ease: "easeOut" } },
  exit: { opacity: 0, height: 0, transition: { duration: 0.2 } },
};

interface AddCardProps {
  column: string;
  setCards: React.Dispatch<React.SetStateAction<CardType[]>>;
}

export const AddCard: React.FC<AddCardProps> = ({ column, setCards }) => {
  const [text, setText] = useState("");
  const [assignee, setAssignee] = useState<string>(USERS[USERS.length - 1].id); // Default to "No Assignee"
  const [createTodo] = useCreateTodoMutation();

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!text.trim().length) return;
      await createTodo({
        title: text.trim(),
        description: "",
        status: column,
        // ...other fields
      });
      const newCard: CardType = {
        column,
        title: text.trim(),
        id: Math.random().toString(36).substr(2, 9),
        assignee: assignee !== "u4" ? USERS.find((u) => u.id === assignee)?.name : undefined,
      };

      setCards((pv) => [...pv, newCard]);
      setAdding(false);
      setText("");
      setAssignee(USERS[USERS.length - 1].id); // Reset to "No Assignee"
    },
    [text, assignee, column, setCards, createTodo]
  );

  const handleCancel = useCallback(() => {
    setAdding(false);
    setText("");
    setAssignee(USERS[USERS.length - 1].id);
  }, []);

  const [adding, setAdding] = useState(false);

  return (
    <>
      {adding ? (
        <motion.form
          layout
          variants={formVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          onSubmit={handleSubmit}
          className="mt-3 bg-white rounded-xl shadow-lg p-4 border border-gray-200"
          role="form"
          aria-label="Add new task form"
        >
          <Textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            autoFocus
            placeholder="Enter a title for this card..."
            className="w-full mb-3 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg resize-none"
            aria-label="Task description"
            rows={3}
          />
          <Select value={assignee} onValueChange={setAssignee}>
            <SelectTrigger className="w-full mb-4 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg" aria-label="Assign to user">
              <SelectValue placeholder="Select an assignee" />
            </SelectTrigger>
            <SelectContent>
              {USERS.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  {user.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <div className="flex items-center justify-end gap-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!text.trim().length}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white text-sm font-medium rounded-lg transition-colors"
            >
              <span>Add Card</span>
              <Plus size={16} />
            </button>
          </div>
        </motion.form>
      ) : (
        <motion.div layout className="mt-3">
          <button
            onClick={() => setAdding(true)}
            className="w-full p-3 border-2 border-dashed border-gray-300 rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 flex items-center justify-center gap-2 text-gray-500 hover:text-gray-600"
            aria-label="Add new task"
          >
            <Plus className="h-4 w-4" />
            <span className="text-sm font-medium">Add a card</span>
          </button>
        </motion.div>
      )}
    </>
  );
};