import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Edit3,
  Trash2,
  User,
  Tag,
  Calendar,
  Paperclip,
  Send,
  Link,
  Archive,
  Clock,
  Play,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from "lucide-react";
import { toast as sonnerToast } from "sonner";
import { ToDo } from "./types";
import { useUpdateTodoMutation } from "@/redux/slices/todoApiSlice";

// Define valid statuses for type safety
type ValidStatus = "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";

interface CardDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  card: ToDo;
  onDelete?: (cardId: string) => void;
  onRefetch?: () => void; // Add refetch function prop
}

export const CardDetailModal: React.FC<CardDetailModalProps> = ({ 
  isOpen, 
  onClose, 
  card, 
  onDelete, 
  onRefetch 
}) => {
  const [newComment, setNewComment] = useState("");
  const [updateTodo, { isLoading: isUpdatingStatus }] = useUpdateTodoMutation();

  const handleClose = () => {
    sonnerToast.info("Modal closed", {
      description: "Card details modal has been closed.",
      position: "top-center",
      duration: 4000,
    });
    onClose();
  };

  const handleDelete = () => {
    const cardId = (card as any).todo_id || card.id;
    if (!onDelete || !cardId) return;

    const toastId = sonnerToast.warning(`Delete task "${card.title || "this task"}"?`, {
      description: "This action cannot be undone.",
      position: "top-center",
      duration: 10000,
      action: {
        label: "Confirm",
        onClick: () => {
          onDelete(cardId.toString());
          sonnerToast.success(`Task "${card.title || "Task"}" deleted`, {
            description: "Task was deleted successfully.",
            position: "top-center",
            duration: 4000,
          });
          sonnerToast.dismiss(toastId);
          onClose();
        },
      },
      closeButton: true,
    });
  };

  // Handle status change
  const handleStatusChange = async (newStatus: ValidStatus) => {
    const cardId = (card as any).todo_id || card.id;
    if (!cardId || card.status === newStatus) return;

    try {
      // Build payload matching the API structure - similar to ModernBoard.tsx
      const payload = {
        todo_id: cardId,
        status: newStatus,
        title: card.title,
        description: card.description,
        priority: card.priority,
        due_date: card.due_date,
        assigned_to: card.assigned_to
      };

      console.log("Updating todo with payload:", payload);
      console.log("Card ID:", cardId);

      // Call the mutation with the payload (todo_id will be extracted by the API slice)
      await updateTodo(payload).unwrap();

      sonnerToast.success(`Status updated to ${newStatus.replace("_", " ").toLowerCase()}`, {
        description: `Task "${card.title}" has been moved to ${newStatus.replace("_", " ").toLowerCase()}.`,
        position: "top-center",
        duration: 4000,
      });

      // Refresh the data
      if (onRefetch) {
        await onRefetch();
      }

      onClose();
    } catch (error: any) {
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          error?.message || 
                          "Failed to update status. Please try again.";
      
      sonnerToast.error("Failed to update status", {
        description: errorMessage,
        position: "top-center",
        duration: 4000,
      });
    }
  };

  // Status options with icons and colors (updated to match API values)
  const statusOptions = [
    {
      value: "PENDING" as ValidStatus,
      label: "Pending",
      icon: Clock,
      color: "text-gray-600",
      bgColor: "hover:bg-gray-100",
      description: "Task is waiting to be started"
    },
    {
      value: "IN_PROGRESS" as ValidStatus,
      label: "In Progress", 
      icon: Play,
      color: "text-blue-600",
      bgColor: "hover:bg-blue-50",
      description: "Task is currently being worked on"
    },
    {
      value: "COMPLETED" as ValidStatus,
      label: "Completed",
      icon: CheckCircle,
      color: "text-green-600", 
      bgColor: "hover:bg-green-50",
      description: "Task has been finished"
    },
    {
      value: "CANCELLED" as ValidStatus,
      label: "Cancelled",
      icon: XCircle,
      color: "text-red-600",
      bgColor: "hover:bg-red-50", 
      description: "Task has been cancelled"
    }
  ];

  const currentStatus = card.status || "PENDING";

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Edit3 className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{card.title}</h2>
                  <p className="text-sm text-gray-500">
                    Status: <span className="font-medium">{card.status?.replace("_", " ") || "Unknown"}</span>
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex">
              <div className="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  <div>
                    <span className="text-xs text-gray-500">Priority</span>
                    <div className="font-semibold">{card.priority || "Not set"}</div>
                  </div>
                  {card.due_date && (
                    <div>
                      <span className="text-xs text-gray-500">Due Date</span>
                      <div className="font-semibold">{card.due_date}</div>
                    </div>
                  )}
                  {card.due_time && (
                    <div>
                      <span className="text-xs text-gray-500">Due Time</span>
                      <div className="font-semibold">{card.due_time}</div>
                    </div>
                  )}
                  <div>
                    <span className="text-xs text-gray-500">Assignee</span>
                    <div className="font-semibold">{card.assigned_to || "Unassigned"}</div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500">Created By</span>
                    <div className="font-semibold">{card.created_by || "Unknown"}</div>
                  </div>
                  {card.labels && card.labels.length > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Labels</span>
                      <div className="flex flex-wrap gap-1">
                        {card.labels.map((label) => (
                          <span
                            key={label.id}
                            className="px-2 py-1 text-xs font-semibold rounded"
                            style={{ backgroundColor: label.color, color: "#fff" }}
                          >
                            {label.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  {(card as any).client_type && (
                    <div>
                      <span className="text-xs text-gray-500">Client Type</span>
                      <div className="font-semibold">{(card as any).client_type}</div>
                    </div>
                  )}
                  {(card as any).set_reminder && (
                    <div>
                      <span className="text-xs text-gray-500">Reminder Set</span>
                      <div className="font-semibold text-green-600">Yes</div>
                    </div>
                  )}
                </div>
                <div className="mb-6">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">Description</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {card.description || "No description provided."}
                    </p>
                    {!card.description && (
                      <p className="text-xs text-gray-500 mt-1 italic">
                        (This field was empty in the submitted payload)
                      </p>
                    )}
                  </div>
                </div>
                
              </div>
              <div className="w-64 bg-gray-50 p-6 border-l border-gray-200">
                <h3 className="text-sm font-semibold text-gray-900 mb-4">Actions</h3>
                
                {/* Status Change Actions */}
                <div className="mb-6">
                  <h4 className="text-xs font-medium text-gray-700 mb-3 uppercase tracking-wide">Change Status</h4>
                  <div className="space-y-2">
                    {statusOptions.map((status) => {
                      const IconComponent = status.icon;
                      const isCurrentStatus = currentStatus === status.value;
                      const isLoading = isUpdatingStatus;
                      
                      return (
                        <button
                          key={status.value}
                          onClick={() => handleStatusChange(status.value)}
                          disabled={isCurrentStatus || isLoading}
                          className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors ${
                            isCurrentStatus 
                              ? "bg-green-100 text-green-700 border border-green-200" 
                              : `${status.color} ${status.bgColor} hover:shadow-sm`
                          } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                        >
                          <IconComponent className="h-4 w-4" />
                          <div className="flex-1 text-left">
                            <div className="font-medium">{status.label}</div>
                            {isCurrentStatus && (
                              <div className="text-xs text-green-600">Current</div>
                            )}
                          </div>
                          {isLoading && (
                            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Other Actions */}
                

                {/* Danger Actions */}
                <hr className="my-4" />
                <div className="space-y-2">
                  <h4 className="text-xs font-medium text-gray-700 mb-3 uppercase tracking-wide">Danger Zone</h4>
                  
                  <button
                    onClick={handleDelete}
                    className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <Trash2 className="h-4 w-4" /> Delete Task
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};