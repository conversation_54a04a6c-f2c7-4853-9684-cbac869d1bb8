import React, { useState } from "react";
import { Board } from "./Board";
import { ModernBoard } from "./ModernBoard";
import { CardDetailModal } from "./CardDetailModal";
import { AddCardModal } from "./AddCardModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

const Todo: React.FC = () => {
  const [useModernBoard, setUseModernBoard] = useState(true);
  const [selectedCard, setSelectedCard] = useState<any>(null);
  const [isAddCardModalOpen, setIsAddCardModalOpen] = useState(false);
  const [addCardColumn, setAddCardColumn] = useState<{ id: string; title: string } | null>(null);

 
  const sampleCard = {
    id: "1",
    title: "Design new landing page",
    description: "Create a modern, responsive landing page for the new product launch with focus on conversion optimization and user experience.",
    column: "In Progress",
    assignee: "<PERSON>",
    labels: [
      { id: "1", name: "Design", color: "#10B981" },
      { id: "2", name: "High Priority", color: "#EF4444" }
    ],
    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    priority: "high" as const,
    attachments: 3,
    comments: 2,
    checklist: { completed: 2, total: 5 }
  };

  const handleAddCard = (card: any) => {
    console.log("Adding card:", card);
    // Here you would typically add the card to your state/database
  };

  return (
    <div className="relative h-screen w-full bg-background text-foreground">
      {/* Toggle Button */}
      <div className="fixed top-4 right-4 z-30">
       
       
      </div>

      {/* Demo Buttons */}
      <div className="fixed bottom-4 right-4 z-30 flex flex-col gap-2">
        
        
      </div>

      {/* Board */}
      {useModernBoard ? <ModernBoard /> : <Board />}

      {/* Modals */}
      <CardDetailModal
        isOpen={!!selectedCard}
        onClose={() => setSelectedCard(null)}
        card={selectedCard || sampleCard}
      />

      <AddCardModal
        isOpen={isAddCardModalOpen}
        onClose={() => {
          setIsAddCardModalOpen(false);
          setAddCardColumn(null);
        }}
        columnId={addCardColumn?.id || ""}
        columnTitle={addCardColumn?.title || ""}
        onAddCard={handleAddCard}
      />
    </div>
  );
};

export default Todo;

    