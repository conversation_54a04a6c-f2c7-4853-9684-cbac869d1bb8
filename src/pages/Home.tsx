import { Screen } from "@/app-components/layout/screen";
import {
  Users,
  UserPlus,
  MapPin,
  FileText,
  Calendar,
  Clock,
  AlertCircle,
  Activity,
  Target,
  BarChart3,
  Bell,
} from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import { Link } from "react-router-dom";
import { useState, useMemo, useEffect } from "react";
import AddProspects from "./Prospects/addlead";
import { useGetDashboardQuery } from "@/redux/slices/dashboard";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetTodoQuery } from "@/redux/slices/todoApiSlice";
import BirthdayCelebration from "@/components/custom/modals/BirthdayCelebration";

// Helper function to get time-based greeting
const getTimeBasedGreeting = () => {
  const hour = new Date().getHours();
  if (hour >= 5 && hour < 12) return "Good Morning";
  if (hour >= 12 && hour < 17) return "Good Afternoon";
  if (hour >= 17 && hour < 22) return "Good Evening";
  return "Good Night";
};

// Helper function to get user role display
const getUserRoleDisplay = (user_details: any) => {
  const department = user_details?.department || "";
  const team = user_details?.team || "";
  const office = user_details?.office || "";

  return {
    department,
    team,
    office,
    displayRole: department || team || "Employee"
  };
};

const Home = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [showBirthdayCelebration, setShowBirthdayCelebration] = useState(false);

  // User authentication and information
  const { user_details } = useAuthHook();
  const employee_no = user_details?.employee_no || "";
  const employee_name = user_details?.fullnames || "";
  const userRole = getUserRoleDisplay(user_details);

  // DEVELOPMENT/TESTING MODE - Set to false for production
  const BIRTHDAY_TEST_MODE = false; // ⚠️ CHANGE TO false FOR PRODUCTION ⚠️

  // Check if today is the user's birthday and show celebration
  useEffect(() => {
    // PRODUCTION MODE: Only show on actual birthdays
    if (!BIRTHDAY_TEST_MODE) {
      if (user_details && 'date_of_birth' in user_details && user_details.date_of_birth && typeof user_details.date_of_birth === 'string') {
        const birthDate = user_details.date_of_birth as string;
        if (isUserBirthday(birthDate)) {
          const timer = setTimeout(() => {
            setShowBirthdayCelebration(true);
          }, 2000); // Show after 2 seconds to allow page to load

          return () => clearTimeout(timer);
        }
      }
    } else {
      // TEST MODE: Show for testing purposes (ONLY when BIRTHDAY_TEST_MODE is true)
      console.warn("🎂 BIRTHDAY TEST MODE ENABLED - This should be disabled in production!");
      const timer = setTimeout(() => {
        setShowBirthdayCelebration(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [user_details]);

  // Helper function to check if today is user's birthday
  const isUserBirthday = (birthDate: string) => {
    if (!birthDate) return false;
    
    const today = new Date();
    const birth = new Date(birthDate);
    
    return today.getMonth() === birth.getMonth() && 
           today.getDate() === birth.getDate();
  };

  // API calls for dashboard data
  const { data: dashboardData, isLoading, isFetching } = useGetDashboardQuery({
    EMPLOYEE_NO: employee_no,
  });

  // Get user's services/todos filtered by employee_no
  const { data: servicesData } = useGetTodoQuery({
    assigned_to: employee_no,
    page: 1,
    page_size: 10,
  });

  // Quick actions based on user permissions and role
  const quickActions = useMemo(() => {
    const actions = [
      {
        title: "View Customers",
        description: "Browse all customers",
        icon: <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
        link: "/customers/overview#all-customers",
        gradient: "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
        border: "border-blue-200 dark:border-blue-800",
        hover: "hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30"
      },
      {
        title: "View Prospects",
        description: "Manage prospects and leads",
        icon: <UserPlus className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />,
        link: "/prospects",
        gradient: "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
        border: "border-emerald-200 dark:border-emerald-800",
        hover: "hover:from-emerald-100 hover:to-teal-100 dark:hover:from-emerald-900/30 dark:hover:to-teal-900/30"
      },
      {
        title: "Book Site Visit",
        description: "Schedule customer visits",
        icon: <MapPin className="w-5 h-5 text-purple-600 dark:text-purple-400" />,
        link: "/book-visit",
        gradient: "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
        border: "border-purple-200 dark:border-purple-800",
        hover: "hover:from-purple-100 hover:to-pink-100 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30"
      },
      {
        title: "View Reports",
        description: "Access sales reports",
        icon: <BarChart3 className="w-5 h-5 text-amber-600 dark:text-amber-400" />,
        link: "/marketerreport",
        gradient: "from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20",
        border: "border-amber-200 dark:border-amber-800",
        hover: "hover:from-amber-100 hover:to-orange-100 dark:hover:from-amber-900/30 dark:hover:to-orange-900/30"
      },
      {
        title: "My Bookings",
        description: "View your bookings",
        icon: <Calendar className="w-5 h-5 text-rose-600 dark:text-rose-400" />,
        link: "/mybookings",
        gradient: "from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20",
        border: "border-rose-200 dark:border-rose-800",
        hover: "hover:from-rose-100 hover:to-pink-100 dark:hover:from-rose-900/30 dark:hover:to-pink-900/30"
      },
      {
        title: "Projects",
        description: "Browse available projects",
        icon: <Target className="w-5 h-5 text-cyan-600 dark:text-cyan-400" />,
        link: "/projects",
        gradient: "from-cyan-50 to-sky-50 dark:from-cyan-900/20 dark:to-sky-900/20",
        border: "border-cyan-200 dark:border-cyan-800",
        hover: "hover:from-cyan-100 hover:to-sky-100 dark:hover:from-cyan-900/30 dark:hover:to-sky-900/30"
      },
    ];

    return actions;
  }, []);

  // Dashboard stats from API
  const dashboardStats = useMemo(() => {
    const stats = [
      {
        title: "Reminders",
        count: dashboardData?.reminders?.data?.length || 0,
        icon: <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />,
        link: "/reminders",
        gradient: "from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20",
        border: "border-blue-200 dark:border-blue-800",
        iconBg: "bg-blue-100 dark:bg-blue-900/30"
      },
      {
        title: "Notifications",
        count: dashboardData?.notifications?.data?.length || 0,
        icon: <Bell className="w-6 h-6 text-amber-600 dark:text-amber-400" />,
        link: "/notifications",
        gradient: "from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20",
        border: "border-amber-200 dark:border-amber-800",
        iconBg: "bg-amber-100 dark:bg-amber-900/30"
      },
      {
        title: "Notes",
        count: dashboardData?.notes?.data?.length || 0,
        icon: <FileText className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />,
        link: "/notes",
        gradient: "from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20",
        border: "border-emerald-200 dark:border-emerald-800",
        iconBg: "bg-emerald-100 dark:bg-emerald-900/30"
      },
      {
        title: "Tickets",
        count: dashboardData?.tickets?.data?.length || 0,
        icon: <AlertCircle className="w-6 h-6 text-rose-600 dark:text-rose-400" />,
        link: "/complaints",
        gradient: "from-rose-50 to-red-50 dark:from-rose-900/20 dark:to-red-900/20",
        border: "border-rose-200 dark:border-rose-800",
        iconBg: "bg-rose-100 dark:bg-rose-900/30"
      },
    ];

    return stats;
  }, [dashboardData]);

  // Services/Tasks data for the user
  const userServices = useMemo(() => {
    if (!servicesData?.results) return [];

    return servicesData.results.slice(0, 5).map((service: any) => ({
      id: service.id,
      title: service.title || service.task || "Task",
      description: service.description || service.details || "",
      status: service.status || "PENDING",
      created_at: service.created_at,
      due_date: service.due_date,
    }));
  }, [servicesData]);

  // Show loader when initially loading or when tab is changing
  const showLoader = isLoading || isFetching;

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 relative transition-colors duration-200">
        {showLoader && (
          <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70 dark:bg-gray-900 dark:bg-opacity-70">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        )}

        <div className="p-6 space-y-6">
          {/* Welcome Section */}
          <div className="bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-800 dark:to-slate-800 border border-slate-200 dark:border-gray-700 rounded-xl p-4 sm:p-6 shadow-lg">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-xl sm:text-2xl font-semibold text-slate-800 dark:text-slate-100 mb-2">
                  {getTimeBasedGreeting()}, {employee_name?.split(" ")[0] || "User"} 👋
                </h1>
                <p className="text-slate-600 dark:text-slate-300 mb-3">
                  Welcome back to Optiven CRM Dashboard
                </p>
                <div className="flex flex-wrap items-center gap-3 sm:gap-6 text-xs sm:text-sm text-slate-500 dark:text-slate-400">
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-emerald-400 rounded-full flex-shrink-0"></span>
                    <span className="truncate">{userRole.office}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"></span>
                    <span className="truncate">{userRole.department}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-purple-400 rounded-full flex-shrink-0"></span>
                    <span className="truncate">{userRole.team}</span>
                  </div>
                </div>
              </div>
              <div className="flex-shrink-0 bg-white/50 dark:bg-gray-700/50 rounded-lg p-3 sm:p-4 backdrop-blur-sm text-center lg:text-right">
                <p className="text-xs sm:text-sm text-slate-500 dark:text-slate-400">Employee ID</p>
                <p className="font-semibold text-slate-800 dark:text-slate-100 text-sm sm:text-base break-all">{employee_no}</p>
                <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions Grid */}
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-6">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.link}
                  className={`bg-gradient-to-br ${action.gradient} border ${action.border} p-5 rounded-xl shadow-sm ${action.hover} hover:shadow-md transition-all duration-300`}
                >
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg backdrop-blur-sm">
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-slate-800 dark:text-slate-100">{action.title}</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-300">{action.description}</p>
                    </div>
                  </div>
                </Link>
              ))}

              {/* Add Prospect Button */}
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="bg-gradient-to-br from-violet-50 to-fuchsia-50 dark:from-violet-900/20 dark:to-fuchsia-900/20 border border-violet-200 dark:border-violet-800 p-5 rounded-xl shadow-sm hover:from-violet-100 hover:to-fuchsia-100 dark:hover:from-violet-900/30 dark:hover:to-fuchsia-900/30 hover:shadow-md transition-all duration-300"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg backdrop-blur-sm">
                    <UserPlus className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-800 dark:text-slate-100">Add Prospect</h3>
                    <p className="text-sm text-slate-600 dark:text-slate-300">Create new lead</p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Dashboard Stats */}
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-6">
              Dashboard Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {dashboardStats.map((stat, index) => (
                <Link
                  key={index}
                  to={stat.link}
                  className={`bg-gradient-to-br ${stat.gradient} border ${stat.border} p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 group`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-slate-600 dark:text-slate-400 uppercase tracking-wide mb-2">
                        {stat.title}
                      </h3>
                      <p className="text-3xl font-bold text-slate-800 dark:text-slate-100">
                        {stat.count}
                      </p>
                    </div>
                    <div className={`p-3 ${stat.iconBg} rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                      {stat.icon}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Services/Tasks Section */}
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-6">
              My Services & Tasks
            </h2>
            <div className="bg-gradient-to-br from-slate-50 to-gray-50 dark:from-gray-800 dark:to-slate-800 border border-slate-200 dark:border-gray-700 rounded-xl p-6 shadow-lg">
              {userServices.length > 0 ? (
                <div className="space-y-4">
                  {userServices.map((service: any) => (
                    <div
                      key={service.id}
                      className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-700/60 border border-slate-200/50 dark:border-gray-600/50 rounded-xl hover:bg-white/80 dark:hover:bg-gray-700/80 hover:shadow-md transition-all duration-300 backdrop-blur-sm"
                    >
                      <div className="flex-1">
                        <h4 className="font-semibold text-slate-800 dark:text-slate-100">
                          {service.title}
                        </h4>
                        {service.description && (
                          <p className="text-sm text-slate-600 dark:text-slate-300 mt-1">
                            {service.description}
                          </p>
                        )}
                        {service.due_date && (
                          <p className="text-xs text-slate-500 dark:text-slate-400 mt-2 flex items-center">
                            <Clock className="w-3 h-3 mr-1" />
                            Due: {new Date(service.due_date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                      <div className="ml-4">
                        <span
                          className={`px-4 py-2 rounded-full text-xs font-semibold shadow-sm ${
                            service.status === "COMPLETED"
                              ? "bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200 dark:from-emerald-900/30 dark:to-green-900/30 dark:text-emerald-400 dark:border-emerald-800"
                              : service.status === "IN_PROGRESS"
                              ? "bg-gradient-to-r from-blue-100 to-sky-100 text-blue-700 border border-blue-200 dark:from-blue-900/30 dark:to-sky-900/30 dark:text-blue-400 dark:border-blue-800"
                              : "bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-700 border border-amber-200 dark:from-amber-900/30 dark:to-yellow-900/30 dark:text-amber-400 dark:border-amber-800"
                          }`}
                        >
                          {service.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-6 border-t border-slate-200/50 dark:border-gray-600/50">
                    <Link
                      to="/admin/services"
                      className="inline-flex items-center text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 text-sm font-semibold transition-colors group"
                    >
                      View All Services
                      <span className="ml-1 group-hover:translate-x-1 transition-transform duration-200">→</span>
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="text-center py-16">
                  <div className="p-4 bg-slate-100 dark:bg-gray-700 rounded-full w-fit mx-auto mb-4">
                    <Activity className="w-8 h-8 text-slate-400" />
                  </div>
                  <p className="text-slate-500 dark:text-slate-400 text-sm">
                    No services assigned to you at the moment
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-6">
              Recent Activity
            </h2>
            <div className="bg-gradient-to-br from-slate-50 to-gray-50 dark:from-gray-800 dark:to-slate-800 border border-slate-200 dark:border-gray-700 rounded-xl p-6 shadow-lg">
              <div className="space-y-4">
                {/* Recent Reminders */}
                {dashboardData?.reminders?.data?.slice(0, 3).map((reminder: any, index: number) => (
                  <div
                    key={`reminder-${index}`}
                    className="flex items-start space-x-4 p-4 bg-gradient-to-r from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20 border border-blue-200/50 dark:border-blue-800/50 rounded-xl hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-sky-100 dark:from-blue-900/40 dark:to-sky-900/40 rounded-xl flex items-center justify-center shadow-sm">
                        <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-slate-800 dark:text-slate-100">
                        {reminder.text || "Reminder"}
                      </p>
                      <p className="text-xs text-slate-500 dark:text-slate-400 mt-1 flex items-center">
                        <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
                        Due: {reminder.date || "Soon"}
                      </p>
                    </div>
                  </div>
                ))}

                {/* Recent Tickets */}
                {dashboardData?.tickets?.data?.slice(0, 2).map((ticket: any, index: number) => (
                  <div
                    key={`ticket-${index}`}
                    className="flex items-start space-x-4 p-4 bg-gradient-to-r from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 border border-rose-200/50 dark:border-rose-800/50 rounded-xl hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-rose-100 to-pink-100 dark:from-rose-900/40 dark:to-pink-900/40 rounded-xl flex items-center justify-center shadow-sm">
                        <AlertCircle className="w-5 h-5 text-rose-600 dark:text-rose-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-slate-800 dark:text-slate-100">
                        Ticket: {ticket.name || "Support Request"}
                      </p>
                      <p className="text-xs text-slate-500 dark:text-slate-400 mt-1 flex items-center">
                        <span className="w-1.5 h-1.5 bg-rose-400 rounded-full mr-2"></span>
                        Status: {ticket.status || "Open"} • {ticket.date || "Recent"}
                      </p>
                    </div>
                  </div>
                ))}

                {/* No Activity Message */}
                {(!dashboardData?.reminders?.data?.length && !dashboardData?.tickets?.data?.length) && (
                  <div className="text-center py-16">
                    <div className="p-4 bg-gradient-to-br from-slate-100 to-gray-100 dark:from-gray-700 dark:to-slate-700 rounded-full w-fit mx-auto mb-4 shadow-sm">
                      <Activity className="w-8 h-8 text-slate-400" />
                    </div>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      No recent activity to display
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

        </div>

        {/* Add Prospect Modal */}
        {isAddModalOpen && (
          <AddProspects
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}

        {/* Birthday Celebration Modal */}
        <BirthdayCelebration
          isOpen={showBirthdayCelebration}
          onClose={() => setShowBirthdayCelebration(false)}
          userName={employee_name || "Valued Team Member"}
        />
      </div>
    </Screen>
  );
};
export default Home;