import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import { Card6 } from "@/components/custom/cards/Card6";
import {
  MessageCircle,
  PlusCircle,
  Search,
  Star,
  TrendingUp,
  Users,
  Heart,
  Sparkles,
  Filter,
  Calendar,
  Award,
  ThumbsUp,
  MessageSquare,
  BarChart3,
  AlertCircle,
  RefreshCw,
  Wifi,
  WifiOff,
} from "lucide-react";
import CreateFeedBackModal from "./CreateFeedBack";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  useCreateFeedbackMutation,
  useLazyGetFeedbackQuery,
} from "@/redux/slices/feedbackApiSlice";

interface FeedbackItem {
  id: string;
  title: string;
  description?: string;
  score: number;
  feedback_type: string;
  entity_type: string;
  entity_name: string;
  created_at: string;
}

interface FeedbackData {
  subject: string;
  feedback_type: string;
  rating: number;
  message?: string;
}

function ViewFeedBack() {
  const [trigger, { data: apiResponse, isLoading, isError, error }] =
    useLazyGetFeedbackQuery();
  const [addFeedback] = useCreateFeedbackMutation();
  const { toast } = useToast();
  const [feedbacks, setFeedbacks] = useState<FeedbackItem[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");

  // Fetch feedback
  useEffect(() => {
    trigger({ page: currentPage });
  }, [trigger, currentPage]);

  // Transform API data
  useEffect(() => {
    console.log("Raw API Response:", apiResponse);
    if (apiResponse?.data?.results) {
      const transformedFeedbacks: FeedbackItem[] = apiResponse.data.results.map(
        (feedback: any) => ({
          id: feedback.feedback_id,
          title: feedback.subject,
          description: feedback.message || "",
          score: feedback.rating || 0,
          feedback_type: feedback.feedback_type || "",
          entity_type: feedback.entity_type || "",
          entity_name: feedback.entity_name || "",
          created_at: feedback.created_at,
        })
      );
      setFeedbacks((prev) =>
        currentPage === 1
          ? transformedFeedbacks
          : [...prev, ...transformedFeedbacks]
      );
    }
  }, [apiResponse, currentPage]);

  // NPS calculation
  const totalFeedbacks = feedbacks.length;
  const promoters = feedbacks.filter((f) => f.score >= 4).length;
  const passives = feedbacks.filter((f) => f.score === 3).length;
  const detractors = feedbacks.filter((f) => f.score <= 2).length;

  const promoterPercentage =
    totalFeedbacks > 0 ? (promoters / totalFeedbacks) * 100 : 0;
  const passivePercentage =
    totalFeedbacks > 0 ? (passives / totalFeedbacks) * 100 : 0;
  const detractorPercentage =
    totalFeedbacks > 0 ? (detractors / totalFeedbacks) * 100 : 0;

  const npsScore = Math.round(promoterPercentage - detractorPercentage);

  const chartData = [
    {
      name: "Promoters (4–5)",
      value: promoters,
      percentage: promoterPercentage,
      color: "#10b981",
    },
    {
      name: "Passives (3)",
      value: passives,
      percentage: passivePercentage,
      color: "#f59e0b",
    },
    {
      name: "Detractors (1–2)",
      value: detractors,
      percentage: detractorPercentage,
      color: "#ef4444",
    },
  ];

  // Filter feedbacks based on search and type
  const filteredFeedbacks = feedbacks.filter((feedback) => {
    const matchesSearch =
      feedback.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      feedback.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      feedback.feedback_type
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      feedback.entity_name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType =
      filterType === "all" ||
      feedback.feedback_type.toLowerCase() === filterType;

    return matchesSearch && matchesType;
  });

  // Helper function to get user-friendly error message
  const getUserFriendlyErrorMessage = (error: any) => {
    if (!error) return "Something went wrong. Please try again.";

    // Handle different types of errors
    if (error.status === 404) {
      return "The requested data could not be found.";
    } else if (error.status === 500) {
      return "Our servers are experiencing issues. Please try again in a few moments.";
    } else if (error.status === 403) {
      return "You don't have permission to access this data.";
    } else if (error.status === 401) {
      return "Please log in to view your feedback.";
    } else if (
      error.originalStatus === "FETCH_ERROR" ||
      error.name === "NetworkError"
    ) {
      return "Unable to connect to our servers. Please check your internet connection.";
    } else if (error.originalStatus === "TIMEOUT_ERROR") {
      return "The request is taking too long. Please try again.";
    } else if (error.data?.message) {
      // If there's a user-friendly message from the API, use it
      return error.data.message;
    } else {
      return "We're having trouble loading your feedback. Please try again.";
    }
  };

  const handleAddFeedback = async (data: FeedbackData) => {
    try {
      await addFeedback({
        subject: data.subject,
        feedback_type: data.feedback_type,
        rating: data.rating,
        message: data.message || undefined,
      }).unwrap();
      setShowModal(false);
      setCurrentPage(1);
      trigger({ page: 1 });
      toast({
        title: "Feedback Added",
        description: `${data.subject} has been submitted successfully.`,
        duration: 3000,
      });
    } catch (err: any) {
      console.error("Failed to add feedback:", err);
      const userFriendlyMessage = getUserFriendlyErrorMessage(err);
      toast({
        title: "Unable to Submit Feedback",
        description: userFriendlyMessage,
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  // Retry function for failed requests
  const handleRetry = () => {
    setCurrentPage(1);
    trigger({ page: 1 });
  };

  // Format date
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Badge styling for feedback_type
  const getFeedbackTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case "compliment":
        return (
          <Badge className="bg-green-100 text-green-800">Compliment</Badge>
        );
      case "complaint":
        return <Badge className="bg-red-100 text-red-800">Complaint</Badge>;
      case "suggestion":
        return <Badge className="bg-blue-100 text-blue-800">Suggestion</Badge>;
      case "question":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Question</Badge>
        );
      case "bug":
        return <Badge className="bg-purple-100 text-purple-800">Bug</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Other</Badge>;
    }
  };

  // Show user-friendly error toast
  useEffect(() => {
    if (error) {
      const userFriendlyMessage = getUserFriendlyErrorMessage(error);
      toast({
        title: "Unable to Load Feedback",
        description: userFriendlyMessage,
        variant: "destructive",
        duration: 5000,
      });
    }
  }, [error, toast]);

  return (
    <Screen>
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto p-6">
          {/* Hero Header Section */}
          <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-green-500 via-emerald-500 to-teal-600 p-8 mb-8 shadow-2xl">
            {/* Decorative Elements */}
            <div className="absolute top-4 right-8 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute bottom-6 left-12 w-16 h-16 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 right-1/4 w-8 h-8 bg-white/20 rounded-full animate-bounce delay-500"></div>

            <div className="relative z-10">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6"
              >
                <div className="flex items-center gap-4">
                  <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm shadow-lg">
                    <MessageCircle className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">
                      Customer Feedback Hub
                    </h1>
                    <p className="text-green-100 text-lg">
                      Collect, analyze and act on valuable customer insights
                    </p>
                    <div className="flex items-center gap-4 mt-3 text-green-100 text-sm">
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-4 w-4" />
                        <span>{totalFeedbacks} Total Feedbacks</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4" />
                        <span>NPS Score: {npsScore}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-4 w-4" />
                        <span>Real-time Analytics</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setShowModal(true)}
                    className="group flex items-center gap-2 px-6 py-3 bg-white text-green-600 rounded-xl font-semibold hover:bg-green-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    <PlusCircle className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
                    <span>Add Feedback</span>
                    <Sparkles className="h-4 w-4 opacity-70" />
                  </button>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Search and Filter Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 mb-8 shadow-lg border border-green-200"
          >
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                <div className="relative w-full md:w-80">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                  <Input
                    placeholder="Search feedback by subject, type, or entity..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-12 pr-4 py-3 rounded-xl border-2 border-green-200 w-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm transition-all duration-200 text-gray-700 placeholder-gray-400"
                  />
                </div>

                <div className="relative w-full md:w-auto">
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="appearance-none pl-12 pr-8 py-3 rounded-xl border-2 border-green-200 w-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm transition-all duration-200 text-gray-700 cursor-pointer min-w-[180px]"
                  >
                    <option value="all">All Types</option>
                    <option value="compliment">Compliments</option>
                    <option value="complaint">Complaints</option>
                    <option value="suggestion">Suggestions</option>
                    <option value="question">Questions</option>
                    <option value="bug">Bug Reports</option>
                  </select>
                  <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2 px-4 py-2 bg-green-50 rounded-xl border border-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>
                    Showing {filteredFeedbacks.length} of {feedbacks.length}{" "}
                    feedbacks
                  </span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Analytics Dashboard */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Total Feedbacks Card */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium">
                      Total Feedbacks
                    </p>
                    <p className="text-3xl font-bold">{totalFeedbacks}</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-xl">
                    <MessageSquare className="h-6 w-6" />
                  </div>
                </div>
              </motion.div>

              {/* NPS Score Card */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-6 text-white shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium">
                      NPS Score
                    </p>
                    <p className="text-3xl font-bold">{npsScore}</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-xl">
                    <TrendingUp className="h-6 w-6" />
                  </div>
                </div>
              </motion.div>

              {/* Promoters Card */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-emerald-100 text-sm font-medium">
                      Promoters
                    </p>
                    <p className="text-3xl font-bold">{promoters}</p>
                    <p className="text-emerald-200 text-xs">
                      {promoterPercentage.toFixed(1)}%
                    </p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-xl">
                    <ThumbsUp className="h-6 w-6" />
                  </div>
                </div>
              </motion.div>

              {/* Average Rating Card */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl p-6 text-white shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-sm font-medium">
                      Avg Rating
                    </p>
                    <p className="text-3xl font-bold">
                      {totalFeedbacks > 0
                        ? (
                            feedbacks.reduce((sum, f) => sum + f.score, 0) /
                            totalFeedbacks
                          ).toFixed(1)
                        : "0.0"}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-3 w-3 ${
                            star <=
                            (totalFeedbacks > 0
                              ? Math.round(
                                  feedbacks.reduce(
                                    (sum, f) => sum + f.score,
                                    0
                                  ) / totalFeedbacks
                                )
                              : 0)
                              ? "fill-yellow-200 text-yellow-200"
                              : "text-yellow-300"
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                  <div className="p-3 bg-white/20 rounded-xl">
                    <Star className="h-6 w-6" />
                  </div>
                </div>
              </motion.div>
            </div>

            {/* NPS Breakdown Chart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-white rounded-2xl p-6 shadow-lg border border-green-200"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-green-100 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-green-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-800">
                  NPS Breakdown
                </h3>
              </div>

              <div className="space-y-4">
                {chartData.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    className="flex items-center gap-4"
                  >
                    <div className="w-24 text-sm font-medium text-gray-600">
                      {item.name.split(" ")[0]}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div className="flex-1 bg-gray-200 rounded-full h-3 overflow-hidden">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${item.percentage}%` }}
                            transition={{
                              duration: 1,
                              delay: 0.6 + index * 0.1,
                            }}
                            className="h-full rounded-full"
                            style={{ backgroundColor: item.color }}
                          />
                        </div>
                        <div className="text-sm font-semibold text-gray-700 w-16">
                          {item.value} ({item.percentage.toFixed(1)}%)
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Feedback Cards Section */}
          <div className="space-y-6">
            {isLoading ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex justify-center items-center py-20"
              >
                <div className="flex items-center gap-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                  <span className="text-gray-600">Loading feedback...</span>
                </div>
              </motion.div>
            ) : isError ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-16"
              >
                <div className="bg-red-50 rounded-2xl p-8 border border-red-200 max-w-md mx-auto">
                  <div className="flex items-center justify-center mb-4">
                    {"originalStatus" in (error ?? {}) &&
                    (error as any).originalStatus === "FETCH_ERROR" ? (
                      <WifiOff className="h-12 w-12 text-red-500" />
                    ) : (
                      <AlertCircle className="h-12 w-12 text-red-500" />
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-red-800 mb-3">
                    Unable to Load Feedback
                  </h3>
                  <p className="text-red-600 text-sm mb-6">
                    {getUserFriendlyErrorMessage(error)}
                  </p>
                  <button
                    onClick={handleRetry}
                    className="inline-flex items-center gap-2 px-6 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors duration-200"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                  </button>
                </div>
              </motion.div>
            ) : filteredFeedbacks.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl shadow-lg p-12 text-center border border-green-200"
              >
                <div className="mx-auto w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
                  <MessageCircle size={36} className="text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">
                  No feedback found
                </h3>
                <p className="text-gray-600 text-lg mb-6">
                  {searchQuery
                    ? "Try adjusting your search terms or filters"
                    : "Be the first to share valuable feedback!"}
                </p>
                <button
                  onClick={() => setShowModal(true)}
                  className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <PlusCircle className="inline-block mr-2 h-5 w-5" />
                  Add First Feedback
                  <Sparkles className="inline-block ml-2 h-4 w-4 opacity-70" />
                </button>
              </motion.div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredFeedbacks.map((feedback, index) => (
                  <motion.div
                    key={feedback.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -5 }}
                    className="group"
                  >
                    <div className="bg-white rounded-2xl shadow-lg border-2 border-green-100 hover:border-green-300 hover:shadow-xl transition-all duration-300 overflow-hidden h-full">
                      {/* Card Header */}
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 border-b border-green-100">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-lg font-bold text-gray-800 truncate max-w-xs">
                            {feedback.title}
                          </h3>
                          {getFeedbackTypeBadge(feedback.feedback_type)}
                        </div>

                        {/* Rating Stars */}
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < feedback.score
                                    ? "fill-yellow-400 text-yellow-400"
                                    : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-semibold text-gray-600">
                            {feedback.score}/5
                          </span>
                        </div>
                      </div>

                      {/* Card Body */}
                      <div className="p-6">
                        <p className="text-gray-600 text-sm mb-4 line-clamp-4 leading-relaxed">
                          {feedback.description ||
                            "No additional message provided."}
                        </p>

                        {/* Entity Info */}
                        <div className="flex items-center gap-2 mb-3 text-xs text-gray-500">
                          <Users className="h-3 w-3" />
                          <span>
                            {feedback.entity_name} ({feedback.entity_type})
                          </span>
                        </div>

                        {/* Footer */}
                        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(feedback.created_at)}</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <MessageCircle className="h-3 w-3" />
                            <span>#{feedback.id}</span>
                          </div>
                        </div>
                      </div>

                      {/* Hover Effect Indicator */}
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>

          {/* Load More Button */}
          {apiResponse?.data?.current_page < apiResponse?.data?.last_page && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-center mt-12"
            >
              <button
                onClick={() => setCurrentPage((prev) => prev + 1)}
                className="group px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <span className="flex items-center gap-2">
                  Load More Feedback
                  <TrendingUp className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
                </span>
              </button>
            </motion.div>
          )}

          {/* Footer Stats */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-12 text-center"
          >
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-green-200">
              <div className="flex items-center justify- gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Heart className="h-4 w-4 text-red-500" />
                  <span>Powered by Customer Insights</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-yellow-500" />
                  <span>Quality Feedback System</span>
                </div>
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-green-500" />
                  <span>Real-time Analytics</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Enhanced Modal */}
      <CreateFeedBackModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSubmit={handleAddFeedback}
        className="animate-fade-in"
      />
    </Screen>
  );
}

export default ViewFeedBack;
