import { store } from '@/redux/store';
import { setCredentials } from '@/redux/authSlice';
import { BASE_URL } from '@/config';
import { contentHeader } from '@/utils/header';

/**
 * Service for managing user permissions
 * Provides functions to refresh permissions from the server
 */
export const PermissionsService = {
  /**
   * Refresh user permissions from the server
   * This ensures that permission changes are reflected immediately in the application
   * 
   * @returns Promise<boolean> - True if permissions were successfully refreshed
   */
  refreshPermissions: async (): Promise<boolean> => {
    const state = store.getState();
    const token = state.auth.token;
    const userDetails = state.auth.user_details;

    if (!token || !userDetails?.employee_no) {
      console.error('Cannot refresh permissions: User not logged in or missing employee number');
      return false;
    }
    
    console.log('Refreshing permissions for user:', userDetails.email);
    console.log('Using employee number:', userDetails.employee_no);
    console.log('Using token:', token.AccessToken ? `${token.AccessToken.substring(0, 10)}...` : 'No token');

    try {
      // Fetch current user permissions using the correct endpoint
      // Create custom headers with the token from Redux store
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Token ${token.AccessToken}`
      };
      
      const response = await fetch(`${BASE_URL}/users/user_2_userpermissions?user=${userDetails.employee_no}`, {
        method: 'GET',
        headers: headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Permission fetch error response:', errorText);
        throw new Error(`Failed to fetch user permissions: ${response.status}`);
      }

      const data = await response.json();
      console.log('Permissions API response:', JSON.stringify(data, null, 2));
      
      // Check if we have a valid response with data
      if (!data) {
        throw new Error('Invalid response format from server');
      }
      
      // Extract permissions from the response
      // The API might return data in different formats, handle both array and object cases
      let permissionsList = [];
      
      if (data.data) {
        // Check if data.data is an array that we can map over
        if (Array.isArray(data.data)) {
          // Store the full permission objects, not just the permission codes
          permissionsList = data.data;
        } else if (typeof data.data === 'object') {
          // If it's an object but not an array, extract permissions differently
          console.log('Permissions data is not an array:', data.data);
          // Try to extract permissions from the object structure
          permissionsList = data.permissions || data.data.permissions || [];
        }
      } else if (data.permissions) {
        // Alternative location for permissions in the response
        permissionsList = Array.isArray(data.permissions) ? data.permissions : [data.permissions];
      }
      
      console.log('Extracted permissions list:', permissionsList);
      
      // Keep the existing user details but update the permissions
      const updatedUserDetails = {
        ...userDetails,
        user_permissions: permissionsList
      };

      // Update the Redux store with the new permissions
      store.dispatch(
        setCredentials({
          token, // Keep the existing token
          user_details: updatedUserDetails,
        })
      );

      console.log('User permissions refreshed successfully');
      return true;
    } catch (error) {
      console.error('Error refreshing permissions:', error);
      return false;
    }
  },

  /**
   * Set up automatic permission refreshing at regular intervals
   * 
   * @param intervalMinutes - How often to refresh permissions (in minutes)
   * @returns Function to clear the interval
   */
  setupAutoRefresh: (intervalMinutes: number = 5): (() => void) => {
    const intervalId = setInterval(() => {
      PermissionsService.refreshPermissions();
    }, intervalMinutes * 60 * 1000);

    return () => clearInterval(intervalId);
  }
};

export default PermissionsService;