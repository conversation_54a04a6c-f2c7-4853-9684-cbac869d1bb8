# Prospect Permissions System

This document outlines the prospect permissions system implemented in the Optiven CRM application.

## Permission Codes

The prospect permissions are defined with the following codes:

| Code | Name | Description |
|------|------|-------------|
| 3001 | VIEW_PROSPECT_HQ | View prospects from HQ office |
| 3002 | VIEW_PROSPECT_KAREN | View prospects from Karen office |
| 3003 | VIEW_PROSPECT_ALL_OFFICES | View prospects from all offices |
| 3004 | VIEW_PROSPECT_OWN_MARKETER | View only prospects assigned to the current user |
| 3005 | VIEW_PROSPECT_ALL_MARKETERS | View prospects assigned to all marketers |
| 3006 | VIEW_PROSPECT_DIASPORA_TEAM | View prospects from Diaspora team |
| 3007 | VIEW_PROSPECT_DIGITAL_TEAM | View prospects from Digital team |
| 3008 | VIEW_PROSPECT_TELEMARKETING_TEAM | View prospects from Telemarketing team |
| 3009 | VIEW_PROSPECT_OTHER_TEAM | View prospects from Other teams |
| 3010 | VIEW_PROSPECT_ALL_TEAMS | View prospects from all teams |
| 3011 | VIEW_PROSPECT_DIASPORA_REGION | View prospects from user's diaspora region only |
| 3012 | VIEW_PROSPECT_ALL_DIASPORA_REGIONS | View prospects from all diaspora regions |

## API Parameters

The prospect view endpoint (`GET/prospect-view/`) accepts the following parameters that are controlled by permissions:

| Parameter | Description | Available Values | Default |
|-----------|-------------|------------------|---------|
| OFFICE | Filter by office | HQ, KAREN, ALL | ALL |
| MARKETER_EMPLOYEE_NO | Filter by marketer | Employee number or ALL | ALL |
| DIASPORA_REGION | Filter by diaspora region | Region name or ALL | ALL |
| ORGANIZATION_TEAM | Filter by organization team | DIASPORA, DIGITAL, TELEMARKETING, OTHER | ALL |
| search | Search by prospect name, lead file number, or plot id | Text string | - |
| page | Page number for pagination | Integer | 1 |
| page_size | Number of results per page | Integer | 20 |

## Usage in Code

### Checking Permissions

To check if a user has specific prospect permissions:

```typescript
import { useProspectPermissions } from '@/hooks/useProspectPermissions';

function ProspectComponent() {
  const { 
    canViewAllOfficesProspects,
    canViewOwnProspects,
    hasAnyProspectAccess
  } = useProspectPermissions();

  if (!hasAnyProspectAccess) {
    return <AccessDenied />;
  }

  // Component logic based on permissions
  return (
    <div>
      {canViewAllOfficesProspects ? (
        <AllOfficesProspectList />
      ) : canViewOwnProspects ? (
        <OwnProspectList />
      ) : (
        <RestrictedView />
      )}
    </div>
  );
}
```

### Getting API Parameters

To get the appropriate API parameters based on user permissions:

```typescript
import { getProspectApiParams } from '@/utils/prospectPermissions';
import { useSelector } from 'react-redux';
import { selectCurrentUserDetails } from '@/redux/authSlice';

function fetchProspects() {
  const userDetails = useSelector(selectCurrentUserDetails);
  
  // Base parameters (like pagination)
  const baseParams = { page: 1, page_size: 20 };
  
  // Get permission-aware parameters
  const apiParams = getProspectApiParams(userDetails, baseParams);
  
  // Use apiParams in your API call
  return api.get('/prospect-view/', { params: apiParams });
}
```

## Permission Level Display

The system can also generate a human-readable description of a user's permission level:

```typescript
import { getProspectPermissionLevel } from '@/utils/prospectPermissions';

function UserPermissionBadge({ userPermissions }) {
  const level = getProspectPermissionLevel(userPermissions);
  
  return <Badge>{level}</Badge>;
}
```

This will display values like "Full Access", "HQ Office, Own Prospects Only", or "No Access" based on the user's permissions.