# Customer Permissions System

This document outlines the customer permissions system implemented in the Optiven CRM application.

## Permission Codes

The customer permissions are defined with the following codes:

| Code | Name | Description |
|------|------|-------------|
| 2001 | VIEW_CUSTOMER_HQ | View customers from HQ office |
| 2002 | VIEW_CUSTOMER_KAREN | View customers from Karen office |
| 2003 | VIEW_CUSTOMER_ALL_OFFICES | View customers from all offices |
| 2004 | VIEW_CUSTOMER_OWN_MARKETER | View only customers assigned to the current user |
| 2005 | VIEW_CUSTOMER_ALL_MARKETERS | View customers assigned to all marketers |
| 2006 | VIEW_CUSTOMER_DIASPORA_TEAM | View customers from Diaspora team |
| 2007 | VIEW_CUSTOMER_DIGITAL_TEAM | View customers from Digital team |
| 2008 | VIEW_CUSTOMER_TELEMARKETING_TEAM | View customers from Telemarketing team |
| 2009 | VIEW_CUSTOMER_OTHER_TEAM | View customers from Other teams |
| 2010 | VIEW_CUSTOMER_ALL_TEAMS | View customers from all teams |
| 2011 | VIEW_CUSTOMER_DIASPORA_REGION | View customers from user's diaspora region only |
| 2012 | VIEW_CUSTOMER_ALL_DIASPORA_REGIONS | View customers from all diaspora regions |

## API Parameters

The customer view endpoint (`GET/customer-view/`) accepts the following parameters that are controlled by permissions:

| Parameter | Description | Available Values | Default |
|-----------|-------------|------------------|---------|
| OFFICE | Filter by office | HQ, KAREN, ALL | ALL |
| MARKETER_EMPLOYEE_NO | Filter by marketer | Employee number or ALL | ALL |
| DIASPORA_REGION | Filter by diaspora region | Region name or ALL | ALL |
| ORGANIZATION_TEAM | Filter by organization team | DIASPORA, DIGITAL, TELEMARKETING, OTHER | ALL |
| search | Search by customer name, lead file number, or plot id | Text string | - |
| page | Page number for pagination | Integer | 1 |
| page_size | Number of results per page | Integer | 20 |

## Usage in Code

### Checking Permissions

To check if a user has specific customer permissions:

```typescript
import { useCustomerPermissions } from '@/hooks/useCustomerPermissions';

function CustomerComponent() {
  const { 
    canViewAllOfficesCustomers,
    canViewOwnCustomers,
    hasAnyCustomerAccess
  } = useCustomerPermissions();

  if (!hasAnyCustomerAccess) {
    return <AccessDenied />;
  }

  // Component logic based on permissions
  return (
    <div>
      {canViewAllOfficesCustomers ? (
        <AllOfficesCustomerList />
      ) : canViewOwnCustomers ? (
        <OwnCustomerList />
      ) : (
        <RestrictedView />
      )}
    </div>
  );
}
```

### Getting API Parameters

To get the appropriate API parameters based on user permissions:

```typescript
import { getCustomerApiParams } from '@/utils/customerPermissions';
import { useSelector } from 'react-redux';
import { selectCurrentUserDetails } from '@/redux/authSlice';

function fetchCustomers() {
  const userDetails = useSelector(selectCurrentUserDetails);
  
  // Base parameters (like pagination)
  const baseParams = { page: 1, page_size: 20 };
  
  // Get permission-aware parameters
  const apiParams = getCustomerApiParams(userDetails, baseParams);
  
  // Use apiParams in your API call
  return api.get('/customer-view/', { params: apiParams });
}
```

## Permission Level Display

The system can also generate a human-readable description of a user's permission level:

```typescript
import { getCustomerPermissionLevel } from '@/utils/customerPermissions';

function UserPermissionBadge({ userPermissions }) {
  const level = getCustomerPermissionLevel(userPermissions);
  
  return <Badge>{level}</Badge>;
}
```

This will display values like "Full Access", "HQ Office, Own Customers Only", or "No Access" based on the user's permissions.