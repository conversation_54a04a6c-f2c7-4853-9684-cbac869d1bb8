// import { UserType } from "@/types/user";
// import toast from "react-hot-toast";

export const getUser = (): any | null => {
  const userString = localStorage.getItem("ncp-u");

  if (userString && userString !== "undefined") {
    try {
      const user: any = JSON.parse(userString);
      return user;
    } catch (error) {
      console.log(error);
      // toast.error(
      //   "Could not sign you in at the moment. Kindly try again later!!"
      // );
      // window.location.replace(`${import.meta.env.VITE_REDIRECT_URI}/login`);
    }
    // } else {
    //   handleLogOut();
    //   toast.error(
    //     "Could not sign you in at the moment. Kindly try again later!!"
    //   );
    //   window.location.replace(`${import.meta.env.VITE_URL}/auth/login`);
  }

  return null; // Ensure the function always returns a value
};

// handle logout
export const handleLogOut = () => {
  const logUserOut = async () => {
    // Remove stored user settings
    localStorage.removeItem("npc-u");
    localStorage.removeItem("settings");
    localStorage.setItem("isLogout", JSON.stringify({ isLogout: true }));
  };

  return logUserOut;
};
