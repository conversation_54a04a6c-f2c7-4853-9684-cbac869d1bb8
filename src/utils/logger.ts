/**
 * Production-safe logger with redaction and environment guards
 * 
 * NEVER log: PII (names, phone, national IDs), auth tokens, session IDs, API keys, 
 * secrets, raw request/response bodies, DB errors with stack traces, financial or HR data, 
 * large arrays/objects.
 * 
 * ALLOWED (dev only): counts, timings, feature flags, non-sensitive IDs.
 * ALLOWED (prod): high-level events and errors via this logger (not console.*), 
 * with redaction and sampling.
 */

type LogLevel = 'silent' | 'error' | 'warn' | 'info' | 'debug';

// Environment detection
const env = import.meta.env.MODE; // Vite environment
const isProduction = env === 'production';
const isDevelopment = env === 'development';

// Log level configuration
const levelFromEnv = (import.meta.env.VITE_LOG_LEVEL as LogLevel) || 
  (isProduction ? 'error' : 'debug');

const LEVEL_ORDER: Record<LogLevel, number> = {
  silent: 0,
  error: 1,
  warn: 2,
  info: 3,
  debug: 4
};

/**
 * Redacts sensitive information from logged data
 */
const redact = (value: unknown): unknown => {
  try {
    if (value === null || value === undefined) {
      return value;
    }

    if (typeof value === 'string') {
      // Redact common sensitive patterns
      if (/token|password|secret|key|id_no|national_id|phone|email|auth/i.test(value)) {
        return '***REDACTED***';
      }
      return value;
    }

    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        return value.map(redact);
      }

      // Handle objects
      const redacted: Record<string, unknown> = {};
      for (const [key, val] of Object.entries(value)) {
        // Redact sensitive keys
        if (/token|password|secret|key|id_no|national_id|phone|email|auth|session/i.test(key)) {
          redacted[key] = '***REDACTED***';
        } else {
          redacted[key] = redact(val);
        }
      }
      return redacted;
    }

    return value;
  } catch {
    return '[[unserializable]]';
  }
};

/**
 * Safely limits object/array size for logging
 */
const limitSize = (value: unknown, maxItems = 3): unknown => {
  if (Array.isArray(value)) {
    if (value.length <= maxItems) {
      return value;
    }
    return {
      count: value.length,
      sample: value.slice(0, maxItems).map(redact)
    };
  }

  if (typeof value === 'object' && value !== null) {
    const entries = Object.entries(value);
    if (entries.length <= maxItems) {
      return redact(value);
    }
    return {
      count: entries.length,
      sample: Object.fromEntries(
        entries.slice(0, maxItems).map(([k, v]) => [k, redact(v)])
      )
    };
  }

  return redact(value);
};

/**
 * Core logging function
 */
function log(level: LogLevel, message: string, ...args: unknown[]): void {
  // Check if we should log at this level
  if (LEVEL_ORDER[level] > LEVEL_ORDER[levelFromEnv]) {
    return;
  }

  // In production, only allow error and warn levels
  if (isProduction && !['error', 'warn'].includes(level)) {
    return;
  }

  // Redact and limit size of arguments
  const safeArgs = args.map(arg => limitSize(arg));
  const safeMessage = typeof message === 'string' ? message : String(redact(message));

  // Use console only in development
  if (isDevelopment) {
    // eslint-disable-next-line no-console
    const consoleMethod = console[level === 'debug' ? 'log' : level] || console.log;
    consoleMethod(`[${level.toUpperCase()}]`, safeMessage, ...safeArgs);
  }

  // In production, you could send to external logging service here
  // Example: sendToLoggingService(level, safeMessage, safeArgs);
}

/**
 * Public logger interface
 */
export const logger = {
  error: (message: string, ...args: unknown[]) => log('error', message, ...args),
  warn: (message: string, ...args: unknown[]) => log('warn', message, ...args),
  info: (message: string, ...args: unknown[]) => log('info', message, ...args),
  debug: (message: string, ...args: unknown[]) => log('debug', message, ...args),
  
  // Utility methods
  redact,
  limitSize,
  
  // Environment info
  isProduction,
  isDevelopment,
  currentLevel: levelFromEnv
};

/**
 * Development-only logging hook for React components
 */
export const useDevLog = () => {
  return isDevelopment ? logger.debug : (..._args: unknown[]) => {};
};

/**
 * Safe logging for arrays/objects with automatic summarization
 */
export const logSummary = (message: string, data: unknown, maxItems = 3) => {
  const summary = limitSize(data, maxItems);
  logger.info(message, summary);
};

/**
 * Safe logging for counts and metrics
 */
export const logMetric = (metric: string, value: number | string, context?: Record<string, unknown>) => {
  const safeContext = context ? redact(context) : undefined;
  logger.info(`Metric: ${metric}`, { value, ...safeContext });
};

export default logger;
