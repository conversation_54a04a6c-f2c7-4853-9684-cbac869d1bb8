/**
 * Redaction utilities for safe structured logging
 * 
 * These utilities help create safe, redacted versions of data for logging
 * while preserving useful information for debugging.
 */

/**
 * Redacts employee data, keeping only safe fields
 */
export const redactEmployee = (employee: any) => {
  if (!employee || typeof employee !== 'object') {
    return { id: 'unknown' };
  }

  return {
    id: employee.id || employee.employee_no || 'unknown',
    department: employee.department || 'unknown',
    role: employee.role || 'unknown',
    // Explicitly exclude: name, phone, email, national_id, fullnames, username
  };
};

/**
 * Redacts customer data, keeping only safe fields
 */
export const redactCustomer = (customer: any) => {
  if (!customer || typeof customer !== 'object') {
    return { id: 'unknown' };
  }

  return {
    id: customer.id || 'unknown',
    status: customer.status || 'unknown',
    // Explicitly exclude: name, phone, email, address, personal details
  };
};

/**
 * Redacts project data, keeping only safe fields
 */
export const redactProject = (project: any) => {
  if (!project || typeof project !== 'object') {
    return { id: 'unknown' };
  }

  return {
    id: project.id || 'unknown',
    name: project.name || 'unknown',
    status: project.status || 'unknown',
    // Explicitly exclude: financial details, personal information
  };
};

/**
 * Redacts vehicle data, keeping only safe fields
 */
export const redactVehicle = (vehicle: any) => {
  if (!vehicle || typeof vehicle !== 'object') {
    return { id: 'unknown' };
  }

  return {
    id: vehicle.id || 'unknown',
    type: vehicle.type || 'unknown',
    status: vehicle.status || 'unknown',
    // Explicitly exclude: license plates, driver details, location
  };
};

/**
 * Redacts site visit data, keeping only safe fields
 */
export const redactSiteVisit = (visit: any) => {
  if (!visit || typeof visit !== 'object') {
    return { id: 'unknown' };
  }

  return {
    id: visit.id || 'unknown',
    status: visit.status || 'unknown',
    date: visit.date || visit.pickup_date || 'unknown',
    // Explicitly exclude: client details, location specifics, personal info
  };
};

/**
 * Creates a safe summary of an array with count and sample
 */
export const summarize = <T>(
  list: T[] | undefined | null,
  pick: (item: T) => any = (item) => item,
  sampleSize = 1
) => {
  if (!Array.isArray(list)) {
    return { count: 0, sample: [] };
  }

  return {
    count: list.length,
    sample: list.slice(0, sampleSize).map(pick)
  };
};

/**
 * Creates a safe summary of an object with key count and sample
 */
export const summarizeObject = (
  obj: Record<string, any> | undefined | null,
  sampleKeys = 3
) => {
  if (!obj || typeof obj !== 'object') {
    return { keyCount: 0, sampleKeys: [] };
  }

  const keys = Object.keys(obj);
  return {
    keyCount: keys.length,
    sampleKeys: keys.slice(0, sampleKeys)
  };
};

/**
 * Redacts API response data, keeping only safe metadata
 */
export const redactApiResponse = (response: any) => {
  if (!response) {
    return { type: 'empty' };
  }

  return {
    type: 'api_response',
    hasData: !!response.data,
    hasResults: !!response.results,
    dataType: Array.isArray(response.data) ? 'array' : typeof response.data,
    resultsType: Array.isArray(response.results) ? 'array' : typeof response.results,
    // Explicitly exclude: actual data content
  };
};

/**
 * Redacts error objects, keeping only safe error information
 */
export const redactError = (error: any) => {
  if (!error) {
    return { type: 'unknown_error' };
  }

  return {
    type: 'error',
    name: error.name || 'UnknownError',
    message: error.message || 'Unknown error',
    status: error.status || error.statusCode || 'unknown',
    // Explicitly exclude: stack traces, sensitive error details
  };
};

/**
 * Redacts form data, keeping only field names and types
 */
export const redactFormData = (formData: any) => {
  if (!formData || typeof formData !== 'object') {
    return { fieldCount: 0 };
  }

  const fields = Object.keys(formData);
  return {
    fieldCount: fields.length,
    fieldNames: fields,
    // Explicitly exclude: actual field values
  };
};

/**
 * Redacts user session data, keeping only safe session info
 */
export const redactSession = (session: any) => {
  if (!session || typeof session !== 'object') {
    return { type: 'no_session' };
  }

  return {
    type: 'session',
    hasUser: !!session.user,
    hasToken: !!session.token,
    // Explicitly exclude: actual user data, tokens, session IDs
  };
};

/**
 * Generic redactor that removes common sensitive fields
 */
export const redactGeneric = (data: any, sensitiveFields: string[] = []) => {
  const defaultSensitiveFields = [
    'password', 'token', 'secret', 'key', 'auth', 'session',
    'name', 'email', 'phone', 'address', 'national_id', 'id_no',
    'fullnames', 'username', 'personal', 'private'
  ];

  const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields];
  
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => redactGeneric(item, sensitiveFields));
  }

  const redacted: Record<string, any> = {};
  for (const [key, value] of Object.entries(data)) {
    const isSensitive = allSensitiveFields.some(field => 
      key.toLowerCase().includes(field.toLowerCase())
    );
    
    if (isSensitive) {
      redacted[key] = '***REDACTED***';
    } else if (typeof value === 'object' && value !== null) {
      redacted[key] = redactGeneric(value, sensitiveFields);
    } else {
      redacted[key] = value;
    }
  }

  return redacted;
};

export default {
  redactEmployee,
  redactCustomer,
  redactProject,
  redactVehicle,
  redactSiteVisit,
  redactApiResponse,
  redactError,
  redactFormData,
  redactSession,
  redactGeneric,
  summarize,
  summarizeObject
};
