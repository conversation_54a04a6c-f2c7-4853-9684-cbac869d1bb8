import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import IdleJs from 'idle-js';
import useLockScreenStore from '@/zustand/useLockScreenStore';

// Define types for our hook parameters
interface UseIdleTimeoutOptions {
  idleTime?: number; // Time in seconds before considering user idle
  onIdle?: () => void; // Callback when user becomes idle
  redirectUrl?: string | null; // URL to redirect to when idle (null for no redirect)
  events?: string[]; // DOM events that reset the idle timer
  keepTracking?: boolean; // Continue tracking after the user goes idle
  startAtIdle?: boolean; // Start in idle state
  onActive?: () => void; // Callback when user becomes active
  onHide?: () => void; // Callback when user switches tabs
  onShow?: () => void; // Callback when user returns to tab
}

// Define the return type of our hook
interface UseIdleTimeoutReturn { }

export const useIdleTimeout = ({
  idleTime = 900, // Default to 15 minutes
  onIdle,
  redirectUrl = '/auth/lockscreen',
  events = ['mousemove', 'keydown', 'mousedown', 'touchstart', 'scroll'],
  keepTracking = true,
  startAtIdle = false,
  onActive,
  onHide,
  onShow
}: UseIdleTimeoutOptions): UseIdleTimeoutReturn => {
  const navigate = useNavigate();
  const location = useLocation()
  const increasePopulation = useLockScreenStore((state) => state.setRoute)
  useEffect(() => {
    // Create a new IdleJs instance
    const idle = new IdleJs({
      idle: idleTime * 1000, // Convert seconds to milliseconds
      onIdle: async () => {
        // console.log(`User has been idle for ${idleTime} seconds`);

        // Call the optional callback if provided
        if (onIdle) onIdle();

        // Redirect to login page if a URL is provided
        if (redirectUrl) {
          // store prev path

          increasePopulation(location.pathname);
          // redirect
          navigate(redirectUrl, { replace: true, state: { preventBack: true } });
        }
      },
      onActive: () => {
        // console.log('User is active');
        if (onActive) onActive();
      },
      onHide: () => {
        // console.log('User has switched tabs');
        if (onHide) onHide();
      },
      onShow: () => {
        // console.log('User is back on the tab');
        if (onShow) onShow();
      },
      events, // Events that reset the idle timer
      keepTracking, // Continue tracking after the user goes idle
      startAtIdle // Start in active or idle state
    });

    // Start the idle tracker
    idle.start();

    // Clean up
    return () => {
      idle.stop();
    };
  }, [idleTime, onIdle, redirectUrl, navigate, events, keepTracking, startAtIdle, onActive, onHide, onShow]);

  return {};
};
