// import toast from "react-hot-toast";

export const addCommaNoDecimal = (value: string) => {
  const fomarted_value = parseFloat(value).toLocaleString();
  return fomarted_value;
};

export const addComma = (value: string) => {
  const fomarted_value = parseFloat(value).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return fomarted_value;
};

export const addComma1 = (value: string) => {
  const fomarted_value = parseFloat(value).toLocaleString(undefined, {
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  });
  return fomarted_value;
};

export const capitalizeFirstLetter = (word: string) => {
  if (!word || typeof word !== "string") return "";
  const capped_first_letter = word[0].toUpperCase();
  const rest_of_words = word.substring(1);
  const capped_word = capped_first_letter + rest_of_words;

  return capped_word;
};

export const genRandomString = (length: number) => {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

// export const copyToClipboard = (refId: any) => {
//   if (refId.current) {
//     navigator.clipboard
//       .writeText(refId.current.innerText)
//       .then(() => toast.success("copied to clipboard"))
//       .catch((err) => toast.error(err));
//   }
// };
