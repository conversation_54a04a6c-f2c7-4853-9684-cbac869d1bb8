import { addComma } from "./helpers";

interface forexDataTypes {
  USD: number;
  GBP: number;
  EURO: number;
}

const handleCurrencyConvertion = (
  value: string,
  currentCurrency: string,
  forexData: forexDataTypes
) => {
  if (currentCurrency !== "KES") {
    const currencies = forexData;
    const selectedCurrencyValue =
      currencies[currentCurrency as keyof forexDataTypes];
    const newValue = parseFloat(value) / selectedCurrencyValue;
    return addComma(String(newValue));
  } else {
    return addComma(value);
  }
};

export default handleCurrencyConvertion;
