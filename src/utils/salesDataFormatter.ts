export function formatShortDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' };
    // e.g., "Jul 27, 2023"
    const formatted = date.toLocaleDateString('en-US', options);
    // Remove comma for "Jul 27 2023"
    return formatted.replace(',', '');
}

export function formatNumberWithCommas(value: string | number): string {
    const cleanedValue = typeof value === 'string' ? value.replace(/,/g, '') : value;
    const num = typeof cleanedValue === 'string' ? parseFloat(cleanedValue) : cleanedValue;
    if (isNaN(num)) return '';
    return Math.round(num).toLocaleString('en-US');
}