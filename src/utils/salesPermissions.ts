/**
 * Sales permissions utility functions
 * These functions handle the logic for filtering sales data based on user permissions
 * Simplified to follow the logistics permissions pattern
 */
import { UserDetails } from '@/redux/authSlice';

// Sales permission codes as defined in the system
export const SALES_PERMISSION_CODES = {
  // Single filter permissions - only one of these should be applied at a time
  VIEW_SALES_HQ: 1001,
  VIEW_SALES_KAREN: 1002,
  VIEW_SALES_ALL_OFFICES: 1003,
  VIEW_SALES_OWN_MARKETER: 1004,
  VIEW_SALES_ALL_MARKETERS: 1005,
  VIEW_SALES_DIASPORA_TEAM: 1006,
  VIEW_SALES_DIGITAL_TEAM: 1007,
  VIEW_SALES_TELEMARKETING_TEAM: 1008,
  VIEW_SALES_OTHER_TEAM: 1009,
  VIEW_SALES_ALL_TEAMS: 1010,
  VIEW_SALES_DIASPORA_REGION: 1011,
  VIEW_SALES_ALL_DIASPORA_REGIONS: 1012,
} as const;

/**
 * Permission descriptions for documentation and UI display
 */
export const SALES_PERMISSION_DESCRIPTIONS = {
  [SALES_PERMISSION_CODES.VIEW_SALES_HQ]: 
    'Permission to view sales data from HQ office',
  [SALES_PERMISSION_CODES.VIEW_SALES_KAREN]: 
    'Permission to view sales data from KAREN office',
  [SALES_PERMISSION_CODES.VIEW_SALES_ALL_OFFICES]: 
    'Permission to view sales data from all offices',
  [SALES_PERMISSION_CODES.VIEW_SALES_OWN_MARKETER]: 
    'Permission to view sales data for the logged-in marketer only',
  [SALES_PERMISSION_CODES.VIEW_SALES_ALL_MARKETERS]: 
    'Permission to view sales data for all marketers',
  [SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_TEAM]: 
    'Permission to view sales data from DIASPORA team',
  [SALES_PERMISSION_CODES.VIEW_SALES_DIGITAL_TEAM]: 
    'Permission to view sales data from DIGITAL team',
  [SALES_PERMISSION_CODES.VIEW_SALES_TELEMARKETING_TEAM]: 
    'Permission to view sales data from TELEMARKETING team',
  [SALES_PERMISSION_CODES.VIEW_SALES_OTHER_TEAM]: 
    'Permission to view sales data from OTHER team',
  [SALES_PERMISSION_CODES.VIEW_SALES_ALL_TEAMS]: 
    'Permission to view sales data from all teams',
  [SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_REGION]: 
    'Permission to view sales data filtered by diaspora region',
  [SALES_PERMISSION_CODES.VIEW_SALES_ALL_DIASPORA_REGIONS]: 
    'Permission to view sales data from all diaspora regions',
} as const;

/**
 * Check if user has a specific sales permission
 */
export const hasSalesPermission = (
  userPermissions: any[],
  permissionCode: number
): boolean => {
  // Extract permissions from potentially nested arrays if needed
  let flatPermissions: (number | string | { permission?: number | string; code?: number | string; id?: number | string })[] = [];
  
  // If we have nested arrays, flatten them
  if (userPermissions.some(p => Array.isArray(p))) {
    userPermissions.forEach(p => {
      if (Array.isArray(p)) {
        flatPermissions = [...flatPermissions, ...p];
      } else {
        flatPermissions.push(p);
      }
    });
  } else {
    flatPermissions = userPermissions;
  }
  
  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = flatPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      // First try to get the permission field (from API response)
      if (p.permission !== undefined) {
        return typeof p.permission === 'string' ? parseInt(p.permission, 10) : p.permission;
      }
      // Then try code or id (from other sources)
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });
  
  return normalizedPermissions.includes(permissionCode);
};

/**
 * Get the appropriate API parameters based on user's sales permissions
 * Simplified to only apply a single filter parameter based on priority
 */
export const getSalesApiParams = (
  userDetails: UserDetails | null,
  baseParams: Record<string, any> = {},
  user2userPermissions: any[] = []
): Record<string, any> => {
  // Extract permissions from user2user permissions
  const userPermissions: (number | string | { permission?: number | string; code?: number | string; id?: number | string })[] = [];
  
  // Add permissions from user2user permissions API
  user2userPermissions.forEach((p: any) => {
    if (p.permission !== undefined) {
      userPermissions.push({ permission: p.permission });
    }
  });

  // If no user2user permissions found, check legacy user_permissions as fallback
  if (userPermissions.length === 0 && userDetails?.user_permissions) {
    userDetails.user_permissions.forEach((p: any) => {
      if (Array.isArray(p)) {
        // If it's an array, add each item
        p.forEach((subP: any) => {
          userPermissions.push(subP);
        });
      } else if (typeof p === 'object') {
        // If it's an object, extract permission, code or id
        userPermissions.push(p);
      } else {
        // Otherwise add as is
        userPermissions.push(p);
      }
    });
  }

  // If still no permissions, return empty result set
  if (userPermissions.length === 0) {
    return { ...baseParams, no_access: true };
  }

  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = userPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      // First try to get the permission field (from API response)
      if (p.permission !== undefined) {
        return typeof p.permission === 'string' ? parseInt(p.permission, 10) : p.permission;
      }
      // Then try code or id (from other sources)
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });

  const params = { ...baseParams };

  // Apply a single filter parameter based on priority
  // Priority order: All access > Office > Team > Marketer > Region
  
  // Check for all access first
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_OFFICES)) {
    params.OFFICE = 'ALL';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_TEAMS)) {
    params.ORGANIZATION_TEAM = 'ALL';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_MARKETERS)) {
    params.MARKETER_EMPLOYEE_NO = 'ALL';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_DIASPORA_REGIONS)) {
    params.DIASPORA_REGION = 'ALL';
    return params;
  }
  
  // Check for specific office access
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_HQ)) {
    params.OFFICE = 'HQ';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_KAREN)) {
    params.OFFICE = 'KAREN';
    return params;
  }
  
  // Check for specific team access
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_TEAM)) {
    params.ORGANIZATION_TEAM = 'DIASPORA';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_DIGITAL_TEAM)) {
    params.ORGANIZATION_TEAM = 'DIGITAL';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_TELEMARKETING_TEAM)) {
    params.ORGANIZATION_TEAM = 'TELEMARKETING';
    return params;
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_OTHER_TEAM)) {
    params.ORGANIZATION_TEAM = 'OTHER';
    return params;
  }
  
  // Check for marketer-specific access
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_OWN_MARKETER)) {
    params.MARKETER_EMPLOYEE_NO = userDetails.employee_no || '';
    return params;
  }
  
  // Check for region-specific access
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_REGION)) {
    params.DIASPORA_REGION = userDetails.diaspora_region || 'USER_REGION';
    return params;
  }

  // If no sales permissions are found, restrict access
  const hasSalesAccess = Object.values(SALES_PERMISSION_CODES).some(code =>
    normalizedPermissions.includes(code)
  );

  if (!hasSalesAccess) {
    params.no_access = true;
  }

  return params;
};

/**
 * Check if user has any sales viewing permissions
 */
export const hasAnySalesPermission = (userPermissions: any[]): boolean => {
  // Extract permissions from potentially nested arrays if needed
  let flatPermissions: (number | string | { permission?: number | string; code?: number | string; id?: number | string })[] = [];
  
  // If we have nested arrays, flatten them
  if (userPermissions.some(p => Array.isArray(p))) {
    userPermissions.forEach(p => {
      if (Array.isArray(p)) {
        flatPermissions = [...flatPermissions, ...p];
      } else {
        flatPermissions.push(p);
      }
    });
  } else {
    flatPermissions = userPermissions;
  }
  
  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = flatPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      // First try to get the permission field (from API response)
      if (p.permission !== undefined) {
        return typeof p.permission === 'string' ? parseInt(p.permission, 10) : p.permission;
      }
      // Then try code or id (from other sources)
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });
  
  // Check if any of the user's permissions match any of the sales permission codes
  return Object.values(SALES_PERMISSION_CODES).some(code =>
    normalizedPermissions.includes(code)
  );
};

/**
 * Get user's sales permission level description
 */
export const getSalesPermissionLevel = (userPermissions: any[]): string => {
  // Extract permissions from potentially nested arrays if needed
  let flatPermissions: (number | string | { code?: number | string; id?: number | string })[] = [];
  
  // If we have nested arrays, flatten them
  if (userPermissions.some(p => Array.isArray(p))) {
    userPermissions.forEach(p => {
      if (Array.isArray(p)) {
        flatPermissions = [...flatPermissions, ...p];
      } else {
        flatPermissions.push(p);
      }
    });
  } else {
    flatPermissions = userPermissions;
  }
  
  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = flatPermissions.map(p => 
    typeof p === 'string' ? parseInt(p, 10) : p
  );

  // Check for all access first
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_OFFICES)) {
    return 'All Offices';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_TEAMS)) {
    return 'All Teams';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_MARKETERS)) {
    return 'All Marketers';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_ALL_DIASPORA_REGIONS)) {
    return 'All Diaspora Regions';
  }
  
  // Check for specific permissions
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_HQ)) {
    return 'HQ Office Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_KAREN)) {
    return 'Karen Office Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_TEAM)) {
    return 'Diaspora Team Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_DIGITAL_TEAM)) {
    return 'Digital Team Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_TELEMARKETING_TEAM)) {
    return 'Telemarketing Team Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_OTHER_TEAM)) {
    return 'Other Team Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_OWN_MARKETER)) {
    return 'Own Sales Only';
  }
  
  if (normalizedPermissions.includes(SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_REGION)) {
    return 'User Diaspora Region Only';
  }

  return 'No Access';
};
