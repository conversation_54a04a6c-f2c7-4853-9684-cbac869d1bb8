/**
 * Production console muting - belt & suspenders approach
 * 
 * This file provides runtime safeguards to mute console methods in production
 * as a final safety net in case any console statements make it through the build process.
 */

// Only apply in production
if (import.meta.env.MODE === 'production') {
  const noop = () => {};
  
  // Override console methods to prevent any accidental logging
  // eslint-disable-next-line no-console
  console.log = noop;
  // eslint-disable-next-line no-console
  console.debug = noop;
  // eslint-disable-next-line no-console
  console.info = noop;
  // eslint-disable-next-line no-console
  console.table = noop;
  // eslint-disable-next-line no-console
  console.dir = noop;
  // eslint-disable-next-line no-console
  console.trace = noop;
  // eslint-disable-next-line no-console
  console.group = noop;
  // eslint-disable-next-line no-console
  console.groupEnd = noop;
  // eslint-disable-next-line no-console
  console.groupCollapsed = noop;
  // eslint-disable-next-line no-console
  console.time = noop;
  // eslint-disable-next-line no-console
  console.timeEnd = noop;
  // eslint-disable-next-line no-console
  console.timeLog = noop;
  // eslint-disable-next-line no-console
  console.count = noop;
  // eslint-disable-next-line no-console
  console.countReset = noop;
  // eslint-disable-next-line no-console
  console.clear = noop;
  
  // Keep error and warn for critical issues, but redirect through proper logging
  const originalError = console.error;
  const originalWarn = console.warn;
  
  // eslint-disable-next-line no-console
  console.error = (...args: any[]) => {
    // In production, you might want to send these to an external service
    // For now, we'll keep them but with a prefix to indicate they're from production
    originalError('[PROD ERROR]', ...args);
  };
  
  // eslint-disable-next-line no-console
  console.warn = (...args: any[]) => {
    // In production, you might want to send these to an external service
    // For now, we'll keep them but with a prefix to indicate they're from production
    originalWarn('[PROD WARN]', ...args);
  };
  
  // Prevent console from being overridden
  Object.freeze(console);
}
