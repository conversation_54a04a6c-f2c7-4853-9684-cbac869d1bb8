export interface ProspectTypes {
  selling_price: number;
  total_paid: number;
  balance: number;
  customer_name: any;
  id: number;
  lead_type: string;
  name: string;
  phone: string;
  alternate_phone: string;
  email: string;
  city: string;
  country: string;
  comment: string;
  date: string;
  status: string;
  category: string;
  pipeline_level: string;
  is_verified: boolean;
  is_converted: boolean;
  customer: string | null;
  no_of_sales: number;
  lead_source_category: number;
  lead_source_subcategory: number;
  lead_source: number;
  project: string;
  department: string | null;
  department_member: string | null;
  marketer: string;
  lead_source_category_name: string;
  lead_source_subcategory_name: string;
  lead_source_name: string;
  project_name: string;
  marketer_name: string;
  marketer_department: string | null;
  department_name: string | null;
  department_member_name: string | null;
}

export interface ProspectsApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: ProspectTypes[];
  };
}
