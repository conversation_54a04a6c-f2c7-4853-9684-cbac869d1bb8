export interface projectTypes {
  projectId: string;
  name: string;
  description: string;
  initials: string | null;
  link: string;
  priority: number;
  tier?: string;
  visibiliy?: string;
  bank?: string;
  account_no?: string;
  website_link?: string;
  percentage_sold?: string;
  open_plots?: string;
  sold_plots?: string;
  reserved_plots?: string;
  plots?: plotTypes;
}

export interface plotTypes {
  plotId?: string;
  project?: string;
  plot_no?: string;
  plot_size?: string;
  plot_type?: string;
  plot_status?: string;
  erp_status?: string;
  location?: string;
  cash_price?: string;
  threshold_price?: string;
}
