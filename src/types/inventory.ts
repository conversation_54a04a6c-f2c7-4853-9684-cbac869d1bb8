export interface PlotTypes {
  marketer: string;
  [x: string]: string;
  plotId: string;
  plot_no: string;
  plot_size: string;
  plot_type: "Residential" | "Commercial";
  plot_status: "Open" | "Reserved" | "Sold";
  erp_status: "Open" | "Reserved" | "Sold";
  location: string;
  cash_price: string;
  threshold_price: string;
  lr_no: string | null;
  view: string | null;
  project: string;
}

export interface InventoryApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: PlotTypes[];
  };
}

// Legal dashboard specific interfaces
export interface LegalPlotData extends PlotTypes {
  customerName?: string;
  marketer?: string;
  marketer_name?: string;
  approval_status?: "Pending" | "Approved" | "Rejected" | "Under Review";
  legal_status?: "Title Deed Ready" | "Title Processing" | "Documentation Pending" | "Complete";
  sale_date?: string;
  transfer_date?: string;
  documents_status?: "Complete" | "Incomplete" | "Missing";
}

export interface LegalMetrics {
  totalPlots: number;
  soldPlots: number;
  reservedPlots: number;
  openPlots: number;
  pendingApprovals: number;
  approvedPlots: number;
  titleDeedsReady: number;
  documentationPending: number;
  averagePrice: number;
  totalValue: number;
  residentialPlots: number;
  commercialPlots: number;
}

export interface LegalDashboardFilters {
  plot_status?: string;
  plot_type?: string;
  location?: string;
  project?: string;
  approval_status?: string;
  legal_status?: string;
  price_range?: {
    min: number;
    max: number;
  };
}
