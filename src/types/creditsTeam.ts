// TypeScript interfaces for Credits Team API

export interface CreditOfficer {
  credit_officer_id: string;
  fullnames: string;
}

export interface CollectionsData {
  Installments_Due_Today: number;
  Overdue_Collections_Collected: number;
  Overdue_Collections: number;
  ALL_Overdue_Collections: number;
  Sales_Deposits_Below_Threshold: number;
  Overdue_Below_Threshold: number;
  Expected_Monthly_Installments: number;
  EXPECTED_Monthly_installments_collected: number;
}

export interface CurrentMonth {
  Period_Start_Date: string;
  Period_End_Date: string;
}

export interface CreditsTeamIndexResponse {
  Collections: CollectionsData;
  Current_Month: CurrentMonth;
  Credits_Teams_Performance: CreditOfficer[];
}

export interface Portfolio {
  all_customers: number;
  all_sales: number;
  portfolio_total_paid: number;
}

export interface CreditOfficerDetailsResponse {
  installments_due_today: number;
  installments_due_today_total: number;
  overdue_collections_collected: number;
  overdue_collections: number;
  all_overdue_collections: number;
  Current_Month: CurrentMonth;
  sales_below_threshold_count: number;
  overdue_below_threshold_count: number;
  monthly_installments_due: number;
  monthly_installments_due_collected: number;
  total_expexted_installments: number;
  Portfolio: Portfolio;
  installments_collected_today: number;
  additionaldeposits_installments_collected: number;
  finalpaymentscollected_mib: number;
  finalpaymentscollected_no_of_payments: number;
}

// Performance calculation helpers
export interface PerformanceMetrics {
  collectionRate: number;
  monthlyPerformance: number;
  portfolioValue: number;
  customerCount: number;
  salesCount: number;
}

export interface CreditOfficerPerformance extends CreditOfficer {
  performance: PerformanceMetrics;
  badge: 'excellent' | 'good' | 'needs-improvement';
}

// Filter and search types
export interface CreditsTeamFilters {
  search?: string;
  performance_level?: 'all' | 'excellent' | 'good' | 'needs-improvement';
  sort_by?: 'name' | 'performance' | 'collections';
  sort_order?: 'asc' | 'desc';
}

// Dashboard summary types
export interface DashboardSummary {
  totalOfficers: number;
  totalCollections: number;
  totalOverdue: number;
  averagePerformance: number;
  topPerformer: CreditOfficer | null;
  periodInfo: CurrentMonth;
}

// API Error types
export interface CreditsTeamApiError {
  status: number;
  message: string;
  details?: string;
}

// Loading states
export interface LoadingStates {
  index: boolean;
  details: boolean;
  refresh: boolean;
}

// Modal states
export interface ModalStates {
  detailModal: boolean;
  filterModal: boolean;
  exportModal: boolean;
}

// Export types for components
export type {
  CreditOfficer as CreditOfficerType,
  CreditsTeamIndexResponse as CreditsTeamIndexType,
  CreditOfficerDetailsResponse as CreditOfficerDetailsType,
  CollectionsData as CollectionsType,
  Portfolio as PortfolioType,
  CurrentMonth as CurrentMonthType
};
