// TypeScript interfaces for Marketer Reports API

export interface MarketerReport {
  id: number;
  marketer_no_id: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: number;
  daily_target: number;
  MIB_achieved: number;
  MIB_Perfomance: number; // Note: API has typo "Perfomance" instead of "Performance"

  // Optional fields for backward compatibility
  line_no?: number;
  marketer_no?: string;
  marketer_name?: string;
  title?: string;
  status?: string;
  commission_rate?: number;
  commission_payable?: number;
}

export interface MarketerReportsApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: MarketerReport[];
}

// Query parameters for marketer periods API
export interface MarketerPeriodsQueryParams {
  office: string;
  start_date: string;
  end_date: string;
  page?: number;
  page_size?: number;
}

// Office period data structure
export interface OfficePeriod {
  period_name: string;
  start_date: string;
  end_date: string;
  target: number;
  achieved: number;
  progress: number;
}

export interface OfficeData {
  office: string;
  total_marketers: number;
  current_period: OfficePeriod;
}

// Marketing Period from the API response
export interface MarketingPeriod {
  period_start_date: string;
  period_end_date: string;
}

// All Marketing Periods API Response
export interface AllMarketingPeriodsResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: MarketingPeriod[];
}

// Portfolio Header data structure from portfolio-headers API
export interface PortfolioHeader {
  line_no: number;
  marketer_no: string;
  period_start_date: string;
  period_end_date: string;
  total_purchases: string;
  marketer_name?: string;
  title?: string;
  monthly_target?: number;
  daily_target?: number;
  MIB_achieved?: number;
  MIB_Perfomance?: number;
  marketer_target?: string;
  MIB_perfomance?: string;
}

// Portfolio Headers API Response
export interface PortfolioHeadersApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    per_page: number;
    results: PortfolioHeader[];
    total_data: number;
  };
}
