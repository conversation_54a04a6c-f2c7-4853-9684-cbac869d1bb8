export interface NoteTypes {
  note_id: string;
  title: string;
  content: string;
  note_type: "General" | "Meeting" | "Important" | "Task" | "Idea" | "Reminder" | "Follow-up" | "Call" | "Email";
  entity_type: "customer" | "prospect" | "leadfile" | "sales" | "general";
  entity_id: string;
  is_private: boolean;
  is_pinned: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  tags?: string[];
  priority?: "low" | "medium" | "high";
  status?: "draft" | "finalized" | "archived";
  
  // Related entity information (populated from API)
  entity_name?: string;
  entity_phone?: string;
  entity_email?: string;
  entity_project?: string;
  marketer_name?: string;
  
  // Computed fields
  word_count?: number;
  read_time?: string;
  change?: string;
  changeLabel?: string;
}

export interface NotesApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: NoteTypes[];
  };
}

export interface NotesFilters {
  entity_type?: "customer" | "prospect" | "leadfile" | "sales" | "general" | "";
  note_type?: string;
  is_private?: boolean;
  is_pinned?: boolean;
  priority?: "low" | "medium" | "high" | "";
  status?: "draft" | "finalized" | "archived" | "";
  created_by?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  tags?: string[];
}

export interface NotesMetrics {
  totalNotes: number;
  customerNotes: number;
  prospectNotes: number;
  salesNotes: number;
  generalNotes: number;
  privateNotes: number;
  pinnedNotes: number;
  draftNotes: number;
  finalizedNotes: number;
  highPriorityNotes: number;
  recentNotes: number;
}

export interface CreateNoteRequest {
  title: string;
  content: string;
  note_type: string;
  entity_type: string;
  entity_id: string;
  is_private?: boolean;
  is_pinned?: boolean;
  priority?: "low" | "medium" | "high";
  status?: "draft" | "finalized";
  tags?: string[];
}

export interface UpdateNoteRequest extends Partial<CreateNoteRequest> {
  note_id: string;
}

// Entity reference types for linking notes
export interface EntityReference {
  id: string;
  name: string;
  type: "customer" | "prospect" | "leadfile" | "sales";
  phone?: string;
  email?: string;
  project?: string;
  marketer?: string;
}

// Note categories for better organization
export const NOTE_CATEGORIES = {
  CUSTOMER: {
    label: "Customer Notes",
    description: "Notes related to existing customers",
    color: "blue",
    icon: "User"
  },
  PROSPECT: {
    label: "Prospect Notes",
    description: "Notes about potential customers",
    color: "green",
    icon: "UserPlus"
  },
  SALES: {
    label: "Sales Notes",
    description: "Notes related to sales activities",
    color: "purple",
    icon: "DollarSign"
  },
  GENERAL: {
    label: "General Notes",
    description: "General notes and reminders",
    color: "gray",
    icon: "FileText"
  }
} as const;

// Note types with their properties
export const NOTE_TYPES = {
  General: { icon: "FileText", color: "gray" },
  Meeting: { icon: "Calendar", color: "blue" },
  Important: { icon: "Star", color: "red" },
  Task: { icon: "CheckSquare", color: "green" },
  Idea: { icon: "Lightbulb", color: "yellow" },
  Reminder: { icon: "Clock", color: "orange" },
  "Follow-up": { icon: "ArrowRight", color: "purple" },
  Call: { icon: "Phone", color: "blue" },
  Email: { icon: "Mail", color: "indigo" }
} as const;
