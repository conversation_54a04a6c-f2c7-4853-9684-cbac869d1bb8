import {
  BaseQuery<PERSON>pi,
  create<PERSON><PERSON>,
  <PERSON>tch<PERSON>rgs,
  fetchBaseQuery,
} from "@reduxjs/toolkit/query/react";
import { RootState } from "./store";
import { logout } from "./authSlice";

const environment = import.meta.env.VITE_PROD;

const url =
  environment == "production"
    ? import.meta.env.VITE_API_URL_PROD
    : import.meta.env.VITE_API_URL_DEV;

const baseQueryWithReauth = async (
  args: string | FetchArgs,
  api: BaseQueryApi,
  extraOptions: Record<string, any>
) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: url,
    prepareHeaders: (headers, { getState, endpoint }) => {
      const publicEndpoints = ["postLogin", "postForgotPassword"];
      const isPublicEndpoint = publicEndpoints.some((ep) =>
        endpoint.includes(ep)
      );

      if (!isPublicEndpoint) {
        const token = (getState() as RootState).auth.token?.AccessToken;
        if (token) {
          headers.set("Authorization", `Token ${token}`);
        }
      }
      return headers;
    },
  });

  const result = await baseQuery(args, api, extraOptions);

  // 401 logout
  if (result?.error?.status === 401) {
    api.dispatch(logout());
  }

  return result;
};

// Define a service using a base URL and expected endpoints
export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),

  tagTypes: [
    "Home",
    "Projects",
    "Logs",
    "Auth",
    "Permissions",
    "Users",
    "Logistics",
    "Customers",
    "Sales",
    "Prospects",
    "CustomerSales",
    "Reminders",

    "Services",

    "CustomerInstallments",
    "CustomerSiteVisits",
    "CustomerCategories",
    "CustomerBookings",
    "Complaints",
    "Engagements",
    "Feedback",
    "Flags",
    "Teams",
    "DashboardStats",
    "UnallocatedLeads",
    "TeleteamSales",
    "DigitalDashboard",
    "TelemarketingCustomers",
    "AllSales",
    "AllProspects",
    "TeleteamDashboard",
    "HROfficeDashboard",
    "Notifications",
    "DigitalCustomers",
    "DigitalAllProspects",
    "DigitalSales",
    "DigitalTeam",
    "DigitalAllSales",
    "DigitalUnallocatedLeads",
    "Notes",
    "Tickets",
    'MainDashboard',
    'Todos',
    "SearchCustomers",

    // Offer Letter tags
    'OfferLetter',
    'OfferLetterIndividual',
    'OfferLetterCompany',
    'OfferLetterGroup',
    'OfferLetterPartner',
    'OfferLetterNextOfKin',
    'OfferLetterPayments',
    'OfferLetterPricing',
    'OfferLetterTerms',
    'OfferLetterReview',
    'PlotPaymentOptions',
  ],
});
