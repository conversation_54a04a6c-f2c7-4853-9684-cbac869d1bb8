import {
  BaseQuery<PERSON>pi,
  create<PERSON><PERSON>,
  Fetch<PERSON>rgs,
  fetchBaseQuery,
} from "@reduxjs/toolkit/query/react";
import { RootState } from "./store";
import { logout } from "./authSlice";

const environment = import.meta.env.VITE_PROD;

const url =
  environment == "production"
    ? import.meta.env.VITE_API_URL_PROD
    : import.meta.env.VITE_API_URL_DEV;

const rawBaseQuery = fetchBaseQuery({
  baseUrl: url,
  prepareHeaders: (headers, { getState, endpoint }) => {
    const publicEndpoints = ["postLogin", "postForgotPassword"];
    const isPublicEndpoint = publicEndpoints.some((ep) =>
      endpoint.includes(ep)
    );

    if (!isPublicEndpoint) {
      const token = (getState() as RootState).auth.token?.AccessToken;
      if (token) {
        headers.set("Authorization", `Token ${token}`);
      }
    }
    return headers;
  },
  responseHandler: (response) => response.blob(), // ✅ This stays here
});

const baseQueryWithReauth = async (
  args: string | FetchArgs,
  api: BaseQueryApi,
  extraOptions: Record<string, any>
) => {
  const result = await rawBaseQuery(args, api, extraOptions);

  if (result?.error?.status === 401) {
    api.dispatch(logout());
  }

  return result;
};

export { baseQueryWithReauth };

// Define a service using a base URL and expected endpoints
// export const blobSlice = createApi({
//   reducerPath: "blobApi",
//   baseQuery: baseQueryWithReauth,
//   endpoints: () => ({}),

//   tagTypes: ["Projects"],
// });
