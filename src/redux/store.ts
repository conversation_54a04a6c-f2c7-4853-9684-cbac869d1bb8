import { configureStore } from "@reduxjs/toolkit";
import { apiSlice } from "./apiSlice";
import { persistedAuthReducer } from "./authSlice";
import {
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
  persistStore,
} from "redux-persist";
import { aiChatSlice } from "./aiChatSlice";
import { downloadSlice } from "./slices/downloadSlice";

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    [downloadSlice.reducerPath]: downloadSlice.reducer,
    auth: persistedAuthReducer,
    [aiChatSlice.reducerPath]: aiChatSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat([apiSlice.middleware, aiChatSlice.middleware]),
  devTools: true,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
