import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { RootState } from './store';

export interface UserDetails {
  email: string | null;
  fullnames: string | null;
  employee_no: string | null;
  department: string | null;
  office: string | null;
  team: string | null;
  user_group: string | null;
  diaspora_region: string | null;
  user_permissions: any[] | null; // More flexible type for permissions
}

interface Token {
  AccessToken: string | null;
  RefreshToken: string | null;
}

interface AuthState {
  token: Token | null;
  user_details: UserDetails | null;
}

const initialState: AuthState = {
  token: null,
  user_details: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<{ token: Token; user_details: UserDetails }>) => {
      state.token = action.payload.token;
      state.user_details = action.payload.user_details;
    },
    logout: (state) => {
      state.token = null;
      state.user_details = null;
    },
  },
});

const persistConfig = {
  key: 'auth',
  storage,
  whitelist: ['token', 'user_details'],
};

export const persistedAuthReducer = persistReducer(persistConfig, authSlice.reducer);
export const { setCredentials, logout } = authSlice.actions;

// Selectors
export const selectCurrentUserDetails = (state: RootState) => state.auth.user_details;
export const selectCurrentToken = (state: RootState) => state.auth.token;
