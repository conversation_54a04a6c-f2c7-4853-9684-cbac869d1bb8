import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

const url = import.meta.env.VITE_AI_CHAT_BASE_URL

export const aiChatSlice = createApi({
  reducerPath: 'ai',
  baseQuery: fetchBaseQuery({
    baseUrl: url,
    // Add authentication headers
    prepareHeaders: (headers) => {
      headers.set('content-type', 'application/json')
      return headers
    },
  }),
  // Tags for cache invalidation
  tagTypes: ['Chat'],
  endpoints: () => ({ })

})