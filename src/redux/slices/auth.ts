import { apiSlice } from "../apiSlice";
import { logout, setCredentials } from "../authSlice";

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    postLogin: builder.mutation({
      query: (body) => ({
        url: "/users/login",
        method: "POST",
        body: body,
      }),
      // Automatically store the token when login is successful
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          const { email, fullnames, employee_no, department, office, team, user_group, user_permissions, AccessToken, RefreshToken } = data;
          dispatch(setCredentials({
            token: { AccessToken, RefreshToken },
            user_details: { email, fullnames, employee_no, department, office, team, user_group, user_permissions }
          }));
        } catch (error) {
          console.log(error)
          // Handle error if needed
        }
      },
    }),
    refreshUserData: builder.query({
      query: () => ({
        url: "/auth/me",
        method: "GET",
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          const { email, fullnames, employee_no, department, office, team, user_group, user_permissions, AccessToken, RefreshToken } = data;
          
          // Only update user details, keep the existing token
          dispatch(setCredentials({
            token: { AccessToken, RefreshToken },
            user_details: { email, fullnames, employee_no, department, office, team, user_group, user_permissions }
          }));
        } catch (error) {
          console.error("Error refreshing user data:", error);
        }
      },
    }),
    postLogout: builder.mutation({
      query: (body) => ({
        url: '/users/logout',
        method: 'POST',
        body: body,
      }),
      // Clear credentials on logout
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          dispatch(logout());
        } catch {
          dispatch(logout());
        }
      },
    }),
  }),
});

export const {
  usePostLoginMutation,
  usePostForgotPasswordMutation,
  usePostLogoutMutation,
  useRefreshUserDataQuery,
  useLazyRefreshUserDataQuery,
} = authApiSlice;
