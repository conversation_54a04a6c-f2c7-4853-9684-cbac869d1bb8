import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";


export interface CommissionHeader {
  id: number;
  period_start_date: string;
  period_end_date: string;
  role: string;
  Deposit_amount: number;
  deposit_perc: number;
  deposit_commission: number;
  installment_amount: number;
  installment_perc: number;
  installment_commission: number;
  Total_commission: number;
  Tl_gained_comm_from_members: number;
  rm_achieved_MIB: number;
  rm_commission_rate: number;
  rm_commission_amount: number;
  commisison_payable_TL: number;
  emp_no_id: string;
}

export interface CommissionLine {
  id: number;
  period_start_date: string;
  period_end_date: string;
  transaction_date: string;
  plot_number: string;
  new_deposits_collected: number;
  installments_collected: number;
  marketer_no_id: string;
}

export interface CommissionHeaderApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: CommissionHeader[];
}

export interface CommissionLineApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: CommissionLine[];
}


export interface HOSGMTarget {
  line_no: number;
  marketer_no: string;
  marketer_name: string;
  title: string;
  status: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: string;
  daily_target: string;
  MIB_achieved: string;
  MIB_Perfomance: string;
  commission_rate: string;
  commission_payable: string;
}

export interface HOSGMTargetApiResponse {
  data: any;
  count: number;
  next: string | null;
  previous: string | null;
  results: HOSGMTarget[];
}

export interface MarketerTarget {
  MIB_Perfomance: any;
  marketer_name: string;
  marketer_no: string;
}

export interface MarketerTargetApiResponse {
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: MarketerTarget[];
  };
  message?: string;
}

export interface TeamReport {
  line_no: number;
  team: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: string;
  daily_target: string;
  MIB_achieved: string;
  MIB_Perfomance: string;
}

export interface TeamReportApiResponse {
  message?: string;
  data?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: TeamReport[];
  };
}

// API Slice
export const commissionApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCommissionHeaders: builder.query<CommissionHeaderApiResponse, { MARKETING_PERIOD: string; MARKETER_EMPLOYEE_NO: string; page?: number; page_size?: number }>({
      query: ({ MARKETING_PERIOD, MARKETER_EMPLOYEE_NO, page = 1, page_size = 20 }) => ({
        url: `/commission-headers-report/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          MARKETING_PERIOD,
          MARKETER_EMPLOYEE_NO,
          page,
          page_size,
        },
      }),
    }),
    getCommissionLines: builder.query<CommissionLineApiResponse, { MARKETING_PERIOD: string; MARKETER_EMPLOYEE_NO: string; page?: number; page_size?: number }>({
      query: ({ MARKETING_PERIOD, MARKETER_EMPLOYEE_NO, page = 1, page_size = 20 }) => ({
        url: `/commission-lines-report/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          MARKETING_PERIOD,
          MARKETER_EMPLOYEE_NO,
          page,
          page_size,
        },
      }),
    }),
    getHOSGMTargets: builder.query<
      HOSGMTargetApiResponse,
      {
        marketer_no?: string;
        status?: string;
        period_start_date?: string;
        period_end_date?: string;
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
      }
    >({
      query: ({
        marketer_no,
        status,
        period_start_date,
        period_end_date,
        search,
        ordering,
        page = 1,
        page_size = 20,
      }) => ({
        url: `/hosgm-targets/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          ...(marketer_no ? { marketer_no } : {}),
          ...(status ? { status } : {}),
          ...(period_start_date ? { period_start_date } : {}),
          ...(period_end_date ? { period_end_date } : {}),
          ...(search ? { search } : {}),
          ...(ordering ? { ordering } : {}),
          page,
          page_size,
        },
      }),
    }),
    getMarketerTargets: builder.query<
      MarketerTargetApiResponse,
      {
        marketer_no?: string;
        period_start_date?: string;
        period_end_date?: string;
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
      }
    >({
      query: ({
        marketer_no,
        period_start_date,
        period_end_date,
        search,
        ordering,
        page = 1,
        page_size = 20,
      }) => ({
        url: `/marketers-targets/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          ...(marketer_no ? { marketer_no } : {}),
          ...(period_start_date ? { period_start_date } : {}),
          ...(period_end_date ? { period_end_date } : {}),
          ...(search ? { search } : {}),
          ...(ordering ? { ordering } : {}),
          page,
          page_size,
        },
      }),
    }),
    getTeamsReport: builder.query<
      TeamReportApiResponse,
      {
        team?: string;
        period_start_date?: string;
        period_end_date?: string;
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
      }
    >({
      query: ({
        team,
        period_start_date,
        period_end_date,
        search,
        ordering,
        page = 1,
        page_size = 20,
      }) => ({
        url: `/team-targets/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          ...(team ? { team } : {}),
          ...(period_start_date ? { period_start_date } : {}),
          ...(period_end_date ? { period_end_date } : {}),
          ...(search ? { search } : {}),
          ...(ordering ? { ordering } : {}),
          page,
          page_size,
        },
      }),
    }),
  }),
});

export const {
  useGetCommissionHeadersQuery,
  useGetCommissionLinesQuery,
  useGetHOSGMTargetsQuery,
  useGetMarketerTargetsQuery,
  useGetTeamsReportQuery,
} = commissionApiSlice;
