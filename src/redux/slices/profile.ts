import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { PortfolioHeadersApiResponse } from "@/types/marketer";

export const marketerProfileApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAllProfile: builder.query({
      query: (params) => ({
        url: "/digital-all-prospects/",
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
    }),

    getPortfolioHeaders: builder.query<PortfolioHeadersApiResponse, any>({
      query: (params) => ({
        url: "portfolio-headers",
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
    }),

    getPortfolioLines: builder.query({
      query: (params) => ({
        url: "/portfolio-lines", // Replace with the actual endpoint URL
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
    }),
  }),
});

// Export hooks for the defined endpoints
export const {
  useGetAllProfileQuery,
  useGetPortfolioHeadersQuery,
  useGetPortfolioLinesQuery,
} = marketerProfileApiSlice;