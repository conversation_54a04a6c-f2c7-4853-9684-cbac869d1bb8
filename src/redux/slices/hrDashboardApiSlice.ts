import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { MarketerReportsApiResponse, MarketerPeriodsQueryParams, AllMarketingPeriodsResponse, MarketerReport } from "@/types/marketer";

// Teams Performance API Types
export interface TeamPerformanceData {
  progress: number;
  total_marketers: number;
  current_period: any;
  line_no: number;
  team: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: number;
  daily_target: number;
  MIB_achieved: number;
  MIB_Perfomance: number; 
}

export interface TeamsPerformanceApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: TeamPerformanceData[];
}

/**
 * Define the expected response structure for the HQ Karen API.
 * Uses the same structure as MarketerReportsApiResponse.
 */
export interface HQKarenApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: MarketerReport[];
}

export interface PeriodMarketersResponse {
  count: number;
  total_pages: number;
  current_page: number;
  page_size: number;
  results: {
    fullnames: string;
    employee_no: string;
    team: string;
    target: number;
    achieved: number;
    progress: number;
  }[];
}

export const hrDashboardApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
   
    gethrDashboard: builder.query({
      query: () => ({
        url: "/hr-office-cards/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (response: any) => {
        console.log("Period Dashboard API Response:", response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error("Period Dashboard API Error:", error);
        return error;
      },
      providesTags: ["HROfficeDashboard"],
    }),

    getTeamsPerformance: builder.query<TeamsPerformanceApiResponse, { page?: number; page_size?: number; period?: string; period_start?: string; period_end?: string; office?: string }>(
      {
        query: (params = {}) => {
          const { page, page_size, period, period_start, period_end, office } = params as any;
          const queryParams: Record<string, any> = {
            OFFICE: office ?? "ALL",
          };

          // Prefer explicit start/end dates when provided; otherwise fall back to MARKETING_PERIOD
          if (period_start && period_end) {
            queryParams.START_DATE = period_start;
            queryParams.END_DATE = period_end;
          } else {
            queryParams.MARKETING_PERIOD = period ?? "ALL";
          }

          if (page !== undefined) queryParams.page = page;
          if (page_size !== undefined) queryParams.page_size = page_size;

          return {
            url: "/overall-teams-performance/",
            method: "GET",
            params: queryParams,
            headers: noAuthHeader(),
          };
        },
        transformResponse: (response: TeamsPerformanceApiResponse) => {
          console.log("Teams Performance API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Teams Performance API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),

    getHQKaren: builder.query<HQKarenApiResponse, { page?: number; page_size?: number; period?: string; period_start?: string; period_end?: string; office?: string }>(
      {
        query: (params = {}) => {
          const { page, page_size, period, period_start, period_end, office } = params as any;
          const queryParams: Record<string, any> = {
            OFFICE: office ?? "ALL",
          };

          // Prefer explicit start/end dates when provided; otherwise fall back to MARKETING_PERIOD
          if (period_start && period_end) {
            queryParams.START_DATE = period_start;
            queryParams.END_DATE = period_end;
          } else {
            queryParams.MARKETING_PERIOD = period ?? "ALL";
          }

          if (page !== undefined) queryParams.page = page;
          if (page_size !== undefined) queryParams.page_size = page_size;

          return {
            url: "/overall-hq-karen-performance/",
            method: "GET",
            params: queryParams,
            headers: noAuthHeader(),
          };
        },
        transformResponse: (response: HQKarenApiResponse) => {
          console.log("HQ Karen API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("HQ Karen API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),


    getPeriods: builder.query<AllMarketingPeriodsResponse, { page?: number; page_size?: number }>(
      {
        query: (params = {}) => ({
          url: "/marketing-period/",
          method: "GET",
          headers: noAuthHeader(),
          params,
        }),
        transformResponse: (response: AllMarketingPeriodsResponse) => {
          console.log("Marketing Periods API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Marketing Periods API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),

    getAllMarketers: builder.query<MarketerReportsApiResponse, { page?: number; page_size?: number }>(
      {
        query: (params = {}) => ({
          url: "/marketers-performance/",
          method: "GET",
          headers: noAuthHeader(),
          params,
        }),
        transformResponse: (response: MarketerReportsApiResponse) => {
          console.log("All maerketers API Responce:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("All marketers API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }),



    //Get all Office Periods
    getOfficePeriods: builder.query({
      query: ({ office }) => ({
        url: `/hr-office-periods/?office=${office}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (response: any) => {
        console.log("Office Periods API Response:", response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error("Office Periods API Error:", error);
        return error;
      },
      providesTags: ["HROfficeDashboard"],
    }),

    // Get Period Marketers
    getPeriodMarketers: builder.query<PeriodMarketersResponse, { office: string; start_date: string; end_date: string; search?: string; page?: number; page_size?: number }>(
      {
        query: (params) => ({
          url: "/hr-period-marketers/",
          method: "GET",
          headers: noAuthHeader(),
          params,
        }),
        transformResponse: (response: PeriodMarketersResponse) => {
          console.log("Period Marketers API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Period Marketers API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),

    // Get all marketing periods
    getAllMarketingPeriods: builder.query<AllMarketingPeriodsResponse, { page?: number; page_size?: number }>(
      {
        query: (params = {}) => ({
          url: "/marketing-period/",
          method: "GET",
          headers: noAuthHeader(),
          params,
        }),
        transformResponse: (response: AllMarketingPeriodsResponse) => {
          console.log("All Marketing Periods API Response:", response);
          // Return the response exactly as it comes from the API
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("All Marketing Periods API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),

    // Get all Marketers Periods (Legacy - using START_DATE/END_DATE)
    getMarketerPeriods: builder.query<MarketerReportsApiResponse, MarketerPeriodsQueryParams>(
      {
        query: ({ office, start_date, end_date }) => {
          const params: Record<string, any> = {
            OFFICE: office,
            START_DATE: start_date,
            END_DATE: end_date,
            MARKETER_EMPLOYEE_NO: "ALL",
          };
          return {
            url: `overall-marketer-performance-in-a-period/`,
            method: "GET",
            headers: noAuthHeader(),
            params,
          };
        },
        transformResponse: (response: MarketerReportsApiResponse) => {
          console.log("Marketer Periods API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Marketer Periods API Error:", error);
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),

    // Get Marketer Performance by Marketing Period (Correct API parameters)
    getMarketerPerformanceByPeriod: builder.query<MarketerReportsApiResponse, {
      marketing_period?: string;
      marketer_employee_no?: string;
      page?: number;
      page_size?: number;
    }>(
      {
        query: (params = {}) => {
          const { marketing_period, marketer_employee_no, page, page_size } = params;
          const queryParams: Record<string, any> = {
            MARKETING_PERIOD: marketing_period ?? "ALL",
            MARKETER_EMPLOYEE_NO: marketer_employee_no ?? "ALL",
          };

          if (page !== undefined) queryParams.page = page;
          if (page_size !== undefined) queryParams.page_size = page_size;

          console.log("API Request URL:", `overall-marketer-performance-in-a-period/`);
          console.log("API Request Params:", queryParams);

          return {
            url: `overall-marketer-performance-in-a-period/`,
            method: "GET",
            headers: noAuthHeader(),
            params: queryParams,
          };
        },
        transformResponse: (response: MarketerReportsApiResponse) => {
          console.log("Marketer Performance by Period API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Marketer Performance by Period API Error:", error);
          console.error("Error details:", JSON.stringify(error, null, 2));
          return error;
        },
        providesTags: ["HROfficeDashboard"],
      }
    ),

    // Get Marketer Performance (Individual Marketer Commissions)
    getMarketerPerformance: builder.query<MarketerReportsApiResponse, {
      marketer_employee_no?: string;
      page?: number;
      page_size?: number;
    }>(
      {
        query: (params = {}) => {
          const { marketer_employee_no, page, page_size } = params;
          const queryParams: Record<string, any> = {
            MARKETER_EMPLOYEE_NO: marketer_employee_no ?? "ALL",
          };
          if (page !== undefined) queryParams.page = page;
          if (page_size !== undefined) queryParams.page_size = page_size;

          return {
            url: "/marketers-performance/",
            method: "GET",
            params: queryParams,
            headers: noAuthHeader(),
          };
        },
        transformResponse: (response: MarketerReportsApiResponse) => {
          console.log("Marketer Performance API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Marketer Performance API Error:", error);
          return error;
        },
        
      }
    ),

    // Get Marketing Periods
    getMarketingPeriods: builder.query<AllMarketingPeriodsResponse, void>(
      {
        query: () => ({
          url: "/marketing-period/",
          method: "GET",
          headers: noAuthHeader(),
        }),
        transformResponse: (response: AllMarketingPeriodsResponse) => {
          console.log("Marketing Periods API Response:", response);
          return response;
        },
        transformErrorResponse: (error) => {
          console.error("Marketing Periods API Error:", error);
          return error;
        },
        
      }
    ),
  }),
});

export const {
    useGethrDashboardQuery,
    useGetOfficePeriodsQuery,
    useGetMarketerPeriodsQuery,
    useGetMarketerPerformanceByPeriodQuery,
    useGetTeamsPerformanceQuery,
    useGetAllMarketingPeriodsQuery,
    useGetPeriodsQuery,
    useGetAllMarketersQuery,
    useGetHQKarenQuery,
    useGetMarketerPerformanceQuery,
    useGetMarketingPeriodsQuery,
    useGetPeriodMarketersQuery,
} = hrDashboardApiSlice;
