
import { apiSlice } from "../apiSlice"

export const cashoncashApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getAllcashoncashSales: builder.query({
            query: (params) => ({
                url: `/cash-on-cash`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
        getcashoncash: builder.query({
            query: ({id:id,params:params}) => ({
                url: `/cash-on-cash/${id}`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
        
         
        
        
    })
});

export const {
    useGetAllcashoncashSalesQuery,
    useGetcashoncashQuery,
    
} = cashoncashApiSlice;
