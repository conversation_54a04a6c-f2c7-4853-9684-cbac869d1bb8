import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const telemarketingApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get teleteam dashboard stats
    getDigitalDashboard: builder.query({
      query: () => ({
        url: "/digital-dashboard/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (response: any) => {
        console.log("Digital Team API Response:", response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error("Digital Dashboard API Error:", error);
        return error;
      },
      providesTags: ["DigitalDashboard"],
    }),

    // Get all customers
    // getAllCustomers: builder.query({
    //   query: (params) => ({
    //     url: "/digital-all-customers/",
    //     method: "GET",
    //     params: params,
    //     headers: noAuthHeader(),
    //   }),
    //   providesTags: ["DigitalCustomers"],
    // }),

    // Get all prospects
    getAllProspects: builder.query({
      query: (params) => ({
        url: "/digital-all-prospects/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["DigitalAllProspects"],
    }),

    // Get all sales
    getAllSales: builder.query({
      query: (params) => ({
        url: "/digital-all-sales/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["DigitalSales"],
    }),

    // Get telemarketing customers
    getDigitalCustomers: builder.query({
      query: (params) => ({
        url: "/digital-customers/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["DigitalCustomers"],
    }),

    

    // Get teleteam sales
    getDigitalSales: builder.query({
      query: (params) => ({
        url: "/teleteam-sales/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["DigitalSales"],
    }),

    // Get unallocated leads
    getDigitalUnallocatedLeads: builder.query({
      query: (params) => ({
        url: "/digital-unallocated-leads/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["DigitalUnallocatedLeads"],
    }),
  }),
});

export const {
  useGetDigitalDashboardQuery,
  // useGetAllCustomersQuery,
  useGetAllProspectsQuery,
  useGetAllSalesQuery,
  useGetDigitalCustomersQuery,
  useGetDigitalSalesQuery,
 
  useGetDigitalUnallocatedLeadsQuery,
} = telemarketingApiSlice;