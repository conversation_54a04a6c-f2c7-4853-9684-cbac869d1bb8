import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { ProspectsApiResponse, ProspectTypes } from "@/types/prospects";
import { getProspectApiParams } from "@/utils/prospectPermissions";
import { RootState } from "../store";

export const propectsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getLeadSourceCategories: builder.query({
      query: (params) => ({
        url: "/leads/lead-source-category",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Prospects"],
    }),

    getLeadSource: builder.query({
      query: (params) => ({
        url: "/leads/lead-source",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Prospects"],
    }),

    getPropects: builder.query<ProspectsApiResponse, any>({
      query: (params) => ({
        url: "/leads/prospects",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformErrorResponse: (error) => {
        return error;
      },
      providesTags: ["Prospects"],
    }),

    addProspect: builder.mutation<ProspectTypes, Partial<ProspectTypes>>({
      query: (data) => ({
        url: "/leads/prospects",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Prospects"],
    }),

    updateProspect: builder.mutation<
      ProspectTypes,
      { id: any } & Partial<ProspectTypes>
    >({
      query: ({ id, ...data }) => ({
        url: `/leads/prospects/${id}`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Prospects"],
    }),

    getProspectDetails: builder.query<ProspectTypes, any>({
      query: (id) => ({
        url: `/leads/prospects/${id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Prospects"],
    }),

    addDiaporaTrip: builder.mutation({
      query: (body) => ({
        url: "/leads/diaspora-trips",
        method: "POST",
        body: body,
        // headers: noAuthHeader(),
      }),
      invalidatesTags: ["Prospects"],
    }),

    getDiaporaTrips: builder.query({
      query: (params) => ({
        url: "/leads/diaspora-trips",
        method: "GET",
        params: params,
        // headers: noAuthHeader(),
      }),
      providesTags: ["Prospects"],
    }),

    updateDiaporaTrip: builder.mutation({
      query: (data) => ({
        url: "/leads/diaspora-trips/" + data?.id,
        method: "PATCH",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Prospects"],
    }),

    addDiaporaTripMember: builder.mutation({
      query: (data) => ({
        url: "/leads/diaspora-marketer-trips",
        method: "POST",
        body: data,
        // headers: noAuthHeader(),
      }),
      invalidatesTags: ["Prospects"],
    }),

    getLeadSourceSubCategories: builder.query({
      query: (params) => ({
        url: "/leads/lead-source-sub-category",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Prospects"],
    }),

    getDiasporaRegions: builder.query({
      query: (params) => ({
        url: "/leads/diaspora-regions",
        method: "GET",
        params: params,
      }),
      providesTags: ["Prospects"],
    }),

    generateLeadFormLink: builder.mutation({
      query: (data) => ({
        url: "/leads/generate-lead-form-link",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
    }),

    addLeadFromLink: builder.mutation({
      query: (data) => ({
        url: "/leads/add-lead-form-link",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
    }),
  }),
});

export const {
  useGetLeadSourceCategoriesQuery,
  useGetLeadSourceQuery,
  useLazyGetLeadSourceQuery,
  useGetPropectsQuery,
  useLazyGetPropectsQuery,
  useAddProspectMutation,
  useUpdateProspectMutation,
  useAddDiaporaTripMutation,
  useGetDiaporaTripsQuery,
  useLazyGetDiaporaTripsQuery,
  useUpdateDiaporaTripMutation,
  useGetProspectDetailsQuery,
  useGetLeadSourceSubCategoriesQuery,
  useAddDiaporaTripMemberMutation,
  useGenerateLeadFormLinkMutation,
  useAddLeadFromLinkMutation,
  useGetDiasporaRegionsQuery,
  useLazyGetDiasporaRegionsQuery,
} = propectsApiSlice;
