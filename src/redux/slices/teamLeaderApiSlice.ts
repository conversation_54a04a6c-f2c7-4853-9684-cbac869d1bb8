import { apiSlice } from "../apiSlice";
import { noAuthHeader } from "@/utils/header";

// Member performance within a team for a marketing period
export interface TeamMemberPerformance {
  employee_no: string;
  fullnames: string;
  period_start_date: string; // e.g. "2025-08-21"
  period_end_date: string;   // e.g. "2025-09-20"
  monthly_target: number;
  daily_target: number;
  MIB_achieved: number;
  MIB_Perfomance: number; // percentage
}

// Team leader + team aggregate with members
export interface TeamTargetItem {
  line_no: number;
  team: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: number;
  daily_target: number;
  MIB_achieved: number;
  MIB_Perfomance: number;
  leader_employee_no: string; // e.g. "OL/HR/256"
  leader_fullnames: string;
  members: TeamMemberPerformance[];
}

export const teamLeaderApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // GET /team-marketer_targets/
    getTeamMarketerTargets: builder.query<
      TeamTargetItem[],
      { leader_employee_no?: string; marketing_period?: string }
    >({
      query: (params = {}) => {
        const { leader_employee_no, marketing_period } = params;
        const queryParams: Record<string, any> = {
          TEAM_LEADER_EMPLOYEE_NO: leader_employee_no ?? "ALL",
          MARKETING_PERIOD: marketing_period ?? "ALL",
        };
        return {
          url: "/team-marketer_targets/",
          method: "GET",
          headers: noAuthHeader(),
          params: queryParams,
        };
      },
      transformResponse: (response: TeamTargetItem[]) => {
        console.log("Team Leader Targets API Response:", response);
        return response ?? [];
      },
      transformErrorResponse: (error) => {
        console.error("Team Leader Targets API Error:", error);
        return error;
      },
      providesTags: ["HROfficeDashboard"],
    }),
  }),
});

export const { useGetTeamMarketerTargetsQuery } = teamLeaderApiSlice;