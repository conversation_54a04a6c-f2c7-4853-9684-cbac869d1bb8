import { apiSlice } from "../apiSlice";
import { noAuthHeader } from "@/utils/header";

// =====================
// Types (migrated from services)
// =====================
export interface GenderDistribution {
  null?: number;
  None_perc?: string;
  M?: number;
  M_perc?: string;
  F?: number;
  F_perc?: string;
}

export interface DepartmentOverview {
  department: string;
  employees_count: number;
  active_employees_count: number;
  inactive_employees_count: number;
}

export interface DepartmentGenderDistribution {
  department: string;
  gender_distribution: Array<{
    gender: string | null;
    total_gender_count: number;
    gender_perc: string;
  }>;
}

export interface DirectorsAdminStats {
  departments_count: number;
  employees_count: number;
  gender_distribution: GenderDistribution;
  marital_distribution: Record<string, any>;
  department_overview: DepartmentOverview[];
  departments_gender_distribution: DepartmentGenderDistribution[];
}

export interface DirectorsAdminStatsResponse {
  statistics: DirectorsAdminStats;
  success: boolean;
}

// =====================
// Performance Stats
// =====================
export interface LeavesStats {
  leave_status_count: any[];
  leave_types_count: any[];
}

// Using exact keys from API payload
export interface DirectorsPerformanceTeamMetrics {
  Installments_Due_Today: number;
  Overdue_Collections_Collected: number;
  Overdue_Collections: number;
  ALL_Overdue_Collections: number;
  Sales_Deposits_Below_Threshold: number;
  Overdue_Below_Threshold: number;
  Expected_Monthly_Installments: number;
  EXPECTED_Monthly_installments_collected: number;
}

export interface DirectorsPerformanceStats {
  leaves_stats: LeavesStats;
  teams_performance: DirectorsPerformanceTeamMetrics[];
}

export interface DirectorsPerformanceStatsResponse {
  statistics: DirectorsPerformanceStats;
  success: boolean;
}

export const directorsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getDirectorsAdminStats: builder.query<DirectorsAdminStatsResponse, void>({
      query: () => ({
        url: "/directors-admin-stats/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["DashboardStats"],
    }),

    getDirectorsPerformanceStats: builder.query<DirectorsPerformanceStatsResponse, void>({
      query: () => ({
        url: "/directors-perform-stats/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["DashboardStats"],
    }),
  }),
});

export const {
  useGetDirectorsAdminStatsQuery,
  useGetDirectorsPerformanceStatsQuery,
} = directorsApiSlice;