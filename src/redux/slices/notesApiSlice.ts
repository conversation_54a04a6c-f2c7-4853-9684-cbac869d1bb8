import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const notesApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getNotes: builder.query({
      query: (params) => ({
        url: "/notifications/all-notes",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        // The API returns data nested under response.data
        const apiData = response.data || {};
        return {
          results: apiData.results || [],
          count: apiData.count || 0,
          current_page: apiData.current_page || 1,
          num_pages: apiData.num_pages || 1,
          total_data: apiData.count || 0,
          per_page:
            apiData.per_page ||
            Math.ceil((apiData.count || 0) / (apiData.num_pages || 1)),
        };
      },
      transformErrorResponse: (error) => {
        console.error("Notes API Error:", error);
        return error;
      },
      providesTags: ["Notes"],
    }),

    getNoteDetails: builder.query({
      query: (noteId) => ({
        url: `/notifications/all-notes/${noteId}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Notes"],
    }),

    createNote: builder.mutation({
      query: (data) => ({
        url: "/notifications/all-notes",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notes"],
    }),

    updateNote: builder.mutation({
      query: ({ noteId, data }) => ({
        url: `/notifications/all-notes/${noteId}`,
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notes"],
    }),

    partialUpdateNote: builder.mutation({
      query: (data) => ({
        url: `/notifications/all-notes/${data?.note_id}`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notes"],
    }),

    deleteNote: builder.mutation({
      query: (noteId) => ({
        url: `/notifications/all-notes/${noteId}`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Notes"],
    }),

    toggleNotePin: builder.mutation({
      query: ({ noteId, data }) => ({
        url: `/notifications/all-notes/${noteId}/toggle_pin`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notes"],
    }),
  }),
});

export const {
  useGetNotesQuery,
  useGetNoteDetailsQuery,
  useCreateNoteMutation,
  useUpdateNoteMutation,
  usePartialUpdateNoteMutation,
  useDeleteNoteMutation,
  useToggleNotePinMutation,

  useLazyGetNotesQuery,
} = notesApiSlice;
