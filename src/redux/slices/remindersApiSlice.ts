// import { contentHeader, noAuthHeader } from "@/utils/header";
// import { apiSlice } from "../apiSlice";

// export const remindersApiSlice = apiSlice.injectEndpoints({
//   endpoints: (builder) => ({
//     getReminders: builder.query({
//       query: (params) => ({
//         url: "/services/reminders/",
//         method: "GET",
//         params: params,
//         headers: noAuthHeader(),
//       }),
//       transformResponse: (response: any) => {
//         // The API returns data nested under response.data
//         const apiData = response.data || {};
//         const count = apiData.total_data || 0;
//         const num_pages = apiData.last_page || 1;
//         return {
//           results: apiData.results || [],
//           count,
//           current_page: apiData.current_page || 1,
//           num_pages,
//           total_data: count,
//           per_page: apiData.per_page || (count === 0 ? 0 : Math.ceil(count / num_pages)),
//         };
//       },
//       transformErrorResponse: (error) => {
//         console.error("Reminders API Error:", error);
//         return error;
//       },
//       providesTags: ["Reminders"],
//     }),

//     getRemindersByPeriod: builder.query({
//       query: (params) => ({
//         url: "/services/reminders/by_period/",
//         method: "GET",
//         params: params,
//         headers: noAuthHeader(),
//       }),
//       transformResponse: (response: any) => {
//         const apiData = response.data || {};
//         return {
//           results: apiData.results || [],
//           count: apiData.count || 0,
//           period: params?.period || 'today',
//         };
//       },
//       providesTags: ["Reminders"],
//     }),

//     getOverdueReminders: builder.query({
//       query: (params) => ({
//         url: "/services/reminders/overdue/",
//         method: "GET",
//         params: params,
//         headers: noAuthHeader(),
//       }),
//       transformResponse: (response: any) => {
//         const apiData = response.data || {};
//         return {
//           results: apiData.results || [],
//           count: apiData.count || 0,
//         };
//       },
//       providesTags: ["Reminders"],
//     }),

//     getUpcomingReminders: builder.query({
//       query: (params) => ({
//         url: "/services/reminders/upcoming/",
//         method: "GET",
//         params: params,
//         headers: noAuthHeader(),
//       }),
//       transformResponse: (response: any) => {
//         const apiData = response.data || {};
//         return {
//           results: apiData.results || [],
//           count: apiData.count || 0,
//         };
//       },
//       providesTags: ["Reminders"],
//     }),

//     getReminderStatistics: builder.query({
//       query: (params) => ({
//         url: "/services/reminders/statistics/",
//         method: "GET",
//         params: params,
//         headers: noAuthHeader(),
//       }),
//       providesTags: ["Reminders"],
//     }),

//     getReminderDetails: builder.query({
//       query: (reminderId) => ({
//         url: `/services/reminders/${reminderId}/`,
//         method: "GET",
//         headers: noAuthHeader(),
//       }),
//       providesTags: ["Reminders"],
//     }),

//     createReminder: builder.mutation({
//       query: (reminderData) => ({
//         url: "/services/reminders/",
//         method: "POST",
//         body: reminderData,
//         headers: contentHeader(),
//       }),
//       invalidatesTags: ["Reminders"],
//     }),

//     updateReminder: builder.mutation({
//       query: ({ reminderId, ...reminderData }) => ({
//         url: `/services/reminders/${reminderId}/`,
//         method: "PATCH",
//         body: reminderData,
//         headers: contentHeader(),
//       }),
//       invalidatesTags: ["Reminders"],
//     }),

//     deleteReminder: builder.mutation({
//       query: (reminderId) => ({
//         url: `/services/reminders/${reminderId}/`,
//         method: "DELETE",
//         headers: noAuthHeader(),
//       }),
//       invalidatesTags: ["Reminders"],
//     }),

//     completeReminder: builder.mutation({
//       query: (reminderId) => ({
//         url: `/services/reminders/${reminderId}/complete/`,
//         method: "PATCH",
//         headers: contentHeader(),
//       }),
//       invalidatesTags: ["Reminders"],
//     }),

//     snoozeReminder: builder.mutation({
//       query: ({ reminderId, snoozeData }) => ({
//         url: `/services/reminders/${reminderId}/snooze/`,
//         method: "PATCH",
//         body: snoozeData,
//         headers: contentHeader(),
//       }),
//       invalidatesTags: ["Reminders"],
//     }),
//   }),
// });

// export const {
//   useGetRemindersQuery,
//   useGetRemindersByPeriodQuery,
//   useGetOverdueRemindersQuery,
//   useGetUpcomingRemindersQuery,
//   useGetReminderStatisticsQuery,
//   useGetReminderDetailsQuery,
//   useCreateReminderMutation,
//   useUpdateReminderMutation,
//   useDeleteReminderMutation,
//   useCompleteReminderMutation,
//   useSnoozeReminderMutation,
// } = remindersApiSlice;
