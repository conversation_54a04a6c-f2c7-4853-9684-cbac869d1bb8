import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const dashboardStatsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getDataDashboardStats: builder.query({
      query: () => ({
        url: "/data-dashboard-stats/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (response: any) => {
        console.log("Dashboard Stats API Response:", response);
        
        // The API returns the data directly in the response
        return {
          customers_count: response.customers_count || 0,
          leads_count: response.leads_count || 0,
          lead_sources_count: response.lead_sources_count || 0,
          lead_sources_subcategories_count: response.lead_sources_subcategories_count || 0,
          lead_sources_categories_count: response.lead_sources_categories_count || 0,
          total_sales: response.total_sales || 0
        };
      },
      transformErrorResponse: (error) => {
        console.error("Dashboard Stats API Error:", error);
        return error;
      },
      providesTags: ["DashboardStats"],
    }),
  }),
});

export const {
  useGetDataDashboardStatsQuery,
} = dashboardStatsApiSlice;