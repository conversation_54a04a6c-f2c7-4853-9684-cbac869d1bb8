import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { apiSlice } from '../apiSlice';

// api/projectsApiSlice.ts
export const projectsApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({        
        // Method 1: Add a separate endpoint for Excel download
        downloadExcel: builder.mutation({
            query: ({url:url,params:params}) => ({
                url: url,
                method: "GET",
                params: { ...params, page_size: 10000 }, // Get all data
            }),
            // Transform response to generate Excel file
            transformResponse: (response: ApiResponse) => {
                generateAndDownloadExcel(response.results, 'export-data.xlsx');
                return response;
            },
        }),
    }),
});

export const { useDownloadExcelMutation } = projectsApiSlice;

interface ApiResponse {
    count: number;
    total_pages: number;
    current_page: number;
    page_size: number;
    next: boolean;
    previous: boolean;
    results: any[];
}

// Utility function to generate and download Excel
export const generateAndDownloadExcel = (data: any[], filename: string = 'export-data.xlsx') => {
    if (!data || data.length === 0) {
        alert('No data to export');
        return;
    }

    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    
    // Convert data to worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // Auto-size columns (optional)
    const columnWidths = Object.keys(data[0] || {}).map(key => ({
        wch: Math.max(key.length, 15) // Minimum width of 15
    }));
    worksheet['!cols'] = columnWidths;
    
    // Add worksheet to workbook 
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Data Sheet');
    
    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
        bookType: 'xlsx', 
        type: 'array' 
    });
    
    // Create blob and download
    const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    saveAs(blob, filename);
};
