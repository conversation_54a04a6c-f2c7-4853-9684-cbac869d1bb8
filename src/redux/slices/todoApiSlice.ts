import { contentHeader, noAuth<PERSON>eader, noContentHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

// Define valid status options (adjust according to your API requirements)
const VALID_STATUSES = ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"];

export const todoListEndpoints = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getTodo: builder.query({
      query: (params) => ({
        url: "/todo/all-todo",
        method: "GET",
        params,
        headers: contentHeader(),
      }),
      providesTags: ["Todos"],
    }),

    createTodo: builder.mutation({
      query: (data) => {
        // Validate status before sending
        if (data.status && !VALID_STATUSES.includes(data.status)) {
          console.error("Invalid status value:", data.status);
          throw new Error(`Status must be one of: ${VALID_STATUSES.join(", ")}`);
        }

        console.log("📤 CREATE TODO Request Details:", {
          url: "/todo/all-todo",
          method: "POST",
          body: data,
          headers: contentHeader(),
          assigned_to: data.assigned_to,
          assigned_to_id: data.assigned_to_id,
          assigned_to_type: typeof data.assigned_to,
          is_employee_no: typeof data.assigned_to === "string" && data.assigned_to.includes("/"),
        });

        return {
          url: "/todo/all-todo",
          method: "POST",
          body: data,
          headers: contentHeader(),
        };
      },
      invalidatesTags: ["Todos"],
    }),

    updateTodo: builder.mutation({
      query: (data) => {
        // Validate status before sending
        if (data.status && !VALID_STATUSES.includes(data.status)) {
          console.error("Invalid status value:", data.status);
          throw new Error(`Status must be one of: ${VALID_STATUSES.join(", ")}`);
        }

        // Extract id from data
        const { todo_id, ...updateData } = data;

        if (!todo_id) {
          throw new Error("Todo ID is required for update");
        }

        console.log("PATCH Request Details:", {
          url: `/todo/all-todo/${todo_id}`,
          method: "PATCH",
          body: updateData,
          headers: contentHeader(),
        });

        return {
          url: `/todo/all-todo/${todo_id}`,
          method: "PATCH",
          body: updateData,
          headers: contentHeader(),
        };
      },
      invalidatesTags: ["Todos"],
    }),

    deleteTodo: builder.mutation({
      query: (data) => {
        const id = data.id || data;

        if (!id) {
          throw new Error("Todo ID is required for deletion");
        }

        console.log("DELETE Request Details:", {
          url: `/todo/all-todo/${id}`,
          method: "DELETE",
          headers: contentHeader(),
          id,
        });

        return {
          url: `/todo/all-todo/${id}`,
          method: "DELETE",
          headers: contentHeader(),
        };
      },
      invalidatesTags: ["Todos"],
    }),
  }),
});

export const {
  useCreateTodoMutation,
  useUpdateTodoMutation,
  useGetTodoQuery,
  useDeleteTodoMutation,
} = todoListEndpoints;