// use this slice for downloading blob file from backend
import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../blobSlice";

export const downloadSlice = createApi({
  reducerPath: "downloadApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Projects"],
  endpoints: (builder) => ({
    downloadProjectPlotsExcel: builder.query<Blob, { project_id: string }>({
      query: (params) => ({
        url: "/inventory/project-plots-report",
        method: "GET",
        params,
      }),
      providesTags: ["Projects"],
    }),
  }),
});

export const { useLazyDownloadProjectPlotsExcelQuery } = downloadSlice;
