import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const telemarketingApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get teleteam dashboard stats
    getTeleteamDashboard: builder.query({
      query: () => ({
        url: "/teleteam-dashboard/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (response: any) => {
        console.log("Teleteam Dashboard API Response:", response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error("Teleteam Dashboard API Error:", error);
        return error;
      },
      providesTags: ["TeleteamDashboard"],
    }),

    // Get all customers
    // getAllCustomers: builder.query({
    //   query: (params) => ({
    //     url: "/all-customers/",
    //     method: "GET",
    //     params: params,
    //     headers: noAuthHeader(),
    //   }),
    //   providesTags: ["Customers"],
    // }),

    // Get all prospects
    getAllProspects: builder.query({
      query: (params) => ({
        url: "/all-prospects/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["AllProspects"],
    }),

    // Get all sales
    getAllSales: builder.query({
      query: (params) => ({
        url: "/all-sales/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["AllSales"],
    }),

    // Get telemarketing customers
    getTelemarketingCustomers: builder.query({
      query: (params) => ({
        url: "/telemarketing-customers/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["TelemarketingCustomers"],
    }),

    // Get teleteam sales
    getTeleteamSales: builder.query({
      query: (params) => ({
        url: "/teleteam-sales/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["TeleteamSales"],
    }),

    // Get unallocated leads
    getUnallocatedLeads: builder.query({
      query: (params) => ({
        url: "/unallocated-leads/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["UnallocatedLeads"],
    }),
  }),
});

export const {
  useGetTeleteamDashboardQuery,
  // useGetAllCustomersQuery,
  useGetAllProspectsQuery,
  useGetAllSalesQuery,
  useGetTelemarketingCustomersQuery,
  useGetTeleteamSalesQuery,
  useGetUnallocatedLeadsQuery,
} = telemarketingApiSlice;