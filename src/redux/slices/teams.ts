import { apiSlice } from "../apiSlice";
import {
  CreditsTeamIndexResponse,
  CreditOfficerDetailsResponse,
} from "@/types/creditsTeam";

export const projectsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getDiasporaDashboard: builder.query({
      query: (params) => ({
        url: "/diaspora-index",
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),

    getDiasporaRegionsDashboard: builder.query({
      query: (params) => ({
        url: "/diaspora-region/",
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),

    getDiasporaRegionTripDashboard: builder.query({
      query: (params) => ({
        url: "/diaspora-region-lead-source/",
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),

    getAccountsDashboard: builder.query({
      query: (params) => ({
        url: `/accounts-index`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),
    getAccountsCreditsDashboard: builder.query({
      query: (params) => ({
        url: `/accounts-credits-team-index`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),
    getAccountsCreditTeamDetails: builder.query({
      query: (params) => ({
        url: `/accounts-credits-team-details`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),
    getGMKarenDashboard: builder.query({
      query: (params) => ({
        url: `/Global-hos-index`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),
    getHqHosDashboard: builder.query({
      query: (params) => ({
        url: `/hq-hos-index`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),
    getMarketerDashboard: builder.query({
      query: (params) => ({
        url: `/Marketer-index`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Teams"],
    }),

    // Credits Team Dashboard endpoints
    getCreditsTeamIndex: builder.query<CreditsTeamIndexResponse, void>({
      query: () => ({
        url: `/accounts-credits-team-index/`,
        method: "GET",
      }),
      providesTags: ["Teams"],
    }),

    getCreditsTeamDetails: builder.query<CreditOfficerDetailsResponse, string>({
      query: (Credit_officer_erp_id: string) => ({
        url: `/accounts-credits-team-details/`,
        method: "GET",
        params: { Credit_officer_erp_id },
      }),
      providesTags: ["Teams"],
    }),
  }),
});

export const {
  useGetAccountsDashboardQuery,
  useGetDiasporaDashboardQuery,
  useGetAccountsCreditsDashboardQuery,
  useGetAccountsCreditTeamDetailsQuery,
  useGetGMKarenDashboardQuery,
  useGetHqHosDashboardQuery,
  useGetMarketerDashboardQuery,
  useGetCreditsTeamIndexQuery,
  useGetCreditsTeamDetailsQuery,
  useGetDiasporaRegionsDashboardQuery,
  useGetDiasporaRegionTripDashboardQuery,
} = projectsApiSlice;
