import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const complaintsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getComplaints: builder.query({
      query: (params) => ({
        url: "/services/complaints/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        console.log("Complaints API Response:", response);

        // Transform the response to match expected structure
        return {
          results: response.results || [],
          count: response.count || 0,
          current_page: response.current_page || 1,
          num_pages: response.num_pages || 1,
          total_data: response.count || 0,
          per_page: Math.ceil(
            (response.count || 0) / (response.num_pages || 1)
          ),
        };
      },
      transformErrorResponse: (error) => {
        console.error("Complaints API Error:", error);
        return error;
      },
      providesTags: ["Complaints"],
    }),

    getComplaintDetails: builder.query({
      query: (complaintId) => ({
        url: `/services/complaints/${complaintId}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Complaints"],
    }),

    createComplaint: builder.mutation({
      query: (data) => ({
        url: "/services/complaints/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Complaints"],
    }),

    updateComplaint: builder.mutation({
      query: ({ complaintId, data }) => ({
        url: `/services/complaints/${complaintId}/`,
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Complaints"],
    }),

    partialUpdateComplaint: builder.mutation({
      query: ({ complaintId, data }) => ({
        url: `/services/complaints/${complaintId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Complaints"],
    }),

    deleteComplaint: builder.mutation({
      query: (complaintId) => ({
        url: `/services/complaints/${complaintId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Complaints"],
    }),
  }),
});

export const {
  useGetComplaintsQuery,
  useGetComplaintDetailsQuery,
  useCreateComplaintMutation,
  useUpdateComplaintMutation,
  usePartialUpdateComplaintMutation,
  useDeleteComplaintMutation,

  useLazyGetComplaintsQuery,
} = complaintsApiSlice;
