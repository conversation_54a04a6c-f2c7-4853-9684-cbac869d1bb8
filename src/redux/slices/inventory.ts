import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { InventoryApiResponse, PlotTypes, LegalDashboardFilters } from "@/types/inventory";

export const inventoryApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all plots with pagination and filters
    getPlots: builder.query<InventoryApiResponse, any>({
      query: (params) => ({
        url: "/inventory/plots",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response: InventoryApiResponse) => {
        console.log("Plots API Response:", response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error("Plots API Error:", error);
        return error;
      },
      providesTags: ["Inventory"],
    }),

    // Get plot by ID
    getPlotById: builder.query<PlotTypes, string>({
      query: (plotId) => ({
        url: `/inventory/plots/${plotId}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Inventory"],
    }),

    // Update plot status
    updatePlotStatus: builder.mutation<PlotTypes, { plotId: string; status: string }>({
      query: ({ plotId, status }) => ({
        url: `/inventory/plots/${plotId}/status`,
        method: "PATCH",
        body: { status },
        headers: contentHeader(),
      }),
      invalidatesTags: ["Inventory"],
    }),

    // Reserve plot
    reservePlot: builder.mutation<PlotTypes, { plotId: string; customerData: any }>({
      query: ({ plotId, customerData }) => ({
        url: `/inventory/plots/${plotId}/reserve`,
        method: "POST",
        body: customerData,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Inventory"],
    }),

    // Get plots by project
    getPlotsByProject: builder.query<InventoryApiResponse, { projectId: string; params?: any }>({
      query: ({ projectId, params }) => ({
        url: `/inventory/plots/project/${projectId}`,
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Inventory"],
    }),

    // Get plots statistics
    getPlotsStatistics: builder.query<any, void>({
      query: () => ({
        url: "/inventory/plots/statistics",
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Inventory"],
    }),

    // Search plots
    searchPlots: builder.query<InventoryApiResponse, { query: string; filters?: LegalDashboardFilters }>({
      query: ({ query, filters }) => ({
        url: "/inventory/plots/search",
        method: "GET",
        params: { q: query, ...filters },
        headers: noAuthHeader(),
      }),
      providesTags: ["Inventory"],
    }),

    // Get legal dashboard data
    getLegalDashboardData: builder.query<any, LegalDashboardFilters>({
      query: (filters) => ({
        url: "/legal/dashboard",
        method: "GET",
        params: filters,
        headers: noAuthHeader(),
      }),
      providesTags: ["Legal", "Inventory"],
    }),

    // Update legal status
    updateLegalStatus: builder.mutation<any, { plotId: string; legalStatus: string; notes?: string }>({
      query: ({ plotId, legalStatus, notes }) => ({
        url: `/legal/plots/${plotId}/status`,
        method: "PATCH",
        body: { legal_status: legalStatus, notes },
        headers: contentHeader(),
      }),
      invalidatesTags: ["Legal", "Inventory"],
    }),

    // Approve plot transfer
    approvePlotTransfer: builder.mutation<any, { plotId: string; approvalData: any }>({
      query: ({ plotId, approvalData }) => ({
        url: `/legal/plots/${plotId}/approve`,
        method: "POST",
        body: approvalData,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Legal", "Inventory"],
    }),
  }),
});

export const {
  useGetPlotsQuery,
  useGetPlotByIdQuery,
  useUpdatePlotStatusMutation,
  useReservePlotMutation,
  useGetPlotsByProjectQuery,
  useGetPlotsStatisticsQuery,
  useSearchPlotsQuery,
  useGetLegalDashboardDataQuery,
  useUpdateLegalStatusMutation,
  useApprovePlotTransferMutation,
} = inventoryApiSlice;
