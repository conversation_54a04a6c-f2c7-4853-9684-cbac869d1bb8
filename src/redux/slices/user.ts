import { contentHeader, noAuth<PERSON>eader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { BASE_URL } from "@/config";

export const userApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // ── Users ────────────────────────────────────────────────────────────────
    getUsers: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number; status?: string }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/users`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data ?? [];
      // },
      providesTags: ["Users"],
    }),
    getUserDetails: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/users/${id}`,
        method: "GET",
        headers: noAuth<PERSON>eader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
    createUser: builder.mutation<any, Partial<any>>({
      query: (data) => ({
        url: `${BASE_URL}/users/users`,
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Users"],
    }),
    updateUserDetails: builder.mutation<
      any,
      { user_id: number } & Partial<any>
    >({
      query: ({ user_id, ...patch }) => ({
        url: `${BASE_URL}/users/users/${user_id}`,
        method: "PATCH",
        body: patch,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Users"],
    }),
    deleteUser: builder.mutation<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/users/${id}`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Users"],
    }),

    // ── Departments ─────────────────────────────────────────────────────────
    getDepartments: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/departments`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
    getDepartment: builder.query<any, number>({
      query: (dp_id) => ({
        url: `${BASE_URL}/users/departments/${dp_id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),

    // ── Groups ──────────────────────────────────────────────────────────────
    getGroups: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/groups`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data?.results ?? [];
      // },
      providesTags: ["Users"],
    }),
    getGroup: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/groups/${id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),

    // ── Teams ───────────────────────────────────────────────────────────────
    getTeams: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/teams`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data?.results ?? [];
      // },
      providesTags: ["Users"],
    }),
    getTeam: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/teams/${id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
  }),
});

export const {
  // Users
  useGetUsersQuery,
  useLazyGetUsersQuery,
  useGetUserDetailsQuery,
  useCreateUserMutation,
  useUpdateUserDetailsMutation,
  useDeleteUserMutation,

  // Departments
  useGetDepartmentsQuery,
  useGetDepartmentQuery,

  // Groups
  useGetGroupsQuery,
  useGetGroupQuery,

  // Teams
  useGetTeamsQuery,
  useGetTeamQuery,
} = userApi;
