import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';


interface Message {
    id: string;
    text: string;
    sender: 'user' | 'contact';
    timestamp: Date;
}

interface AIChatState {
    messages: Message[];
    setMessages: (route: Message) => void;
}

const useAIChatStore = create<AIChatState>()(
    persist(
        (set, get) => ({
            messages: [{
                id: '1',
                text: 'Hello! How can I help you today?',
                sender: 'contact',
                timestamp: new Date(Date.now() - 60000)
            }],
            setMessages: (newMessage) =>
                set({ messages: [...get().messages, newMessage] }),
        }),
        {
            name: 'ai-chat-storage',
            storage: createJSONStorage(() => localStorage),
        }
    )
);

export default useAIChatStore;