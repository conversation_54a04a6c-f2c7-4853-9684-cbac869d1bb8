import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface LockScreenState {
    route: string;
    setRoute: (route: string) => void;
}

const useLockScreenStore = create<LockScreenState>()(
    persist(
        (set) => ({
            route: '/',
            setRoute: (route) => set({ route }),
        }),
        {
            name: 'lock-screen-storage', 
            storage: createJSONStorage(() => localStorage), 
        }
    )
);

export default useLockScreenStore;