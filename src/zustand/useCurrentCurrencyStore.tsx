import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface currencyState {
  currency: string;
  setCurrency: (route: string) => void;
}

const useCurrentCurrencyStore = create<currencyState>()(
  persist(
    (set) => ({
      currency: "KES",
      setCurrency: (currency) => set({ currency }),
    }),
    {
      name: "currency-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);

export default useCurrentCurrencyStore;
