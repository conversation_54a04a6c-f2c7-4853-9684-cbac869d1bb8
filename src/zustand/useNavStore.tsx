import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface NavState {
    levelOne: string;
    setLevelOne: (route: string) => void;
    levelTwo: string;
    setLevelTwo: (route: string) => void;
    levelThree: string;
    setLevelThree: (route: string) => void;
    levelOneActive: string;
    setLevelOneActive: (route: string) => void;
    levelTwoActive: string;
    setLevelTwoActive: (route: string) => void;
    levelThreeActive: string;
    setLevelThreeActive: (route: string) => void;
}

const useNavStore = create<NavState>()(
    persist(
        (set) => ({
            levelOne: 'Main',
            setLevelOne: (levelOne) => set({
                levelOne,
                levelTwo: '',
                levelThree: '',
            }),
            levelTwo: '',
            setLevelTwo: (levelTwo) => set({
                levelTwo,
                levelThree: '',

                levelThreeActive: ''
            }),
            levelThree: '',
            setLevelThree: (levelThree) => set({ levelThree }),
            levelOneActive: '',
            setLevelOneActive: (levelOneActive) => set({ levelOneActive }),
            levelTwoActive: '',
            setLevelTwoActive: (levelTwoActive) => set({ levelTwoActive }),
            levelThreeActive: '',
            setLevelThreeActive: (levelThreeActive) => set({ levelThreeActive }),
        }),
        {
            name: 'nav-storage',
            storage: createJSONStorage(() => localStorage),
        }
    )
);

export default useNavStore;