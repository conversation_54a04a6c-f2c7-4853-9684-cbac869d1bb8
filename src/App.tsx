import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ToastProvider } from "./components/custom/Toast/MyToast";
import { useTheme } from "./hooks/use-theme";
import AuthIndex from "./pages/authPages";
import Reset from "./pages/authPages/Reset";
import ForgetPassword from "./pages/authPages/ForgetPassword";
import Home from "./pages/Home";
import AssignRole from "./pages/AssignRole";
import Permissions from "./pages/PermissionManagement/PermissionsSet";
import Groups from "./pages/Groups/groups";
import LogisticsDash from "./pages/logistics/LogisticsDash";
import Clients from "./pages/logistics/ClientsSection/ClientsPage";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import LogisticsPermissionTest from "./pages/test/LogisticsPermissionTest";
import DriversPage from "./pages/logistics/DriversSection/DriversPage";
import LogisticsStats from "./pages/logistics/Stats";
import LogisticReports from "./pages/logistics/LogisticReports";
import UsersTable from "./pages/users/UsersTable";
import UsersGrid from "./pages/users/UsersGrid";
import Profile from "./pages/users/Profile";
import SiteVisitForm from "./pages/logistics/SiteVisitForm";
import AllSiteVisits from "./pages/logistics/AllSiteVisits";
import SiteVisitDetails from "./pages/logistics/SiteVisitDetails";
import Projects from "./pages/inventory/Projects";
import ProjectDetails from "./pages/inventory/Projects/ProjectDetails";
import DiasporaReports from "./pages/Diaspora/diasporareport";
import MpesaTransactionsPage from "./pages/inventory/MpesaTransactions";
import Reports from "./pages/Bookingsreport/report";
import SellingReport from "./pages/Diaspora";
import DiasporaTrips from "./pages/inventory/diaspora/DiasporaTrips";
import AllBookings from "./pages/inventory/accounts/AllBookings";
import PaymentPlanChecker from "./pages/inventory/pricing/PaymentPlanChecker";
import ProjectPricing from "./pages/inventory/pricing/ProjectPricing";
import Customers from "./pages/customers/Customer";
import Prospects from "./pages/Prospects/prospects";
import CustomerDetails from "./pages/customers/CustomerDetails";
import Bookings from "./pages/Bookingsreport/bookings";
import BookingApproval from "./pages/inventory/BookingApproval";
import LockScreenPage from "./pages/authPages/LockScreen";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import IdleTimeoutModal from "./components/auth/IdleTimeoutModal";
import ProtectedPermissionRoute from "./components/auth/ProtectedPermissionRoute";
import PermissionRouteGuard from "./components/auth/PermissionRouteGuard";
import Vehicles from "./pages/logistics/vehicle/Vehicles";
import AllSiteReport from "./pages/logistics/AllSiteReport";
import SalesOverview from "./pages/Sales/Overview/SalesOverview";
import SalesCard from "./pages/Sales/Overview/SalesCard";
import PermissionTest from "./pages/test/PermissionTest";
import PermissionSystemTest from "./pages/test/PermissionSystemTest";
import RoutePermissionTest from "./pages/test/RoutePermissionTest";

import ComplaintsCard from "./pages/Complaints/complaintscard";
import FlagList from "./pages/Flags/FlagsList";
import EventsReminder from "./pages/Reminder/EventReminder";
import NotesList from "./pages/Notifications/Notifications";
import CustomerMarketerEngagement from "./pages/Engagements/CustomerEngagement";
import LoginPage from "./pages/authPages/SignIn";
import DiasporaReservations from "./pages/inventory/diaspora/DiasporaReservations";

import ViewFeedBack from "./pages/FeedBack/ViewFeedBacks";
import ViewNotes from "./pages/Notes/notesview";
import Surveys from "./pages/Surveys/SurveyList";
import Todo from "./pages/todo/todo";
import HrDashboard from "./pages/CustomDshboard/HrDashboard";

import CustomersOverview from "./pages/customers/CustomersOverview";
import PropectDetails from "./pages/Prospects/PropectDetails";
import Accounts from "./pages/Dashboards/Accounts/Accounts";
import GMKaren from "./pages/Dashboards/GM-Karen/GM-Karen";
import LegalDashboard from "./pages/CustomDshboard/LegalTeam/LegalDash";
import OfferLettersDashboard from "./pages/CustomDshboard/LegalTeam/OfferLettersDashboard";
import CreditsTeamDashboard from "./pages/CustomDshboard/CreditsTeam/CreditsTeamDashboard";
import TelemarketersDash from "./pages/CustomDshboard/TelemarketersView/TelemarketersDash";
import DigitalDash from "./pages/CustomDshboard/DigitalView/Dashboard";
import DiasporaDashboard from "./pages/CustomDshboard/Diaspora/Dashboard";
import DataDash from "./pages/CustomDshboard/DataView/DataDash";
import LeadSourcesPage from "./pages/CustomDshboard/DataView/LeadSourcesPage";
import LeadForm from "./pages/CustomDshboard/DataView/LeadForm";
import DirectorsDashboardTabs from "./pages/CustomDshboard/DirectorsView/DirectorsDashboardTabs";
import DirectorsDashboardV2 from "./pages/CustomDshboard/DirectorsView/DirectorsDashboardV2";

import HOS from "./pages/Dashboards/HOS/HOS";
// import DiasporaDashboard from "./pages/Dashboards/Diaspora/Diaspora";
import MarketerDashboard from "./pages/Dashboards/Marketer/Marketer";

import InventoryLogs from "./pages/inventory/InventoryLogs";
import Index from "./pages/Teams/Index";
import DepartmentsIndex from "./pages/Departments/Index";
import CreditTeamDetails from "./pages/Dashboards/CreditsTeamDetails/CreditTeamDetails";
import GroupIndex from "./pages/Groups/GroupIndex";
import FlagsList from "./pages/Flags/FlagsList";

import Receipts from "./pages/inventory/diaspora/Receipts";
import CommisionsView from "./pages/Commissions/CommissionsView";
import SalesReports from "./pages/Reports/SalesReports/SalesReports";
import MarketsView from "./pages/Targets/MarketersView";
import CustomerReports from "./pages/Reports/CustomerReports/CustomerReports";
import MarketerTargets from "./pages/Performance/MarketerTargets";
import MarketerCommissions from "./pages/Performance/MarketerCommissions";
import GMDashboard from "./pages/Performance/GMDashboard";
import TeamLeaderDashboard from "./pages/Performance/TeamLeaderDashboard";

import Tickets from "./pages/Tickets";
import TicketDetails from "./pages/Tickets/TicketDetails";
import TicketCategories from "./pages/Tickets/TicketCategories";
import TicketSources from "./pages/Tickets/TicketSources";
import TicketLogs from "./pages/Tickets/TicketLogs";
import OfferLetter, { OfferLetterList } from "./pages/OfferLetter";
import { useEffect } from "react";
import PermissionsService from "./services/permissionsService";
import ChatComponent from "./components/chat/ChatComponent";
import OfferLetterLanding from "./pages/OfferLetter/steps/current/OfferLetterLanding";

import ArchivedBooking from "./pages/inventory/accounts/ArchivedBookings";
import AccountsReports from "./pages/inventory/accounts/Reports/index";
import MarketersPerfomance from "./pages/Performance/MarketersPerfomance";
import CashOnCash from "./pages/Performance/CashonCash";
import MarketerProfile from "./pages/Performance/PortfolioComponents/MarketerProfileModal";
import AccountsReservedPlots from "./pages/inventory/accounts/AccountsReservedPlots";

function App() {
  const { theme } = useTheme();

  // Set up automatic permission refreshing
  useEffect(() => {
    // Refresh permissions immediately when app loads
    PermissionsService.refreshPermissions();

    // Set up automatic refreshing every 5 minutes
    const clearAutoRefresh = PermissionsService.setupAutoRefresh(5);

    // Clean up when component unmounts
    return clearAutoRefresh;
  }, []);

  return (
    <>
      <ToastProvider theme={theme} position="top-right" />

      <Router>
        <Routes>
          <Route path="/auth" element={<AuthIndex />}>
            <Route path="/auth/login" element={<LoginPage />} />
            <Route path="/auth/lockscreen" element={<LockScreenPage />} />
            <Route path="/auth/forgot-password" element={<ForgetPassword />} />
          </Route>
          <Route path="/auth/reset-password" element={<Reset />} />
          <Route path="/lead-form" element={<LeadForm />} />
          {/*teams*/}
          <Route path="hr-team" element={<HrDashboard />} />

          <Route element={<ProtectedRoute />}>
            <Route element={<IdleTimeoutModal />}>
              <Route element={<PermissionRouteGuard />}>
                <Route element={<ChatComponent />}>
                  <Route path="/" element={<Home />} />
                  {/* logistics  */}
                  <Route path="/logistics-dash" element={<LogisticsDash />} />
                  <Route path="/logistics" element={<Clients />} />
                  <Route path="/logistics/vehicles" element={<Vehicles />} />
                  <Route path="/Drivers" element={<DriversPage />} />
                  <Route path="/logistics-stats" element={<LogisticsStats />} />
                  <Route
                    path="/logistics-reports"
                    element={<LogisticReports />}
                  />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/book-visit" element={<SiteVisitForm />} />
                  <Route path="/all-visits" element={<AllSiteVisits />} />
                  <Route
                    path="/logistics/site-visits/:id"
                    element={<SiteVisitDetails />}
                  />
                  <Route
                    path="/engagements"
                    element={<CustomerMarketerEngagement />}
                  />
                  <Route path="/notes" element={<ViewNotes />} />
                  <Route path="/digital-team" element={<DigitalDash />} />
                  <Route path="/data-team" element={<DataDash />} />
                  <Route
                    path="/diasporareports"
                    element={<DiasporaReports />}
                  />
                  <Route path="/mybookings" element={<Bookings />} />
                  <Route
                    path="/commission report"
                    element={<CommisionsView />}
                  />
                  <Route
                    path="/performance"
                    element={<MarketersPerfomance />}
                  />
                  <Route path="team-leader" element={<TeamLeaderDashboard />} />
                  <Route
                    path="/portfolio"
                    element={<MarketerProfile marketerData={null} />}
                  />
                  <Route path="/legal-team" element={<LegalDashboard />} />
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                  <Route
                    path="/diaspora-reservations"
                    element={<DiasporaReservations />}
                  />
                  <Route
                    path="/engagements"
                    element={<CustomerMarketerEngagement />}
                  />
                  <Route
                    path="/tele-marketing-team"
                    element={<TelemarketersDash />}
                  />
                  <Route
                    path="/admin/services"
                    element={
                      <ProtectedPermissionRoute requiredPermissions={[116]}>
                        <Todo />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route path="/targets" element={<MarketsView />} />
                  <Route
                    path="/marketer-targets"
                    element={<MarketerTargets />}
                  />
                  <Route
                    path="/marketer-commissions"
                    element={<MarketerCommissions />}
                  />
                  <Route
                    path="/gm-dashboard"
                    element={
                      <ProtectedPermissionRoute
                        requiredPermissions={[7114, 7115]}
                      >
                        <GMDashboard />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route path="/marketerreport" element={<Reports />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  <Route path="/" element={<Home />} />
                  <Route path="/cash-on-cash" element={<CashOnCash />} />
                  <Route path="/profile/:id" element={<Profile />} />
                  <Route path="/logistics-dash" element={<LogisticsDash />} />
                  <Route
                    path="/permissions"
                    element={
                      <ProtectedPermissionRoute requiredPermissions={[117]}>
                        <Permissions />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route
                    path="/Groups"
                    element={
                      <ProtectedPermissionRoute requiredPermissions={[117]}>
                        <Groups />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route path="/clients" element={<Clients />} />
                  <Route path="/logistics" element={<Clients />} />
                  ``
                  <Route path="/Drivers" element={<DriversPage />} />
                  <Route path="/admin/assign-role" element={<AssignRole />} />
                  <Route path="/logistics-stats" element={<LogisticsStats />} />
                  <Route
                    path="/logistics-reports"
                    element={<LogisticReports />}
                  />
                  <Route path="/users-list" element={<UsersTable />} />
                  <Route path="/users-grid" element={<UsersGrid />} />
                  <Route path="/feedback" element={<ViewFeedBack />} />
                  <Route path="surveys" element={<Surveys />} />
                  <Route path="/book-visit" element={<SiteVisitForm />} />
                  <Route path="/all-visits" element={<AllSiteVisits />} />
                  <Route
                    path="/logistics/site-visits/:id"
                    element={<SiteVisitDetails />}
                  />
                  <Route
                    path="/diasporareports"
                    element={<DiasporaReports />}
                  />
                  <Route
                    path="/mybookings/all-bookings"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/mpesa-bookings"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/special-bookings"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/waiting-approval"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/other-bookings"
                    element={<Bookings />}
                  />
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route
                    path="/mpesa-transactions"
                    element={<MpesaTransactionsPage />}
                  />
                  <Route path="/marketerreport" element={<Reports />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                  <Route
                    path="/inventory/accounts/all-bookings"
                    element={<AllBookings />}
                  />
                  <Route
                    path="/inventory/pricing/project-pricing"
                    element={<ProjectPricing />}
                  />
                  <Route
                    path="/inventory/pricing/payment-plan-checker"
                    element={<PaymentPlanChecker />}
                  />
                  <Route path="/Customers" element={<Customers />} />
                  <Route path="/prospects" element={<Prospects />} />
                  <Route path="/customer/:id" element={<CustomerDetails />} />
                  <Route
                    path="/allsitevisitreport"
                    element={<AllSiteReport />}
                  />
                  <Route path="/complaints" element={<ComplaintsCard />} />
                  <Route path="/reminders" element={<EventsReminder />} />
                  <Route path="/flags" element={<FlagsList />} />
                  <Route path="notifications" element={<NotesList />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  {/* users, client and permissions  */}
                  <Route path="/users-list" element={<UsersTable />} />
                  <Route path="/users-grid" element={<UsersGrid />} />
                  <Route path="/admin/assign-role" element={<AssignRole />} />
                  <Route path="/admin/departments" element={<Index />} />
                  <Route
                    path="/admin/departments"
                    element={<DepartmentsIndex />}
                  />
                  <Route path="/admin/groups" element={<GroupIndex />} />
                  <Route path="/profile/:id" element={<Profile />} />
                  <Route path="/permissions" element={<Permissions />} />
                  <Route path="/Groups" element={<Groups />} />
                  <Route path="/clients" element={<Clients />} />
                  {/* reports  */}
                  <Route
                    path="/diasporareports"
                    element={<DiasporaReports />}
                  />
                  <Route path="/marketerreport" element={<Reports />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  <Route
                    path="/allsitevisitreport"
                    element={<AllSiteReport />}
                  />
                  {/* inventory  */}
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route path="/mybookings" element={<Bookings />} />
                  <Route
                    path="/inventory/accounts/all-bookings"
                    element={<AllBookings />}
                  />
                  <Route
                    path="/inventory/accounts/archived-bookings"
                    element={<ArchivedBooking />}
                  />
                  <Route
                    path="/inventory/booking-approvals"
                    element={<BookingApproval />}
                  />
                  <Route path="/diaspora-receipts" element={<Receipts />} />
                  <Route
                    path="/mpesa-transactions"
                    element={<MpesaTransactionsPage />}
                  />
                  <Route
                    path="/inventory/pricing/project-pricing"
                    element={<ProjectPricing />}
                  />
                  <Route
                    path="/inventory/pricing/payment-plan-checker"
                    element={<PaymentPlanChecker />}
                  />
                  <Route path="/inventory/logs" element={<InventoryLogs />} />
                  {/* sales  */}
                  <Route path="/sales/overview" element={<SalesOverview />} />
                  <Route
                    path="/sales/sales-card/:lead_file_no"
                    element={<SalesCard />}
                  />
                  {/* customers  */}
                  <Route path="/Customers" element={<Customers />} />
                  <Route path="/customer/:id" element={<CustomerDetails />} />
                  <Route
                    path="/Customers/overview"
                    element={<CustomersOverview />}
                  />
                  {/* prospects / leads  */}
                  <Route path="/prospects" element={<Prospects />} />
                  <Route path="/prospects/:id" element={<PropectDetails />} />
                  {/* Services  */}
                  <Route path="/complaints" element={<ComplaintsCard />} />
                  <Route path="/reminders" element={<EventsReminder />} />
                  <Route path="/flags" element={<FlagList />} />
                  <Route path="notifications" element={<NotesList />} />
                  {/* analytics  */}
                  <Route
                    path="/directors"
                    element={<DirectorsDashboardTabs />}
                  />
                  <Route
                    path="/directors-v2"
                    element={<DirectorsDashboardV2 />}
                  />
                  <Route path="/accounts-dashboard" element={<Accounts />} />
                  <Route path="/gm-karen-dashboard" element={<GMKaren />} />
                  <Route path="/hq-hos-dashboard" element={<HOS />} />
                  <Route
                    path="/diaspora-dashboard"
                    element={<DiasporaDashboard />}
                  />
                  <Route
                    path="/credits-team-dashboard"
                    element={<CreditsTeamDashboard />}
                  />
                  <Route
                    path="/credits-team-dashboard/:credit_officer_id"
                    element={<CreditTeamDetails />}
                  />
                  <Route
                    path="/marketer-dashboard"
                    element={<MarketerDashboard />}
                  />
                  {/* Test pages */}
                  <Route
                    path="/test/permissions"
                    element={<PermissionTest />}
                  />
                  <Route
                    path="/test/permission-system"
                    element={<PermissionSystemTest />}
                  />
                  <Route
                    path="/test/logistics-permissions"
                    element={<LogisticsPermissionTest />}
                  />
                  <Route
                    path="/test/route-permissions"
                    element={<RoutePermissionTest />}
                  />
                  {/* reports  */}
                  <Route
                    path="/reports/sales-reports"
                    element={<SalesReports />}
                  />
                  <Route
                    path="/customer-reports"
                    element={<CustomerReports />}
                  />
                  {/* logistics  */}
                  <Route path="/logistics-dash" element={<LogisticsDash />} />
                  <Route path="/logistics" element={<Clients />} />
                  <Route path="/logistics/vehicles" element={<Vehicles />} />
                  <Route path="/Drivers" element={<DriversPage />} />
                  <Route path="/logistics-stats" element={<LogisticsStats />} />
                  {/* <Route path="/logistics-reports" element={<LogisticReports />} /> */}
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/book-visit" element={<SiteVisitForm />} />
                  <Route path="/all-visits" element={<AllSiteVisits />} />
                  <Route
                    path="/logistics/site-visits/:id"
                    element={<SiteVisitDetails />}
                  />
                  <Route
                    path="/engagements"
                    element={<CustomerMarketerEngagement />}
                  />
                  <Route path="/notes" element={<ViewNotes />} />
                  <Route path="/digital-team" element={<DigitalDash />} />
                  <Route path="/data-team" element={<DataDash />} />
                  <Route
                    path="/diasporareports"
                    element={<DiasporaReports />}
                  />
                  <Route path="/mybookings" element={<Bookings />} />
                  <Route path="/legal-team" element={<LegalDashboard />} />
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                  <Route
                    path="/diaspora-reservations"
                    element={<DiasporaReservations />}
                  />
                  <Route
                    path="/engagements"
                    element={<CustomerMarketerEngagement />}
                  />
                  <Route
                    path="/tele-marketing-team"
                    element={<TelemarketersDash />}
                  />
                  <Route
                    path="/admin/services"
                    element={
                      <ProtectedPermissionRoute requiredPermissions={[116]}>
                        <Todo />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route path="/marketerreport" element={<Reports />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  <Route path="/" element={<Home />} />
                  <Route path="/profile/:id" element={<Profile />} />
                  <Route path="/logistics-dash" element={<LogisticsDash />} />
                  <Route
                    path="/permissions"
                    element={
                      <ProtectedPermissionRoute requiredPermissions={[117]}>
                        <Permissions />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route
                    path="/Groups"
                    element={
                      <ProtectedPermissionRoute requiredPermissions={[117]}>
                        <Groups />
                      </ProtectedPermissionRoute>
                    }
                  />
                  <Route path="/clients" element={<Clients />} />
                  <Route path="/logistics" element={<Clients />} />
                  ``
                  <Route path="/Drivers" element={<DriversPage />} />
                  <Route path="/admin/assign-role" element={<AssignRole />} />
                  <Route path="/offer-letter" element={<OfferLetter />} />
                  <Route path="/offer-letters" element={<OfferLetterList />} />
                  <Route path="/ol" element={<OfferLetterLanding />} />
                  <Route path="/logistics-stats" element={<LogisticsStats />} />
                  <Route
                    path="/logistics-reports"
                    element={<LogisticReports />}
                  />
                  <Route path="/users-list" element={<UsersTable />} />
                  <Route path="/users-grid" element={<UsersGrid />} />
                  <Route path="/feedback" element={<ViewFeedBack />} />
                  <Route path="surveys" element={<Surveys />} />
                  <Route path="/book-visit" element={<SiteVisitForm />} />
                  <Route path="/all-visits" element={<AllSiteVisits />} />
                  <Route
                    path="/logistics/site-visits/:id"
                    element={<SiteVisitDetails />}
                  />
                  <Route
                    path="/diasporareports"
                    element={<DiasporaReports />}
                  />
                  <Route
                    path="/mybookings/all-bookings"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/mpesa-bookings"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/special-bookings"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/waiting-approval"
                    element={<Bookings />}
                  />
                  <Route
                    path="/mybookings/other-bookings"
                    element={<Bookings />}
                  />
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route
                    path="/mpesa-transactions"
                    element={<MpesaTransactionsPage />}
                  />
                  <Route path="/marketerreport" element={<Reports />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                  <Route
                    path="/inventory/accounts/all-bookings"
                    element={<AllBookings />}
                  />
                  <Route
                    path="/inventory/accounts/reports"
                    element={<AccountsReports />}
                  />
                  <Route
                    path="/inventory/pricing/project-pricing"
                    element={<ProjectPricing />}
                  />
                  <Route
                    path="/inventory/pricing/payment-plan-checker"
                    element={<PaymentPlanChecker />}
                  />
                  <Route path="/Customers" element={<Customers />} />
                  <Route path="/prospects" element={<Prospects />} />
                  <Route path="/customer/:id" element={<CustomerDetails />} />
                  <Route
                    path="/allsitevisitreport"
                    element={<AllSiteReport />}
                  />
                  <Route path="/complaints" element={<ComplaintsCard />} />
                  <Route path="/reminders" element={<EventsReminder />} />
                  <Route path="/flags" element={<FlagsList />} />
                  <Route path="notifications" element={<NotesList />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  {/* users, client and permissions  */}
                  <Route path="/users-list" element={<UsersTable />} />
                  <Route path="/users-grid" element={<UsersGrid />} />
                  <Route path="/admin/assign-role" element={<AssignRole />} />
                  <Route path="/admin/departments" element={<Index />} />
                  <Route
                    path="/admin/departments"
                    element={<DepartmentsIndex />}
                  />
                  <Route path="/admin/groups" element={<GroupIndex />} />
                  <Route path="/profile/:id" element={<Profile />} />
                  <Route path="/permissions" element={<Permissions />} />
                  <Route path="/Groups" element={<Groups />} />
                  <Route path="/clients" element={<Clients />} />
                  {/* reports  */}
                  <Route
                    path="/diasporareports"
                    element={<DiasporaReports />}
                  />
                  <Route path="/marketerreport" element={<Reports />} />
                  <Route path="/plotmarketreport" element={<SellingReport />} />
                  <Route
                    path="/allsitevisitreport"
                    element={<AllSiteReport />}
                  />
                  {/* inventory  */}
                  <Route path="/projects" element={<Projects />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route path="/mybookings" element={<Bookings />} />
                  <Route
                    path="/inventory/accounts/all-bookings"
                    element={<AllBookings />}
                  />
                  <Route
                    path="/inventory/booking-approvals"
                    element={<BookingApproval />}
                  />
                  <Route path="/diaspora-receipts" element={<Receipts />} />
                  <Route
                    path="/mpesa-transactions"
                    element={<MpesaTransactionsPage />}
                  />
                  <Route
                    path="/inventory/pricing/project-pricing"
                    element={<ProjectPricing />}
                  />
                  <Route
                    path="/inventory/pricing/payment-plan-checker"
                    element={<PaymentPlanChecker />}
                  />
                  <Route path="/inventory/logs" element={<InventoryLogs />} />
                  {/* sales  */}
                  <Route path="/sales/overview" element={<SalesOverview />} />
                  <Route
                    path="/sales/sales-card/:lead_file_no"
                    element={<SalesCard />}
                  />
                  {/* customers  */}
                  <Route path="/Customers" element={<Customers />} />
                  <Route path="/customer/:id" element={<CustomerDetails />} />
                  <Route
                    path="/Customers/overview"
                    element={<CustomersOverview />}
                  />
                  {/* prospects / leads  */}
                  <Route path="/prospects" element={<Prospects />} />
                  <Route path="/prospects/:id" element={<PropectDetails />} />
                  {/* Services  */}
                  <Route path="/complaints" element={<ComplaintsCard />} />
                  <Route path="/reminders" element={<EventsReminder />} />
                  <Route path="/flags" element={<FlagList />} />
                  <Route path="notifications" element={<NotesList />} />
                  {/* analytics  */}
                  <Route
                    path="/directors"
                    element={<DirectorsDashboardTabs />}
                  />
                  <Route path="/accounts-dashboard" element={<Accounts />} />
                  <Route path="/gm-karen-dashboard" element={<GMKaren />} />
                  <Route path="/hq-hos-dashboard" element={<HOS />} />
                  <Route
                    path="/diaspora-dashboard"
                    element={<DiasporaDashboard />}
                  />
                  <Route
                    path="/credits-team-dashboard"
                    element={<CreditsTeamDashboard />}
                  />
                  <Route
                    path="/credits-team-dashboard/:credit_officer_id"
                    element={<CreditTeamDetails />}
                  />
                  <Route
                    path="/marketer-dashboard"
                    element={<MarketerDashboard />}
                  />
                  {/* Test pages */}
                  <Route
                    path="/test/permissions"
                    element={<PermissionTest />}
                  />
                  <Route
                    path="/test/permission-system"
                    element={<PermissionSystemTest />}
                  />
                  <Route
                    path="/test/logistics-permissions"
                    element={<LogisticsPermissionTest />}
                  />
                  <Route
                    path="/test/route-permissions"
                    element={<RoutePermissionTest />}
                  />
                  {/* reports  */}
                  <Route
                    path="/reports/sales-reports"
                    element={<SalesReports />}
                  />
                  {/* Unauthorized access page */}
                  <Route path="/unauthorized" element={<UnauthorizedPage />} />
                  {/* tickets  */}
                  <Route path="/ticketing" element={<Tickets />} />
                  <Route path="/ticketing/:id" element={<TicketDetails />} />
                  <Route
                    path="/ticketing/categories"
                    element={<TicketCategories />}
                  />
                  <Route
                    path="/ticketing/sources"
                    element={<TicketSources />}
                  />
                  <Route path="/ticketing/logs" element={<TicketLogs />} />
                </Route>
              </Route>
              <Route element={<ChatComponent />}>
                <Route path="/" element={<Home />} />
                {/* logistics  */}
                <Route path="/logistics-dash" element={<LogisticsDash />} />
                <Route path="/logistics" element={<Clients />} />
                <Route path="/logistics/vehicles" element={<Vehicles />} />
                <Route path="/Drivers" element={<DriversPage />} />
                <Route path="/logistics-stats" element={<LogisticsStats />} />
                <Route
                  path="/logistics-reports"
                  element={<LogisticReports />}
                />
                <Route path="/profile" element={<Profile />} />
                <Route path="/book-visit" element={<SiteVisitForm />} />
                <Route path="/all-visits" element={<AllSiteVisits />} />
                <Route
                  path="/logistics/site-visits/:id"
                  element={<SiteVisitDetails />}
                />
                <Route
                  path="/engagements"
                  element={<CustomerMarketerEngagement />}
                />
                <Route path="/notes" element={<ViewNotes />} />
                <Route path="/digital-team" element={<DigitalDash />} />
                <Route path="/data-team" element={<DataDash />} />
                <Route
                  path="/data-team/lead-sources"
                  element={<LeadSourcesPage />}
                />
                <Route path="/diasporareports" element={<DiasporaReports />} />
                <Route path="/mybookings" element={<Bookings />} />
                <Route path="/commission report" element={<CommisionsView />} />
                <Route path="/legal-team" element={<LegalDashboard />} />
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                <Route
                  path="/diaspora-reservations"
                  element={<DiasporaReservations />}
                />
                <Route
                  path="/engagements"
                  element={<CustomerMarketerEngagement />}
                />
                <Route
                  path="/tele-marketing-team"
                  element={<TelemarketersDash />}
                />
                <Route
                  path="/admin/services"
                  element={
                    <ProtectedPermissionRoute requiredPermissions={[116]}>
                      <Todo />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route path="/targets" element={<MarketsView />} />
                <Route path="/marketer-targets" element={<MarketerTargets />} />
                <Route
                  path="/marketer-commissions"
                  element={<MarketerCommissions />}
                />
                <Route
                  path="/gm-dashboard"
                  element={
                    <ProtectedPermissionRoute
                      requiredPermissions={[7114, 7115]}
                    >
                      <GMDashboard />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route path="/marketerreport" element={<Reports />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                <Route path="/" element={<Home />} />
                <Route path="/profile/:id" element={<Profile />} />
                <Route path="/logistics-dash" element={<LogisticsDash />} />
                <Route
                  path="/permissions"
                  element={
                    <ProtectedPermissionRoute requiredPermissions={[117]}>
                      <Permissions />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route
                  path="/Groups"
                  element={
                    <ProtectedPermissionRoute requiredPermissions={[117]}>
                      <Groups />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route path="/clients" element={<Clients />} />
                <Route path="/logistics" element={<Clients />} />
                ``
                <Route path="/Drivers" element={<DriversPage />} />
                <Route path="/admin/assign-role" element={<AssignRole />} />
                <Route path="/logistics-stats" element={<LogisticsStats />} />
                <Route
                  path="/logistics-reports"
                  element={<LogisticReports />}
                />
                <Route path="/users-list" element={<UsersTable />} />
                <Route path="/users-grid" element={<UsersGrid />} />
                <Route path="/feedback" element={<ViewFeedBack />} />
                <Route path="surveys" element={<Surveys />} />
                <Route path="/book-visit" element={<SiteVisitForm />} />
                <Route path="/all-visits" element={<AllSiteVisits />} />
                <Route
                  path="/logistics/site-visits/:id"
                  element={<SiteVisitDetails />}
                />
                <Route path="/diasporareports" element={<DiasporaReports />} />
                <Route path="/mybookings/all-bookings" element={<Bookings />} />
                <Route
                  path="/mybookings/mpesa-bookings"
                  element={<Bookings />}
                />
                <Route
                  path="/mybookings/special-bookings"
                  element={<Bookings />}
                />
                <Route
                  path="/mybookings/waiting-approval"
                  element={<Bookings />}
                />
                <Route
                  path="/mybookings/other-bookings"
                  element={<Bookings />}
                />
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route
                  path="/mpesa-transactions"
                  element={<MpesaTransactionsPage />}
                />
                <Route path="/marketerreport" element={<Reports />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                <Route
                  path="/inventory/accounts/all-bookings"
                  element={<AllBookings />}
                />
                <Route
                  path="/inventory/accounts/reserved-bookings"
                  element={<AccountsReservedPlots />}
                />
                <Route
                  path="/inventory/pricing/project-pricing"
                  element={<ProjectPricing />}
                />
                <Route
                  path="/inventory/pricing/payment-plan-checker"
                  element={<PaymentPlanChecker />}
                />
                <Route path="/Customers" element={<Customers />} />
                <Route path="/prospects" element={<Prospects />} />
                <Route path="/customer/:id" element={<CustomerDetails />} />
                <Route path="/allsitevisitreport" element={<AllSiteReport />} />
                <Route path="/complaints" element={<ComplaintsCard />} />
                <Route path="/reminders" element={<EventsReminder />} />
                <Route path="/flags" element={<FlagsList />} />
                <Route path="notifications" element={<NotesList />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                {/* users, client and permissions  */}
                <Route path="/users-list" element={<UsersTable />} />
                <Route path="/users-grid" element={<UsersGrid />} />
                <Route path="/admin/assign-role" element={<AssignRole />} />
                <Route path="/admin/departments" element={<Index />} />
                <Route
                  path="/admin/departments"
                  element={<DepartmentsIndex />}
                />
                <Route path="/admin/groups" element={<GroupIndex />} />
                <Route path="/profile/:id" element={<Profile />} />
                <Route path="/permissions" element={<Permissions />} />
                <Route path="/Groups" element={<Groups />} />
                <Route path="/clients" element={<Clients />} />
                {/* reports  */}
                <Route path="/diasporareports" element={<DiasporaReports />} />
                <Route path="/marketerreport" element={<Reports />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                <Route path="/allsitevisitreport" element={<AllSiteReport />} />
                {/* inventory  */}
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route path="/mybookings" element={<Bookings />} />
                <Route
                  path="/inventory/accounts/all-bookings"
                  element={<AllBookings />}
                />
                <Route
                  path="/inventory/accounts/reserved-bookings"
                  element={<AccountsReservedPlots />}
                />
                <Route
                  path="/inventory/booking-approvals"
                  element={<BookingApproval />}
                />
                <Route path="/diaspora-receipts" element={<Receipts />} />
                <Route
                  path="/mpesa-transactions"
                  element={<MpesaTransactionsPage />}
                />
                <Route
                  path="/inventory/pricing/project-pricing"
                  element={<ProjectPricing />}
                />
                <Route
                  path="/inventory/pricing/payment-plan-checker"
                  element={<PaymentPlanChecker />}
                />
                <Route path="/inventory/logs" element={<InventoryLogs />} />
                {/* sales  */}
                <Route path="/sales/overview" element={<SalesOverview />} />
                <Route
                  path="/sales/sales-card/:lead_file_no"
                  element={<SalesCard />}
                />
                {/* customers  */}
                <Route path="/Customers" element={<Customers />} />
                <Route path="/customer/:id" element={<CustomerDetails />} />
                <Route
                  path="/Customers/overview"
                  element={<CustomersOverview />}
                />
                {/* prospects / leads  */}
                <Route path="/prospects" element={<Prospects />} />
                <Route path="/prospects/:id" element={<PropectDetails />} />
                {/* Services  */}
                <Route path="/complaints" element={<ComplaintsCard />} />
                <Route path="/reminders" element={<EventsReminder />} />
                <Route path="/flags" element={<FlagList />} />
                <Route path="notifications" element={<NotesList />} />
                {/* analytics  */}
                <Route path="/directors" element={<DirectorsDashboardTabs />} />
                <Route path="/accounts-dashboard" element={<Accounts />} />
                <Route path="/gm-karen-dashboard" element={<GMKaren />} />
                <Route path="/hq-hos-dashboard" element={<HOS />} />
                <Route
                  path="/diaspora-dashboard"
                  element={<DiasporaDashboard />}
                />
                <Route
                  path="/credits-team-dashboard"
                  element={<CreditsTeamDashboard />}
                />
                <Route
                  path="/credits-team-dashboard/:credit_officer_id"
                  element={<CreditTeamDetails />}
                />
                <Route
                  path="/marketer-dashboard"
                  element={<MarketerDashboard />}
                />
                {/* Test pages */}
                <Route path="/test/permissions" element={<PermissionTest />} />
                <Route
                  path="/test/permission-system"
                  element={<PermissionSystemTest />}
                />
                <Route
                  path="/test/logistics-permissions"
                  element={<LogisticsPermissionTest />}
                />
                <Route
                  path="/test/route-permissions"
                  element={<RoutePermissionTest />}
                />
                {/* reports  */}
                <Route
                  path="/reports/sales-reports"
                  element={<SalesReports />}
                />
                <Route path="/customer-reports" element={<CustomerReports />} />
                {/* logistics  */}
                <Route path="/logistics-dash" element={<LogisticsDash />} />
                <Route path="/logistics" element={<Clients />} />
                <Route path="/logistics/vehicles" element={<Vehicles />} />
                <Route path="/Drivers" element={<DriversPage />} />
                <Route path="/logistics-stats" element={<LogisticsStats />} />
                {/* <Route path="/logistics-reports" element={<LogisticReports />} /> */}
                <Route path="/profile" element={<Profile />} />
                <Route path="/book-visit" element={<SiteVisitForm />} />
                <Route path="/all-visits" element={<AllSiteVisits />} />
                <Route
                  path="/logistics/site-visits/:id"
                  element={<SiteVisitDetails />}
                />
                <Route
                  path="/engagements"
                  element={<CustomerMarketerEngagement />}
                />
                <Route path="/notes" element={<ViewNotes />} />
                <Route path="/digital-team" element={<DigitalDash />} />
                <Route path="/data-team" element={<DataDash />} />
                <Route
                  path="/data-team/lead-sources"
                  element={<LeadSourcesPage />}
                />
                <Route path="/diasporareports" element={<DiasporaReports />} />
                <Route path="/mybookings" element={<Bookings />} />
                <Route path="/legal-team" element={<LegalDashboard />} />
                <Route
                  path="/legal-team/offer-letters-dashboard"
                  element={<OfferLettersDashboard />}
                />
                <Route
                  path="/credits-team/dashboard"
                  element={<CreditsTeamDashboard />}
                />
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                <Route
                  path="/diaspora-reservations"
                  element={<DiasporaReservations />}
                />
                <Route
                  path="/engagements"
                  element={<CustomerMarketerEngagement />}
                />
                <Route
                  path="/tele-marketing-team"
                  element={<TelemarketersDash />}
                />
                <Route
                  path="/admin/services"
                  element={
                    <ProtectedPermissionRoute requiredPermissions={[116]}>
                      <Todo />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route path="/marketerreport" element={<Reports />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                <Route path="/" element={<Home />} />
                <Route path="/profile/:id" element={<Profile />} />
                <Route path="/logistics-dash" element={<LogisticsDash />} />
                <Route
                  path="/permissions"
                  element={
                    <ProtectedPermissionRoute requiredPermissions={[117]}>
                      <Permissions />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route
                  path="/Groups"
                  element={
                    <ProtectedPermissionRoute requiredPermissions={[117]}>
                      <Groups />
                    </ProtectedPermissionRoute>
                  }
                />
                <Route path="/clients" element={<Clients />} />
                <Route path="/logistics" element={<Clients />} />
                ``
                <Route path="/Drivers" element={<DriversPage />} />
                <Route path="/admin/assign-role" element={<AssignRole />} />
                <Route path="/offer-letter" element={<OfferLetter />} />
                {/* <Route path="/offer-letters" element={<OfferLetterList />} />
                <Route path="/ol" element={<OfferLetterLanding />} /> */}
                <Route path="/logistics-stats" element={<LogisticsStats />} />
                <Route
                  path="/logistics-reports"
                  element={<LogisticReports />}
                />
                <Route path="/users-list" element={<UsersTable />} />
                <Route path="/users-grid" element={<UsersGrid />} />
                <Route path="/feedback" element={<ViewFeedBack />} />
                <Route path="surveys" element={<Surveys />} />
                <Route path="/book-visit" element={<SiteVisitForm />} />
                <Route path="/all-visits" element={<AllSiteVisits />} />
                <Route
                  path="/logistics/site-visits/:id"
                  element={<SiteVisitDetails />}
                />
                <Route path="/diasporareports" element={<DiasporaReports />} />
                <Route path="/mybookings/all-bookings" element={<Bookings />} />
                <Route
                  path="/mybookings/mpesa-bookings"
                  element={<Bookings />}
                />
                <Route
                  path="/mybookings/special-bookings"
                  element={<Bookings />}
                />
                <Route
                  path="/mybookings/waiting-approval"
                  element={<Bookings />}
                />
                <Route
                  path="/mybookings/other-bookings"
                  element={<Bookings />}
                />
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route
                  path="/mpesa-transactions"
                  element={<MpesaTransactionsPage />}
                />
                <Route path="/marketerreport" element={<Reports />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                <Route path="/diaspora-trips" element={<DiasporaTrips />} />
                <Route
                  path="/inventory/accounts/all-bookings"
                  element={<AllBookings />}
                />
                <Route
                  path="/inventory/accounts/reserved-bookings"
                  element={<AccountsReservedPlots />}
                />
                <Route
                  path="/inventory/pricing/project-pricing"
                  element={<ProjectPricing />}
                />
                <Route
                  path="/inventory/pricing/payment-plan-checker"
                  element={<PaymentPlanChecker />}
                />
                <Route path="/Customers" element={<Customers />} />
                <Route path="/prospects" element={<Prospects />} />
                <Route path="/customer/:id" element={<CustomerDetails />} />
                <Route path="/allsitevisitreport" element={<AllSiteReport />} />
                <Route path="/complaints" element={<ComplaintsCard />} />
                <Route path="/reminders" element={<EventsReminder />} />
                <Route path="/flags" element={<FlagsList />} />
                <Route path="notifications" element={<NotesList />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                {/* users, client and permissions  */}
                <Route path="/users-list" element={<UsersTable />} />
                <Route path="/users-grid" element={<UsersGrid />} />
                <Route path="/admin/assign-role" element={<AssignRole />} />
                <Route path="/admin/departments" element={<Index />} />
                <Route
                  path="/admin/departments"
                  element={<DepartmentsIndex />}
                />
                <Route path="/admin/groups" element={<GroupIndex />} />
                <Route path="/profile/:id" element={<Profile />} />
                <Route path="/permissions" element={<Permissions />} />
                <Route path="/Groups" element={<Groups />} />
                <Route path="/clients" element={<Clients />} />
                {/* reports  */}
                <Route path="/diasporareports" element={<DiasporaReports />} />
                <Route path="/marketerreport" element={<Reports />} />
                <Route path="/plotmarketreport" element={<SellingReport />} />
                <Route path="/allsitevisitreport" element={<AllSiteReport />} />
                {/* inventory  */}
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route path="/mybookings" element={<Bookings />} />
                <Route
                  path="/inventory/accounts/all-bookings"
                  element={<AllBookings />}
                />
                <Route
                  path="/inventory/accounts/reserved-bookings"
                  element={<AccountsReservedPlots />}
                />
                <Route
                  path="/inventory/booking-approvals"
                  element={<BookingApproval />}
                />
                <Route path="/diaspora-receipts" element={<Receipts />} />
                <Route
                  path="/mpesa-transactions"
                  element={<MpesaTransactionsPage />}
                />
                <Route
                  path="/inventory/pricing/project-pricing"
                  element={<ProjectPricing />}
                />
                <Route
                  path="/inventory/pricing/payment-plan-checker"
                  element={<PaymentPlanChecker />}
                />
                <Route path="/inventory/logs" element={<InventoryLogs />} />
                {/* sales  */}
                <Route path="/sales/overview" element={<SalesOverview />} />
                <Route
                  path="/sales/sales-card/:lead_file_no"
                  element={<SalesCard />}
                />
                {/* customers  */}
                <Route path="/Customers" element={<Customers />} />
                <Route path="/customer/:id" element={<CustomerDetails />} />
                <Route
                  path="/Customers/overview"
                  element={<CustomersOverview />}
                />
                {/* prospects / leads  */}
                <Route path="/prospects" element={<Prospects />} />
                <Route path="/prospects/:id" element={<PropectDetails />} />
                {/* Services  */}
                <Route path="/complaints" element={<ComplaintsCard />} />
                <Route path="/reminders" element={<EventsReminder />} />
                <Route path="/flags" element={<FlagList />} />
                <Route path="notifications" element={<NotesList />} />
                {/* analytics  */}
                <Route path="/directors" element={<DirectorsDashboardTabs />} />
                <Route path="/accounts-dashboard" element={<Accounts />} />
                <Route path="/gm-karen-dashboard" element={<GMKaren />} />
                <Route path="/hq-hos-dashboard" element={<HOS />} />
                <Route
                  path="/diaspora-dashboard"
                  element={<DiasporaDashboard />}
                />
                <Route
                  path="/credits-team-dashboard"
                  element={<CreditsTeamDashboard />}
                />
                <Route
                  path="/credits-team-dashboard/:credit_officer_id"
                  element={<CreditTeamDetails />}
                />
                <Route
                  path="/marketer-dashboard"
                  element={<MarketerDashboard />}
                />
                {/* Test pages */}
                <Route path="/test/permissions" element={<PermissionTest />} />
                <Route
                  path="/test/permission-system"
                  element={<PermissionSystemTest />}
                />
                <Route
                  path="/test/logistics-permissions"
                  element={<LogisticsPermissionTest />}
                />
                <Route
                  path="/test/route-permissions"
                  element={<RoutePermissionTest />}
                />
                {/* reports  */}
                <Route
                  path="/reports/sales-reports"
                  element={<SalesReports />}
                />
                {/* Unauthorized access page */}
                <Route path="/unauthorized" element={<UnauthorizedPage />} />
                {/* tickets  */}
                <Route path="/ticketing" element={<Tickets />} />
                <Route path="/ticketing/:id" element={<TicketDetails />} />
                <Route
                  path="/ticketing/categories"
                  element={<TicketCategories />}
                />
                <Route path="/ticketing/sources" element={<TicketSources />} />
                <Route path="/ticketing/logs" element={<TicketLogs />} />
              </Route>
            </Route>
          </Route>
        </Routes>
      </Router>
    </>
  );
}

export default App;
