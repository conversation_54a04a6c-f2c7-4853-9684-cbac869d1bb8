# Logging Policy

## Overview

This document defines our logging policy to ensure sensitive data never reaches the console and production logs remain clean and secure.

## Core Principles

### ❌ NEVER Log These (Any Environment)

- **PII (Personally Identifiable Information)**

  - Names, phone numbers, email addresses
  - National IDs, passport numbers, employee numbers
  - Addresses, personal details

- **Authentication & Security**

  - Auth tokens, session IDs, API keys
  - Passwords, secrets, private keys
  - JWT tokens, refresh tokens

- **Sensitive Business Data**

  - Financial information, payment details
  - HR data, salary information
  - Customer personal information
  - Raw request/response bodies

- **Large Data Structures**
  - Arrays with >10 items
  - Objects with >5 properties
  - Full database records
  - Complete API responses

### ✅ ALLOWED (Development Only)

- **Counts and Metrics**

  - Array lengths, object counts
  - Performance timings
  - Feature flag states
  - Non-sensitive IDs (project IDs, etc.)

- **Safe Debugging Info**
  - Component lifecycle events
  - API call status (success/failure)
  - Error types (without details)
  - User actions (without personal data)

### ✅ ALLOWED (Production)

- **High-level Events**
  - Application startup/shutdown
  - Critical errors (via proper logger)
  - Security events (via proper logger)
  - Performance metrics (via proper logger)

## Implementation

### Use the Logger Utility

```typescript
import { logger, useDevLog, logSummary, logMetric } from "@/utils/logger";
import { redactEmployee, summarize } from "@/utils/redactors";

// ✅ Good - Safe logging
logger.info("Employees loaded", { count: employees.length });
logMetric("api_call_duration", 150, { endpoint: "/api/employees" });

// ✅ Good - Redacted data
const safeEmployees = employees.map(redactEmployee);
logSummary("Employee data", safeEmployees);

// ✅ Good - Development only
const devLog = useDevLog();
devLog("Debug info", { nonSensitiveData: "value" });

// ❌ Bad - Direct console usage
// console.log('Employees:', employees); // Contains PII
// console.log('User data:', user); // Contains sensitive info
```

### Redaction Examples

```typescript
// ❌ Bad - Logs sensitive data
// console.log('Employee:', employee);

// ✅ Good - Redacted employee data
logger.info("Employee processed", redactEmployee(employee));
// Output: { id: "EMP001", department: "Sales", role: "Manager" }

// ❌ Bad - Logs large arrays
// console.log('All employees:', employees);

// ✅ Good - Summarized data
logSummary("Employees loaded", employees, 3);
// Output: { count: 100, sample: [{ id: "EMP001" }, { id: "EMP002" }, { id: "EMP003" }] }
```

## Environment-Specific Behavior

### Development

- All log levels enabled
- Console output visible
- Debug information available
- Data redaction applied

### Production

- Only `error` and `warn` levels
- Console methods muted
- All data redacted
- Logs sent to external service (if configured)

## Enforcement

### Build-Time

- ESLint rule prevents `console.*` usage
- Vite strips console statements in production builds
- TypeScript compilation validates logger usage

### Runtime

- Console methods overridden in production
- Logger utility enforces redaction
- Environment guards prevent sensitive logging

### CI/CD

- Pre-commit hooks check for console usage
- Build process validates no console statements
- Automated testing ensures logging compliance

## Migration Guide

### From Console to Logger

```typescript
// Before
// console.log('Total employees loaded:', employees.length, employees.slice(0,3));

// After
logger.info("Employees loaded", { count: employees.length });
```

### From Debug Logs to Dev-Only Logs

```typescript
// Before
// console.log('Sample employees:', employees.slice(0, 3));

// After
const devLog = useDevLog();
devLog(
  "Employee sample",
  employees.slice(0, 1).map((e) => e.id)
);
```

### From Error Logs to Safe Error Logs

```typescript
// Before
console.error("API Error:", error);

// After
logger.error("API call failed", redactError(error));
```

## Code Review Checklist

- [ ] No direct `console.*` usage
- [ ] All logged data is redacted
- [ ] No PII in log messages
- [ ] Large data structures summarized
- [ ] Development-only logs use `useDevLog()`
- [ ] Error logs use `redactError()`
- [ ] API responses use `redactApiResponse()`

## Tools and Utilities

### Logger Functions

- `logger.error(message, ...args)` - Error logging
- `logger.warn(message, ...args)` - Warning logging
- `logger.info(message, ...args)` - Info logging
- `logger.debug(message, ...args)` - Debug logging (dev only)

### Redaction Functions

- `redactEmployee(employee)` - Safe employee data
- `redactCustomer(customer)` - Safe customer data
- `redactProject(project)` - Safe project data
- `redactVehicle(vehicle)` - Safe vehicle data
- `redactSiteVisit(visit)` - Safe site visit data
- `redactApiResponse(response)` - Safe API response
- `redactError(error)` - Safe error data

### Utility Functions

- `useDevLog()` - Development-only logging hook
- `logSummary(message, data, maxItems)` - Summarized logging
- `logMetric(metric, value, context)` - Metric logging
- `summarize(list, pick, sampleSize)` - Array summarization

## Monitoring and Alerts

- Monitor for console usage in production
- Alert on sensitive data in logs
- Track logging compliance metrics
- Regular security audits of log content

## Contact

For questions about this logging policy, contact the development team or create an issue in the repository.
