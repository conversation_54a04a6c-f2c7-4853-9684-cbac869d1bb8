#!/usr/bin/env node

/**
 * Husky Setup Script
 *
 * This script sets up <PERSON>sky pre-commit hooks to enforce logging policy.
 */

import { execSync } from "child_process";
import fs from "fs";
import path from "path";

const HOOKS_DIR = ".husky";
const PRE_COMMIT_HOOK = path.join(HOOKS_DIR, "pre-commit");

function setupHusky() {
  try {
    // console.log('🔧 Setting up Husky pre-commit hooks...');

    // Create .husky directory if it doesn't exist
    if (!fs.existsSync(HOOKS_DIR)) {
      fs.mkdirSync(HOOKS_DIR, { recursive: true });
      // console.log('📁 Created .husky directory');
    }

    // Create pre-commit hook
    const preCommitContent = `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-commit checks..."

# Run linting
echo "📝 Running ESLint..."
npm run lint

# Check for console usage
echo "🔍 Checking for console usage..."
npm run check-console

echo "✅ Pre-commit checks passed!"
`;

    fs.writeFileSync(PRE_COMMIT_HOOK, preCommitContent);

    // Make the hook executable
    if (process.platform !== "win32") {
      execSync(`chmod +x ${PRE_COMMIT_HOOK}`);
    }

    // console.log('✅ Pre-commit hook created successfully!');
    // console.log('📋 The hook will run:');
    // console.log('   - ESLint checks');
    // console.log('   - Console usage validation');
    // console.log('');
    // console.log('💡 To test the hook manually:');
    // console.log('   npm run pre-commit');
  } catch (error) {
    console.error("❌ Error setting up Husky:", error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupHusky();
}

export { setupHusky };
