#!/usr/bin/env node

/**
 * Console Usage Checker
 *
 * This script checks for console usage in the codebase and fails if any are found.
 * It's designed to be run in CI/CD pipelines and pre-commit hooks.
 */

import { execSync } from "child_process";
import path from "path";

// Configuration
const CONFIG = {
  // Directories to check
  srcDir: "src",

  // File patterns to include
  includePatterns: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"],

  // File patterns to exclude
  excludePatterns: [
    "**/*.spec.*",
    "**/*.test.*",
    "**/*.stories.*",
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/coverage/**",
  ],

  // Console methods to check for
  consoleMethods: [
    "log",
    "info",
    "debug",
    "table",
    "dir",
    "trace",
    "group",
    "groupEnd",
    "groupCollapsed",
    "time",
    "timeEnd",
    "timeLog",
    "count",
    "countReset",
    "clear",
  ],

  // Allow these console methods (for critical errors)
  allowedMethods: ["warn", "error"],

  // Exit codes
  EXIT_SUCCESS: 0,
  EXIT_FAILURE: 1,
};

/**
 * Run ripgrep to find console usage
 */
function findConsoleUsage() {
  try {
    // Build the ripgrep command
    const patterns = CONFIG.consoleMethods
      .filter((method) => !CONFIG.allowedMethods.includes(method))
      .map((method) => `console\\.${method}\\b`)
      .join("|");

    const includeGlobs = CONFIG.includePatterns
      .map((pattern) => `--glob '${pattern}'`)
      .join(" ");
    const excludeGlobs = CONFIG.excludePatterns
      .map((pattern) => `--glob '!${pattern}'`)
      .join(" ");

    const command = `rg -n "${patterns}" ${CONFIG.srcDir} ${includeGlobs} ${excludeGlobs}`;

    // console.log(`🔍 Checking for console usage with command: ${command}`);

    const output = execSync(command, {
      encoding: "utf8",
      stdio: "pipe",
    });

    return output.trim();
  } catch (error) {
    // ripgrep returns non-zero exit code when no matches are found
    if (error.status === 1) {
      return ""; // No matches found
    }
    throw error;
  }
}

/**
 * Parse and format console usage results
 */
function parseResults(output) {
  if (!output) {
    return [];
  }

  const lines = output.split("\n");
  const results = [];

  for (const line of lines) {
    if (line.trim()) {
      const [filePath, lineNumber, ...rest] = line.split(":");
      const content = rest.join(":").trim();

      results.push({
        file: filePath,
        line: parseInt(lineNumber),
        content: content,
      });
    }
  }

  return results;
}

/**
 * Generate a detailed report
 */
function generateReport(results) {
  if (results.length === 0) {
    return {
      success: true,
      message: "✅ No console usage found!",
      details: [],
    };
  }

  const fileGroups = results.reduce((groups, result) => {
    if (!groups[result.file]) {
      groups[result.file] = [];
    }
    groups[result.file].push(result);
    return groups;
  }, {});

  const details = Object.entries(fileGroups).map(([file, fileResults]) => ({
    file,
    count: fileResults.length,
    lines: fileResults.map((r) => r.line),
  }));

  return {
    success: false,
    message: `❌ Found ${results.length} console usage(s) in ${
      Object.keys(fileGroups).length
    } file(s)`,
    details,
  };
}

/**
 * Print the report
 */
function printReport(report) {
  // console.log('\n' + '='.repeat(60));
  // console.log('CONSOLE USAGE CHECK REPORT');
  // console.log('='.repeat(60));
  // console.log(report.message);

  if (!report.success && report.details.length > 0) {
    // console.log('\n📋 Details:');
    report.details.forEach((detail) => {
      // console.log(`  📁 ${detail.file}`);
      // console.log(`     Count: ${detail.count} usage(s)`);
      // console.log(`     Lines: ${detail.lines.join(', ')}`);
    });

    // console.log('\n💡 To fix these issues:');
    // console.log('  1. Replace // console.log/info/debug with logger.info/debug');
    // console.log('  2. Replace console.table/dir with logSummary()');
    // console.log('  3. Use redactEmployee(), redactCustomer(), etc. for data');
    // console.log('  4. Use useDevLog() for development-only logging');
    // console.log('\n📖 See docs/logging-policy.md for detailed guidelines');
  }

  // console.log('='.repeat(60) + '\n');
}

/**
 * Main function
 */
function main() {
  try {
    // console.log('🚀 Starting console usage check...');

    const output = findConsoleUsage();
    const results = parseResults(output);
    const report = generateReport(results);

    printReport(report);

    if (report.success) {
      // console.log('🎉 Console usage check passed!');
      process.exit(CONFIG.EXIT_SUCCESS);
    } else {
      // console.log('💥 Console usage check failed!');
      process.exit(CONFIG.EXIT_FAILURE);
    }
  } catch (error) {
    console.error("❌ Error running console usage check:", error.message);
    process.exit(CONFIG.EXIT_FAILURE);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { findConsoleUsage, parseResults, generateReport, CONFIG };
